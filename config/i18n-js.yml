# Split context in several files.
# By default only one file with all translations is exported and
# no configuration is required. Your settings for asset pipeline
# are automatically recognized.
#
# If you want to split translations into several files or specify
# locale contexts that will be exported, just use this file to do
# so.
#
# If you're going to use the Rails 3.1 asset pipeline, change
# the following configuration to something like this:
#
#   translations:
#     - file: "app/assets/javascripts/i18n/translations.js"
#
# If you're running an old version, you can use something
# like this:
#
#   translations:
#     - file: "public/javascripts/translations.js"
#       only: "*"
#
fallbacks:
  es-CL: :es
translations:
  - file: 'app/assets/javascripts/globals/translations.js'
    only: ['*.javascripts.*', '*.colors_title', '*.color.*']
  - file: 'app/assets/javascripts/v5/globals/translations.js'
    only: ['*.js.v5.*']
  - file: 'engines/lux/app/assets/javascripts/lux/v2/globals/translations.js'
    only: ['*.lux.v2.js.*']
  - file: 'engines/pioneer/app/assets/javascripts/pioneer/v2/globals/translations.js'
    only: ['*.pioneer.js.v2.*']

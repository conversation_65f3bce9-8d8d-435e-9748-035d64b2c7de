Gp::Application.configure do
  # Code is not reloaded between requests
  config.cache_classes = true

  # Full error reports are disabled and caching is turned on
  config.consider_all_requests_local = false
  config.action_controller.perform_caching = true

  # Do not eager load code on boot.
  config.eager_load = true

  config.cache_classes = true

  # Disable Rails's static asset server (Apache or nginx will already do this)
  config.serve_static_files = true
  # config.assets.css_compressor = :yui
  # config.assets.js_compressor = :closure

  # Compress JavaScripts and CSS
  config.assets.compress = true

  # Don't fallback to assets pipeline if a precompiled asset is missed
  config.assets.compile = true

  # Generate digests for assets URLs
  config.assets.digest = true

  # Enable stdout logger
 # config.logger = Logger.new(STDOUT)

  # Set log level

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation can not be found)
  config.i18n.fallbacks = true

  # Send deprecation notices to registered listeners
  config.active_support.deprecation = :notify

  # TODO: This should be configured to work with the new infra for Avenida.co.ar
  # For now, this is using the GP SMTP server to delivery errors and exceptions
  config.middleware.use ExceptionNotification::Rack,
    :ignore_crawlers => %w{Googlebot bingbot},
    :email => {
      :email_prefix => "[Avenida Platform - Exception] ",
      :sender_address => %{"Backend Exception Notification" <<EMAIL>>},
      :exception_recipients => %w{<EMAIL>},
      :delivery_method => :smtp,
      :smtp_settings => {
        :address => "email-smtp.us-east-1.amazonaws.com",
        :port => 587,
        :enable_starttls_auto => true,
        :user_name => "AKIAJIDFDI65WED6ANPQ",
        :password => "AqHXtfA7SIhgcyuOcFNkxPYX5+vjIzZcm8Nvxn3PY/AP",
        :authentication => :login
      }
    },
    :faily => {}

    config.action_mailer.smtp_settings = {
      address: APP_CONFIG[Rails.env]['postmark']['smtp_server'],
      user_name: APP_CONFIG[Rails.env]['postmark']['api_key'],
      password: APP_CONFIG[Rails.env]['postmark']['api_key'],
      port: 587,
      authentication: :cram_md5,
      enable_starttls_auto: true
    }

    config.action_mailer.delivery_method = :smtp
    config.active_record.raise_in_transactional_callbacks = true
end

Gp::Application.configure do
  # Settings specified here will take precedence over those in config/application.rb

  # Show full error reports and disable caching
  config.consider_all_requests_local       = true
  config.action_controller.perform_caching = false

  # Do not eager load code on boot.
  config.eager_load = false

  config.cache_classes = false

  config.log_level = :debug

  # Don't care if the mailer can't send
  config.action_mailer.raise_delivery_errors = true
  # config.action_mailer.perform_deliveries = false
  config.action_mailer.default_url_options = { host: 'localhost', port: 3000, protocol: 'http' }

  # Para usar la gema letter_opener en vez de enviar el email
  config.action_mailer.delivery_method = :letter_opener

  # For now, this is using the GP SMTP server to delivery errors and exceptions
  config.middleware.use ExceptionNotification::Rack,
                        :ignore_crawlers => %w{Googlebot bingbot},
                        :email => {
                            :email_prefix => "[Avenida Platform - Exception] ",
                            :sender_address => %{"Backend Exception Notification" <<EMAIL>>},
                            :exception_recipients => %w{<EMAIL>},
                            :delivery_method => :letter_opener,
                            :smtp_settings => {
                                :address => "email-smtp.us-east-1.amazonaws.com",
                                :port => 587,
                                :enable_starttls_auto => true,
                                :user_name => "AKIAJIDFDI65WED6ANPQ",
                                :password => "AqHXtfA7SIhgcyuOcFNkxPYX5+vjIzZcm8Nvxn3PY/AP",
                                :authentication => :login
                            }
                        },
                        :faily => {}

  # Print deprecation notices to the Rails logger
  config.active_support.deprecation = :log

  # Do not compress assets
  config.assets.compress = false

  # Expands the lines which load the assets
  config.assets.debug = true

  # Provisto por la gema: 'quiet_assets' para activar el tema del debugging de assets de nuevo
  config.quiet_assets = true

  # Paperclip.options[:command_path] = "/usr/local/bin"
  Paperclip.options[:command_path] = "/usr/bin/"

  # config.middleware.insert_after(ActionDispatch::Static, Rack::LiveReload)
  # ...or, change some options...
  # config.middleware.insert_before(
  #   Rack::Lock, Rack::LiveReload,
  #   :min_delay => 500,
  #   :max_delay => 10000,
  #   :port => 56789,
  #   :host => 'myhost.cool.wow',
  #   :ignore => [ %r{dont/modify\.html$} ]
  # )

   ###-----------------### PROFILING AND PERFORMANCE ###-----------------###
  # Performance testing gems
  # config.middleware.use(Rack::RubyProf, path: '/Users/<USER>/Sites/goodpeople/europa_platform/tmp/profile')
  # config.middleware.use "Rack::RequestProfiler"

  # Disable Rack Mini Profiler by default
  # Rack::MiniProfiler.config.enabled = false

  # Setup for Bullet
  # config.after_initialize do
  #   Bullet.enable = true
  #   Bullet.bullet_logger = true
  #   Bullet.add_footer = true
  #   # Bullet.raise = true
  # end
  ###--------------### END - PROFILING AND PERFORMANCE ###--------------###

  # Prevent "undefined class/module Brand" error
  # http://aaronvb.com/articles/37-rails-caching-and-undefined-class-module
  # config.after_initialize do
  #   Dir["#{Rails.root}/app/models/**/*.rb"].each { |file| require_dependency file }
  # end

  config.active_record.migration_error = :page_load
  config.active_record.raise_in_transactional_callbacks = true
end

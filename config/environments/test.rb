Gp::Application.configure do
  # Settings specified here will take precedence over those in config/application.rb
  config.eager_load = false

  # The test environment is used exclusively to run your application's
  # test suite. You never need to work with it otherwise. Remember that
  # your test database is "scratch space" for the test suite and is wiped
  # and recreated between test runs. Don't rely on the data there!
  config.cache_classes = true

  # Configure static asset server for tests with Cache-Control for performance
  config.serve_static_files = true
  config.static_cache_control = "public, max-age=3600"

  # Show full error reports and disable caching
  config.consider_all_requests_local       = true
  config.action_controller.perform_caching = false
  config.cache_store = :null_store
  config.log_level = :debug

  # Raise exceptions instead of rendering exception templates
  config.action_dispatch.show_exceptions = true

  # Disable request forgery protection in test environment
  config.action_controller.allow_forgery_protection    = false

  # Tell Action Mailer not to deliver emails to the real world.
  # The :test delivery method accumulates sent emails in the
  # ActionMailer::Base.deliveries array.
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.raise_delivery_errors = false

  config.action_mailer.smtp_settings = {
      address: APP_CONFIG[Rails.env]['mail_test']['smtp_server'],
      port: APP_CONFIG[Rails.env]['mail_test']['port'],
      domain: 'gmail.com',
      authentication: :plain,
      enable_starttls_auto: true,
      user_name: APP_CONFIG[Rails.env]['mail_test']['email'],
      password: APP_CONFIG[Rails.env]['mail_test']['password'],

  }

  # Print deprecation notices to the stderr
  config.active_support.deprecation = :stderr
  Paperclip.options[:command_path] = "/usr/bin/"

  config.active_record.raise_in_transactional_callbacks = true
end

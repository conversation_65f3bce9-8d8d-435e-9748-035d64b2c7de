require 'api_constraints'

Gp::Application.routes.draw do

  resources :gateway_credentials
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'

  get ':store/feed', to: 'mkp/feeds#index', format: 'rss'

  constraints domain: "avenida.com" do
    get "/" => redirect { |params| "https://www.avenida.com.ar" }
  end

  root to: 'landings#show'

  get ':network', to: 'landings#show',
    as: :network_root,
    constraints: { network: Network.all_active_param_regex }

  get 'home', to: 'landings#show'

  get '404', :to => "errors#not_found"
  get '422', :to => "errors#unacceptable"
  get '500', :to => "errors#internal_error"

  # User management
  resources :users, only: [:new, :create] do
    post 'follow', to: 'users#follow'
    post 'unfollow', to: 'users#unfollow'
  end


  match 'login', to: 'user_sessions#new', as: :login, protocol: SECURE_PROTOCOL, via: [:get, :options]
  post 'login', to: 'user_sessions#new_login', as: :new_login, protocol: SECURE_PROTOCOL
  get 'logout', to: 'user_sessions#destroy', as: :logout, protocol: SECURE_PROTOCOL
  get 'users/email_available', to: 'users#check_email_availability', as: :email_available
  get 'users/login_available', to: 'users#check_login_availability', as: :login_available
  get 'users/signup', to: 'users#signup'
  match 'signup', to: 'users#new', as: :sign_up, protocol: SECURE_PROTOCOL, via: [:get, :post, :options]
  get 'users/recommendations', to: 'users#recommendations'

  resources :password_resets, only: [:new, :create, :edit, :update], protocol: SECURE_PROTOCOL do
    get :done, on: :collection
  end

  # Third-party authentication
  match 'auth/:provider/callback'   , to: 'authentications#authorized', via: [:get, :post]
  get   'auth/check'                , to: 'authentications#check'
  match 'auth/failure'              , to: 'authentications#failure', via: [:get, :post]
  post 'authentications'            , to: 'authentications#create', as: :authentications, protocol: SECURE_PROTOCOL
  delete 'authentications/:provider', to: 'authentications#destroy', as: :authentication

  resources :partial_signups, only: [:create]

  match 'search', to: 'search#search', as: :search, via: [:get, :post]

  # Settings routes
  scope protocol: SECURE_PROTOCOL do
    get 'settings/account', to: 'settings#account'
    put 'settings/account', to: 'settings#update_account'
    get 'settings/password', to: 'settings#password'
    put 'settings/password', to: 'settings#update_password'
    get 'settings/widgets', to: 'settings#widgets'
    get 'settings/orders', to: 'settings#orders'
    get 'settings/apps', to: 'settings#apps'
    get 'settings/admins', to: 'settings#admins'
    post 'settings/add_admin', to: 'settings#add_admin', as: :add_admin
    put 'settings/admins/:id', to: 'settings#update_admin', as: :settings_admin
    delete 'settings/remove_admin', to: 'settings#remove_admin', as: :remove_admin
  end

  match 'widgets/products', to: 'widgets#shop_products', via: [:get, :post]
  get 'widgets/olark_chat', to: 'widgets#olark_chat', via: [:get, :post]

  resources :newsletter_subscriptions, only: [:create]

  # Static pages
  scope '(:network)', network: Network.all_active_param_regex do
    get 'pages/manifesto', to: redirect('/')
    get 'pages/team', to: redirect('/')
    get 'pages/social-responsibility', to: redirect('/')
    get 'pages/press', to: redirect('/')
    get 'pages/brands', to: redirect('/')
    get 'pages/privacy', to: redirect('/')
    get 'pages/terms', to: redirect('/')
    get 'pages/cambios-y-devoluciones', to: 'pages#changes'
    get 'pages/politicas-de-privacidad', to: 'pages#privacy'
    get 'pages/preguntas-frecuentes', to: 'pages#faqs'
    get 'pages/terminos-y-condiciones', to: 'pages#terms'
    get 'pages/stores', to: redirect('/')
    get 'pages/faqs', to: redirect('/')
    get 'pages/contact', to: redirect('/')
    get 'pages/careers', to: redirect('/')
  end

  # To handle the Custom Landings for each Network
  # scope ':network', network: /us/ do
  get 'jokes', to: 'home#jokes', as: :jokes_home
  get 'jokes', to: redirect{ |p, request| "?#{request.query_string}" }
  # end
  # To handle the Hotsale URL for Argentina, the network is not optional

  get 'blog', to: redirect('/')
  # To view posts, the network is not optional
  scope ':network', network: /us|ar/ do
    get 'blog', to: redirect('/')
    get 'blog/:id(/:slug)', to: redirect('/')
  end

  post 'users/brand_application', to: 'users#brand_application', as: :brand_application
  get 'brands/:id/admins', to: 'brand_admins#index'
  post 'brands/:id/admins', to: 'brand_admins#create'
  get 'users/current/:id', to: 'users#current', as: :users_current

  namespace :social, path: '' do
    get 'community', to: 'community#index'

    get 'feeds/all', to: 'feeds#all'
    get 'feeds/profiles/:login', to: 'feeds#profile', constraints: { login: /[0-9A-Za-z\-\.\_]+/ }, as: :feeds_profile
    get 'feeds/home', to: 'feeds#home'
    get 'feeds/search', to: 'feeds#search'

    resource :profile, only: [:edit, :update] do
      get 'brand', on: :collection
      get 'external_exists'
    end

    get ':login/followers', to: 'follows#followers', constraints: { login: /[0-9A-Za-z\-\.\_]+/ }, as: :followers
    get ':login/following', to: 'follows#following', constraints: { login: /[0-9A-Za-z\-\.\_]+/ }, as: :following

    post 'whatsups/scrap', to: 'whatsups#scrap'
    resources :whatsups, only: [:show, :create, :destroy] do
      post :favorites, to: 'favorites#create', id_param: 'whatsup_id'
      delete :favorites, to: 'favorites#destroy', id_param: 'whatsup_id'
      get 'recommended_products', on: :member
      resources :comments, only: [:create], as: :social_comments
    end

    resources :albums, only: [:show, :create, :destroy] do
      post :favorites, to: 'favorites#create', id_param: 'album_id'
      delete :favorites, to: 'favorites#destroy', id_param: 'album_id'
      resources :comments, only: [:create], as: :social_comments
      get 'recommended_products', on: :member
    end

    match 'after_signup/follow_recommendations',
      to: 'after_signup#follow_recommendations', via: [:get, :post]
    get 'after_signup/invite_friends', to: 'after_signup#invite_friends'
    post 'after_signup/send_invitations', to: 'after_signup#send_invitations'
    resources :invitations, only: [:create]

    resource :unpublished_pictures, only: [:create]

    get 'mentions/mentionable', to: 'mentions#mentionable'

    post '/sports/:sport_id/follow', to: 'sports#follow'
    post '/sports/:sport_id/unfollow', to: 'sports#unfollow'
  end

  resources :banners, only: [:index, :show, :update]


  scope '(:network)', network: Network.all_active_param_regex do
    match :products, controller: 'mkp/catalog', action: 'index', as: :catalog, via: [:get, :post]
    namespace :mkp, path: 'shop' do
      resources :carts, only: [:show] do
        get 'items', on: :collection
        post 'sync', on: :collection
      end
    end
  end

  namespace :api do
    get  'recommendations', to: 'recommendations#all'
    get  'social_users', to: 'users#all'
    post 'social_users', to: 'sign_ups#social_user'
    post 'brands',       to: 'sign_ups#brand'
    put  'disable_followers_module', to: 'sessions#disable_followers_module'

    resources :suggestions, only: [:index]

    namespace :v1 do
      post 'authenticate', to: 'authentication#authenticate'
      get  'categories', to: 'categories#index'
      get 'categories/:id', to: 'categories#show'
      get  'manufacturers', to: 'manufacturers#index'
      get 'manufacturers/:id', to: 'manufacturers#show'
      get  'shops', to: 'shops#index'
      get 'shops/:id', to: 'shops#show'
      get  'orders', to: 'suborders#index'
      get 'orders/:id', to: 'suborders#show'
      resources :products do
        put 'async', on: :member, to: 'products#update_async'
      end
    end

    namespace :angular_app do
      namespace :v1, path: '' do
        namespace :integration, path: '' do
          scope 'aerolineas_argentinas', as: 'aerolineas_argentinas' do
            post 'frequent_traveler_information', to: 'aerolineas_argentinas#frequent_traveler_information', as: 'frequent_traveler_information'
            post 'mile_accreditation', to: 'aerolineas_argentinas#mile_accreditation', as: 'mile_accreditation'
          end
        end

        namespace :bna do
          get 'hash_validator/validate', to: 'hash_validator#validate'
        end
      end
    end

    namespace :angular_app do
      namespace :v1, path: '' do
        namespace :integration, path: '' do
          post 'aerolineas_argentinas_mile_exchange', to: 'purchases#aerolineas_argentinas_mile_exchange', as: 'aerolineas_argentinas_mile_exchange'
        end
      end
    end

    namespace :mkp, path: 'shop' do
      post :votes, controller: 'votes', action: 'create'
      delete :votes, controller: 'votes', action: 'destroy'
      match 'variants/:color_id', controller: 'variants', action: 'show', defaults: { format: 'json' }, via: [:get, :post]
      post :notifications, controller: 'webhook', action: 'notifications'
      post '/shippings/webhook', controller: 'tracking', action: 'create'
    end

    namespace :mkp, path: 'webhooks', constraints: { format: :json } do
      post 'shopify/:integration_id', to: 'webhook#shopify', as: :shopify_webhooks
      post 'shipnow', to: 'webhook#shipnow', as: :shipnow_webhook
      #post 'mercadolibre', to: 'webhook#mercadolibre', as: :mercadolibre_webhooks
      post 'lion', to: 'webhook#lion', as: :lion_webhooks
      get  'pickit', to: 'webhook#pickit', as: :pickit_webhook
      post  'payments', to: 'webhook#payments', as: :payments_webhook
    end

    namespace :mkp do
      resources :tracking, only: :show

      namespace :checkout, constraints: { format: :json }, protocol: SECURE_PROTOCOL do
        resources :addresses, only: [:index, :create, :update]

        resources :delivery_options, only: [:create] do
          post 'set', on: :collection
        end

        scope 'coupons', as: 'coupons' do
          post 'apply', to: 'coupons#apply'
          delete 'clear', to: 'coupons#clear'
        end

        scope 'payment', as: 'payment' do
          post 'verify', to: 'payment#verify'
        end

        scope 'cart_items', as: 'cart_items' do
          post 'remove', to: 'cart_items#remove'
        end

        scope 'pickup', as: 'pickup' do
          post 'quotation', to: 'pickup#get_pickup_quotation'
          post 'pickup_point', to: 'pickup#get_selected_pickup_point'
        end
      end
    end
  end

  mount Lux::Engine, at: 'manage'

  # Facebook app redirect to Lux from requests of shops previously set
  scope '(:network)', network: Network.all_active_param_regex do
    # Facebook uses POST to request a page
    match 'pages/facebook-eshop-ar' => redirect('/manage/facebook_app/eshop'), via: [:get, :post]
    match 'pages/facebook-app/:shop_id' => redirect("/manage/facebook_app/goodpeople_shop/%{shop_id}"), via: [:get, :post], as: 'pages_facebook_app'
  end

  scope 'admin' do
    mount Pioneer::Engine, at: 'pioneer'
    mount Galileo::Engine, at: 'galileo'
    mount Faily::Engine, at: 'faily'
  end

  resources :feedbacks, only: [:create]


  # TODO osti, revisar, catalog_root is still in use
  scope ':network', network: Network.all_active_param_regex do


    namespace :mkp, path: '' do
      match ':manufacturer_id/:product_slug', to: 'variants#show', via: [:get, :post]
      get :products, to: 'catalog#index', as: 'catalog_root'
      match ':slug_1(/:slug_2)(/:slug_3)(/:slug_4)', to: 'catalog#legacy', as: :slugged_catalog, via: [:get, :post]
    end
    namespace :mkp, path: 'shop' do
      match :variants, to: redirect { |params, request| Mkp::Catalog::UrlHandler.path(request.params, { slugify: false }) }, via: [:get, :post]
      match :products, to: 'catalog#index', as: :catalog, via: [:get, :post]

      resources :orders, only: [] do
        resources :orders_reviews, only: [:new, :create], path: 'reviews'
      end

      root to: redirect('/')

      resources :products, only: [] do
        resources :questions, only: [:create]
        resources :reviews, only: [:create] do
          resource :report, only: [:create, :destroy]
        end
        post :favorites, to: 'favorites#create', id_param: 'product_id'
        delete :favorites, to: 'favorites#destroy', id_param: 'product_id'
      end

      resources :coupons, only: [:create, :destroy]

      get  'checkout',   to: 'checkout#init', protocol: SECURE_PROTOCOL
      scope 'checkout', as: 'checkout', protocol: SECURE_PROTOCOL do
        # TODO: Please, review this routes.
        post 'authorize_payment', to: 'checkout#authorize_payment'
        post 'successful_authorization', to: 'checkout#successful_authorization'
      end

      get 'brands', to: 'brands#index'

      match ':manufacturer_id/:product_slug', to: redirect { |params, _| \
        "/#{params[:network]}/#{params[:manufacturer_id]}/#{params[:product_slug]}"
      }, via: [:get, :post]
    end
  end

  if ENABLE_SIDEKIQ_WEB
    require 'sidekiq/web'
    require 'sidekiq_status/web'
    mount Sidekiq::Web => '/sidekiq'
  end

  draw :api_angular_v1
  draw :api_v2
  mount MailPreview => 'mail_view'
end

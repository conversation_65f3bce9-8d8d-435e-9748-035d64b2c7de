unless Rails.env.development? || Rails.env.test?
  Paperclip.interpolates :token do |attachment, style|
    attachment.instance.token
  end

  Paperclip.interpolates :friendly_name do |attachment, style|
    attachment.instance.friendly_id
  end

  Paperclip.interpolates :public_folder do |attachment, style|
    File.join(attachment.instance.token[20..21], attachment.instance.token[7..9])
  end

  Paperclip::Attachment.default_options.merge!(
    {
        storage: :s3,
        :s3_protocol => :https,
        s3_credentials: "#{Rails.root}/config/aws.yml",
        s3_host_alias: PAPERCLIP['s3_host_alias'],   # statics.avenida.com add for probe new static aws files
        #s3_host_alias: 'statics.avenida.com',   #add for probe new static aws files
        path: 'avenida/:attachment/:public_folder/:token_:style.:extension',
        #url: ':s3_path_url',
        url: ':s3_alias_url', #add for probe statics
        bucket: PAPERCLIP['bucket'],
        convert_options: { all: '-auto-orient' },
        s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate }
    })
  Paperclip::Attachment.default_options[:use_timestamp] = false
end

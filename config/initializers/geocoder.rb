Geocoder.configure(
  # Hay varios disponibles, Google, Yahoo, Bing, etc (https://github.com/alexreisner/geocoder#listing-and-comparison)
  :lookup => :google,
  :ip_lookup => :freegeoip,
  # Para el caso de Google, necesitamos API cuando usamos las version Premier
  # config.api_key = ""

  # Timeout para hacer geocodings lookup (default 3)
  :timeout => 5,

  # Unidad por defecto es:
  :units => :km,

  # Para el cache
  :cache => $redis
)

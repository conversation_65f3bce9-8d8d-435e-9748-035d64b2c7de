AbstractController::Rendering.module_eval do
  private

  # RATIONALE
  # The idea is to plug into the AbstractController::Rendering#render call
  # and use the keys that they use to properly select the :app_template_file instead
  # of the action (this :app_template_file is set in the #_normalize_args
  # in the Rendering module) so we are going to override that or set that.
  def _process_options(options)
    # if #render_properly is used so a :version key should be in the options hash
    #
    # I benchamark this also and there was not significant difference by assigning
    # in the "if" and this is more clear
    if options.key?(:version)
      _version = options.delete(:version)
      _device = options.delete(:device)
      _prefixes = options[:prefixes]

      # By doing a benchmark (lib/dev_commands/benchmarks/render_properly_prefixes.rb)
      # this is the fastest option, this is important beacause this is run
      # everytime someone is rendering something
      options[:prefixes] = _prefixes.each_with_object([]) do |prefix, result|
        result << "#{prefix}/#{_version}/#{_device}" if _device
        result << "#{prefix}/#{_version}"
        result << prefix
      end
    end
  end
end

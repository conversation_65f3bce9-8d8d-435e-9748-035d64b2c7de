# Tag requests with an UUID so we can keep track of them, grep them, etc.
# This is in order to minimize the pain of not being able to follow a complete
# request on our development and production logs.
Gp::Application.config.log_tags = [
  :host,
  :uuid,
  lambda { |request| request.cookie_jar["_session_id"] }
]

Rails::Rack::Logger.class_eval do
  protected

  def started_request_message(request)
    'Started %s "%s" for %s at %s on [%s]' % [
    request.request_method,
    request.filtered_path,
    request.ip,
    Time.now.to_default_s,
    request.user_agent ]
  end
end


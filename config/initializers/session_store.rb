# Be sure to restart your server when you modify this file.

# Gp::Application.config.session_store :cookie_store, key: '_gp_session'

# Use the database for sessions instead of the cookie-based default,
# which shouldn't be used to store highly confidential information
# (create the session table with "rails generate session_migration")
# Gp::Application.config.session_store :active_record_store

# debug redis

# muestra toda comunicacion con redis
# telnet 127.0.0.1 6379
# MONITOR

# aplicacion python que corre web
# redweb
# http://127.0.0.1:8081/


# Configuracion
# config/initializers/session_store.rb
# My::Application.config.session_store :redis_store, :servers => "redis://:secret@127.0.0.1:6999/10"

# Instead of :servers => "" you can also use a hash, e.g. :servers => { :host => "localhost", :port => 6380, :db => 0, :namespace => 'sessions' }

# Optional params:
#   :expire_in => TTL in seconds # :expires_in or :expire_after work as well

redis_host = APP_CONFIG[Rails.env]['redis']['host']
redis_port = APP_CONFIG[Rails.env]['redis']['port']
redis_db   = APP_CONFIG[Rails.env]['redis']['db']
Gp::Application.config.session_store :redis_store, servers: { host: redis_host, port: redis_port, db: redis_db, namespace: 'sessions' }, expire_in: 24.hours

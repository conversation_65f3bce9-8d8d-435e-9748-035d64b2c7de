require 'ipaddr'

class Rack::Attack
  ### Throttle Spammy Clients ###

  # If any single client IP is making tons of requests, then they're probably
  # malicious or a poorly-configured scraper. Either way, they don't deserve to
  # hog all of the app server's CPU. Cut them off!

  # Throttle all requests by IP (60rpm)
  #
  # Key: "rack::attack:#{Time.now.to_i/:period}:req/ip:#``{req.ip}"
#  throttle('req/ip', :limit => 300, :period => 5.minutes) do |req|
#    req.ip
#  end


  ### Prevent Brute-Force Login Attacks ###

  # The most common brute-force login attack is a brute-force password attack
  # where an attacker simply tries a large number of emails and passwords to see
  # if any credentials match.
  #
  # Another common method of attack is to use a swarm of computers with
  # different IPs to try brute-forcing a password for a specific account.

  # Throttle POST requests to /login by IP address
  #
  # Key: "rack::attack:#{Time.now.to_i/:period}:logins/ip:#{req.ip}"
  throttle('logins/ip', :limit => 5, :period => 20.seconds) do |req|
    if req.path == '/login' && req.post?
      req.ip
    end
  end
end

# Block requests with /wp-admin (Wordpress stuff)
Rack::Attack.blocklist('block /wp-admin requests') do |req|
  req.url.match('/wp-admin')
end

# Block requests with /proc/self/environ
Rack::Attack.blocklist('block /proc/self/environ requests') do |req|
  req.url.match('/proc/self/environ')
end

# Block requests from EasouSpider User-Agent
Rack::Attack.blocklist('block EasouSpider') do |req|
  # Request are blocked if the return value is truthy
  !(req.user_agent =~ /EasouSpider/).nil?
end

# Block requests from WinInet Test User-Agent
Rack::Attack.blocklist('block WinInet Test') do |req|
  # Request are blocked if the return value is truthy
  !(req.user_agent =~ /WinInet Test/).nil?
end

# Block requests from IE 7 because of suspicious navigation
Rack::Attack.blocklist('block suspicious IE7') do |req|
  # Request are blocked if the return value is truthy
  req.path == '/us/shop' && !(req.user_agent =~ /MSIE 7.0/).nil?
end

# Block requests from IE 6 because of suspicious navigation
Rack::Attack.blocklist('block suspicious IE6') do |req|
  # Request are blocked if the return value is truthy
  req.path == '/us/shop' && !(req.user_agent =~ /MSIE 6.0/).nil?
end

# Block requests from Baiduspider User-Agent and Bot Subnet
# According to this site (http://www.spambotsecurity.com/forum/viewtopic.php?f=7&t=2949)
# all of this are the networks in which Baidu operates
#
#   IPAddr.new('***********/20').include?(req.ip) ||
#   IPAddr.new('************/24').include?(req.ip) ||
#   IPAddr.new('************/21').include?(req.ip) ||
#   IPAddr.new('************/20').include?(req.ip) ||
#   IPAddr.new('**********/16').include?(req.ip) ||
#   IPAddr.new('************/22').include?(req.ip)
#
# But we only notice it from ***********/21 and ***********/22 so that's why
# those are the only ones being block for now
Rack::Attack.blocklist('block Baiduspider') do |req|
  # Request are blocked if the return value is truthy
  !(req.user_agent =~ /Baiduspider/).nil?
end
Rack::Attack.blocklist('block Baiduspider Bot Subnet') do |req|
  # Request are blocked if the return value is truthy
  IPAddr.new('***********/21').include?(req.ip) || IPAddr.new('***********/22').include?(req.ip)
end

# Block requests from a particular IP
Rack::Attack.blocklist('block IP ************') do |req|
  # Request are blocked if the return value is truthy
  '************' == req.ip
end

# Block requests from the SiteUnder Campaign from Pampa Network
Rack::Attack.blocklist('block affiliates running siteunder Campaigns from PampaNetwork') do |req|
  # Request are blocked if the return value is truthy
  req.url.match('affiliateid=5295d79576b89') || req.url.match('affiliateid=55ca93f95dbc0')
end

Rack::Attack.blocklist('block requests with /etc/passwd with fail2ban') do |req|
  # `filter` returns truthy value if request fails, or if it's from a previously banned IP
  # so the request is blocked
  Rack::Attack::Fail2Ban.filter(req.ip, :maxretry => 3, :findtime => 10.minutes, :bantime => 5.minutes) do
    # The count for the IP is incremented if the return value is truthy.
    CGI.unescape(req.query_string) =~ %r{/etc/passwd}
  end
end

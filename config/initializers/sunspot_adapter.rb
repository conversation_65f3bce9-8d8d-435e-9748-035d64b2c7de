class InheritanceInstanceAdapter < Sunspot::Adapters::InstanceAdapter
  def id
    @instance.id
  end

  def index_id
    Sunspot::Adapters::InstanceAdapter.index_id_for(@instance.class.base_class.name, id)
  end
end

if Rails.env.production? || Rails.env.development?
  Sunspot::Adapters::InstanceAdapter.register(InheritanceInstanceAdapter,
                                              Lux::Order,
                                              Lux::Product,
                                              Lux::Shop,
                                              Lux::Suborder,
                                              Lux::Variant)
end

# This file is needed when you got this error:
# ActiveRecord::StatementInvalid: Mysql2::Error: All parts of a PRIMARY KEY must be NOT NULL; if you need NULL in a key, use UNIQUE instead
# If so, please uncomment lines below
# Don't forget to comment them again after you run db:migrate

#require 'active_record/connection_adapters/mysql2_adapter'
#
#class ActiveRecord::ConnectionAdapters::Mysql2Adapter
#  NATIVE_DATABASE_TYPES[:primary_key] = "int(11) auto_increment PRIMARY KEY"
#end

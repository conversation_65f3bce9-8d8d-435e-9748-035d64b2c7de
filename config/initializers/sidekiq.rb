require 'sidekiq'
require 'sidekiq/web'
require 'sidekiq_status/web'

Sidekiq::Web.disable :sessions

Sidekiq.configure_server do |config|
  config.redis = { url: REDIS_URL, :namespace => 'sk' }
end

Sidekiq.configure_client do |config|
  config.redis = { url: REDIS_URL, :namespace => 'sk' }
end

Sidekiq::Web.use(Rack::Auth::Basic) do |user, password|
  [user, password] == [SIDEKIQ_USER, SIDEKIQ_PASSWORD]
end






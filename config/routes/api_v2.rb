namespace :api, defaults: { format: 'json' }do
  namespace :v2 do
    resources :documentations, only: [:index], format: 'slim'
    resources :orders, only: [:index, :create, :show, :update] do
      member do
        post :cancel
        put :update_liquided
      end
    end
    resources :products, only: [:index, :show]
    resources :variants, only: [:show]
    namespace :tdd do
      resources :registers, only: [:create, :show, :update, :destroy]
      resources :members, only: [:update]
    end
  end
end

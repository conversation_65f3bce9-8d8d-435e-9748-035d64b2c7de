ANGULAR_APP_VERSION = "v1"
namespace :api, defaults: { format: 'json' } do
  namespace :angular_app do
    scope module: ANGULAR_APP_VERSION do
      resources :bines, only: :index
      resources :brands, only: [:show]
      resources :categories,  only: :index
      resources :contact, only: :create
      resources :coupons, only: :create

      resources :checkout, only: [] do
        collection do
          get :bines
          get :first_purchase
          post :init
          post :cart_quantity
          post :coupon
          post :remove_coupon
          post :address
          post :delivery_options
          post :subscription_newsletter
          post :withdrawal_branch
          post :payment
          post :done
          post :authorize_todopago
          post :installments
          post :installments_no_bines
          post :jubilo_credit_request
          post 'payment_intent/:gateway', :to => 'checkout#payment_intent', :as => 'payment_intent'
        end
      end

      resources :landings, only: [:show] do
        get :home, on: :collection
      end

      resources :legacy, only: :create
      resources :menus, only: [:index]

      resources :my_account, only: [] do
        collection do
          resources :addresses, only: [:index, :show, :create, :update, :destroy]
          resources :orders, only: [:index, :show]
          resource :profiles, only: [:show, :update]
          resources :wishlists, only: [:index, :show, :update, :destroy]
          resources :transactions_point, only: [:index, :show]
          get :coupons, to: 'coupons#index'
        end
      end

      resources :newsletters, only: [:create]

      resources :orders, only: [] do
        post :tracking, on: :collection
      end

      resources :ping, only: :index

      resources :products, only: [:show] do
        collection do
          post :calculate_shipping
          post :stock_by_shopping
        end
      end

      resources :search, only: [:index, :show] do
        get :suggestions, on: :collection
      end

      resources :search, only: [:index, :show]

      resources :sessions, only: [:create] do
        post :macro_login, on: :collection
      end

      resources :stores, only: [] do
        collection do
          get :gateway
          get :current_store
        end
      end

      resources :users, only: [:create] do
        collection do
          get :me
          post :omniauth
        end
      end

      resources :users, only: [:logout] do
        collection do
          get :logout
        end
      end

      namespace :boca do
        resource :registers, only: [] do
          collection do
            post :sign_up
            post :payment
          end
        end
      end
      namespace :jubilo do
        post :auth, to: 'auth#authorizate'
        resource :resources, only: [] do
          collection do
            get :credit
            get :client
            get :search
            get :sellers
            get :payment_options
            post :grant_credit
            delete :referrer
          end
        end
      end

      namespace :integration do
        resources :sube_cards, only: [:index, :show, :create, :update, :destroy] do
          get :verify, on: :collection
        end
        post :points_exchange, to: 'purchases#points_exchange', as: 'points_exchange'
        post :points_refund, to: 'purchases#points_refund', as: 'points_refund'
        post :reservable_purchase, to: 'purchases#reservable_purchase', as: 'reservable_purchase'
        post :voucher_points_exchange, to: 'purchases#voucher_points_exchange', as: 'voucher_points_exchange'
      end
    end
  end
end

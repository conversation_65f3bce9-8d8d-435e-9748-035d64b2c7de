production:
  atene:
    url: "http://ecommerce.atheneelogistic.com/"
    keys:
      username: 'a<PERSON><PERSON>@avenida.com'
      password: 'aven<PERSON><PERSON><PERSON><PERSON>'
      grant_type: 'password'
  
  aws:
    access_key_id: ********************
    secret_access_key: TEQ/0v/byYZkprKkhsCfnyp626/5IvVlLpe2houL
    s3_region: 'us-east-1'
    tdd:
      bucket: 'avenida-production/tdd'

  brukman:
    url: "https://virtualseller-7930.cloudforce.com"
    auth_url: "https://virtualseller.cloudforce.com/services/oauth2/token"
    keys:
      grant_type: 'password'
      client_id: '3MVG9A2kN3Bn17hukGcvPEwC6PNBwtvpd4dVaVNAwzgqhsO64mgv37tdhs8V_nY__0596B7Lcl1kEVsF.rAIW'
      client_secret: '3367300000407281615'
      username: 'j<PERSON><PERSON><PERSON>@virtualseller.com'
      password: '2017costarica'

  boca:
    url: 'https://sgs-rfs.bocajuniors.com.ar/sgs-rfs/services'
    key: '$2a$10$qejid/EqRYsyrzJd7F8DPeQn5Vfk4dRVrYxibsIHMWToMGmS9.pgy'

  irsa:
    chequeregalo:
      url: https://irsachrwebapi.irsacorp.com.ar
      keys:
        grant_type: client_credentials
        client_id: avenida
        client_secret: 52CE10C7-152F-455C-89DB-AABC2AB180A4

  jubilo:
    production:
      url: https://webapi.jubilo.com.ar:44300
    staging:
      url: https://webapitest.jubilo.com.ar:44301

  colppy:
    client:
      username: '<EMAIL>'
      password: '4b6307b1d3127146a2491baf569ee591'
    company_id: '10516'
    user:
      username: '<EMAIL>'
      password: 'd895b01f33af90277de31ed87a41bcf6'

    sportclub:
      client:
        username: '<EMAIL>'
        password: 'SMOKEmachine4b6307b1d31'
      company_id: '6653'
      user:
        username: '<EMAIL>'
        password: 'SMOKEmachine1d85d2598f0'

  sportclub:
    endpoint: 'https://api_sport_club'
    token: 'token'

  hostname: 'api.avenida.com'

  include_analytics: true
  include_olark_chat: false
  include_intercom: false
  include_adroll: false

  system_point:
    url: http://*************
    avenida:
      private_key: 1ca13556e493e0ad08af57fdfc1b2c91cf9c70597e6df1eca9bbce295845c4cbceb0e820b40ba42757b0a6efc24c009947dcffe672506de7acd61ad84572ee8d
    tddboca:
      private_key: dc78501c70ce0c79fe9683983f4e0b3b40c94af19ce360bb572dc3cced1b5a5c2bd4b655ec27461d3a6bf1ec8c92dea2451a87af6efbd293f3b8dea1bacd6a6d
    bancomacro:
      private_key: 16b95a6b4e3557086dfe3d73f7db354e298287414db034b00b95eebfb70ed4862932d445f35252f9a86f52865c1ac1fa7ca38832634862046e11866a063bc914


  redis:
    host: 'redis-api-core.avenida.com.ar'
    port: 6379
    db: 1
    url: 'redis://redis-api-core.avenida.com.ar:6379/1'
    redis_url: 'redis://redis-api-core.avenida.com.ar:6379'

  google:
    api: ''
    analytics:
      tracking_id: 'UA-********-1'
      domain: 'avenida.com'
    analytics_reporter_account:
      key_path: 'config/keys/production-key.p12'
      issuer: '<EMAIL>'
    conversion_id: '1234'
    tag_manager:
      container_id: 'GTM-KDZG78'
    maps: 'AIzaSyBnm2JLc5zyNyHibqFeWxkVq3PGR4--Dhc'

  facebook_api:    ''
  facebook_secret: ''
  gmaps_api_key:   ''
  twitter_api:     ''
  twitter_secret:  ''
  issuu_api:       ''
  issuu_secret:    ''

  mailchimp:
    lists:
      avenida:
        api_key: '*************************************'
        subscribers_id: '14502bf392'
        popup_id: '08b67b2046'
        buyers_id: '8a8dd2ba37'
      tiendalanueva:
        api_key: '*************************************'
        subscribers_id: ''
        popup_id: 'eed8db89b3'
        buyers_id: '081a3c6f01'

  mercadopago:
    old:
      client_id: '***************'
      client_secret: 'rXejMwE6SgCvW7W2R50q1DdWG7TmaXv2'
    new:
      public_key: 'APP_USR-be01758d-69a3-4399-b327-c288f010525c'
      access_token: 'APP_USR-***************-091018-dd63dac9d1df47f9439a588c9dcc4cc7-*********'
  
  todopago:
    app_key: 'TODOPAGO ABCE34420FD66AF529601F997CF960FA'
    merchant: '348422'
    security: 'ABCE34420FD66AF529601F997CF960FA'
    endpoint: 'https://apis.todopago.com.ar'
    authorize_url: 'https://api.avenida.com.ar'
    hybrid_js: 'https://forms.todopago.com.ar/resources/TPHybridForm-v0.1.js'
    
  decidir:
    avenida_jr_site_id: "00151229"
    public_key: "a93eb596cd294ef99aa4cfb7eca00ce5"
    private_key: "7c34a757a6174466b46a700fcbf94e62"
    endpoint: "https://live.decidir.com/api/v1"
    tdd:
      private_key: "e2bfea22e2f9459aa8fbd80b047f9318"
      public_key: "03febc0060834510814c146b5c37b407"
    bancociudad:
      public_key: 1da34c60f75e4c34ab93d7007e59ce3a
      private_key: 1a887218cb1e4fb784136480f283c0f9

  braintree:
   merchant_id: 'bp9h294hdfzqpmnd'
   public_key: '5j6fjvtftwy2k9wd'
   private_key: '509d7e8bee5f441254eeeaf487ab68f9'
   env: 'sandbox'

  firstdata:
   domain: 'https://www5.ipg-online.com/ipgapi/services/order.wsdl'

  sendgrid:
   api_key: '*********************************************************************'
   subscribers_list_id: '800306'

  secret_token: "a0d850fa3d8361b313ffdd6ce5a755d8c7057fe34ebc3f4cf6ac32770915bce"

  ssl_enabled: true

  tarjeta_digital:
    domain: https://ccpaysrv.quasarbox.com
    user: ntxgNwhja6sdE7x8Ptp7rvVjbNZ3AMq8zAHMsmndYv6XWhkESZ3KAdR29vyv
    password: s7mp5JnzSCz8C8FebnLWH9SZ5rDrFnV3HYcAYkfQCTYdfncRg5GE
    retailer: 100840

  oca_epak:
    user: '<EMAIL>'
    password: 'avenida2017'
    account_number: '146845/000'
    cuit: '30-********-9'

  on_staging: false

  string:
    security: "b93ebfe8f53d31d30b527a6c77c00882b397ac190048a18ae5851400006d317250d4319074fc584e30e77026c0aa0a0988ad594220abc8e47430b20e9deec7bf"

  affiliates:
    pampa:
      us:
        param_name: ''
        api_token: ''
        account_id: ''
        campaign_id: ''
        currency: ''
      ar:
        param_name: ''
        api_token: ''
        account_id: ''
        campaign_id: ''
        currency: ''

  paperclip:
    s3_host_alias: 'statics.avenida.com'
    #s3_host_alias: 's3.amazonaws.com/a00.gpstatic.com'
    #s3_host_alias: 'gpcdn-dev.s3.amazonaws.com'
    bucket: 'gpcdn-dev'

  pickit:
    endpoint: 'https://core.pickit.net/app.php/CallMethod'
    api_key: 'O52QXX4Q67'
    token_id: 'WJ9QPX6745'


  facebook_pixel:
    ar:
      tracking_id: ****************
    us:
      tracking_id: ****************

  mercadolibre:
    AR:
      uid: '*********'
      account_name: '<EMAIL>'
    app_key: '****************'
    app_secret: 'W8xdSCWoFn64FVUjUS776WVwQLrVjfZJ'
    callback_url: 'https://api.avenida.com.ar/manage/shops/integrations/complete/mercadolibre'
    site_country: 'MLA'

  lion_key: "4E9078F5-DF24-4BB5-82B2-0B48DA6CE3BC"

  shopify:
    api_key: ''
    secret: ''

  enable_sidekiq_web: true

  external_communications:
    key: '6cd5bb8a4ba03482019a2e7a7cd98b2a'

  postmark:
    api_key: '************************************'
    smtp_server: 'smtp.postmarkapp.com'

  sitemaps:
    servers:
      avenida:
        - ip: '*************'
          username: 'deploy'
          password: 'avenida_production'
          sitemap_destination_folder: '/home/<USER>/apps/angular/current/dist'

  shipnow:
    api_endpoint: 'https://api.shipnow.com.ar'
    access_token: 'gVs2ved80fce6-f4QWdtaUTVCVtPZhL3g5Nd-CBSfuGS4257dA'
    admin_endpoint: 'sellers.shipnow.com.ar'
    store_id: 401
    meli_store_id: 9

  smart_carrier:
    api_key: 'EdrMEMWeLaNozKqxyc96U1UU7XYrH62W'
    macro_api_key: '46S35Yk8fZX14avfC3xCbeT4TYLJLSN2'
    url: 'https://api.shipping.avenida.com.ar'
    #url: 'https://api.krabpack.com'

  veinteractive:
    enabled: true
    tag: '//configusa.veinteractive.com/tags/68B6E1E9/5044/473E/8B6C/1786B4748263/tag.js'
    pixel: '//cdsusa.veinteractive.com/DataReceiverService.asmx/Pixel?journeycode=68B6E1E9-5044-473E-8B6C-1786B4748263'

  sidekiq:
    user: '<EMAIL>'  
    password: 'Yu9U2^SkB@$j'

  visa_puntos:
    default_api_key: '9787a595-d9f8-438f-b03b-db4fb49fe599'
    endpoint: 'http://test-gateway.avenida.com.ar:83/v1'
    cod_premio: 'V5010001'

  macro_login:
    endpoint: 'http://test-gateway.avenida.com.ar:82/v1'
    api_key: '102dae8c-c3de-4960-ab9d-cbee946497a2'

  technisys:
    url: 'https://***************'

  krabpack:
    #url: 'https://api.krabpack.com/'
    url: 'https://api.shipping.avenida.com.ar'
    api_key: 'EdrMEMWeLaNozKqxyc96U1UU7XYrH62W'
    macro_api_key: '46S35Yk8fZX14avfC3xCbeT4TYLJLSN2'

  pyp:
    recarga_url: 'https://logistica.puntosypremios.com.ar'
    catalogo_url: 'https://pyp-microsites.herokuapp.com'
    recarga_catalogo_id: '351'
    recarga_api_key: 'd4801b750238199f'
    product_catalog_id: 'AVENIDA_MACRO_CATALOGO'
    product_api_key: 'qdVNyMqpwmMx_GGJ8Kc9Jw'

  aerolineas_argentinas:
    url: 'https://esbar02.aerolineas.com.ar'

  sube:
    url: 'http://test-gateway.avenida.com.ar:81/api/v1/'

  bna:
    #salt-test: '3d4bab17-5651-4875-8530-1b041794e279'
    salt: '4j8ggb56-8735-9863-0975-6t096478f561'
  pypbm:
    shop_name: 'PyPBM'
    catalog_token: '1yIWbST8MOXimVYSm-aCyg'
    catalog_microsite: 'AVENIDA_MACRO_IMPORTADOS'
    redemption_id: '251'
    redemption_token: 'c75a3d6e261154f3'
  pypbm-g:
    shop_name: 'PyPBM-G'
    catalog_token: 'xMRb6l-Iis7Zrw-Mj9ehYg'
    catalog_microsite: 'AVENIDA_MACRO_AHORA12'
    redemption_id: '250'
    redemption_token: '7de00f5fb772156e'


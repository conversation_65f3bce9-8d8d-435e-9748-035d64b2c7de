development: &app_config
  affiliates:
    pampa:
      us:
        param_name: 'affiliateid'
        api_token: 'US_PampaAPI_Token'
        account_id: 'US_PampaAccount_ID'
        campaign_id: 'US_PampaCampaign_ID'
        currency: 'USD'
      ar:
        param_name: 'affiliateid'
        api_token: 'AR_PampaAPI_Token'
        account_id: 'AR_PampaAccount_ID'
        campaign_id: 'AR_PampaCampaign_ID'
        currency: 'ARS'

  atene:
    url: "http://athenee-api-dev.theamalgama.com"
    keys:
      username: '<EMAIL>'
      password: 'example123'
      grant_type: 'password'

  boca:
    url: 'http://sgs-test.bocajuniors.com.ar:8080/sgs-rfs/services'
    key: '$2a$10$LaB9G0Qm7/f5XfkpWk.a0.2BoSQeUDM76kyABAJ6PKK6x4YlWtJ0q'


  aws:
    access_key_id:
    secret_access_key:
    tdd:
      bucket:

  irsa:
    chequeregalo:
      url: https://example.irsacorp.com.ar/connect/token
      grant_type: client_credentials
      client_id: gp
      client_secret: example

  jubilo:
   production:
     url:
   staging:
     url: 'https://webapitest.jubilo.com.ar:44301'

  brukman:
    url: "https://virtualseller-7930.cloudforce.com"
    auth_url: "https://virtualseller.cloudforce.com/services/oauth2/token"
    keys:
      grant_type: 'password'
      client_id: ''
      client_secret: '3367300000407281615'
      username: '<EMAIL>'
      password: 'Pepo'

  braintree:
    merchant_id: 'bp9h294hdfzqpmnd'
    public_key: '5j6fjvtftwy2k9wd'
    private_key: '509d7e8bee5f441254eeeaf487ab68f9'
    env: 'sandbox'

  colppy:
    client:
      username: '<EMAIL>'
      password: '4b6307b1d3127146a2491baf569ee591'
    company_id: '10516'
    user:
      username: '<EMAIL>'
      password: '0738ea47f561f781da212cf482a1fbb4'

    sportclub:
      client:
        username: ''
        password: ''
      company_id: ''
      user:
        username: ''
        password: ''

  sportclub:
    endpoint: 'https://api.sportclub.com.ar'
    token: 'token'

  easypost_api_key: 'PSmNT4LTwGWMDaaDktCLgQ' # Test API Key

  enable_sidekiq_web: true

  external_communications:
    key: '6cd5bb8a4ba03482019a2e7a7cd98b2a'

  facebook_api:    '***************'
  facebook_secret: '********************************'
  facebook_pixel:
    ar:
      tracking_id: ''
    us:
      tracking_id: ''

  google:
    analytics:
      tracking_id: 'UA-7444591-2'
      domain: 'v2-staging.goodpeople.com'
    analytics_reporter_account:
      key_path: 'config/keys/dev-test-analytics-reporter.p12'
      issuer: '<EMAIL>'
    api: ''
    conversion_id:    '1234'
    tag_manager:
      container_id: 'GTM-KDZG78'

  hostname: 'http://0.0.0.0:3000'

  include_analytics: true
  include_olark_chat: false

  issuu_api:       '6lo8iz8e34hoavt94ztk2iwvhpo5ht9h'
  issuu_secret:    'xrcbpkerwqnssavig77nt14nt8m7g22q'

  lion_key: "4E9078F5-DF24-4BB5-82B2-0B48DA6CE3BC"

  mailchimp:
    api_key: ''
    lists:
      avenida:
        subscribers_id: ''
        popup_id: ''
        buyers_id:
      tiendalanueva:
        subscribers_id: ''
        popup_id: ''
        buyers_id:

  mercadolibre:
    app_key:        '***************'
    app_secret:     'B8DfTfWt0ebwxRY5Jh4SlWNX2IxlCfda'
    AR:
      uid: '*********'
      account_name: '<EMAIL>'
    callback_url:   'http://localhost:3000'
    site_country:   'MLA'

  mercadopago:
    old:
      client_id:     '9444'
      client_secret: 'CVvuJZxuPfpaM6dDWN6JYggXNoVnSB2P'
    new:
      public_key: 'TEST-05343ed9-fc8c-4994-8723-f08059425e2d'
      access_token: 'TEST-12128-050413-ea866dd236eee1b6ca89e569b9eb88d6__LA_LB__-********'

  decidir:
    public_key: "e9cdb99fff374b5f91da4480c8dca741"
    private_key: "92b71cf711ca41f78362a7134f87ff65"
    endpoint: "https://developers.decidir.com/api/v2"
    # desarrollo: endpoint: "https://developers.decidir.com/api/v1"
    # productivo: endpoint: "https://live.decidir.com/api/v1"
    # avenida_site_id: "********"
    avenida_jr_site_id: "********"
    tdd:
      public_key: ""
      private_key: ""
    bancociudad:
      public_key:
      private_key:

  todopago:
    app_key:  'PRISMA A793D307441615AF6AAAD7497A75DE59'
    merchant: '2159'
    security: 'A793D307441615AF6AAAD7497A75DE59'
    endpoint: 'https://developers.todopago.com.ar'
    authorize_url: 'http://localhost:3000'
    hybrid_js: 'https://developers.todopago.com.ar/resources/TPHybridForm-v0.1.js'

  tarjeta_digital:
    domain: 'https://qa.ccpaysrv.quasarbox.com'
    user: 'avenida'
    password: 'avenidaavenida'
    retailer: 100840

  firstdata:
    domain: 'https://test.ipg-online.com/ipgapi/services/order.wsdl'
    # produccion domain: 'https://www5.ipg-online.com/ipgapi/services/order.wsdl'
    # desarrollo domain: 'https://test.ipg-online.com/ipgapi/services/order.wsdl'

  oca_epak:
    account_number: ''
    cuit: ''
    password: ''
    user: ''

  on_staging: false

  string:
    security: "security"

  system_point:
    url: http://localhost:9292
    tddboca:
      private_key: secret
    bancomacro:
      private_key: secret
  paperclip:
    s3_host_alias: 'avenida-dev.s3.amazonaws.com'
    bucket: 'avenida-dev'

  pickit:
    endpoint: 'https://core.pickitlabs.com/app.php/CallMethod'
    api_key: '43EVPYXV7W'
    token_id: 'P4NB49QYE6'

  redis:
    host: 'redis-api-core-redis.avenida.com.ar'
    port: 6379
    db:   0
    url:  'redis://host: redis-api-core-redis.avenida.com.ar:6379/0'

  secret_token: '9cb236a95769c4f3c1b5723e5a9aed9e11b1af64832c3971f80a544db5dc19c0b13bb4b795ff8aecd6f365f1c1ccec9247ae0c03429eb16ba0d97d79f04e6676'

  sendgrid:
    api_key: '*********************************************************************'
    subscribers_list_id: '800311'

  shipnow:
    api_endpoint: 'https://api-staging.shipnow.com.ar'
    access_token: 'xRKUNC1FqoMCNiOgSpjafZgwKcKfVDB4plie6_S6RfcSsPdGyw'
    admin_endpoint: 'sellers-staging.shipnow.com.ar'
    store_id: 118
    meli_store_id: 117

  smart_carrier:
    api_key: '5Dgvqqmxtp4B9KzUu1dgfVkGq7RfAPBx'
    url: 'http://test.smartcarrier.redbee.io/api'

  shopify:
    api_key: 'c01d66cff1008b8074b1e5a7d922fcfd'
    secret: '7cc17e8d317d30037bc3e2b120399969'

  ssl_enabled: false

  twitter_api:     'tXbpF4rSTkGcJ1XeDqhtgw'
  twitter_secret:  'tuESMI1d3rdpEPuk3DZ9w2OD8HzahOmWiAjkK163mI'

  veinteractive:
    enabled: false
    tag: '//configusa.veinteractive.com/tags/68B6E1E9/5044/473E/8B6C/1786B4748263/tag.js'
    pixel: '//cdsusa.veinteractive.com/DataReceiverService.asmx/Pixel?journeycode=68B6E1E9-5044-473E-8B6C-1786B4748263'

  sidekiq:
    user: "SIDEKIQ"
    password: "PASSWORD"

  visa_puntos:
    default_api_key: '30e10594-ff57-4fd9-b2d3-169a7d53bb6a'
    endpoint: 'http://test-gateway.avenida.com.ar:83/v1/'
    cod_premio: 'V5010001'

  macro_login:
    endpoint: 'http://test-gateway.avenida.com.ar:82/v1'
    api_key: '9aeac789-96ed-4900-a7cc-313fae7cd1e2'

  technisys:
    url: 'https://***************'

  aerolineas_argentinas:
    url: 'https://pswtest.aerolineas.com.ar'

  krabpack:
    url: 'https://api.test.krabpack.com'
    api_key: uOpLhuSRWslMCOJRdGBUZOXNuBfPRRiP
    macro_api_key: uOpLhuSRWslMCOJRdGBUZOXNuBfPRRiP

  pyp:
    recarga_url: 'https://staging.logistica.puntosypremios.com.ar'
    catalogo_url: 'https://pyp-microsites-staging.herokuapp.com'
    recarga_catalogo_id: 'avenida_recargas'
    recarga_api_key: '366dd428338752ee'
    product_catalog_id: 'macro_recargas_avenida'
    product_api_key: 'qY-cpD3_2KFFH61tC0wjXA'

  sube:
    url: 'http://localhost:9000/api/v1/'

production:
  <<: *app_config

profile:
  <<: *app_config

test:
  <<: *app_config
  braintree:
    merchant_id: 'bp9h294hdfzqpmnd'
    public_key: '5j6fjvtftwy2k9wd'
    private_key: '509d7e8bee5f441254eeeaf487ab68f9'
    env: 'sandbox'

  capybara:
    javascript_driver: :selenium # options are :selenium | :poltergeist | :webkit | :quiet_webkit | :webkit_debug | :rack_test
    default_wait_time: 20
    sleep_lapse: 5

  easypost_api_key: 'PSmNT4LTwGWMDaaDktCLgQ'

  include_analytics: true

  mercadolibre:
    app_key:        '***************'
    app_secret:     'B8DfTfWt0ebwxRY5Jh4SlWNX2IxlCfda'
    callback_url:   'http://localhost:3000'
    site_country:   'MLA'

  mercadopago:
    old:
      client_id:     '9444'
      client_secret: 'CVvuJZxuPfpaM6dDWN6JYggXNoVnSB2P'
    new:
      public_key: 'TEST-05343ed9-fc8c-4994-8723-f08059425e2d'
      access_token: 'TEST-12128-050413-ea866dd236eee1b6ca89e569b9eb88d6__LA_LB__-********'

  paperclip:
    s3_host_alias: 'gpcdn-test.s3.amazonaws.com'
    bucket: 'gpcdn-test'

  shopify:
    api_key: 'c01d66cff1008b8074b1e5a7d922fcfd'
    secret: '7cc17e8d317d30037bc3e2b120399969'

en:
  will_paginate:
    previous_label: «
    next_label: »
    simple_previous_label: « Previous
    simple_next_label: Next »
    page_gap: "&hellip;"
  date:
    abbr_day_names:
    - Sun
    - Mon
    - Tue
    - Wed
    - Thu
    - Fri
    - Sat
    abbr_month_names:
    -
    - Jan
    - Feb
    - Mar
    - Apr
    - May
    - Jun
    - Jul
    - Aug
    - Sep
    - Oct
    - Nov
    - Dec
    day_names:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    formats:
      default: ! "%m-%d-%Y"
      long: ! "%B %d, %Y"
      short: ! "%b %d"
      day_and_year: ! "%B %d, %Y"
      month_and_year: ! "%B %Y"
    month_names:
    -
    - January
    - February
    - March
    - April
    - May
    - June
    - July
    - August
    - September
    - October
    - November
    - December
    order:
    - :year
    - :month
    - :day
  datetime:
    distance_in_words:
      about_x_hours:
        one: "1 hour ago"
        other: "%{count} hours ago"
      about_x_months:
        one: "1 month ago"
        other: "%{count} months ago"
      about_x_years:
        one: "1 year ago"
        other: "%{count} years ago"
      almost_x_years:
        one: almost 1 year ago
        other: almost %{count} years ago
      half_a_minute: half a minute ago
      less_than_x_minutes:
        one: less than a minute ago
        other: less than %{count} minutes ago
      less_than_x_seconds:
        one: less than 1 second ago
        other: less than %{count} seconds ago
      over_x_years:
        one: over 1 year
        other: over %{count} years
      x_days:
        one: 1 day
        other: ! '%{count} days ago'
      x_minutes:
        one: 1 minute
        other: ! '%{count} minutes ago'
      x_months:
        one: 1 month
        other: ! '%{count} months ago'
      x_seconds:
        one: 1 second
        other: ! '%{count} seconds ago'
    prompts:
      day: Day
      hour: Hour
      minute: Minute
      month: Month
      second: Seconds
      year: Year
  errors: &errors
    format: ! '%{attribute} %{message}'
    messages:
      accepted: must be accepted
      blank: can't be blank
      confirmation: doesn't match confirmation
      empty: can't be empty
      equal_to: must be equal to %{count}
      even: must be even
      exclusion: is reserved
      greater_than: must be greater than %{count}
      greater_than_or_equal_to: must be greater than or equal to %{count}
      inclusion: is not included in the list
      invalid: is invalid
      less_than: must be less than %{count}
      less_than_or_equal_to: must be less than or equal to %{count}
      not_a_number: is not a number
      not_an_integer: must be an integer
      odd: must be odd
      record_invalid: ! 'Validation failed: %{errors}'
      taken: has already been taken
      too_long:
        one: is too long (maximum is 1 character)
        other: is too long (maximum is %{count} characters)
      too_short:
        one: is too short (minimum is 1 character)
        other: is too short (minimum is %{count} characters)
      wrong_length:
        one: is the wrong length (should be 1 character)
        other: is the wrong length (should be %{count} characters)
    template:
      body: ! 'There were problems with the following fields:'
      header:
        one: 1 error prohibited this %{model} from being saved
        other: ! '%{count} errors prohibited this %{model} from being saved'
  helpers:
    select:
      prompt: Please select
    submit:
      create: Create %{model}
      submit: Save %{model}
      update: Update %{model}
  number:
    currency:
      format:
        delimiter: ! ','
        format: ! '%u%n'
        precision: 2
        separator: '.'
        significant: false
        strip_insignificant_zeros: false
        unit: $
    format:
      delimiter: ! ','
      precision: 2
      separator: '.'
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: ! '%n %u'
        units:
          billion: Billion
          million: Million
          quadrillion: Quadrillion
          thousand: Thousand
          trillion: Trillion
          unit: ''
      format:
        delimiter: ','
        precision: 2
        significant: true
        strip_insignificant_zeros: false
      storage_units:
        format: ! '%n %u'
        units:
          byte:
            one: Byte
            other: Bytes
          gb: GB
          kb: KB
          mb: MB
          tb: TB
    percentage:
      format:
        delimiter: ','
    precision:
      format:
        delimiter: ','
  support:
    array:
      last_word_connector: ! ', and '
      two_words_connector: ! ' and '
      words_connector: ! ', '
  time:
    am: am
    formats:
      default: ! '%a, %d %b %Y %H:%M:%S %z'
      long: ! '%B %d, %Y %H:%M'
      short: ! '%d %b %H:%M hs.'
      shorter: ! "%B %d, %H:%M:%S hs."
      shortest: ! "%B %d, %Y"
    pm: pm

  # remove these aliases after 'activemodel' and 'activerecord' namespaces are removed from Rails repository
  activemodel:
    errors:
      <<: *errors
  activerecord:
    errors:
      <<: *errors
      models:
        user:
          attributes:
            current_password:
              invalid: Does not match your current password.
  controllers:
    application:
      login_required: You must first log in or sign up before accessing this page.
      logout_required: You must be logged out to access this page.
    password_resets:
      user_not_found: No user was found with that login or email address.
      password_updated: Password successfully updated.
      token_not_found: |
                       We're sorry, but we could not locate your account.
                       If you are having issues, try copying and pasting the URL
                       from your email into your browser or restarting the
                       reset password process.
    authentications:
      auth_taken: You cannot grant access to that Facebook user because is associate it to another user in Avenida.com
  error_pages:
    go_to_home: "Go to Home page"
    error_404:
      title: "We couldn't find that here"
      description: "Meanwhile, you can keep finding new products here!"
    error_422:
      title: "An error has occurred"
      description: "We have been notified of the problem and we will fix it as soon as possible"
    error_500:
      title: "An error has occurred"
      description: "We have been notified of the problem and we will fix it as soon as possible"
  notifications:
    commented_post_html: "%{actor} commented on your %{post_type}."
    followed_html: "%{actor} is following you."
    commented_same_post_html: "%{actor} commented on %{author}'s %{post_type}."
    answered_html: "We heard back from %{actor} in regards of your question on the product %{product}. We sent you an email with the response. (Don't forget to look into your span folder!)"
    favorited_html: "%{actor} high fived your %{post_type}."
    mentioned_html: "%{actor} tagged you."
  notification_mailer:
    mentioned:
      subject: "@%{login} mentioned you in a %{item_class_name}"
      comment: comment
      post: post
      main_text_html: "mentioned you on a %{item_class_name}:"
      see_text_html: "See %{item_class_name}"
    commented_post:
      subject: "@%{login} commented on your post: %{post_title}"
      title: commented on your post
    commented_same_post:
      subject: "@%{login} commented on @%{user_login}'s post: %{post_title}"
      title: "commented on @%{user_login}'s post"
    favorited:
      subject: "@%{login} high fived your post: %{title}"
      title: high fived your post
    followed:
      subject: "@%{login} is following you"
      back_subject: "%{full_name} (@%{login}) is following you back"
      title: is following you
      followed_by: Followed by
      followed_and: and
      followed_others:
        one: 1 other
        other: "%{count} others"
      following: "Following:"
      followers: "Followers:"
      message_title: You have a new follower on Avenida.com!
    see_post: See Post
    see_comment_thread: See Comment Thread
    see_profile: See Profile
    follow: Follow
    following: Following
  password_resets:
    new:
      title: Reset Password
      description: Fill out your login or email below and instructions to reset your password will be emailed to you.
      email_or_login: "Email or Login"
      reset: Reset my Password
      placeholder: Provide login or email
    edit:
      title: New Password
      description: Please, enter your new password.
      new_password: New Password
      confirm_password: Confirm Password
      update_password: Update my Password
    done:
      title: Success!
      description: Instructions to reset your password have been emailed to you. Please check your email.
  promotion_mailer:
    entered_contest:
      subject: Hooking Up Our Friends
      hey: Hey @%{login},
      we_miss_you_html: We miss you and wanted to be sure you did not miss out on any opportunities to win some rad new gear from our friends at %{href}. We entered you in the contest by "Following" this brand for you. You can see more of their content and products on their profile on Avenida.com.com.
      name_href: '%{name}'
      follow_other_brands: Follow other brands and people to see what other Avenida.com are up too!
      go_to: Go to Avenida.com
  integration_mailer:
    import_and_sync:
      subject: Products Imported from %{integration_name}
      body:
        zero: No new imported products.
        one: Successfully imported 1 product.
        other: Successfully imported %{count} products.
      images_message: Now you will be able to see your products through the Shop Admin. Its possible that the product's images took more time to load because are being uploaded asynchronously.
  import_label_mailer:
    notify_errors:
      subject: Errores de Importacion
      body:
        errors: Se han detectado errores en las siguentes subordenes %{row_errors}
    notify_success:
      subject: Importacion Exitosa
      body:
        success: Operacion realizada exitosamente.
  search:
    search:
      social_activity: SOCIAL ACTIVITY
      saying_about: "SEE WHAT PEOPLE ARE SAYING ABOUT %{query}"
      no_feeds_found: No feeds found
      users_found: USERS FOUND
      products_found: PRODUCTS FOUND
      found_matches: "FOUND %{query} MATCHES"
      found_matches_more: "FOUND MORE THAN %{query} MATCHES"
      no_users_found: No users found
      variants_button: View all products found
  user_mailer:
    welcome_email:
      were_stoked: We're stoked you joined Avenida.com!
      you_now_have_access: |
                           You now have access to the newest outdoor and action sports brands that you can’t find anywhere else.
                           This is a global community based on having fun, pushing your limits, and giving back.
      to_get_the_most: 'To get the most out of the network, make sure you:'
      update_your_profile: Update your profile
      follow_brands: Follow brands
      follow_people: Follow people
      upload_share_video: Upload/share a video
      upload_share_photos: Upload/share photos
      keep_livin_the_goodlife: Keep livin' the GoodLife!
      the_goodpeople_crew: The Avenida.com Crew
  payment-gateways:
    DecidirDistributed: "Decidir distribuido"
    Decidir: "Decidir"
    AvenidaDecidirDistributed: "Decidir distribuido*"
    AvenidaDecidir: "Decidir*"
    AvenidaModoDistributed: "Modo distribuido"
    AvenidaMercadopago: "MercadoPago"

# IMPORTANT Add translations according to the app/views directory

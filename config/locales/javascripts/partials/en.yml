en:
  javascripts:
    partials:
      form:
        uploading_msg: Uploading...
        upload_complete: Upload complete
        upload_error: "There was a problem uploading the file, try again please."
        multiple_files_not_allowed: 'You can only select one file'
        confirm_delete: "Are you sure that you want to remove %{file} from the queue?"
        file_type_not_allowed: "File type not allowed, you can only upload %{types}"
        upload_file_too_big: "File too big, max file size is %{max}MB"
        named_upload_error: "There was a problem uploading %{file}, try again please."
        select_a_sport: 'Select sport tags:'
        popular_tags: POPULAR TAGS
        username_required: Username is required.
        username_too_short: Username must be at least 3 characters long.
        username_too_long: Username must be at most 25 characters long.
        invalid_username: 'Username must start with a letter and contain only numbers(0-9), letters (a-z), hyphens (- or _) and dots (.).'
        password_required: Password is required.
        password_too_short: Password must be at least 4 characters long.
        password_whitespace: Password cannot contain white spaces.
        password_not_match: Password does not match.
        name_required: Name is required.
        first_name_required: First Name is required.
        last_name_required: Last Name is required.
        email_required: Email is required.
        invalid_email: Invalid Email.
        name_too_long: Name too long.
        first_name_too_long: First Name too long.
        last_name_too_long: Last name too long.
      comments:
        need_login: You must log in to be able to comment
      favorite_button:
        need_login: You must log in to be able to high five
      follow_button:
        need_login: You must log in to be able to follow


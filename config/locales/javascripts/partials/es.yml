es:
  javascripts:
    partials:
      form:
        uploading_msg: Subiendo...
        upload_complete: Upload completado
        upload_error: "Hubo un problema subiendo el archivo, por favor inténtelo nuevamente."
        multiple_files_not_allowed: 'Solo podés seleccionar un archivo'
        confirm_delete: "Seguro que quieres eliminar %{file}?"
        file_type_not_allowed: "El tipo de archivo no está permitido, solo podés subir %{types}"
        upload_file_too_big: "El archivo es muy grande, el tamaño máximo es %{max}MB"
        named_upload_error: "Hubo un problema subiendo el archivo %{types}, por favor inténtelo nuevamente."
        select_a_sport: 'Tags de deportes:'
        popular_tags: ETIQUETAS POPULARES
        username_required: Debes ingresar tu nombre de usuario.
        username_too_short: El usuario debe tener almenos 3 caracteres de largo.
        username_too_long: El usuario no puede ser más largo de 25 caracteres.
        invalid_username: 'El usuario debe comenzar con una letra y puede contener solo números (0-9), letras (a-z), guiones (- o _) y puntos(.).'
        password_required: Debes ingresar una contraseña.
        password_too_short: La contraseña no puede ser más largo de 4 caracteres.
        password_whitespace: La contraseña no puede tener espacios en blanco.
        password_not_match: La contraseña no coincide.
        name_required: Nombre es requerido.
        first_name_required: Nombre es requerido.
        last_name_required: Apellido es requerido.
        email_required: Email es requerido.
        invalid_email: Email inválido.
        name_too_long: El Nombre es muy largo.
        first_name_too_long: El Nombre es muy largo.
        last_name_too_long: El Apellido es muy largo.
      comments:
        need_login: Tienes que estar registrado para poder comentar
      favorite_button:
        need_login: Tienes que estar registrado para poder hacer high five
      follow_button:
        need_login: Tienes que estar registrado para poder seguir

en:
  social:
    profiles:
      brand_edit_fields:
        about_brand: About your brand in 160 characters or less
      non_profit_edit_fields:
        about_non_profit: About your non profit in 160 characters or less
      no_posts:
        create_first_post_html: "Oh no! %{name}, you need to create your first post.<br>Don't worry, %{href} to make one."
        click_here: click here
      user_edit_fields:
        first_name: First Name
        real_name: "Enter your real name, so people you know recognize you."
        last_name: Last Name
        bio: Bio
        about_yourself: About yourself in 160 characters or less
      brand_show:
        title: '%{brand} - Online Community'
        description: '%{brand} - Shop online plus view new snow, surf, skate images and videos.'
        see_more_products: See more products from this brand
        see_more_products_from_shop: See more products from this shop
        aggregated_profiles_links:
          instagram_html: '<a href="http://instagram.com/%{name}" target="_blank" rel="nofollow">@%{name}</a> via instagram'
          youtube_html: '<a href="http://www.youtube.com/user/%{name}" target="_blank" rel="nofollow">%{name}</a> via youtube'
          vimeo_html: '<a href="http://vimeo.com/%{name}" target="_blank" rel="nofollow">%{name}</a> via vimeo'
      non_profit_show:
        title: '%{non_profit} - Online Community'
        description: '%{non_profit} - Shop online plus view new snow, surf, skate images and videos.'
      edit:
        head_title: Edit your Profile
        profile: Profile
        public_information: "This information appears on your public profile, search results, and beyond."
        photo: Photo
        identity_on_goodpeople: This photo is your identity on GoodPeople and appears with your posts.
        choose_photo: Choose Photo
        cover: Cover
        cover_dimensions: Recommended dimensions of 1800x430px
        cover_max_size: Less than 10MB
        save_changes: Save Changes
        aggregated_title: Add Social Media Feeds
        aggregated_description: "Link your social media accounts to automatically post your content on your GoodPeople profile and stream it to your followers. (Please, make sure it's a valid account)"
        aggregated_profiles:
          youtube:
            label: Youtube Channel
            placeholder: GoodPeopleTv
            help: "Youtube User part of the url, i.e. <strong>GoodPeopleTV</strong> on <a href=\"http://www.youtube.com/user/GoodPeopleTV\">http://www.youtube.com/user/<strong>GoodPeopleTV</strong></a><br/>Name part of the channel url, i.e. <strong>UC2sMIPrRdeteuxDUr3i9xSQ</strong> on <a href=\"http://www.youtube.com/channel/UC2sMIPrRdeteuxDUr3i9xSQ\">http://www.youtube.com/channel/<strong>UC2sMIPrRdeteuxDUr3i9xSQ</strong></a>"
          instagram:
            label: Instagram User
            placeholder: goodpeoplelife
            help: "Name part of the user url, i.e. <strong>goodpeoplelife</strong> on <a href=\"http://instagram.com/goodpeoplelife\">http://instagram.com/<strong>goodpeoplelife</strong></a>"
          vimeo:
            label: Vimeo User
            placeholder: user3839254
            help: "Name part of the user url, i.e. <strong>user3839254</strong> on <a href=\"http://vimeo.com/user3839254\">http://vimeo.com/<strong>user3839254</strong></a>"
          twitter:
            label: Twitter User
            placeholder: GoodPeople
            help: "Twitter username, i.e. <strong>GoodPeople</strong> on <a href=\"https://twitter.com/GoodPeople\">https://twitter.com/<strong>GoodPeople</strong></a>"
      edit_js:
        upload_error: "There was a problem uploading the avatar, try again please."
      user_show:
        followers: FOLLOWERS
        following: FOLLOWING
        edit: Edit
        no-recommendations: "There are no recommendations for you at this moment."
        follow_more: Follow More
      user_meta:
        description: "Latest posts from %{full_name} (@%{login})."
      profile_js:
        first_name_required: First Name is required.
        last_name_required: Last Name is required.
        first_name_too_long: First Name can't be longer than 127 characters.
        last_name_too_long: Last name can't be longer than 127 characters.

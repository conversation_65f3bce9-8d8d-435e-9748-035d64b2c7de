en:
  mkp:
    checkout:
      v5:
        partials:
          braintree:
            creditcards: 'Credit Cards '
            paypal: 'Or Pay With '
            creditcard_number: 'Credit Card Number'
            cardholder_name: 'Cardholder Name'
            expiration_month: 'Exp. Month'
            expiration_year: 'Exp. Year'
            cvv: 'CVV/CID'
            cvv_explanation_html: "<h4>What is a Card Security Code?</h4><p>Visa (CVV) & Mastercard (CVC), are three digits located on the back part of the card to the right of signature strip.</p><p>American Express (CID), four digits printed on the front of the card over the card number.</p>"
            country: Country
            state: State
            street_address: Street Address
            postal_code: Zip Code
            select_state: Select State
          coupon:
            title: Redeem Promo Code?
            button: Apply
            applied: 'Coupon: '
            remove: Remove
          footer:
            payment_methods_title: Payment Methods
            payment_methods:
              mastercard: MasterCard
              visa: Visa
              american: American Express
              paypal: PayPal
            secure: Secure Payments
          header:
            home: Home
            community: Community
            login: Login
          payment:
            title: Payment Method
          pickup_location:
            email: Email
            full_name: Full Name
            title: Pickup Location
            search_label: Enter your address and find the closest pickup point
            search_placeholder: Write your address here
            pickup_selector_label: Pickup location
            phone: Telephone
          pickup_selector:
            shipping_default: Home delivery
            shipping_pickup: Store Pickup
          shipping_address:
            title: Shipping Address
            edit_address: Edit Address
            delete_address: Remove
            add_new_address: Add New Address
            full_name: 'First & Last Name'
            email: 'E-Mail'
            phone: 'Phone Number'
            street_1: 'Address (Line 1)'
            street_2: 'Address (Line 2)'
            country: Country
            state: State
            city: City
            zip: Zip Code
            dni:
            select_state: Select State
            select_country: Select Country
          shipping_methods:
            title: Shipping Method
            need_address: First you need to enter a new address or choose an existing one
          summary:
            pay_button: 'Buy'
            properties:
              color: 'Color: '
              dimensions: 'Dimensions: '
              hardness: 'Property one: '
              length: 'Property two: '
              material: 'Property three: '
              size: 'Size: '
              percentage: 'Percentage: '
              payment_method: 'Payment Method: '
            quantity: 'Quantity: '
            remove: Remove
            title: Order Summary
            subtotal: Subtotal
            total: Total
            total_shipping_cost: "Shipping & Handling"
            total_taxes: Taxes
            discount: Discount
            handling_discount: Extra Discount
          thanks:
            subtitle: "%{name}, your order is complete"
            continue: "Continue Shopping"
            body:
              thanks: Thanks for shopping on GoodPeople!
              single_suborder: "We've received your order (#%{id}) and notified the seller of the purchase."
              multiple_suborders: "We've received your orders (#%{ids}) and notified each seller of the purchase."
              message: "You're probably pretty pumped, so hang tight! The brands on GoodPeople are small, passionate teams hustling to get your order out as quickly as possible. The goal is to process your order in 1-3 business days, but occasionally it takes a bit longer. Ya know... patience is a virtue. ;)"
              email_aware_html: "<div class='title'>KEEP AN EYE OUT (for our emails)</div>We just sent an email with the order details to <span class='email'>%{email}</span>. Make sure you receive all of your order updates - add <strong><EMAIL></strong> to your address book! (It’s no fun digging through the Spam folder…)"

en:
  mkp:
    shop_mailer:
      question_user:
        subject: "Your question about <b>%{product_title}</b> has been sent to <b>%{shop_title}</b>"
        msg_html: "Thanks for your interest in <b>%{product_title}</b>. <b>%{shop_title}</b> has been notified about your question: <br><br><i>\"%{question_text}\"</i><br><br>Don’t worry, we'll follow up as soon as we get an answer."
      question_shop:
        subject: "You've received a question about the %{product_title}"
        msg_html: 'Good news, %{customer_login} has asked a question about your product, the <b>%{product_title}</b>.<br><br><i>%{question_text}</i>'
        msg_extra: "Here's a good chance to do some killer customer service!"
        link_text: "Reply Now"
      answer_shop:
        subject: 'Your answer to "%{question_text}"'
        hi: 'Hey %{name}!'
        msg_html: "We heard back from <b>%{manufacturer_link}</b> in regards of your question on the product %{link_product}."
        link_text: "Buy Now"
        you_ask: "You asked:"

es:
  mkp:
    orders:
      buyer:
        title: Complete your personal and shipping information
        first_name: Nombre
        last_name: <PERSON>pellido
        email: Email
        personal_information:
          title: Your personal information
        shipping_information:
          title: Shipping information
        order_detail:
          title: Productos que estas comprando
      address:
        address: "Dirección"
        address_2: "Dirección 2"
        city: Ciudad
        state: Provincia
        zip: CP
        country: "País"
      shipping_options:
        postmaster:
          ground: Ground Shipping (8-15 days)
          3day: 3-5 Day Shipping
          2day: 2 Day Shipping
          1day: 1 Day Shipping
          intl_priority: International Priority Shipping
          intl_express: International Express Shipping
        usps:
          '1': Priority Mail (2-3 Day Shipping)
          '28': Priority Mail Small Flat Rate Box (2 Day Shipping)
          '17': Priority Mail Medium Flat Rate Box (2 Day Shipping)
          '22': Priority Mail Large Flat Rate Box (2 Day Shipping)
          '3': Priority Mail Express (1-2 Day Shipping)
          '55': Priority Mail Express Flat Rate Boxes (2 Day Shipping)
          '0': First-Class Mail Parcel
          '4': Standard Post Shipping
        oca: Servicio e-Pack de OCA (3-10 días)
      order_line_item:
        size: Talle
        color: Color
        quantity: Cantidad
      index:
        placeholder: 'Código del Cupón...'
        apply_coupon: Aplicar
        title: Productos en tu carrito
        subtitle: "Podes confirmar la compra, o seguir buscando más productos"
        item_title: Producto
        item_status: Estado
        item_qty: Cant.
        item_price: Precio
        item_total_price: Total
        buy_button: 'Confirmar compra'
        continue_shopping: 'Seguir comprando'
        no_products: No tienes ningún producto en tu carrito.
        coupons_title: Cupones
      login:
        subtitle: "Para poder continuar con la compra, y seleccionar la dirección de envío necesitas estar logueado. Podes hacerlo con tu usuario y pass o con cualquiera de las redes sociales."
        title: "Tenes que estar registrado para poder continuar con la compra"
        sign_in: Ingresar
        password: Contraseña
        enter_email: Ingresar email
        username: 'Usuario or email:'
        password: 'Contraseña:'
        forgot_password: Olvidaste tu contraseña?
        register: No estas registrado? Hacelo ahora.
        social_media_alternatives: "O a través de: "
      address_select:
        button: 'Enviar a esta dirección'
      potential_shipping_address:
        button: 'Enviar a esta dirección'
        add_address: "Seleccionar otra dirección de envío:"
      shipping:
        add_address_title: "O agregá otra dirección para el envío"
        title: "Elegí el modo de envío"
        must_choose: 'Debés elegir un método de envío.'
        address_list_title: 'Elegí la direecion de envío'
        complete_fields: 'Complete los campos faltantes por favor.'
      checkout_steps:
        pick_products:
          one: "Elegiste <strong class='label'>%{count}</strong> producto"
          other: "Elegiste <strong class='label'>%{count}</strong> productos"
        shipping_logged: Seleccionar dirección de envío
        shipping_unlogged: Logueate para continuar
        confirm_and_pay: Confirmar orden y pagar
        complete_order: Orden completa
      checkout:
        coupons:
          coupons: Coupons
        coupons:
          title: Cupones
        summary:
          shipping_cost: "Costo de envío:"
          total_cost: 'Total a pagar'
          finish_button: 'Finalizar Compra'
          product_list_title: 'Productos en la orden:'
          title: 'Detalle de la orden:'
        confirm:
          title: "Confirmá la compra de %{title} y  clickeá en el botón de 'Pagar'"
          shipping_title: "Los productos que estas comprando se envian a la siguiente dirección:"
        complete:
          title: "¡Gracias por la compra!"
          approved: "¡Ya recibimos tu pago por lo que ya estamos armando el envío para que lo recibas cuanto antes!"
          pending: "Estamos esperando tu pago, en cuanto se acredite realizaremos el envío para que puedas recibirlo cuanto antes. Recordá que hasta que no recibamos la confirmación del pago no podemos realizar el envío. Algunos medios de pago demoran entre 24-48hs."
          in_process: "Estamos esperando tu pago, en cuanto se acredite realizaremos el envío para que puedas recibirlo cuanto antes. Actualmente el pago esta en proceso de verificación por parte de MercadoPago cualquier cosa debes contactarlos para ver cual fue el inconveniente y porqué esta en proceso."
          order_nmbr_title: Nro de orden
          order_detail_title: El detalle de tu compra
          email:
            order_item:
              price: Precio
              link: Enlace
              quantity: Cantidad
              color: Color
              size: Talle
              installment: Cuotas
            buyer:
              subject: "Felicitaciones por tu compra de %{order_title} en GoodPeople"
              hi: Hola
              congratulations: ", felicitaciones por tu compra en GoodPeople"
              data_title: Datos del Comprador
              credentials: Nombre
              email: Correo electrónico
              total: Total de la compra
              shop: Tienda
              seller_data: Datos del Vendedor
            seller:
              subject: "Felicitaciones por tu venta de %{order_title} en GoodPeople"
              congrats_pre: Felicitaciones
              congrats_post: Acabas de vender un producto en GoodPeople.com.
              data_title: Datos del Comprador
              buyer_credentials: Nombre
              email: Correo electrónico
              total: Precio Total
              shop: Tienda

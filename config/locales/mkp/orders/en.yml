en:
  mkp:
    orders:
      buyer:
        title: Complete your personal and shipping information
        first_name: First name
        last_name: Last name
        email: Email
        personal_information:
          title: Your personal information
        shipping_information:
          title: Shipping information
        order_detail:
          title: "Products you're buying"
      address:
        address: Address 1
        address_2: Address 2
        city: City
        state: State
        zip: Zipcode
        country: Country
      shipping_options:
        postmaster:
          ground: Ground Shipping (8-15 days)
          3day: 3-5 Day Shipping
          2day: 2 Day Shipping
          1day: 1 Day Shipping
          intl_priority: International Priority Shipping
          intl_express: International Express Shipping
        usps:
          '1': Priority Mail (2-3 Day Shipping)
          '28': Priority Mail Small Flat Rate Box (2 Day Shipping)
          '17': Priority Mail Medium Flat Rate Box (2 Day Shipping)
          '22': Priority Mail Large Flat Rate Box (2 Day Shipping)
          '3': Priority Mail Express (1-2 Day Shipping)
          '55': Priority Mail Express Flat Rate Boxes (2 Day Shipping)
          '0': First-Class Mail Parcel
          '4': Standard Post Shipping
        oca: OCA Service Shipping (3-10 days)
      order_line_item:
        size: Size
        color: color
        quantity: Quantity
      index:
        placeholder: 'Coupon code...'
        apply_coupon: Apply
        title: Products in your shopping cart
        subtitle: "You can checkout right away, or keep looking for products"
        item_title: 'Item'
        item_status: 'Status'
        item_qty: 'Qty'
        item_price: 'Price'
        item_total_price: 'Total'
        buy_button: 'Proceed to Checkout'
        continue_shopping: 'Continue Shopping'
        no_products: You don't have products on your cart.
        coupons_title: Cupones
      login:
        sign_in: Sign In
        title: "You have to be logged in to continue with checkout"
        subtitle: "Please use your credentials or use any of the social options below"
        password: Password
        enter_email: Enter email address
        username: 'Username or email:'
        password: 'Password:'
        forgot_password: Forgot your password?
        register: Not a user yet? Register.
        social_media_alternatives: "Or with: "
      shipping:
        title: "Choose shipping address"
        add_address_title: Add another one
        must_choose: "You have to choose a shipping method first"
        complete_fields: "Please, complete all the empty fields"
      address_select:
        button: 'Send to this address'
      potential_shipping_address:
        add_address: "Click here to add another address:"
        button: 'Send to this address'
      checkout_steps:
        pick_products: "<strong class='label'>%{count}</strong> Products to purchase"
        shipping_logged: Select shipping method and address
        shipping_unlogged: Log In to continue
        confirm_and_pay: Confirm order and pay
        complete_order: Order complete
      checkout:
        coupons:
          coupons: Coupons
          title: Coupons
        summary:
          shipping_cost: "Shipping Cost: "
          total_cost: "Total Cost"
          finish_button: 'Finish purchase'
          product_list_title: 'Order Product list:'
          title: 'Order detail:'
        confirm:
          title: "Confirm purchase of %{title} y click on 'Pay with Paypal' button"
          shipping_title:  "The products you are purchasing 'll be sent to the address below:"
        complete:
          title: Thanks for your order!
          approved: "We recieved the payment correctly! we're preparing the order to ship it when is ready"
          pending: "The payment is pending from approval, as soon as we get a confirmation from PayPal, we'll process the order."
          in_process: "The payment is in process, as soon as we get a confirmation from PayPal, we'll process the order."
          order_nmbr_title: "Order #"
          order_detail_title: The order detail
          email:
            order_item:
              price: Price
              link: Link
              quantity: Quantity
              color: Color
              size: Size
            buyer:
              subject: "Purchase confirmation of %{order_title} at GoodPeople.com"
              hi: "Hey"
              congratulations: ", congratulations on your purchase at GoodPeople"
              data_title: "Customer information"
              credentials: Name
              email: Email
              total: Total Price
              shop: Shop
              seller_data: "Seller's information"
            seller:
              subject: "Sale confirmation of %{order_title} at GoodPeople.com"
              congrats_pre: Congratulations
              congrats_post: You just sold an item at GoodPeople.com.
              data_title: "Customer information"
              buyer_credentials: Name
              email: Email
              total: Total Price
              shop: Shop

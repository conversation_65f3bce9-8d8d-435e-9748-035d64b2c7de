credit:
  format: fixed
  lenght: 240

  head:
    - position: # Registro fijo "0"
        from: 1
        to: 1
      default: 0
    - method: brand_type # Constante siempre "DB"(Tar<PERSON>. Credito)
    - method: current_date # Fecha transaccion (ddmmaa)
    - position: # Código del Banco del Establecimiento + Código de la  Sucursal del Establecimiento (6 espacios)
        from: 1
        to: 1
      default: '007039'
    - position: # Nro de Lote
        from: 1
        to: 4
      footer_method: lot
    - position: # Uso reservado
        from: 1
        to: 7
      default: " "
    - position: # Code Transaction(Debito en pesos)
        from: 1
        to: 1
      default: "0005"
    - position: # nro de establecimiento o comercio
        from: 1
        to: 1
      default: "0028614295"
    - position: # cant comprobantes
        from: 1
        to: 2
      footer_method: count_rows #modificar esto!!!!!!!!
    - position: # importe por lote
        from: 1
        to: 15
      footer_method: amount_rows
    - position: # Tipo de archivo 0
        from: 1
        to: 1
      default: " "
    - position: # nro de caja
        from: 1
        to: 1
      default: "9295"
    - position: # Tipo de archivo 0
        from: 1
        to: 18
      default: " "

  body:
    - position: # Tipo de registro Fijo 3
        from: 1
        to: 3
      default: " "
    - method: number # Numero de tarjeta de credito
    - position: # contaste espacios
        from: 1
        to: 1
      default: " "
    - position: # cupon
        from: 1
        to: 1
      footer_method: cupon #modificar
    - method: current_date # Fecha
    - position: # contaste espacios
        from: 1
        to: 8
      default: " "
    - method: brand_amount # Importe
    - position: # contaste espacios
        from: 1
        to: 7
      default: " "
    - method: reference # REferencia o número de comprobante o Nro. Secuencial ascendente único por archivo
    - position: # contaste espacios
        from: 1
        to: 1
      default: " "

debit:
  format: fixed
  lenght: 240

  head:
    - position: # Tipo de registro fijo 0
        from: 1
        to: 1
      default: 0
    - method: brand_type # Constante DEBLIQC & DEBLIQQ
    - position: # -numero del establecimiento
        from: 1
        to: 1
      default: 0028614295
    - position: # Constante
        from: 1
        to: 1
      default: "900000    "
    - method: current_date # Fecha AAAAMMDD
    - method: current_time # HORA HHMM
    - position: # Tipo de archivo 0
        from: 1
        to: 1
      default: 0
    - position: # Constante de espacios
        from: 1
        to: 57
      default: " "
    - position: # Marca de fin de linea.
        from: 1
        to: 1
      default: "*"

  body:
    - position: # Tipo de registro Fijo 1
        from: 1
        to: 1
      default: 1
    - method: number # Numero de tarjeta de credito
    - position: # contaste espacios
        from: 1
        to: 3
      default: " "
    - method: reference # REferencia o número de comprobante o Nro. Secuencial ascendente único por archivo
    - method: current_date # Fecha AAAAMMDD
    - position: # Codigo de transacción constante 0005
        from: 1
        to: 1
      default: "0005"
    - method: brand_amount # Importe
    - position: # Identificador del débito
        from: 1
        to: 1
      default: "Tddeporte      "
    - position: # Código de alta de Identificador Constante ?E? si es el primer débito informado, sino espacios
        from: 1
        to: 29
      default: " "
    - position: # Marca de fin de linea.
        from: 1
        to: 1
      default: "*"


  footer:
    - position:
        from: 1
        to: 1
      default: 9
    - method: brand_type
    - position:
        from: 1
        to: 1
      default: 0028614295
    - position:
        from: 1
        to: 1
      default: "900000    "
    - method: current_date
    - method: current_time
    - position:
        from: 1
        to: 7
      footer_method: count_rows
    - position:
        from: 1
        to: 15
      footer_method: amount_rows
    - position:
        from: 1
        to: 36
      default: " "
    - position:
        from: 1
        to: 1
      default: "*"

surrender:
  debit:
    fields:
      - name: numero_de_tarjeta
        from: 2
        chars: 16
      - name: identificador
        from: 21
        chars: 8
      - name: fecha
        from: 29
        chars: 8
      - name: cod_transaccion
        from: 37
        chars: 4
      - name: importe
        from: 41
        chars: 15
      - name: id_debito
        from: 56
        chars: 15
      - name: codigo_rechazo
        from: 101
        chars: 3
      - name: description
        from: 104
        chars: 40
  credit:
    fields:
      - name: cod_banco
        from: 2
        chars: 3
      - name: cod_casa
        from: 5
        chars: 3
      - name: lote
        from: 8
        chars: 4
      - name: cod_transaccion
        from: 12
        chars: 4
      - name: numero_de_establecimiento
        from: 17
        chars: 10
      - name: numero_de_tarjeta
        from: 27
        chars: 16
      - name: numero_de_cupon
        from: 43
        chars: 8
      - name: fecha_de_origen #object
        from: 51
        chars: 6
      - name: importe
        from: 63
        chars: 15
      - name: cuotas
        from: 78
        chars: 2
      - name: identificador
        from: 95
        chars: 15
      - name: marca_primer_débito
        from: 110
        chars: 1
      - name: numero_de_cuenta
        from: 111
        chars: 10
      - name: codigo_de_rama_del_tipo_de_seguro
        from: 121
        chars: 3
      - name: endoso_de_la_poliza
        from: 124
        chars: 3
      - name: estado_del_movimiento
        from: 130
        chars: 1
      - name: rechazo_codigo_motivo_1
        from: 131
        chars: 2
      - name: descripcion_rechazo_codigo_motivo_1
        from: 133
        chars: 29
      - name: rechazo_codigo_motivo_2
        from: 162
        chars: 2
      - name: descripcion_rechazo_codigo_motivo_2
        from: 164
        chars: 29
      - name: fecha_de_presentacion #object
        from: 225
        chars: 6
      - name: fecha_de_pago #object
        from: 231
        chars: 6
      - name: cartera
        from: 237
        chars: 2


format: fixed
lenght: 240

head: null

body:
  - position:
      from: 1
      to: 9
    format: integer
    name: Identifación del Socio.
    method: identify
  - position:
      from: 10
      to: 25
    format: integer
    name: Numero de tarjeta cabal.
    method: number
  - position:
      from: 26
      to: 36
    format: integer
    name: Importe del débito.
    method: brand_amount
  - position:
      from: 37
      to: 118
    format: string
    name: Libre
    default: " "
  - position:
      from: 119
      to: 143
    format: string
    name: <PERSON>mpa<PERSON><PERSON> de seguro.
    default: " "
  - position:
      from: 144
      to: 149
    format: integer
    name: Fecha de la presentación DDMMAA
    method: current_date
  - position:
      from: 150
      to: 176
    format: string
    name: Libre
    default: " "
  - position:
      from: 177
      to: 187
    format: integer
    name: Número de comercio cabal
    default: "12345678912"
  - position:
      from: 188
      to: 188
    format: string
    name: Código de moneda
    default: P
  - position:
      from: 189
      to: 209
    format: string
    name: Libre
    default: " "
  - position:
      from: 210
      to: 213
    format: integer
    name: Numero de ticket
    default: " "
  - position:
      from: 214
      to: 223
    format: string
    name: Libre
    default: " "
  - position:
      from: 224
      to: 225
    format: integer
    name: Codigo de operación
    default: 01
  - position:
      from: 226
      to: 240
    format: string
    name: Libre
    default: " "

# ey.yml supports many deploy configuration options when committed in an
# application's repository.
#
# Valid locations: REPO_ROOT/ey.yml or REPO_ROOT/config/ey.yml.
#
# Examples options (defaults apply to all environments for this application):
#
# defaults:
#   migrate: true                           # Default --migrate choice for ey deploy
#   migration_command: 'rake migrate'       # Default migrate command to run when migrations are enabled
#   branch: default_deploy_branch           # Branch/ref to be deployed by default during ey deploy
#   bundle_without: development test        # The string to pass to bundle install --without ''
#   maintenance_on_migrate: true            # Enable maintenance page during migrate action (use with caution) (default: true)
#   maintenance_on_restart: false           # Enable maintanence page during every deploy (default: false for unicorn & passenger)
#   ignore_database_adapter_warning: false  # Hide the warning shown when the Gemfile does not contain a recognized database adapter (mongodb for example)
#   your_own_custom_key: 'any attribute you put in ey.yml is available in deploy hooks'
# environments:
#   YOUR_ENVIRONMENT_NAME: # All options pertain only to the named environment
#     any_option: 'override any of the options above with specific options for certain environments'
#     migrate: false
#
# Further information available here:
# https://support.cloud.engineyard.com/entries/20996661-customize-your-deployment-on-engine-yard-cloud
#
# NOTE: Please commit this file into your git repository.
#
---
environments:
  production: {}
defaults:
  bundler: true
  migrate: false
  precompile_assets: true
  precompile_unchanged_assets: true
  migration_command: rake db:migrate

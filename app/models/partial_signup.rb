class PartialSignup < ActiveRecord::Base
  has_one :avatar,
          class_name: Social::Attachment::AvatarPicture,
          as: 'owner',
          dependent: :destroy

  validates :email,
            presence: true, 
            uniqueness: true,
            format: {
              with: /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\Z/i,
              on: :create }

  def self.new_for_user(user_attributes, avatar_photo)
    new(user_attributes) do |s|
      if avatar_photo.present?
        avatar_photo = URI.parse(avatar_photo) if avatar_photo.instance_of?(String)
        s.avatar = Social::Attachment::AvatarPicture.new(photo: avatar_photo)
      end
    end
  end

  def name
    "#{first_name} #{last_name}"
  end
end

class SaleItem < ActiveRecord::Base
  acts_as_listable_view

  scope :created_at_gte, ->(reference_time) { where('lstble_sale_items.created_at >= ?', reference_time) }
  scope :created_at_lt, ->(reference_time) { where('lstble_sale_items.created_at < ?', reference_time) }
  scope :orders, -> { where(listable_type: 'Mkp::Order') }
  scope :purchases, -> { where(listable_type: 'Purchase') }
  scope :with_store_id, ->(store_ids) { where(store_id: [*store_ids]) }
  scope :with_payment_created_at_gte,  ->(reference_time) do
    joins("INNER JOIN mkp_payments ON lstble_sale_items.listable_id = mkp_payments.sale_item_id AND lstble_sale_items.listable_type = mkp_payments.sale_item_type")
      .where('mkp_payments.created_at >= ?', reference_time).uniq
  end
  scope :with_payment_created_at_lt, ->(reference_time) do
    joins("INNER JOIN mkp_payments ON lstble_sale_items.listable_id = mkp_payments.sale_item_id AND lstble_sale_items.listable_type = mkp_payments.sale_item_type")
      .where('mkp_payments.created_at <= ?', reference_time).uniq
  end
  scope :with_payment_status, ->(payment_status) do
    joins("INNER JOIN mkp_payments ON lstble_sale_items.listable_id = mkp_payments.sale_item_id AND lstble_sale_items.listable_type = mkp_payments.sale_item_type")
      .where(mkp_payments: { status: payment_status }).uniq
  end
  scope :with_shipment_status, ->(shipment_status) do
    joins("INNER JOIN mkp_shipments ON lstble_sale_items.listable_id = mkp_shipments.sale_item_id AND lstble_sale_items.listable_type = mkp_shipments.sale_item_type AND mkp_shipments.status = '#{shipment_status}'").uniq
  end
  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    terms = query.downcase.split(/\s+/)
    terms = terms.map{ |e| ('%' + e.gsub('*', '%') + '%').gsub(/%+/, '%')}
    num_or_conds = 1

    sql = terms.map{ |term| "lstble_sale_items.listable_id like ?" }.join(' OR ')
    cobis_sql = terms.map{ |term| "lstble_sale_items.id_cobis like ?" }.join(' OR ')
    users_sql = "email like ? OR first_name like ? OR last_name like ?"
    users_query = "%#{query}%"

    guest_ids = Mkp::Guest.where(users_sql, users_query, users_query, users_query).ids
    customer_ids = Mkp::Customer.where(users_sql, users_query, users_query, users_query).ids

    where(customer_type: 'Mkp::Guest', customer_id: guest_ids)
      .or(where(customer_type: 'Mkp::Customer', customer_id: customer_ids))
      .or(where(sql, *terms.map{ |e| [e] * num_or_conds }.flatten))
      .or(where(cobis_sql, *terms.map{ |e| [e] * num_or_conds }.flatten))
  end

	def method_missing(method_name, *args, &block)
    if listable.respond_to?(method_name)
      listable.send(method_name)
    else
      super
    end
  end

  def respond_to_missing?(method_name, *args)
    listable.respond_to?(method_name) or super
  end
end
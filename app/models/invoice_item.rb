class InvoiceItem < ActiveRecord::Base
  belongs_to :suborder, polymorphic: true
  belongs_to :shop, class_name: 'Mkp::Shop'
  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :supplier_invoice, required: false

  scope :with_store_id, ->(store_id) { where("invoice_items.store_id in (?)", store_id) }
  scope :with_shop_id, ->(shop_ids) { where("invoice_items.shop_id in (?)", shop_ids) }
  scope :with_created_from, ->(created_from) { where('invoice_items.created_at >= ?', "#{created_from} 00:00:00") }
  scope :with_created_to, ->(created_to) { where('invoice_items.created_at <= ?', "#{created_to} 23:59:59") }
  scope :with_fulfilled_from, ->(fulfilled_from) { where('invoice_items.fulfilled_at >= ?', "#{fulfilled_from} 00:00:00") }
  scope :with_fulfilled_to, ->(fulfilled_to) { where('invoice_items.fulfilled_at <= ?', "#{fulfilled_to} 23:59:59") }
  scope :with_shipment_status, ->(shipment_status) { where(shipment_status: shipment_status) }
  scope :with_invoice_status, ->(invoice_status) {where("invoice_items.invoice_status = ?", invoice_status)}
  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    terms = query.downcase.split(/\W+/)
    if terms.size > 1
      where("invoice_items.order_id in (?)", terms).or(where("invoice_items.suborder_id in (?)", terms)).or(where("invoice_items.transaction_id in (?)", terms))
    else
      terms = terms.map{ |e| (e.gsub('*', '%') + '%').gsub(/%+/, '%')}
      where("invoice_items.order_id like ?", terms).or(where("invoice_items.suborder_id like ?", terms)).or(where("invoice_items.transaction_id like (?)", terms))
    end
  end

  def self.create_from(order)
    basic_create(order, false)
  end

  def self.create_return_from(order)
    basic_create(order, true)
  end

  def self.basic_create(order, negative)
    suborders = []
    if order.class.name == 'Mkp::Order'
      suborders << order.suborders
    else
      suborders << order
    end

    suborders.flatten.each do |suborder|
      InvoiceItem.create(suborder: suborder, shop: suborder.shop, store: suborder.store,
                         order_id: suborder.order.id, created_at: suborder.created_at,
                         amount: (negative ? -suborder.total : suborder.total),
                         points: (negative ? -suborder.total_points : suborder.total_points),
                         total: suborder.total_points_price,
                         point_equivalent: suborder.point_equivalent,
                         iva: suborder.try(:iva) || 21,
                         transaction_id: suborder.payments.map(&:get_external_id).join('-'),
                         shipment_status: shipment_status_from(suborder),
                         invoice_status: 'in_process')
    end
  end

  def self.shipment_status_from(suborder)
    return 'delivered' if suborder.is_a?(Purchase)
    'unfulfilled'
  end

  def self.update_fulfilled_at(suborder, date)
    self.where(suborder: suborder).update_all(fulfilled_at: date)
  end

  def self.update_shipment_status(shipment)
    self.where(suborder: shipment.suborder).update_all(shipment_status: shipment.status)
  end

  def calculate_fulfilled_at
    fulfilled_at =
        if suborder.fulfilled?
          shipments_delivered = suborder.shipments.select {|each| each.delivered?}
          if shipments_delivered.any?
            shipments_delivered.first.updated_at
          else
            if suborder.shipments.any?
              suborder.shipments.last.updated_at
            else
              suborder.updated_at
            end
          end
        else
          nil
        end
    update_attributes(fulfilled_at: fulfilled_at)
  end

  def suborder_status
    suborder.status
  end

  def fulfilled?
    suborder.fulfilled?
  end

  def store_name
    store.name
  end

  def supplier_name
    shop.try(:name) || suborder.gateway
  end

  def supplier
    shop
  end

  def point_equivalent_without_iva
    point_equivalent / (1 + (iva / 100))
  end

  def total_without_iva
    points * point_equivalent_without_iva
  end

  def self.available_invoice_status
    %W[in_process pending invoiced]
  end
end

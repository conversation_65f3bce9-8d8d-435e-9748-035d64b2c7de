class InvoiceReport < ActiveRecord::Base
  belongs_to :shop, class_name: 'Mkp::Shop', required: false
  belongs_to :store, class_name: 'Mkp::Store'

  def shop_names
    (shop_ids.split(',').map {|each| (Mkp::Shop.find each).name}).join(' - ') if shop_ids
  end

  def parameters
    parameter = ""
    parameter << "<b>Tienda:</b> #{store.name}<br>" if store.present?
    parameter << "<b>Proveedores:</b> #{shop_names}<br>" if shop_ids.present?
    parameter << "<b>Creado desde el</b> #{created_from.strftime("%d-%m-%Y")}<br>" if created_from.present?
    parameter << "<b>Creado hasta el</b> #{created_to.strftime("%d-%m-%Y")}<br>" if created_to.present?
    parameter << "<b>Entregado desde el</b> #{fulfilled_from.strftime("%d-%m-%Y")}<br>" if fulfilled_from.present?
    parameter << "<b>Entregado hasta el</b> #{fulfilled_to.strftime("%d-%m-%Y")}<br>" if fulfilled_to.present?
    parameter << "<b>Estado del envio:</b> #{with_shipment_status}<br>" if with_shipment_status.present?
    parameter << "<b>Estado de facturación</b> #{with_invoice_status}<br>" if with_invoice_status.present?
    parameter << "<b>Query:</b> #{search_query}" if search_query.present?
    parameter.html_safe
  end

  def execute_report
    @entities = InvoiceItem.includes(:suborder => [:shipments]).all
    @entities = @entities.with_store_id(store_id) if store.present?
    @entities = @entities.with_shop_id(shop_id) if shop.present?
    @entities = @entities.with_created_from(created_from) if created_from.present?
    @entities = @entities.with_created_to(created_to) if created_to.present?
    @entities = @entities.with_fulfilled_from(fulfilled_from) if fulfilled_from.present?
    @entities = @entities.with_fulfilled_to(fulfilled_to) if fulfilled_to.present?
    @entities = @entities.with_shipment_status(with_shipment_status) if with_shipment_status.present?
    @entities = @entities.with_invoice_status(with_invoice_status) if with_invoice_status.present?
    @entities = @entities.search_query(search_query) if search_query.present?
    @entities.order('created_at desc')
  end
end

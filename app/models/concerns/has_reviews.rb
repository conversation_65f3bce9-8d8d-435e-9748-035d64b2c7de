module Concerns
  module HasReviews
    extend ActiveSupport::Concern

    included do
      has_many :reviews, class_name: 'Mkp::Review'
    end

    def rate
      return @rate unless @rate.blank?

      average = stars.present? ? stars.inject(:+).to_f / stars.length : 0
      @rate = (average * 2).round / 2.0
    end

    def stars
      @stars ||= reviews.curated.pluck(:stars)
    end

    def reviews_count
      stars.length
    end

    def reviewed_by(user)
      if user.is_a?(Mkp::Guest)
        review = Mkp::Review::Guest.where(customer_id: user.id, product_id: id)
      else
        review = Mkp::Review::User.where(customer_id: user.id, product_id: id)
      end
      review.present?
    end
  end
end

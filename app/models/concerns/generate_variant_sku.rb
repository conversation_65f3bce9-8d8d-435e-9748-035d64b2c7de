module Concerns
  module <PERSON>rateVariantSku
    extend ActiveSupport::Concern

    included do
      before_validation :build_gp_sku, if: -> { new_record? }
    end

    def retrieve_gp_sku
      update_sku.join('-')
    end

    def retrieve_product_id_sku
      "#{product_id}-#{sku}"
    end

    private

    def build_gp_sku(_sku = [])
      return if self.gp_sku.present?
      if product.try(:manufacturer).present? && product.manufacturer.sku_id.present?
        sku_parts = update_sku
        sku = sku_parts.join('-')
        if present_gp_sku?(sku) || _sku.include?(sku)
          sku_parts << random
          sku = sku_parts.join('-').gsub('--', '-')
        end
      end

      sku ||= 'CHANGE_ME'
      self.gp_sku = sku
    end

    def update_sku
      sku_parts = [product.manufacturer.sku_id]
      sku_parts << product_gp_sku_id
      sku_parts << properties_combination
      sku_parts.reject { |part| part.empty? }
    end

    def random
      ('a'..'z').to_a.sample(3).join.upcase
    end

    def present_gp_sku?(sku)
      Mkp::Variant.find_by(gp_sku: sku).present?
    end

    def product_gp_sku_id
      product.id.to_s.rjust(5, '0')
    end

    def properties_combination
      return 'NP' if product.has_no_property?
      hash_properties = product.available_properties.select{ |p| p.is_a?(Hash) }
      standard_properties = product.available_properties - hash_properties

      property_array = standard_properties.sort.map do |key|
        property_value = if key == :color
                           color = properties[key]
                           color[:name].present? ? color[:name] : color[:hex]
                         else
                           properties[key]
                         end
        property_value ||= ''
        property_value.gsub(/\W+/, '').upcase
      end
      hash_property_array = hash_properties.map do |property|
        properties[property[:slug].to_sym].gsub(/\W+/, '_').upcase
      end
      (property_array + hash_property_array).join('-')
    rescue
      'NP'
    end
  end
end

module Concerns
  module HasPublishablePictures
    extend ActiveSupport::Concern

    module ClassMethods

      # Add the async publish feature of the images to a model
      #
      # ==== Examples
      #
      # Adding this lines in your model after including the concern:
      #
      ##      has_many_publishable_pictures :pictures,
      ##                                    class_name: Social::Attachment::AlbumPicture,
      ##                                    foreign_key: :owner_id,
      ##                                    dependent: :destroy
      #
      # will be like including this in the model:
      #
      ##      has_many :pictures,
      ##               class_name: Social::Attachment::AlbumPicture,
      ##               foreign_key: :owner_id,
      ##               dependent: :destroy
      ##      attr_accessor "unpublished_picture_attributes"
      ##      before_save { |publisher| publish_pictures(Social::Attachment::AlbumPicture, :pictures) }
      #
      #
      def has_one_publishable_picture(name, options = {})
        has_one(name, options)
        attr_accessor "unpublished_#{name}_id"
        before_save { |publisher| publish_picture(options[:class_name], name) }
      end

      def has_many_publishable_pictures(name, options = {})
        has_many(name, options)
        attr_accessor "unpublished_#{name.to_s.singularize}_attributes"
        before_save { |publisher| publish_pictures(options[:class_name], name) }
      end
    end

    private

      def publish_picture(class_name, picture_attr_name)
        picture_class = class_name.is_a?(String) ? class_name.constantize : class_name
        unpublished_picture_id = send("unpublished_#{picture_attr_name}_id")
        return if unpublished_picture_id.blank?
        unpublished_picture = picture_class.unpublished_class.find(unpublished_picture_id)
        picture = unpublished_picture.publish(picture_class)
        send("#{picture_attr_name}=", picture)
      end

      def publish_pictures(class_name, pictures_attr_name)
        picture_class = class_name.is_a?(String) ? class_name.constantize : class_name
        unpublished_picture_attributes = send("unpublished_#{pictures_attr_name.to_s.singularize}_attributes")
        return if (unpublished_picture_attributes.blank? || unpublished_picture_attributes.all?(&:blank?))

        unpublished_picture_ids = []

        unpublished_picture_attributes.each do |attributes|
          id = attributes.delete(:id)
          unpublished_picture_ids << id
          picture_class.unpublished_class.find(id).update_attributes(attributes)
        end

        unpublished_pictures = picture_class.unpublished_class.where(id: unpublished_picture_ids)
        new_pictures = unpublished_pictures.map { |p| p.publish(picture_class) }
        new_pictures.each { |p| p.owner = self if p.respond_to?(:owner) }
        previous_pictures = send(pictures_attr_name)
        previous_pictures << new_pictures
      end
  end
end

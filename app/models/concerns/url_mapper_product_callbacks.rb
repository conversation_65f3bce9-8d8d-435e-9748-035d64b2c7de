module Concerns
  module UrlMapperProductCallbacks
    extend ActiveSupport::Concern

    HABTM_ASSOCIATIONS = UrlMapperService::PRODUCT_HABTM_ASSOCIATIONS

    included do
      after_create  :generate_urls
      after_save    :update_urls, if: ->(m) {
        changes_come_from_update_action?(m) && UrlMapperService.changes_are_relevant?(m)
      }
      before_destroy :destroy_urls

      HABTM_ASSOCIATIONS.each do |association|
        if self.method_defined?("before_remove_for_#{association}")
          self.send("before_remove_for_#{association}=", [:changing_association])
        end

        if self.method_defined?("before_add_for_#{association}=")
          self.send("before_add_for_#{association}=", [:changing_association])
        end
      end
    end

    def generate_urls
      UrlMapperService.generate_urls_for(self)
    end

    def changing_association(association)
      association_name = get_association_name(association)

      if changed_attributes[association_name].nil?
        previous_content = send(association_name).dup # `dup` rationale: force evaluation.
        self.changed_attributes.merge!({ association_name => previous_content })
      end
    end

    def destroy_urls
      UrlMapperService.destroy_urls_for(self)
    end

    def update_urls
      UrlMapperService.update_urls_for(self)
    end

    private

    def changes_come_from_update_action?(model)
      !model.id_changed? && !model.deleted_at_changed?
    end

    def get_association_name(association)
      association.class.name.demodulize.downcase.pluralize
    end
  end
end

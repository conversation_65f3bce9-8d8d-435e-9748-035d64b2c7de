module Concerns
  module UrlMapperCategoryCallbacks
    extend ActiveSupport::Concern

    included do
      after_save :generate_new_urls, if: ->(m) {
        m.active &&
        changes_come_from_update_action?(m) &&
        UrlMapperService.changes_are_relevant?(m)
      }

      before_destroy :destroy_urls, if: ->(m) {
        m.active
      }
    end

    def destroy_urls
      UrlMapperService.destroy_urls_for(self)
    end

    def generate_new_urls
      UrlMapperService.generate_urls_for(self)
    end

    private

    def changes_come_from_update_action?(model)
      !model.id_changed? && model.updated_at_changed?
    end
  end
end

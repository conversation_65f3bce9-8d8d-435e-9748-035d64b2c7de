module Concerns
  module HasSlugOnRedis
    extend ActiveSupport::Concern

    included do
      before_save do |model|
        if model.persisted? && model.slug_changed?
          RedisSlug.destroy(model.class.find(model))
        end
      end

      after_save do |model|
        if model.slug_changed?
          RedisSlug.save(model)
        end
      end

      before_destroy do |model|
        RedisSlug.destroy(model)
      end
    end
  end
end

module Concerns
  module Favoritable
    extend ActiveSupport::Concern

    included do
      has_many :favorites,
               as: :favoritable,
               dependent: :destroy
    end

    def favorited_by(user)
      Favorite.create! do |f|
        f.user = user
        f.favoritable = self
      end
    end

    def unfavorited_by(user)
      favorites.where(user_id: user.id).destroy_all
    end

    def users_favoriting
      User.joins('RIGHT OUTER JOIN favorites ON favorites.user_id = users.id')
      .where('favorites.favoritable_id' => id, 'favorites.favoritable_type'=> self.class)
    end

    def favorited_by?(user)
      return false if user.blank?
      favorites.where(user_id: user.id).present?
    end

    def favorites_count
      favorites.nil? ? 0 : favorites.count
    end
  end
end

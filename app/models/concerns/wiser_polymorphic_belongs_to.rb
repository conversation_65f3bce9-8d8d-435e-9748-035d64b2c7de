# This make possible to do eager loading with polymorphic associations
module Concerns
  module WiserPolymorphicBelongsTo
    extend ActiveSupport::Concern

    def wiser_polymorphic_belongs_to(association, class_name, foreign_key, foreign_type_column, original_table)
      conditions = Proc.new do
        respond_to?(foreign_type_column) ?
          where('1 = ?', eval(foreign_type_column) == class_name) :
          where("#{original_table}.#{foreign_type_column} = ?", class_name)
      end
      belongs_to association, -> { conditions }, class_name: class_name, foreign_key: foreign_key
    end
  end
end

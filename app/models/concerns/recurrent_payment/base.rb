module RecurrentPayment
  module Base
    extend Forwardable
    extend ActiveSupport::Concern

    def_delegators :klass, :identify, :current_date, :current_time, :brand_type, :reference, :payment_expiration, :period
    def_delegator :klass, :credit_card_amount, :brand_amount

    def amount
      subscription.amount.to_i
    end

    def due
      subscription.due.to_i
    end

    def subscription
      member.customer.subscription
    end

    def parse_zero(value, length, default = '0')
      value.to_s.rjust(length, default)
    end

    def build_payment
      payments = subscription.payments
      payment = payments.current_period.last
      payment.present? && payment.pending? && (payment.update(amount: amount)) && return
      period = Date.today.strftime('%m/%y')
      { period: period, amount: amount, status: 0, recurrent: true, subscription_id: subscription.id }
    end

    def klass
      @klass ||= "RecurrentPayment::#{brand.capitalize}".constantize.new(self)
    end
  end
end

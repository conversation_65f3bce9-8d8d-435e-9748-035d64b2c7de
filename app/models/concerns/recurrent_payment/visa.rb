module RecurrentPayment
  class Visa
    attr_accessor :card

    def initialize(card)
      @card = card
    end

    def credit_card_amount
      price = sprintf('%.2f', card.due).to_s.split(".").join("")
      card.parse_zero( price, 15)
    end

    def current_date
      format = credit? ? "%d%m%y" : "%Y%m%d"
      Date.today.strftime(format)
    end

    def current_time
      Time.now.strftime("%H%M")
    end

    def brand_type
      credit? ? 'DB' : 'DEBLIQD '
    end

    def reference
      credit? ? credit_reference : debit_reference
    end

    private
    def credit?
      card.card_type == "CREDIT" || card.card_type == "credit"
    end

    def credit_reference
      member = card.member
      card.parse_zero(member.customer.store_id, 7) + card.parse_zero(member.id, 8)
    end

    def debit_reference
      member = card.member
      card.parse_zero(member.customer.store_id, 2) + card.parse_zero(member.id, 6)
    end
  end
end


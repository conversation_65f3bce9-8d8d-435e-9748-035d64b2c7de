module RecurrentPayment
  class Amex
    attr_accessor :card

    def initialize(card)
      @card = card
    end

    def credit_card_amount
      price = sprintf('%.2f', card.due).to_s.split(".").join("")
      card.parse_zero( price, 11)
    end

    def reference
      member = card.member
      card.parse_zero(member.customer.store_id, 3) + card.parse_zero(member.id, 7)
    end

    def identify
      member = card.member
      card.parse_zero(member.customer.store_id, 3) + card.parse_zero(member.id, 5)
    end

    def current_date
      @current_date ||= DateTime.now
      @current_date.strftime("%Y%m%d")
    end

    def current_time
      @current_date.strftime("%H%M%S")
    end
  end
end

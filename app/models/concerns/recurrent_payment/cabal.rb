module RecurrentPayment
  class Cabal
    attr_accessor :card

    def initialize(card)
      @card = card
    end

    def credit_card_amount
      price = sprintf('%.2f', card.due).to_s.split(".").join("")
      card.parse_zero( price, 11)
    end

    def identify
      member = card.member
      card.parse_zero(member.customer.store_id, 2) + card.parse_zero(member.id, 7)
    end

    def current_date
      Date.today.strftime("%d%m%y")
    end
  end
end

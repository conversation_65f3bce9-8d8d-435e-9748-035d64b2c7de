module RecurrentPayment
  class Mastercard
    attr_accessor :card

    def initialize(card)
      @card = card
    end

    def credit_card_amount
      price = sprintf('%.2f', card.due).to_s.split(".").join("")
      card.parse_zero( price, 11)
    end

    def current_date
      Date.today.strftime("%d%m%y")
    end

    def payment_expiration
      (Time.now + 2.month ).strftime("%d%m%y")
    end

    def period
      Date.today.strftime("%m/%y")
    end

    def reference
      member = card.member
      card.parse_zero(member.customer.store_id, 3) + card.parse_zero(member.id, 9)
    end
  end
end


module Concerns
  module GeneratesNotifications
    extend ActiveSupport::Concern

    module ClassMethods
      def generates_notification_on_create(*event_types)
        after_create do |object_created|
          event_types.each do |event_type|
            NotifyUserWorker.perform_in(90.seconds,
                                        event_type,
                                        id: id,
                                        klass: object_created.class.to_s)
          end
        end
      end
    end
  end
end

class BulkImport < ActiveRecord::Base
  has_attached_file :file
  do_not_validate_attachment_file_type :file
  before_post_process :rename_file

  def rename_file
    extension = File.extname(file_file_name).downcase
    file.instance_write :file_name, "#{(0...15).map { ('a'..'z').to_a[rand(26)] }.join}-#{Time.now.to_i}#{extension}"
  end

  before_create :generate_token

  private

  def generate_token
    self.token = loop do
      random_token = SecureRandom.urlsafe_base64(nil, false)
      break random_token unless BulkImport.exists?(token: random_token)
    end
  end
end

# Represents the authentication of a user via a third-party (<PERSON><PERSON><PERSON>, Twitter).
class Authentication < ActiveRecord::Base
  belongs_to :user

  validates_uniqueness_of :uid, scope: :type

  def self.normalize_auth_hash!(auth_hash)
    subclass = of_provider(auth_hash.provider)
    subclass.normalize_auth_hash!(auth_hash)
  end

  def self.of_provider(type)
    subclass_name_by(type).constantize
  end

  def self.create_and_associate_user(auth_hash, user)
    auth = create_from_auth_hash(auth_hash)
    auth.user = user
    auth.save
  end

  def self.associate_user(auth_id, user_id)
    auth = find(auth_id)
    auth.update_attribute(:user_id, user_id) unless auth.has_user?
    auth
  end

  def self.find_or_create_by_auth_hash(auth_hash)
    auth = find_by_auth_hash(auth_hash)
    return auth if auth
    create_from_auth_hash(auth_hash)
  end

  def self.of(user)
    find_by_user_id_and_type(user.id, name)
  end

  def self.is_uid_taken?(uid)
    (auth = find_by_uid_and_type(uid, name)).nil? ? false : (auth.has_user? ? true : (self.cleanup_failed(uid);false))
  end

  def self.cleanup_failed(uid)
    find_by_uid_and_type(uid, name).destroy
  end

  def login_user
    UserSession.create(user, true)
  end

  def expired?
    expirable? && oauth_expires_at.past?
  end

  def has_user?
    user.present?
  end

  def has_user_with_google_auth?(auth_hash)
    try_user = User.find_by_email(auth_hash.info.email)
    try_user.present? &&
      Authentication.find_by_user_id(try_user.id).present? &&
      Authentication.find_by_user_id(try_user.id).type == "Authentication::Google"
  end

  def self.deauthorize(user)
    where(user_id: user.id, type: name).first.destroy
  end

  def oauth_expires_in=(seconds)
    self.oauth_expires_at = Time.now + seconds.to_i
  end

  private

  def self.subclass_name_by(provider)
    "Authentication::#{provider.capitalize}"
  end

  def self.find_by_auth_hash(auth_hash)
    find_by_uid(auth_hash.uid, name)
  end

  def self.find_authentication_of(user, type)
    find_by_user_id(user.id)
  end

  def self.create_from_auth_hash(auth_hash)
    auth = new_from_auth_hash(auth_hash)
    auth.save
    auth
  end

  def self.new_from_auth_hash(auth_hash)
    subclass = self.of_provider(auth_hash.provider)

    subclass.new do |a|
      a.type = subclass.name
      a.uid = auth_hash.uid
      a.oauth_token = auth_hash.credentials.token
      a.oauth_secret = auth_hash.credentials.secret
      if auth_hash.credentials.expires_at
        a.oauth_expires_at = Time.at(auth_hash.credentials.expires_at)
      end
      a.avatar_url = auth_hash.info.image
    end
  end
end

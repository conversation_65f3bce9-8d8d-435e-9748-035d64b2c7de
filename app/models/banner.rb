class Banner < ActiveRecord::Base
  TEMPLATES = [:paint, :wave]

  validates :columns, presence: true, unless: :draft?
  validates :link, presence: true, unless: :draft?
  validates :starts_on, presence: true, unless: :draft?
  validates :title, presence: true, unless: :draft?
  validates :type, presence: true

  validate :link_must_have_protocol, unless: :draft?

  after_initialize :generate_token

  scope :active,    ->          { where('starts_on < ? AND ( ends_on IS NULL OR ends_on > ? )', Time.now, Time.now) }
  scope :list,      ->(options) { where(network: options[:network], columns: options[:columns]) }
  scope :published, ->          { where(draft: false) }
  scope :sample,    ->(n)       { order('RAND()').limit(n) }

  PAPERCLIP_CONFIGS = {
    path: 'vnrs/:public_folder/:attachment_:token_:style.:extension'
  }

  has_attached_file :desktop_image,
    PAPERCLIP_CONFIGS.merge(styles: ->(a) { { thumbnail: 'x50',
                                              normal: "#{a.instance.desktop_width}x#{a.instance.desktop_height}#" } })
  has_attached_file :mobile_image
    PAPERCLIP_CONFIGS.merge(styles: ->(a) { { thumbnail: 'x50',
                                              normal: "#{a.instance.mobile_width}x#{a.instance.mobile_height}#" } } )

  validates_attachment :desktop_image,
                       content_type: { content_type: %w(image/bmp image/gif image/jpeg image/png application/octet-stream) },
                       size: { in: 0..2.megabytes }
  validates_attachment :mobile_image,
                       content_type: { content_type: %w(image/bmp image/gif image/jpeg image/png application/octet-stream) },
                       size: { in: 0..2.megabytes }

  validates_attachment_presence :desktop_image, unless: :draft?
  validates_attachment_presence :mobile_image, unless: :draft?

  store :content, accessors: [:web_description,
                              :web_headline,
                              :position,
                              :mobile_description,
                              :mobile_headline]

  def active?
    return false if Time.now < starts_on
    return true if ends_on.blank?
    Time.now < ends_on
  end

  def awaiting?
    Time.now < starts_on
  end

  def self.desktop_height(columns)
    case columns
    when 1 then 300
    when 2 then 300
    when 4 then 500
    end
  end

  def desktop_height
    self.class.desktop_height(columns)
  end

  def self.desktop_width(columns)
    case columns
    when 1 then 300
    when 2 then 682
    when 4 then 1600
    end
  end

  def desktop_width
    self.class.desktop_width(columns)
  end

  def has_external_link?
    !link.include?(HOSTNAME)
  end

  def height
    HEIGHT
  end

  def self.mobile_height
    300
  end

  def mobile_height
    self.class.mobile_height
  end

  def self.mobile_width
    620
  end

  def mobile_width
    self.class.mobile_width
  end

  def thumbnail
    desktop_image.url(:thumbnail)
  end

  protected

  def generate_token
    unless attribute_present?('token')
      self.token = Digest::MD5.hexdigest("#{rand.to_s}#{Time.now.to_i.to_s}")
    end
  end

  private

  def link_must_have_protocol
    if self.link !~ /^(\/|http)/i
      errors.add(:link, "has to start with '/' or http")
    end
  end

  class Shop < Banner
    validates :shop_id, presence: true
    belongs_to :shop, class_name: '::Mkp::Shop'

  end

  class Network < Banner; end
end

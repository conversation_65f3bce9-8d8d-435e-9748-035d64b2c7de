module ExceptionNotifier
  class FailyNotifier

    def initialize(options);end

    def call(exception, options = {})
      backtrace = options[:data][:backtrace] if options[:data] && options[:data][:backtrace]
      backtrace = exception.backtrace if backtrace.nil? && exception.backtrace

      if backtrace
        exception_description = backtrace.first.split('/').last
        Faily::Failure.create(exception: exception_description, backtrace: backtrace.join("|"))
      end
    end
  end
end

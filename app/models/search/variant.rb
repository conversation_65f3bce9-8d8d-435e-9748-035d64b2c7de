module Search
  class Variant < Base
    def self.all(query, network = nil, limit = nil)
      variants_search = Sunspot.search(::Mkp::Variant) do
        fulltext query do
          boost_fields :manufacturer_name_exact_match => 100.0,
                       :manufacturer_name => 10.0,
                       :title => 5.0,
                       :description => 1.0
        end

        with :network, network if network.present?
        with :deleted, false
        with :shop_visible, true
        with :shop_deleted, false
        with :display_variant, true
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now
      end

      results = variants_search.results
      results = results[0, limit] unless limit.nil?
      results
    end
  end
end

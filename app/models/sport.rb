# Represents a sport or sport category. Categorization has only two levels:
# parent categories and specific sports.
class Sport < ActiveRecord::Base
  include Concerns::HasSlugOnRedis

  has_ancestry orphan_strategy: :restrict

  store :description,
        accessors: [:description_ar, :description_us]

  extend FriendlyId

  friendly_id :name, use: [:scoped, :slugged], scope: :ancestry

  # has_and_belongs_to_many :products, class_name: Mkp::Product
  #
  # has_and_belongs_to_many :categories, class_name: Mkp::Category

  default_scope { order('display_order, name ASC') }

  def self.children
    Sport.where('ancestry IS NOT NULL')
  end

  def self.followable_sports
    unfollowable_root_names = ['Offtopic']
    unfollowable_chidlren_names = ['Other']

    unfollowable_roots = roots.where('name IN (?)', unfollowable_root_names)
    children = where('ancestry IS NOT NULL')

    if unfollowable_roots.empty?
      children_from_followable_roots = children
    else
      children_from_followable_roots = children.where('ancestry NOT IN (?)',
                                                      unfollowable_roots.map(&:id))
    end

    children_from_followable_roots.where('name NOT IN (?)',
                                         unfollowable_chidlren_names)
  end

  def get_description(network)
    send("description_#{network.downcase}")
  end
end

module Social
  class HomeFeedCache

    VERSION = 'v4'

    def initialize(user)
      @user = user
    end

    def fetch
      if Rails.cache.exist?(cache_key)
        read
      else
        feed = yield
        write(feed)
        feed
      end
    end

    def write(feed)
      cached_items = feed.map { |i| CachedFeedItem.new(i) }
      cached_feed = { items: cached_items, total_entries: feed.total_entries }
      Rails.cache.write(cache_key, cached_feed)
    end

    private

    def read
      cached_feed = Rails.cache.read(cache_key)
      cached_feed[:items].paginate(page: 1,
                                   total_entries: cached_feed[:total_entries])
    end

    def cache_key
      "#{VERSION}_home_feed_#{@user.id}"
    end
  end
end

module Social

  # Represents basic statistics of an user's activity.
  class UserStats
    ATTRIBUTE_NAMES = [:followers, :followings, :whatsups, :pictures]

    ATTRIBUTE_NAMES.each do |name|
      define_method("#{name}_count") { @counts[name] }
      define_method("has_#{name}?") { @counts[name] > 0 }
    end

    # Returns a new instance of 'UserStats' for the specified 'User'.
    def self.of(user)
      sent_whatsups_ids = user.sent_whatsups.map(&:id)
      sent_pics_count = Social::Attachment::WhatsupPicture.where(owner_type: 'Social::Whatsup', owner_id: sent_whatsups_ids).size
      counts = {
        followers: user.followers_count,
        followings: user.followed_users.count,
        whatsups: sent_whatsups_ids.size - sent_pics_count,
        pictures: sent_pics_count + user.albums.count
      }

      UserStats.new(counts)
    end

    private

    def initialize(counts)
      @counts = counts
    end
  end
end

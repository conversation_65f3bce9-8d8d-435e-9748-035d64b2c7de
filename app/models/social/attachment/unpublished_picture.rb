module Social
  module Attachment
    class UnpublishedPicture < Picture
      has_attached_file :photo,
                        convert_options: { all: '-auto-orient', m: '-quality 90' },
                        storage: :filesystem,
                        path: ':rails_root/public/system/temp_pictures/:token_:style.:extension',
                        url: '/system/temp_pictures/:token_:style.:extension',
                        styles: { m: '900x1600>' }

      # Publishes this picture and returns it as an instance of the specified
      # type.
      def publish(as_type)
        update_attribute(:type, as_type.to_s)
        published_picture = as_type.find(id)
        published_picture.update_attribute(:processing, true)
        published_picture
      end

      protected

      def processes_photos_async?
        false
      end
    end
  end
end

module Social
  module Attachment
    class CoverPicture < Picture
      has_attached_file :photo,
                        default_url: ->(a){ a.instance.owner.class::DEFAULT_COVER_URL },
                        styles: ->(a){ a.instance.class.styles },
                        processors: [:thumbnail, :paperclip_optimizer]

      def self.styles
        {
          t: '100x100#', # cropped
          m_cover: '500x300>', # max sizes, with aspect ratio
          cover: '1800', # use to be  cover: '1800x430>'
          h_cover: '2500' # use to be  cover: '2500x600>'
        }
      end

      def touch_picture_owner
        return if not self.respond_to?(:owner)
        self.owner.respond_to?(:final_touch) ? self.owner.final_touch : self.owner.touch
      end

      def refresh_feed
        return if not self.respond_to?(:owner)
        return if not self.owner.respond_to?(:author)

        Rails.logger.info("Refreshing feed of user [#{self.owner.author.id}]")
        Social::HomeFeed.of(self.owner.author).refresh
      end
    end
  end
end

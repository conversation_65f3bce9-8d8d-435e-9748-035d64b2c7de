module Social
  module Attachment
    class AlbumPicture < Picture
      has_attached_file :photo,
                        styles: ->(a){ a.instance.class.styles },
                        processors: [:thumbnail, :paperclip_optimizer]

      auto_html_for :description do
        html_escape
        link target: '_blank', rel: 'nofollow'
      end unless Rails.env.test? # TODO, this is failing when running bundle exec rake db:schema:load RAILS_ENV=test (codeship)

      def self.styles
        {
          m: '500x500#', # cropped
          l: '900x1600>' # max sizes, with aspect ratio
        }
      end
    end
  end
end

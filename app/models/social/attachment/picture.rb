module Social
  module Attachment
    class Picture < ActiveRecord::Base
      self.table_name = :pictures

      attr_accessor :styles

      belongs_to :owner, polymorphic: true, touch: true

      has_attached_file :photo,
                        processors: [:thumbnail]

      validates_attachment :photo,
                           presence: true,
                           content_type: { content_type: %w(image/bmp image/gif image/jpeg image/png application/octet-stream) },
                           size: { in: 0..24.megabytes }

      after_initialize :generate_token

      after_commit :process_photo_async, if: :persisted?

      def self.unpublished_class
        UnpublishedPicture
      end

      def generate_token
        unless attribute_present?('token')
          self.token = Digest::MD5.hexdigest("#{rand.to_s}#{Time.now.to_i.to_s}")
        end
      end

      def url(style = :l)
        if processing
          style = :m unless style == :original
          unpublished_photo(style, :url)
        else
          photo.url(style)
        end
      end

      def unpublished_photo(style = :l, option)
        # Temporal if paperclip does not fix their issue with the method attachment_definitions
        path_definition = UnpublishedPicture.attachment_definitions[:photo][option]
        # path_definition = case option
        #   when :url then '/system/temp_pictures/:token_:style.:extension'
        #   when :path then ':rails_root/public/system/temp_pictures/:token_:style.:extension'
        #   else ''
        # end
        Paperclip::Interpolations.interpolate(path_definition, self.photo, style)
      end

      protected

      def processes_photos_async?
        true
      end

      private

      def process_photo_async
        if processes_photos_async? && processing
          PictureUploadWorker.perform_async(self.class.to_s, id)
        end
      end
    end
  end
end

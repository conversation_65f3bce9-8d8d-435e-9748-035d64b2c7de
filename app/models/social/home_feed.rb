module Social
  class HomeFeed
    def self.of(user)
      feed_builder = HomeFeedBuilder.new(user)
      feed_cache = HomeFeedCache.new(user)
      new(feed_cache, feed_builder)
    end

    def initialize(feed_cache, feed_builder)
      @feed_cache = feed_cache
      @feed_builder = feed_builder
    end

    def fetch(options)
      if options[:page].to_i == 1
        @feed_cache.fetch { @feed_builder.build(page: 1) }
      else
        @feed_builder.build(options)
      end
    end

    def refresh
      feed = @feed_builder.build(page: 1)
      @feed_cache.write(feed)
    end
  end
end
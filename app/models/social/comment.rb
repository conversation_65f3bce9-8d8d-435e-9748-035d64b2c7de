module Social

  # Represents a user comment on a whatsup or an album.
  class Comment < ActiveRecord::Base
    include ::Concerns::GeneratesNotifications

    MAX_LENGTH = 255

    belongs_to :user
    belongs_to :target, polymorphic: true

    validates :body, presence: true
    validates :user_id, presence: true
    validates :target, presence: true

    before_create :normalize

    generates_notification_on_create :commented, :mentioned

    private

    def normalize
      self.body = body.strip
      self.body = self.body[0...MAX_LENGTH]
    end

    auto_html_for :body do
      html_escape
      mention
      link target: '_blank', rel: 'nofollow'
    end
  end
end

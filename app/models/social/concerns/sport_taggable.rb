module Social
  module Concerns
    module SportTaggable
      def self.included(base)
        base.class_eval do
          if base.respond_to?(:has_many)
            base.has_many(:sport_taggings,
                          as: :sport_taggable,
                          dependent: :destroy)
            base.has_many(:sports, through: :sport_taggings)
          end
          if base.respond_to?(:scope)
            base.scope :with_sports, ->(sport_ids) {
              includes(:sports).where('sports.id IN(?)', sport_ids)
            }
          end
        end
      end
    end
  end
end
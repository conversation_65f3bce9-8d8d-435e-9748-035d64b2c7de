module Social
  module Concerns
    module <PERSON><PERSON><PERSON>
      def self.included(base)
        base.class_eval do
          base.after_create :curate_feed_item
          base.after_destroy :delete_curated_feed_item
        end
      end

      def curate_feed_item
        CurateFeedItemWorker.perform_in(1.minutes, self.id, self.class.to_s)
      end

      def delete_curated_feed_item
        curated_feed_item = Social::CuratedFeedItem.where(item_id: self.id,
                                                          item_type: self.class.to_s).destroy_all
      end
    end
  end
end

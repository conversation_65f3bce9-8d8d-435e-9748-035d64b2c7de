module Social
  class Album < ActiveRecord::Base
    include ::Concerns::Favoritable
    include ::Concerns::HasPublishablePictures
    include Social::Concerns::Deletable
    include Social::Concerns::Curatable
    include Social::Concerns::SportTaggable

    belongs_to :author, class_name: 'User'

    has_many :comments,
             as: :target,
             dependent: :destroy

    has_many_publishable_pictures :pictures,
                                  class_name: Social::Attachment::AlbumPicture,
                                  foreign_key: :owner_id,
                                  dependent: :destroy

    validates_associated :pictures
    #validates :unpublished_picture_attributes, presence: true, on: :create
    validates :title, presence: true
    validates :title, length: { maximum: 255 }
    validates :description, length: { maximum: 255 }

    # updates_home_feed of: :author, on: [:create, :destroy]

    searchable do
      text :title
      text :description
    end

    auto_html_for :title do
      html_escape
      mention
      link target: '_blank', rel: 'nofollow'
    end

    auto_html_for :description do
      html_escape
      mention
      link target: '_blank', rel: 'nofollow'
    end

    attr_writer :selected_picture_index

    def picture
      pictures.first if pictures.any?
    end

    def selected_picture_index
      @selected_picture_index || 0
    end

    def final_touch
      touch if pictures.select(&:processing).empty?
    end

  end
end

module Social
  class CachedFeedItem
    attr_reader :cache_key, :id, :type

    def initialize(item)
      @type = item.class
      @item = item
      @id = item.id
      @cache_key = item.cache_key
      _author = item.author
      @author_id = _author.id
      @author_type = _author.class
      @profile_cache_key = _author.profile.cache_key
    end

    def favorited_by?(user)
      return false if user.blank?
      Favorite.where(favoritable_id: id,
                     favoritable_type: type,
                     user_id: user.id).present?
    end

    def author
      author_class = Struct.new(:profile, :id, :class)

      profile_class = Struct.new(:cache_key)

      author_class.new(profile_class.new(@profile_cache_key),
                       @author_id,
                       @author_type)
    end

    def marshall_dump
      { type: @item.class,
        id: @item.id,
        cache_key: item.cache_key,
        profile_cache_key: item.author.profile.cache_key }
    end

    def marshall_load(data)
      @id = data[:id]
      @type = data[:type].constantize
      @cache_key = data[:cache_key]
      @profile_cache_key = data[:profile_cache_key]
    end

    def reload
      @type.find(@id)
    end
  end
end

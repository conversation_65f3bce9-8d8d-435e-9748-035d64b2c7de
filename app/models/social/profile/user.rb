module Social
  module Profile
    class User < Base
      DEFAULT_COVER_URL = '/assets/social/profiles/gp-user-placeholder-bg.jpg'

      self.table_name = :profiles

      validates :bio, length: { maximum: BIO_MAX_LENGTH }
      validates :first_name, :last_name, length: { in: 1..127 }

      alias_attribute :name, :first_name

      serialize :followed_sport_ids, Array

      def add_followed_sport(sport_id)
        sport = Sport.find(sport_id)
        update_attribute(:followed_sport_ids, followed_sport_ids + [sport.id])
      end

      def remove_followed_sport(sport_id)
        sport = Sport.find(sport_id)
        update_attribute(:followed_sport_ids, followed_sport_ids - [sport.id])
      end

      def followed_sports
        Sport.where(id: followed_sport_ids)
      end

      def full_name
        "#{first_name} #{last_name}"
      end
    end
  end
end

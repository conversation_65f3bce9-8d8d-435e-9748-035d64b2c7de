require 'open_uri_redirections'

module Social
  module Profile
    class Base < ActiveRecord::Base
      include ::Concerns::HasPublishablePictures

      self.abstract_class = true

      has_one_publishable_picture :avatar,
                                  class_name: Social::Attachment::AvatarPicture,
                                  as: 'owner',
                                  dependent: :destroy
      has_one_publishable_picture :cover,
                                  class_name: Social::Attachment::CoverPicture,
                                  as: 'owner',
                                  dependent: :destroy

      belongs_to :user, class_name: '::User', touch: true

      alias_method :original_avatar, :avatar
      alias_method :original_cover, :cover

      def avatar
        original_avatar || build_avatar
      end

      def cover
        original_cover || build_cover
      end

      def assign_avatar_from_url(url)
        self.avatar = build_avatar

        if url =~ /^#{URI::regexp}$/
          image_file = open(URI.parse(url), allow_redirections: :safe)
          self.avatar.photo = image_file
        else
          file_path = File.join(Rails.root, 'public', url.gsub('_l', '_m').split('?')[0])
          self.avatar.photo = File.open(file_path)
        end
      end

      def add_followed_sport(sport_id)
        return
      end

      def remove_followed_sport(sport_id)
        return
      end

      def followed_sports
        []
      end

      protected

      def normalize_website_url
        unless website_url.blank? || website_url.include?('http://') || website_url.include?('https://')
          self.website_url = 'http://' + website_url
        end
      end
    end
  end
end

module Social
  module Profile
    class Brand < Base
      DEFAULT_COVER_URL = '/assets/mkp/header-avenida.jpg'
      self.table_name = :brand_profiles

      serialize :sports_ids

      store :data, accessors: [:badges]

      validates :name, length: { in: 1..127 }
      alias_attribute :full_name, :name
      alias_attribute :bio, :description

      before_save :normalize_website_url

      belongs_to :manufacturer, class_name: Mkp::Manufacturer

      def badges
        data[:badges].nil? ? [] : data[:badges]
      end

      def has_badge?(badge)
        badges.include? badge.to_s
      end

      def first_name
        name
      end

      def last_name
        ''
      end
    end
  end
end
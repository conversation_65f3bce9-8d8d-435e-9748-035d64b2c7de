module Social

  # A facade over libraries for publishing to Facebook and Twitter.
  class SocialPublisher
    def self.publish_on_facebook(whatsup)
      authentication = Authentication::Facebook.of(whatsup.author)
      client = facebook_client(authentication)
      post_content = as_facebook_post_content(whatsup)
      client.feed!(post_content)
    rescue FbGraph::Invalid<PERSON><PERSON>, FbGraph::Unauthorized => e
      authentication.update_column(:can_write, false)
      log_publish_error(whatsup, e)
    rescue => e
      log_publish_error(whatsup, e)
    end

    def self.publish_on_twitter(whatsup)
      authentication = Authentication::Twitter.of(whatsup.author)
      client = twitter_client(authentication)
      content = as_twitter_post_content(whatsup)
      client.update(content)
    rescue Twitter::Error::Unauthorized => e
      authentication.update_column(:can_write, false)
      log_publish_error(whatsup, e)
    rescue => e
      log_publish_error(whatsup, e)
    end

    private

    def self.as_facebook_post_content(whatsup)
      params = {}
      params[:message] = whatsup.body unless whatsup.body.blank?
      params[:picture] = whatsup.picture.photo.url(:m) unless whatsup.picture.nil?
      params[:link] = whatsup_url(whatsup)
      params[:name] = 'GoodPeople'
      params[:description] = whatsup.body unless whatsup.body.blank?
      params
    end

    def self.as_twitter_post_content(whatsup)
      "#{whatsup.body} #{whatsup_url(whatsup)}"
    end

    def self.whatsup_url(whatsup)
      url_helpers = Rails.application.routes.url_helpers
      url_helpers.social_whatsup_url(whatsup, host: HOSTNAME)
    end

    def self.facebook_client(authentication)
      FbGraph::User.me(authentication.oauth_token)
    end

    def self.twitter_client(authentication)
      Twitter::Client.new(consumer_key: TWITTER_API,
                          consumer_secret: TWITTER_SECRET,
                          oauth_token: authentication.oauth_token,
                          oauth_token_secret: authentication.oauth_secret)
    end

    def self.log_publish_error(whatsup, exception)
      Rails.logger.info("Post[#{whatsup.inspect}] could not be published on Social Network. Error: #{exception}")
    end
  end
end
module Social
  class HomeFeedBuilder
    ITEMS_PER_PAGE = 12

    def initialize(user)
      @user = user
    end

    def build(options)
      followed_user_ids = @user.followed_users.pluck(:id)
      author_ids = followed_user_ids + [@user.id]
      whatsups, albums = find_whatsups_and_albums_authored_by(author_ids)

      merge_and_paginate(whatsups, albums, options)
    end

    private

    def find_whatsups_and_albums_authored_by(author_ids)
      whatsups = Whatsup.includes(author: :profile)
                        .where(author_id: author_ids)
                        .order('created_at DESC')
      albums = Album.includes(author: :profile)
                    .where(author_id: author_ids)
                    .order('created_at DESC')

      [whatsups, albums]
    end

    def merge_and_paginate(whatsups, albums, options)
      page = options[:page].to_i
      per_page = (options[:per_page] || ITEMS_PER_PAGE).to_i

      limit = per_page * page
      total_entries = whatsups.count + albums.count

      whatsups = whatsups.limit(limit)
      albums = albums.limit(limit)

      items = whatsups + albums
      items = sort(items)
      items.paginate(page: page,
                     per_page: per_page,
                     total_entries: total_entries)
    end

    def sort(items)
      items.sort { |a, b| b.created_at <=> a.created_at }
    end
  end
end

# encoding: UTF-8

module Social
  class Mention
    include Draper::Decoratable

    LOGIN_REGEX = /[a-zA-Z0-9][a-zA-Z0-9\-\._]*[a-zA-Z0-9]/

    MENTION_REGEX = /
      ([^a-zA-Z0-9_!#\$%&*@＠]|^|[rR][tT])   # $1: Preceeding character
      ([@＠])                                # $2: At mark
      (#{LOGIN_REGEX})                       # $3: Login
    /ox

    attr_reader :from, :to, :on

    def initialize(options)
      @on, @from, @to = options[:on], options[:from], options[:to]
    end

    def self.from_hash(hash)
      on = hash[:on_class].constantize.find(hash[:on_id])
      from = User.find(hash[:from_id])
      to = User.find(hash[:to_id])

      new(on: on, from: from, to: to)
    end

    def self.scan_mentioned_users(text)
      mentioned_logins = scan_mentioned_logins(text)
      User.where(login: mentioned_logins)
    end

    def self.scan_mentioned_logins(text)
      text.scan(MENTION_REGEX).map { |match| match[2] }
    end

    def self.replace_all(text)
      text.gsub(MENTION_REGEX) { $1 + yield($3) }
    end

    def to_hash
      { on_id: @on.id, on_class: @on.class.name, from_id: @from.id, to_id: @to.id }
    end

    def to_self?
      @from == @to
    end
  end
end

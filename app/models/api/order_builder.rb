module Api
  class OrderBuilder
    include AngularApp::V1::Concerns::CheckoutBuildable
    include ActiveModel::Model
    include ::AbstractController::Helpers
    include ::CheckoutFlow

    attr_accessor :store_id, :store

    attr_accessor :first_name, :last_name, :email, :phone, :address, :depth,
      :street_number, :city, :state, :postal_code, :real_remote_ip

    attr_accessor :items
    attr_accessor :params
    attr_accessor :cart, :checkout_session, :checkout_cart, :discount,
      :payment, :order, :shipments

    validates :first_name, :last_name, :email, :phone, :address,
      :street_number, :city, :state, :postal_code, :store_id, presence: true

    validate :store_exists
    validate :email_format
    validate :items_in_cart

    def save
      return false unless valid?
      set_params

      build_cart
      cart.destroy and return false unless build_address
      build_checkout_cart

      if checkout_cart.balance_due?
        process_order
      else
        errors.add("Cart seems to be empty") and return false
      end
    rescue ActiveRecord::RecordNotSaved
      errors.add("object could not be saved")
      false
    end

    private

    def set_params
      @network = 'AR'
      @current_store = store
      @params = ActionController::Parameters.new({ items: items })
    end

    def store_exists
      self.store = ::Mkp::Store.find_by_id(store_id)
      errors.add(:store, "doesnt exists") if store.nil?
    end

    def email_format
      errors.add(:base, 'Invalid Email') unless EmailUtils.valid_email_addresses?(email)
    end

    def items_in_cart
      return errors.add(:items, 'should be an array of items') unless items.is_a?(Array)

      self.items = items
        .map(&:symbolize_keys)
        .select{|i| i.has_key?(:variant_id) && i.has_key?(:quantity)}

      errors.add(:items, "has an invalid format") if self.items.empty?
    end

    def build_address
      guest = ::Mkp::Guest.new
      guest.first_name = first_name
      guest.last_name = last_name
      guest.email = email.downcase
      guest.network = "AR"
      guest.save!

      address_model = guest.addresses.new
      address_model.first_name = guest.first_name
      address_model.last_name = guest.last_name
      address_model.telephone = phone
      address_model.address = address
      address_model.address_2 = depth
      address_model.street_number = street_number
      address_model.city = city
      address_model.state = state
      address_model.country = "AR"
      address_model.zip = postal_code
      address_model.save!

      self.cart.customer = guest
      self.cart.address = address_model
      self.cart.save!
    rescue ActiveRecord::RecordNotSaved
      false
    end

    def process_order
      self.payment = ::Avenida::Payments::Empty.collect_payment(checkout_cart, {})
      self.order = ::Mkp::PurchaseProcessor.create_order!(checkout_cart.customer, checkout_cart, payment, store_id)
      create_and_associate_shipments
      cart.destroy if cart.present?
      true
    end

    def create_and_associate_shipments
      current_address = checkout_cart.customer.addresses.find(checkout_cart.address_id)
      choosen_delivery_options = checkout_cart.choosen_delivery_options
      self.shipments = ::Mkp::PurchaseProcessor.create_shipments!(order, current_address, choosen_delivery_options)
    end
  end
end

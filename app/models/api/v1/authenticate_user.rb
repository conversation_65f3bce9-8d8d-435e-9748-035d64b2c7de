class Api::V1::AuthenticateUser
  attr_accessor :email, :password, :errors, :result

  def initialize(email, password)
    @email = email
    @password = password
    @errors = {}
  end

  def call
    if user
      @result = JsonWebToken.encode(user_id: user.id)
      true
    else
      @errors[:user_authentication] = 'invalid credentials'
      false
    end
  end

  private

  def user
    user = User.find_by_email(email)
    return user if user && user.valid_password?(password)
    nil
  end
end
module Api
  module V1
    class AttributesBuilder
      attr_reader :error, :result

      def initialize(user, product_data)
        @user = user
        @images = product_data['images']
        @variants_data = product_data['variants'].try(:uniq)
        @package_data = product_data['packages']
        @product_data = product_data.except('images', 'variants', 'packages')

        @shop = @user.shops.where(id: product_data[:shop_id]).first
      end

      def perform
        if @shop
          build_product_attributes
        else
          @error = "The shop doesn't belong to the user"
        end
      end

      def relate_images(product_id)
        return if @images.blank?

        styles = {
          st: '100x100#', # small-thumb (cropped)
          t: '130x130>', # thumbnail (cropped if bigger than specified)
          m: '300x300#', # medium (cropped)
          ml: '450x450>', # medium-large (cropped if bigger than specified)
          l: '720x720>' # large (cropped if bigger than specified)
        }

        product_picture_class.destroy_all(product_id: product_id)
        view_order = 1
        @images.each do |image_data|
          picture = product_picture_class.new(product_id: product_id,
                                              photo: open(image_data['url']),
                                              view_order: view_order)
          picture.styles = styles
          picture.processing = false
          picture.save
          view_order += 1
        end
      end

      private

      def build_product_attributes
        @result = {
          available_on: parse_date(@product_data['available_on']), # available_on format 'dd/mm/yyyy'
          currency_id: @shop.currency.id,
          handle_stock: true,
          shop_id: @shop.id,
          sale_on: parse_date(@product_data['sale_on']), # available_on format 'dd/mm/yyyy
          sale_until: parse_date(@product_data['sale_until']), # available_on format 'dd/mm/yyyy'
          available_properties: build_available_properties,
          variants_attributes: build_variant_attributes,
          packages_attributes: build_package_attributes
        }.compact

        @product_data.each do |key, value|
          @result[key] = value unless @result.key?(key.to_sym)
        end
      end

      def build_available_properties
        if @variants_data.present?
          properties = (@variants_data.map { |each| each['properties'] }).compact
          if properties.present?
            properties = properties.map(&:keys).compact.flatten
            return properties.flatten.uniq.map!(&:to_sym) if properties.present?
          end
        end

        [:noproperty]
      end

      def build_variant_attributes
        return nil if @variants_data.blank?

        variant_attributes = []
        @variants_data.each do |variant_data|
          hash = {
            sku: variant_data['sku'],
            quantity: variant_data['quantity'].to_i,
            ean_code: variant_data['ean_code'],
            properties: build_properties(variant_data['properties']),
            points_price: variant_data['points_price'],
            discount_top: variant_data['discount_top']
          }

          variant_attributes << hash
        end

        variant_attributes
      end

      def build_package_attributes
        return nil if @package_data.blank?

        network = Network[@user.network || Network.default]
        package_attributes = []
        package_data = @package_data.first
        package_attributes << {
          width: package_data['width'], # millimeters
          height: package_data['height'], # millimeters
          length: package_data['length'], # millimeters
          weight: package_data['weight'], # kilograms
          length_unit: network.length_unit,
          mass_unit: network.mass_unit
        }
        package_attributes
      end

      def build_properties(properties_data)
        result = {}
        result[:noproperty] = true if properties_data.blank?
        properties_data.each do |key, value|
          result[key] = property_value_of(key, value, @shop.network)
        end

        result
      end

      def property_value_of(property_name, property_value, network)
        if property_name == 'color'
          parse_color(property_value.capitalize, network)
        else
          property_value
        end
      end

      def parse_date(date_or_nil)
        return Time.zone.parse(date_or_nil) if date_or_nil

        nil
      end

      def parse_color(color_name, network)
        available_colors ||= I18n.t('color', locale: Network[network].locale)

        available_colors.each do |hex, name|
          return { name: name, hex: hex.to_s } if color_name == name
        end

        nil
      end

      def product_picture_class
        ::Mkp::Attachment::ProductPicture
      end
    end
  end
end

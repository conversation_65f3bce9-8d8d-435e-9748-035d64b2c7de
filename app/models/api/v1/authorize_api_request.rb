class Api::V1::AuthorizeApiRequest

  def initialize(headers = {})
    @headers = headers
    @errors = {}
  end

  def call
    user
  end

  private

  attr_reader :headers

  def user
    begin
      @user = nil
      decoded_token = decoded_auth_token
      @user = User.find(decoded_token.first["user_id"]) unless decoded_token.nil?
    rescue Exception => e
      @errors[:token] = e.message unless @errors[:token].present?
    ensure
      return @user
    end
  end

  def decoded_auth_token
    begin
      @decoded_auth_token = nil
      auth_header = http_auth_header
      @decoded_auth_token = JsonWebToken.decode(http_auth_header) unless auth_header.nil?
    rescue Exception => e
      @errors[:token] = e.message unless @errors[:token].present?
    ensure
      return @decoded_auth_token
    end
  end

  def http_auth_header
    if headers['Authorization'].present?
      return headers['Authorization'].split(' ').last
    else
      @errors[:token] = 'Missing token'
    end
    nil
  end

end

# Represents the relation of a user that favorites a social or marketplace item.
class Favorite < ActiveRecord::Base
  include ::Concerns::GeneratesNotifications

  belongs_to :user
  belongs_to :favoritable, polymorphic: true

  validates_presence_of :user_id
  validates_presence_of :favoritable_id

  validates :user_id, uniqueness: { scope: [:favoritable_id, :favoritable_type] }

  generates_notification_on_create :favorited
end

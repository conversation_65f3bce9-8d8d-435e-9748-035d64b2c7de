class SettingCoupon < ActiveRecord::Base
  belongs_to :voucher

  serialize :restrictions, Hash
  COUPON_POLICIES = Mkp::Coupon::COUPON_POLICIES

  validates_presence_of :minimum_value
  validates_presence_of :starts_at
  validates_presence_of :expires_at
  validates_presence_of :amount, if: :value_policy?
  validates_presence_of :percent, if: :percent_policy?
  validates :amount,  numericality: { greater_than: 0 }, if: :value_policy?
  validates :percent, numericality: { greater_than: 0, less_than: 101 }, if: :percent_policy?

  def build_coupon!(item)
    integrate_supplier.build_coupon(item)
  end

  private
  def value_policy?
    policy == 'value'
  end

  def percent_policy?
    policy == 'percent'
  end

  def integrate_supplier
    @integrate_supplier ||= "Voucher::Supplier::#{voucher.supplier.capitalize}".constantize.new(voucher)
  end
end

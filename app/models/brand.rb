class Brand < User
  delegate :name, :website_url, to: :profile, prefix: false, allow_nil: true

  has_and_belongs_to_many :pro_athletes,
                          join_table: 'brands_users',
                          association_foreign_key: 'user_id'

  has_many :admins, through: :brand_admins
  has_many :brand_admins, dependent: :destroy
  has_many :brand_profiles, foreign_key: :user_id, class_name: "Social::Profile::Brand"
  has_one :profile, foreign_key: :user_id, class_name: "Social::Profile::Brand"
  has_one :manufacturer, through: :profile

  accepts_nested_attributes_for :profile

  scope :with_visible_shop, ->{ joins(:shops).where('mkp_shops.visible' =>  true) }

  searchable do
    text :name do
      profile.name
    end

    boolean :has_enough_products do |b|
      if b.manufacturer.present?
        b.manufacturer.products.count >= 3
      end
    end

    boolean :active_brand do |brand|
      if brand.manufacturer.present?
        variants = brand.manufacturer.products.flat_map(&:variants)
        variants.any? { |v| v.updated_at >= Time.now - 90.days }
      end
    end

    boolean :brands_profile_with_cover_and_avatar do |brand|
      brand.profile.avatar.present? && brand.profile.cover.present?
    end

    integer :id
    string :login
    string :network
  end

  def self.managed_by(user)
    BrandAdmin.where(admin_id: user.id).map(&:brand_id)
  end

  def shop
    shops.where('mkp_shop_admins.owner' => true).first
  end

  def managers
    admins.where('brand_admins.admin_id <> brand_admins.brand_id')
  end

  def can_be_managed_by?(user)
    admins.include?(user)
  end

  def related_manufacturer
    profile.manufacturer || Mkp::Manufacturer.where('name = ?', name).first
  end
end

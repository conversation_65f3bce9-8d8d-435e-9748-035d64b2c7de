class ImportRecurrentPayment < ActiveRecord::Base
  has_and_belongs_to_many :payments,
                          class_name: 'Payment',
                          join_table: :imports_payments,
                          foreign_key: :import_id,
                          association_foreign_key: :payment_id

  scope :created_at_gte, ->(reference_time) { where('import_recurrent_payments.created_at >= ?', reference_time) }
  scope :created_at_lt, ->(reference_time) { where('import_recurrent_payments.created_at < ?', reference_time) }

  filterrific available_filters: [
                :created_at_gte,
                :created_at_lt,
               ]
end

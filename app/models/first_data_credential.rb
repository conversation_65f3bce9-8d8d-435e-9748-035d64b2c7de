class FirstDataCredential < ActiveRecord::Base
  belongs_to :store, class_name: "Mkp::Store"
  belongs_to :shop, class_name: "Mkp::Store", required: false

  has_attached_file :ssl_cert
  has_attached_file :ssl_cert_key
  do_not_validate_attachment_file_type :ssl_cert
  do_not_validate_attachment_file_type :ssl_cert_key

  def token
    store.token
  end

  def categories_ids
    category ? category.split(',').map(&:to_i) : []
  end
end

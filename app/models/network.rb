require 'ostruct'
# Network Configuration Object
module Network
  module_function

  def default
    config.each { |network, config| return network if config['default?'] }
    nil
  end

  def all
    config.map { |network, _| network }
  end

  def all_full
    config.reject { |_, config| config['thin?'] }
          .map { |network, _| network }
  end

  def all_thin
    config.select { |_, config| config['thin?'] }
          .map { |network, _| network }
  end

  def all_active
    config.select { |_, config| config['active?'] }
          .map { |network, _| network }
  end

  def all_visible
    config.select { |_, config| config['active?'] && config['visible'] }
          .map { |network, _| network }
  end

  def all_visible_countries
    config.select { |_, config| config['active?'] && config['visible'] }
          .map { |_, config| config['title'] }
  end

  def all_active_param_regex
    if @_param_regex.blank?
      @_param_regex = Regexp.new(Network.all_active.map(&:downcase).join('|'))
    end
    @_param_regex
  end

  def [](network)
    OpenStruct.new(config[network.try(:upcase)])
  end

  def for(network)
    if network.present? && config[network.upcase].present?
      self[network.upcase]
    else
      self[default]
    end
  end

  def config
    NETWORKS_CONFIG
  end

  def all_social_profiles_for(network)
    network = full_network_for(network)
    self[network].social_profiles
  end

  def footer_config(network, identifier)
    network = full_network_for(network)
    self[network].footer[identifier]
  end

  def full_network_for(network)
    if self[network].thin?
      default
    else
      network
    end
  end

  def recommendations_types_for(network)
    network = full_network_for(network)
    self[network].recommendations_types
  end

  def social_profiles_for(network, social_identifier)
    network = full_network_for(network)
    self[network].social_profiles[social_identifier]
  end
end

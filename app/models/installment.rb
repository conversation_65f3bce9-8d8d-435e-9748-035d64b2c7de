class Installment < ActiveRecord::Base
  belongs_to :bin
  belongs_to :payment_program
  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :category, class_name: Mkp::Category
  belongs_to :product, class_name: Mkp::Product

  validates :number, presence: true, numericality: { greater_than_or_equal_to: 1 }

  scope :actived, -> { where(active: true).order(number: :asc) }

  def search=(data)
    # DO SOMETHING WITH data
  end

  def search
    # DO SOMETHING WITH data
  end

  def full_description
    if cft == 0 && tea == 0 && coef == 0
      "#{number}"
    else
      "(#{number} - cft: #{cft}, tea: #{tea}, coef: #{coef})"
    end
  end

  def total_by_installment(purchase_total)
    (total_amount(purchase_total) / number).round(2)
  end

  def total_amount(purchase_total)
    purchase_total * (coef == 0 ? 1 : coef)

  end

  def additional_total_information
    payment_program ? "#{payment_program.display_name(self)}" : ""
  end
end

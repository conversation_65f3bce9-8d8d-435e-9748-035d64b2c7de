class Payment < ActiveRecord::Base
  enum status: [:pending, :collected, :cancelled, :refunded]
  serialize :gateway_data, Hash
  belongs_to :subscription
  has_one :invoice
  has_one :customer, through: :subscription
  has_and_belongs_to_many :exports,
                          class_name: 'ExportRecurrentPayment',
                          join_table: :exports_payments,
                          foreign_key: :payment_id,
                          association_foreign_key: :export_id

    has_and_belongs_to_many :imports,
                            class_name: 'ImportRecurrentPayment',
                            join_table: :imports_payments,
                            foreign_key: :payment_id,
                            association_foreign_key: :export_id

  before_create :add_period

  scope :current_period, -> { where(period: Date.today.strftime('%m/%y')) }

  validates_presence_of :amount

  #Filterrific search
  scope :created_at_gte, ->(reference_time) { where('payments.created_at >= ?', reference_time) }
  scope :created_at_lt, ->(reference_time) { where('payments.created_at <= ?', reference_time) }
  scope :with_billable, ->(billable) { where(billable: billable ) }
  scope :with_recurrent, ->(recurrent) { where(recurrent: recurrent) }
  scope :with_status, ->(status) { where(status: status) }
  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    terms = query.downcase.split(/\s+/)
    terms = terms.map{ |e| ('%' + e.gsub('*', '%') + '%').gsub(/%+/, '%')}
    Payment.where('mkp_customers.doc_number LIKE ? OR mkp_customers.email LIKE ? OR payments.period LIKE ?', terms[0], terms[0], terms[0]).includes(:invoice, subscription: {customer: {member: :cards}}).references(:suscription).order(id: :desc)
  end
  filterrific available_filters: [
                :created_at_gte,
                :created_at_lt,
                :with_billable,
                :with_recurrent,
                :with_status,
                :search_query
               ]

  def can_invoice?
    billable? && invoice.blank? && collected?
  end

  def card
    self.subscription&.customer&.member&.cards&.last
  end

  private
  def add_period
    self.period = Date.today.strftime('%m/%y')
  end
end

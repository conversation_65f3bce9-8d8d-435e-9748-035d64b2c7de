class SupplierStock < ActiveRecord::Base
  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :supplier

  def self.create_from(order)
    basic_create(order)
  end

  def self.create_return_from(order)
    #doNothing
  end

  def self.basic_create(order)
    suborders = []
    if order.class.name == 'Mkp::Order'
      suborders << order.suborders
    else
      suborders << order
    end

    suborders.flatten.each do |suborder|
      supplier_slug =
          if suborder.class.name == 'Mkp::Suborder'
            suborder.shop.slug
          else
            suborder.gateway
          end

      supplier = Supplier.find_by_slug supplier_slug.downcase
      if supplier && supplier.manage_stock?
        current_stock = supplier.current_supplier_stock
        SupplierStock.create(supplier: supplier, store_id: supplier.store.id, stock: current_stock.stock, consumed_stock: suborder.total)
      end
    end
  end

  def supplier_name
    supplier.name
  end

  def current_stock
    stock - consumed_stock
  end

  def unit
    supplier.unit
  end
end

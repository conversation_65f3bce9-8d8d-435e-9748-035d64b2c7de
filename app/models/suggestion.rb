class Suggestion < ActiveRecord::Base
  serialize :data, Hash

  after_create :save_on_redis, if: -> { persisted? }
  before_update :save_on_redis, if: -> { is_necessary_to_save? }

  before_destroy :delete_from_redis,  if: -> { active }

  validates :term, presence: true
  validates_uniqueness_of :term, scope: :network

  scope :by_network, -> (network) { where(network: network) }
  scope :active,     -> { where(active: true) }
  scope :searches,   -> { where(active: false) }

  SUGGESTIONS_TO_SHOW = 5

  def slug
    term.gsub(' ','-')
  end

  def aliases
    data[:aliases] ||= []
  end

  def is_search?
    !active
  end

  def increase_count(hits = 1)
    update_attributes(count: count + hits)
  end

  def convert_to_alias(target_suggestion)
    unless target_suggestion.aliases.include?(term)
      target_suggestion.data[:aliases] = target_suggestion.aliases << term
      target_suggestion.save!
    end
    target_suggestion.increase_count(count)
    destroy
  end

  def self.search(term, network)
    matches = Soulmate::Matcher.new("#{network.downcase}:suggestion").matches_for_term(term, options = {limit: SUGGESTIONS_TO_SHOW})
    matches.map do |match|
      {
        id: match['id'],
        label: match['term'],
        value: match['term'],
        url: match['data']['url']
      }
    end
  end

  def save_on_redis
    if active
      loader = Soulmate::Loader.new("#{network.downcase}:suggestion")
      loader.add(
        'term'    => term,
        'id'      => id,
        'score'   => score,
        'data'    => { 'url' => data[:url] },
        'aliases' => aliases
      )
      UrlMapper.map_out(self , network.downcase)
    else
      delete_from_redis
    end
  end

  def delete_from_redis
    Soulmate::Loader.new("#{network.downcase}:suggestion").remove("id" => id)
    UrlMapper.purge_map(self, network.downcase)
  end

  def is_necessary_to_save?
    return true if (data != Suggestion.find(id).data)
    false
  end

end

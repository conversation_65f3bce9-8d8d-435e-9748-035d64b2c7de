class View::OrderReport < ActiveRecord::Base
  self.primary_key = :order_id

  serialize :destination_address, OpenStruct
  serialize :properties, Hash
  serialize :gateway_data

  include View::Base
  def self.refresh
    Scenic.database.refresh_materialized_view('order_reports', concurrently: false, cascade: false)
  end

  ADDRESS_METHOD = %i[ full_name state telephone zip city ]

  ADDRESS_METHOD.each do |address_method|
    define_method(address_method) do
      destination_address&.send(address_method)
    end
  end

  def customer_address
    destination_address&.to_h&.slice(:address, :address_2, :street_number).values&.compact&.join(" ")
  rescue
    '-'
  end

  def property
    properties[:name] || properties[:size] || ''
  end

  def label_amount
    gateway_data&.dig(:rate, :precio) || '-'
  end

  def label_paid_by
    return '--' if shipment_status == 'unfulfilled'
    (courier.present? && 'Avenida Store') || 'Shop'
  end

  def installments
    gateway_data&.dig("installments") || '-'
  end

  def bin
    gateway_data&.dig("bin") || '-'
  end
end

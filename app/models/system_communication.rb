# Public: STI Communication system
# This class is abstract and contains shared logic between all communications
# only
class SystemCommunication < ActiveRecord::Base
  belongs_to :customer, polymorphic: true

  validates :data,
            :customer_id,
            :customer_type,
            :type, presence: true

  scope :pending, -> { where('notified_at IS NULL') }

  # Public: Communication created after a customer made a review.
  class ReviewThanks < SystemCommunication
    store :data, accessors: [:review_communication_id, :coupon_id, :notified_at]

    validates :network, presence: true

    def self.create(review_communication, coupon_id)
      communication                         = new
      communication.customer                = review_communication.customer
      communication.network                 = review_communication.network
      communication.review_communication_id = review_communication.id
      communication.coupon_id               = coupon_id
      communication.save

      communication
    end
  end

  # Public: Communication created after a customer purchase a product
  class Review < SystemCommunication
    store :data, accessors: [
      :suborder_id,
      :shipment_id,
      :variants_ids,
      :token,
      :thanks_communication_id
    ]

    validates :network, presence: true

    # Public: it creates a new communication based on a shipment
    #
    # shipment - instance of Mkp::Shipment
    #
    # Examples
    #
    #   create(shipment.last)
    #   # => SystemCommunication::Review
    #
    # Returns the duplicated String.
    def self.create(shipment)
      items = shipment.items
      order = items.first.order
      variants_ids = items.map(&:variant_id)

      communication              = new
      communication.customer     = order.customer
      communication.network      = order.network
      communication.token        = SecureRandom.uuid
      communication.shipment_id  = shipment.id
      communication.variants_ids = variants_ids
      communication.save

      communication
    end

    def assign_thanks_communication(communication_id)
      self.thanks_communication_id = communication_id
      save
    end
  end

  # Public: Communication created from Pioneer with a message to merchants
  class Merchant < SystemCommunication
    store :data, accessors: [:subject, :body, :recipient, :cc_recipients,
                             :bcc_recipients, :admin_id, :sender_id]

    validates :network, presence: true

    def self.create(shop, subject, body, sender_id, admin_id)
      communication               = new
      communication.customer      = shop
      communication.subject       = subject
      communication.body          = body
      communication.network       = shop.network
      communication.recipient     = get_shop_owner(shop)
      communication.cc_recipients = get_shop_managers(shop)
      communication.sender_id     = sender_id.to_i
      communication.admin_id      = admin_id
      communication.save

      communication
    end

    class << self
      private

      def get_shop_owner(shop)
        shop.owner.try(:email)
      end

      def get_shop_managers(shop)
        shop.managers.map(&:email).join(',')
      end
    end
  end

  # Public: Communication created for users who left carts
  class LeftCart < SystemCommunication
    store :data, accessors: [:cart_id, :cart_status,
                             :coupon_id, :items, :notify_at]

    scope :communications_within_limit, ->(customer)  do
      if customer.is_a?(Mkp::Guest)
        where('customer_email = ? &&
               customer_type = "Mkp::Guest" &&
               created_at > ?', customer.email, 4.days.ago)
      else
        where('customer_id = ? && customer_type = ? && created_at > ?',
              customer.id,
              customer.class.name,
              4.days.ago)
      end
    end

    def self.exceded_limit_for?(customer)
      communications_within_limit(customer).any?
    end

    def self.customer_bought_recently?(customer)
      customer.orders.where(created_at: 48.hours.ago..Time.now).any?
    end

    def self.valid?(customer, cart)
      return false unless cart.entered_checkout?
      return false if exceded_limit_for?(customer)
      return false if customer_bought_recently?(customer)
      true
    end

    def self.create(cart)
      communication                = new
      communication.cart_id        = cart.id
      communication.cart_status    = cart.status
      communication.customer_id    = cart.customer_id
      communication.customer_type  = cart.customer.class.name
      communication.customer_email = cart.customer.email
      communication.items          = build_items(cart)
      communication.network        = cart.network
      communication.notify_at      = cart.updated_at + 24.hours
      communication.save

      communication
    end

    def valid_customer?
      return if (self.class.communications_within_limit(customer) - [self]).any?
      return if self.class.customer_bought_recently?(customer)
      true
    end

    def valid_cart?
      cart = Mkp::Cart.find(cart_id)
      cart.entered_checkout?
    end

    class << self
      private

      def build_items(cart)
        cart.items.each_with_object({}) do |item, items|
          items[item.variant_id.to_s] = item.quantity
        end
      end
    end
  end
end

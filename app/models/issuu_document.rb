class IssuuDocument
  extend ActiveModel::Naming

  attr_reader :title,
              :description

  def self.all(options = {})
    docs = Issuu::Document.list(options)
    docs.present? ? docs.map { |d| self.new d } : []
  end

  def initialize(issuu_document)
    @document = issuu_document
    @document.attributes.each do |name, value|
      instance_variable_set("@#{name}",value)
    end
  end

  def id
    @id ||= @document.documentId
  end

  def created_at
    @created_at ||= Date.parse(@document.publishDate)
  end

  def thumb
    @thumb ||= "//image.issuu.com/#{id}/jpg/page_1_thumb_large.jpg"
  end

  def link
    @link ||= "http://issuu.com/#{@username}/docs/#{@name}"
  end

  # Marshal serialization methods (to be used on cached)
  # http://www.ruby-doc.org/core-2.1.2/Marshal.html
  def marshal_dump
    [title, description, id, created_at, thumb, link]
  end

  def marshal_load array
    @title, @description, @id, @created_at, @thumb, @link = array
  end
end

class SupplierInvoice < ActiveRecord::Base
  has_many :invoice_items
  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :shop, class_name: 'Mkp::Shop'

  scope :with_store_id, ->(store_id) { where("supplier_invoices.store_id in (?)", store_id) }
  scope :with_created_from, ->(created_from) { where('supplier_invoices.created_at >= ?', "#{created_from} 00:00:00") }
  scope :with_created_to, ->(created_to) { where('supplier_invoices.created_at <= ?', "#{created_to} 23:59:59") }
  scope :with_invoice_status, ->(invoice_status) {where("supplier_invoices.invoice_status = ?", invoice_status)}
  scope :with_shop_id, ->(shop_ids) do
    ids = shop_ids.split(',').flatten
    if ids.size > 1
      where((ids.map {|id| "supplier_invoices.shop_ids like '%#{id}%'"}).join(' and '))
    else
      where("supplier_invoices.shop_ids like '%#{ids.first}%'")
    end
  end
  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    terms = query.downcase.split(/\W+/)
    terms = terms.map{ |e| (e.gsub('*', '%') + '%').gsub(/%+/, '%')}
    if terms.size > 1
      where("supplier_invoices.external_id in (?)", terms).or(where("supplier_invoices.id in (?)", terms)).or(where("supplier_invoices.order_number in (?)", terms)).or(where("supplier_invoices.invoice_number in (?)", terms))
    else
      where("supplier_invoices.external_id like ?", terms).or(where("supplier_invoices.id like ?", terms)).or(where("supplier_invoices.order_number like ?", terms)).or(where("supplier_invoices.invoice_number like ?", terms))
    end
  end
  accepts_nested_attributes_for :invoice_items, allow_destroy: true

  def supplier_names
    (shop_ids.split(',').map {|each| (Mkp::Shop.find each).name}).join(' - ') if shop_ids
  end

  def store_name
    store.name
  end

  def total
    invoice_items.to_a.sum(&:total)
  end

  def points
    invoice_items.to_a.sum(&:points)
  end

  def self.available_invoice_status
    %W[pending invoiced]
  end

  def from_to
    sorted_invoice_items = invoice_items.sort_by { |each | each.created_at }
    "#{sorted_invoice_items.first.created_at.strftime("%d-%m-%Y")} al #{sorted_invoice_items.last.created_at.strftime("%d-%m-%Y")}"
  end

end

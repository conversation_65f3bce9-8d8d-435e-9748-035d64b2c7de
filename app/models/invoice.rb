class Invoice < ActiveRecord::Base
  serialize :gateway_data, Hash
  belongs_to :payment
  validates :amount, presence: true, numericality: true, on: :update
  delegate :customer, to: :payment

  def generate!
    invoice = gateway.build_invoice(customer, payment)
    return unless invoice.present?
    update_attributes!(params_by(invoice))
    Accounting::InvoiceMailer.notify_customer(self).deliver
  end

  def url
    gateway_object_id && customer.present? && customer.email.present? && gateway.get_invoice_url(customer, gateway_object_id)
  end

  private
  def params_by(invoice)
    {
      amount: invoice.total_charged,
      gateway_object_id: invoice.id,
      number: invoice.number,
      gateway_data: invoice.data
    }
  end

  def gateway
    @gateway ||= Tdd::Invoices::Colppy.new
  end
end

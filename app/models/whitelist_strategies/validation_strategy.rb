module WhitelistStrategies
  class ValidationStrategy
    def self.available_options
      [ "FirstGeneralPurchaseStrategy", "FirstReservableProductPurchaseStrategy", "MultipleWhitelistStrategy"]
    end

    def self.authorized?(number, store, cart)
      raise "Subclasses must implement this method"
    end

    def self.build_unauthorized_data(number, store, cart)
      raise "Subclasses must implement this method"
    end

    def authorized_installment?(doc_type, doc_number, store, installment)
      raise "Subclasses must implement this method"
    end
  end
end

module WhitelistStrategies
  class FirstReservableProductPurchaseStrategy < ValidationStrategy

    def self.authorized?(dni, store, products_ids)
      orders = orders_for(dni, store, products_ids)
      orders.empty?
    end

    def self.build_unauthorized_data(dni, store, products_ids)
      orders = orders_for(dni, store, products_ids)

      products = Mkp::Product.includes(:order_items => [suborder: :order]).where(id: products_ids, transaction_type: Mkp::Product.transaction_types['reservable']).where(mkp_orders: { id: orders.map(&:id) })
      message = products.count > 1 ? 'Usted ya adquirió estos productos:' : 'Usted ya adquirió este producto:'

      {:products => products, :message => message}
    end

    def self.orders_for(dni, store, products_ids)
      Mkp::Order.joins(:items => [:product])
        .joins("INNER JOIN mkp_customers ON mkp_orders.customer_id = mkp_customers.id AND mkp_orders.customer_type = 'Mkp::Customer'")
        .where(store_id: store.id)
        .where(mkp_products: { id: products_ids, transaction_type: Mkp::Product.transaction_types['reservable'] })
        .where(mkp_customers: { doc_type: 'DNI', doc_number: dni })
    end

    def self.authorized_installment?(document_type, document_number, store, installment)
      installment.valid_for == 'first_purchase' && authorized?(document_type, document_number, store)
    end
  end
end
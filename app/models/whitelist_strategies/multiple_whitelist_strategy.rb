module WhitelistStrategies
  class MultipleWhitelistStrategy < ValidationStrategy

    def self.first_purchase?(document_type, document_number, store)
      orders = Mkp::Order.joins(:payments).where(store: store, mkp_payments: {document_type: document_type, document_number: document_number, status: 'collected'})
      orders.empty?
    end

    def self.whitelist_user?(document_number, store, installments)
      Dni.exists?(number: document_number, store: store, installments: installments)
    end

    def self.build_unauthorized_data(number, store, cart)
      message = 'Usted ya realizó una compra en la tienda.'
      products = []

      {:products => products, :message => message}
    end

    def self.authorized_installment?(document_type, document_number, store, installment)
      (installment.valid_for == 'first_purchase' && first_purchase?(document_type, document_number, store)) ||
          (installment.valid_for == 'whitelist_user' && whitelist_user?(document_number, store, installment.number))
    end
  end
end

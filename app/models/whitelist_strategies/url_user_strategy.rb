module WhitelistStrategies
  class UrlUserStrategy < UserStrategy
    def self.exists?(number, store)
      self.send(store.name, number)
    end

    def self.sportclub(number)
      # code for dni validation, waiting for sportclub api to be ready
       begin
         uri = URI.parse("#{SPORTCLUB_ENDPOINT}/member/#{number}")
         http = Net::HTTP.new(uri.host, uri.port)

         request = Net::HTTP::Get.new(uri.request_uri)
         request["Auth"] = "Bearer #{SPORTCLUB_TOKEN}"

         response = http.request(request)

         return false unless response.any?
         return false unless response[0].key?(:Estado)
         return response[0][:Estado] == 1 ? true : false
       rescue StandardError => e
         return false
       end
    end
  end
end

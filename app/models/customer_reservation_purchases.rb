class CustomerReservationPurchases < ActiveRecord::Base
  validates :customer_dni, presence: true
  validates :product_id, presence: true
  validates :store_id, presence: true

  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :product, class_name: 'Mkp::Product'

  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    where("customer_reservation_purchases.customer_dni like ?", "%#{query}%")
  end

  def force_load_product
    # TODO cargar la asociacion unscoped
     Mkp::Product.unscoped.find(product_id)
  end
end

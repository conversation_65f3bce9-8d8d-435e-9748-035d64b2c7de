module Pages
  class Landing < ActiveRecord::Base
    include ::Concerns::Avenidable
    has_many :components, class_name: 'Pages::Components::Base', dependent: :destroy

    validates_presence_of :network
    validates_uniqueness_of :home, scope: :network, if: ->(landing) { landing.validate_one_store }
    validates :slug, uniqueness: {scope: :store_id, allow_blank: true}

    after_save  :map_path

    scope :with_components, ->{ joins(:components).order('components.position ASC') }
    scope :for_network, ->(network) { where(network: network) }
    scope :home_for_network, ->(network) { with_components.for_network(network).where(home: true, active: true) }
    scope :active_for_network, ->(network) { with_components.for_network(network).where(active: true) }

    HOSTNAME = 'https://www.avenida.com.ar'
    def is_home?
      home
    end

    def path
      return HOSTNAME if store.nil?
      ( store.hostname.present? ? store.hostname : HOSTNAME ) + '/' + slug.to_s
    end

    protected

    def map_path
      # We don't want to map other stores
      return unless store.try(:name) == "avenida"

      unless is_home?
        if slug_changed?
          UrlMapper.purge_path(slug_was, network)
        end

        unless UrlMapper.match({path: slug, network: network}).present?
          UrlMapper.map_out(self, network)
        end
      end
    end

    def validate_one_store
      home && active && store.landings.where(home: true).reject{|l| l.id == id}.map(&:home).count >= 1
    end

    private

    def find_component(klass_name)
      components.find do |component|
        component.type =~ /#{Regexp.quote(klass_name)}/
      end
    end

  end
end

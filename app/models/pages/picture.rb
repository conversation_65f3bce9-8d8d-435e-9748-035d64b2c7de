module Pages
  module GenericPictureStyles
    def styles
      { thumb: '200x200>' }
    end
  end

  class Picture < ActiveRecord::Base
    extend GenericPictureStyles
    include GenericPictureStyles

    self.table_name = :components_pictures

    has_attached_file :image,
      styles: lambda {|a| a.instance.class.styles },
      path: 'avenida/components/pictures/:attachment/:public_folder/:token_:style.:extension',
      processors: [:thumbnail, :paperclip_optimizer],
      default_style: :thumb,
      convert_options: {all: '-sampling-factor 4:2:0 -strip -quality 85 -colorspace sRGB'}

    serialize :available_styles, Hash

    validates_attachment_content_type :image, content_type: /\Aimage\/.*\Z/

    after_initialize :generate_token

    private

    def generate_token
      unless attribute_present?('token')
        self.token = Digest::MD5.hexdigest("#{rand}#{Time.now.to_i}")
      end
    end

  end
end

module Pages
  module Components
    class MiniLogo < Base
      ALLOWED_KEYS = [
        items: [:title, :link, :desktop_picture_id, :mobile_picture_id]
      ].freeze

      PICTURE_KEYS = [ :desktop_picture_id, :mobile_picture ].freeze
      QUANTITY_AMOUNT = 12
      validates_numericality_of :columns, equal_to: 1, on: :update

      def self.styles
        { desktop: '140x85#', mobile: '140x85#', thumb: '140x85#' }
      end

      def init
        return unless super
        item = { title: nil, description: nil, link: nil, desktop_picture_id: nil, mobile_picture_id: nil }
        self.columns = 1
        self.items = [item] * QUANTITY_AMOUNT
        self.setup[:items] = items
      end

      def display_in_main_container?
        true
      end

      def items
        @items ||= self.setup[:items].map { |hash| OpenStruct.new(hash) }
      end
    end
  end
end

module Pages
  module Components
    class Cols5GenericV1 < Cols5V1
      ALLOWED_KEYS = [
        items: [:title,
                :link,
                :picture_id
              ]
      ].freeze

      PICTURE_KEYS = [
        :picture_id
      ].freeze

      N_OF_ITEMS = 5

      def self.styles
        { desktop: '236x289', thumb: '236x289#' }
      end

      def init
        if super
          self.columns = 5
          N_OF_ITEMS.times do
            self.items << {
              title: nil,
              link: nil,
              picture_id: nil
            }
          end
        self.setup[:items] = items
        end
      end

    end
  end
end

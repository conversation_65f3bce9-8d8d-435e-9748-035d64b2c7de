module Pages
  module Components
    class Cols2GenericV1 < Cols2V1
      ALLOWED_KEYS = [
        items: [:title,
                :link,
                :picture_id
              ]
      ].freeze

      PICTURE_KEYS = [
        :picture_id
      ].freeze

      N_OF_ITEMS = 4

      def self.styles
        { desktop: '620x270>', thumb: '470x200>' }
      end

      def init
        if super
          self.columns = 2
          N_OF_ITEMS.times do
            self.items << {
              title: nil,
              link: nil,
              picture_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

    end
  end
end

module Pages
  module Components
    class Base < ActiveRecord::Base
      attr_accessor :items

      DEFAULT_KEYS = [
        :title,
        :description,
        :link,
        :picture_id,
        :brand_id,
        :variant_id,
        :desktop_picture_id,
        :mobile_picture_id
      ].freeze

      PICTURE_KEYS = []

      AVAILABLE_COMPONENTS = [
        { name: 'Hero Cover', type: 'Pages::Components::Hero' },
        { name: 'Hero Cover & 2 Banners', type: 'Pages::Components::HeroBanner' },
        { name: '2 Banners per row (ie: Categories)', type: 'Pages::Components::Cols2GenericV1' },
        { name: '3 Pics in 2 columns', type: 'Pages::Components::Cols2Pic3V1'},
        { name: '5 Banners per row (ie: Sports)', type: 'Pages::Components::Cols5GenericV1' },
        { name: 'Featured Brands', type: 'Pages::Components::Cols4BrandsV1' },
        { name: 'Featured Brand (New)', type: 'Pages::Components::Cols2BrandV2'},
        { name: 'Featured Variants', type: 'Pages::Components::Cols4VariantsV1' },
        { name: 'Featured Variants 2', type: 'Pages::Components::Cols2VariantsV1' },
        { name: 'Featured Posts', type: 'Pages::Components::Cols4PostsV1' },
        { name: 'Links', type: 'Pages::Components::Cols4LinksV1' },
        { name: '1 Slim banner', type: 'Pages::Components::Cols1Pics1V1' },
        { name: '2 Small banners', type: 'Pages::Components::Cols1Pics2V1' },
        { name: 'Editor', type: 'Pages::Components::Editor' },
        { name: 'Slider', type: 'Pages::Components::Slider' },
        { name: 'Big Slider', type: 'Pages::Components::BigSlider' },
        { name: 'MiniLogo', type: 'Pages::Components::MiniLogo' },
        { name: 'Subscription', type: 'Pages::Components::Subscription'}
      ].freeze

      self.table_name = 'components'

      serialize :setup

      belongs_to :landing, class_name: 'Pages::Landing'

      validates_presence_of :position
      # validates_uniqueness_of :order, scope: :landing_id

      after_initialize :init
      before_save :validate_attributes
      before_destroy :delete_pictures

      #Scopes para los camponentes de las ladings
      scope :active, -> { where(active: true) }
      scope :at_date_start, -> (date) { where('starts_on < ? OR starts_on is null', date) }
      scope :at_date_end, -> (date) { where('ends_on > ? OR ends_on is null', date) }

      def display_in_main_container?
        true
      end

      protected

      def init
        return false if self.persisted?
        self.items = []
        self.setup = {}
        self.position = 1 # TODO Quitar luego de terminar la logica del order
      end

      def validate_attributes
        return if self.setup.blank?

        self.setup.each do |key, value|
          unless has_allowed_keys?(key, value)
            raise ArgumentError, "Allowed keys: #{self.class::ALLOWED_KEYS.join(', ')}"
          end
        end
      end

      def network
        @network ||= landing.network
      end

      private

      def has_allowed_keys?(key, value)
        key = key.to_sym
        if value.is_a?(Array)
          value.all? do |inner_hash|
            inner_hash.keys.all? do |inner_key|
              inner_key = inner_key.to_sym
              inner_key_group = self.class::ALLOWED_KEYS.detect do |element|
                element.is_a?(Hash) && element.key?(key)
              end
              if inner_key_group[key].presence
                if inner_key_group[key].include?(inner_key)
                  return true
                else
                  key_present = inner_key_group[key].detect do |element|
                   element.key?(key) if element.is_a?(Hash)
                  end
                  return key_present.presence
                end
              end
            end
          end
        else
          self.class::ALLOWED_KEYS.include?(key)
        end
      end

      def delete_pictures
        self.class::PICTURE_KEYS.each do |picture_key|
          setup[:items].each do |setup|
            if (picture_id = setup[picture_key]).present?
              picture = ::Pages::Picture.find(picture_id)
              picture.destroy
            end
          end
        end
      end

      def search_variants(exclude = nil, products = nil)
        store_shops = store&.active_shops.pluck(:shop_id)
        full_network = Network[network].thin? ? Network.default : network.upcase
        search = Mkp::Variant.search do
          without :id, exclude
          with :deleted, false
          with :shop_visible, true
          with :display_variant, true
          with :network, full_network

          if !products.nil?
            with :product_id, products
          end

          if store.present?
            with :store_id, store.id
            with :approved_for_store_ids, store.id if store.product_approval
          end
          
          with :shop_id, store_shops if store_shops.present?

          with(:quantity).greater_than 0
          with(:available_on).less_than Time.now

          #with(:sold_count).greater_than 0
          #with(:last_sold_at).greater_than Time.now - 4.months

          order_by :created_at, :desc
          if Network[network].thin?
            any_of do
              with :available_countries, network.upcase
              with :available_countries, 'all'
            end
          end
        end
      end
    end
  end
end
module Pages
  module Components
    class Cols4VariantsV1 < Cols4V1
      include VariantComponent

      ALLOWED_KEYS = [
        :view_more_url,
        items: [:variant_id, :variant_product_sku]
      ].freeze

      N_OF_ITEMS = 8

      attr_accessor :store

      def featured_variants(store)
        @store = store
        @featured_variants ||= Mkp::Variant.includes(variant_includes).where('mkp_variants.id IN (?)', featured_variants_ids)
        while (@featured_variants.size < N_OF_ITEMS && @featured_variants.size > 0)
          @featured_variants.each do |var|
            @featured_variants.push(var)
            if @featured_variants.size == N_OF_ITEMS
              return @featured_variants
            end
          end
        end
        return @featured_variants
      end

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do |index|
            self.items << {
              variant_id: nil,
              variant_product_sku: nil
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def variant_includes
        [:picture, product: [:shop, :manufacturer, :category]]
      end
    end
  end
end

module Pages
  module Components
    class HeroBanner < Base
      ALLOWED_KEYS = [
          items: [:title,
                  :description,
                  :link,
                  :desktop_picture_id,
                  :mobile_picture_id
          ]
      ].freeze

      PICTURE_KEYS = [
          :desktop_picture_id,
          :mobile_picture_id
      ].freeze

      N_OF_ITEMS = 5

      validates_numericality_of :columns, equal_to: 1, on: :update

      def self.styles
        { desktop: '620x556#', mobile: '620x266#', thumb: '300x156#' }
      end

      def init
        if super
          self.columns = 1
          self.items = []
          N_OF_ITEMS.times do
            self.items << {
                title: nil,
                description: nil,
                link: nil,
                desktop_picture_id: nil,
                mobile_picture_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

      def display_in_main_container?
        true
      end

      def items
        @items ||= setup[:items].map { |hash| OpenStruct.new(hash) }
      end

      def slides
        items.first(3)
      end

      def banners
        items.last(2)
      end
    end
  end
end
module Pages
  module Components
    class Cols2VariantsV1 < Cols2V1
      include VariantComponent

      ALLOWED_KEYS = [ :view_more_url, items: [:variant_id, :variant_product_sku]].freeze

      N_OF_ITEMS = AMOUNT_ITEMS = 50

      attr_accessor :store

      def featured_variants(store)
        @store = store
        @featured_variants ||= Mkp::Variant.includes(variant_includes).where(id: featured_variants_ids)
      end

      def init
        return unless super
        self.columns = 2
        self.setup[:items] = [{ variant_id: nil, variant_product_sku: nil }] * AMOUNT_ITEMS
      end

      private

      def variant_includes
        [:picture, product: [:shop, :manufacturer, :category]]
      end
    end
  end
end

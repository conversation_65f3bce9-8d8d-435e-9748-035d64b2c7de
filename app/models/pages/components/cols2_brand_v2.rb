module Pages
  module Components
    class Cols2BrandV2 < Base
      self.abstract_class = true

      VARIANTS_TO_DISPLAY = 3

      ALLOWED_KEYS = [
          items: [:title,
                  items: [:brand_id, :view_more_url]
          ]
      ].freeze

      N_OF_ITEMS = 1
      N_OF_LINKS = 1

      def brand
        @brand ||= Brand.includes(profile: [:cover, :avatar]).find(brand_id)
      end

      def brand_id
        setap = self.setup[:items].map { |hash| OpenStruct.new(hash) }
        setap.first.items.first[:brand_id]
      end

      def variants
        brandy = User.find_by_login!(self.brand.login)
        @variants ||= []

        case brandy.type
          when 'Brand'
            load_variants_from_brand(brandy)
        end

        @variants
      end

      def load_variants_from_brand(user)
        @variants = []
        @variants += load_variants_from_manufacturer(user.related_manufacturer)
        @variants += load_variants_from_shop(user.shop)
      end

      def load_variants_from_manufacturer(manufacturer)
        return [] if @variants.length >= VARIANTS_TO_DISPLAY
        return [] if !@manufacturer = manufacturer.presence

        manufacturer_id = @manufacturer.id

        Mkp::Variant.search(include: [:picture, product: [:shop, :currency, :pictures]]) do
          with :deleted, false
          with :shop_visible, true
          with :display_variant, true
          with(:quantity).greater_than 0
          with(:available_on).less_than Time.now
          with :manufacturer_id, manufacturer_id
          paginate(page: 1, per_page: VARIANTS_TO_DISPLAY)
          order_by :random
        end.results
      end

      def load_variants_from_shop(shop)
        return [] if @variants.length >= VARIANTS_TO_DISPLAY
        return [] unless @shop = shop.presence

        shop_id         = @shop.id
        variants_length = VARIANTS_TO_DISPLAY - @variants.length
        variants_ids    = @variants.map(&:id).presence
        Mkp::Variant.search(include: [:picture, product: [:shop, :currency, :pictures]]) do
          with :deleted, false
          with :shop_visible, true
          with :display_variant, true
          with(:quantity).greater_than 0
          with(:available_on).less_than Time.now
          with :shop_id, shop_id
          without :id, variants_ids
          paginate(page: 1, per_page: variants_length)
          order_by :random
        end.results
      end

      def get_featured_variants
        if @variants.size < 12
          @variants[0..3]
        else
          @variants.shift(VARIANTS_TO_DISPLAY / 4)
        end
      end

      def init
        if super
          self.columns = 1
          N_OF_ITEMS.times do
            self.items << {
                title: nil,
                items: column_items
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def column_items
        N_OF_LINKS.times.collect do
          {
              brand_id: nil,
          }
        end
      end
    end
  end
end

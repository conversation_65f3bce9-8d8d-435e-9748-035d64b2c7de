module Pages
  module Components
    class Cols2Pic3V1 < Cols2V1
      ALLOWED_KEYS = [
        items: [:title,
                :link,
                :picture_id
              ]
      ].freeze

      PICTURE_KEYS = [
        :picture_id
      ].freeze

      N_OF_ITEMS = 3

      def self.styles
        { desktop: '620x270>', thumb: '235x200>', banner: '604x544>' }
      end

      def init
        if super
          self.columns = 2
          N_OF_ITEMS.times do
            self.items << {
              title: nil,
              link: nil,
              picture_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

    end
  end
end

module Pages
  module Components
    class Cols4LinksV1 < Cols4V1
      ALLOWED_KEYS = [
        items: [:title,
                items: [:text, :url]
              ]
      ].freeze

      N_OF_ITEMS = 4
      N_OF_LINKS = 3

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do
            self.items << {
              title: nil,
              items: column_items
            }
          end
        self.setup[:items] = items
        end
      end

      private

      def column_items
        N_OF_LINKS.times.collect do
          {
            text: nil,
            url: nil
          }
        end
      end
    end
  end
end

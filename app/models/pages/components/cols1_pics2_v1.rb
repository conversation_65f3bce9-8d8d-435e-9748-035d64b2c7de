module Pages
  module Components
    class Cols1Pics2V1 < Cols2V1
      ALLOWED_KEYS = [
          :title,
          items: [:link, :picture_id]
      ].freeze

      PICTURE_KEYS = [
          :picture_id
      ].freeze

      N_OF_ITEMS = 2

      def self.styles
        { banner_large: '1260x150#', banner_small: '620x150#' }
      end

      def init
        if super
          self.columns = 2
          N_OF_ITEMS.times do
            self.items << {
                link: nil,
                picture_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

    end
  end
end

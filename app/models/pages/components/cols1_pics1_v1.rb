module Pages
  module Components
    class Cols1Pics1V1 < Base
      ALLOWED_KEYS = [
        items: [:title,
                :link,
                :desktop_picture_id,
                :mobile_picture_id
          ]
      ].freeze

      PICTURE_KEYS = [
        :desktop_picture_id,
        :mobile_picture_id
      ].freeze

      N_OF_ITEMS = 1

      validates_numericality_of :columns, equal_to: 1, on: :update

      def self.styles #Image size collection
        { desktop: '1260x150#', mobile: '620x120#'}
      end

      def init
        if super
          self.columns = 1
          N_OF_ITEMS.times do
            self.items << {
              title: nil,
              link: nil,
              desktop_picture_id: nil,
              mobile_picture_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

    end
  end
end

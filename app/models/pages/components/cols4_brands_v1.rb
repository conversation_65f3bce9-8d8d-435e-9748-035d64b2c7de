module Pages
  module Components
    class Cols4BrandsV1 < Cols4V1
      ALLOWED_KEYS = [
        :view_more_url,
        items: [:brand_id]
      ].freeze

      N_OF_ITEMS = 8

      def featured_brands
        @featured_brands ||= Brand.includes(profile: [:cover, :avatar]).where(id: featured_brands_ids)
      end

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do |index|
            self.items << {
              brand_id: nil
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def featured_brands_ids
        (fixed_brands_ids + random_brands_ids).take(N_OF_ITEMS)
      end

      def fixed_brands_ids
        setup[:items].map{ |hash| hash[:brand_id] }.select(&:present?).uniq
      end

      def random_brands_ids
        return [] if fixed_brands_ids.length >= N_OF_ITEMS
        full_network = Network[network].thin? ? Network.default : network.upcase
        Brand.where(network: full_network).with_visible_shop
             .joins(:profile).select('distinct(users.id)')
             .where('brand_profiles.id IN (?)', brands_profile_with_cover_and_avatar_ids)
             .order('RAND()')
             .limit(N_OF_ITEMS)
             .pluck(:id)
      end

      def brands_profile_with_cover_and_avatar_ids
        []
      end
    end
  end
end

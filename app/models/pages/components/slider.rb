module Pages
  module Components
    class Slider < Base
      ALLOWED_KEYS = [
        items: [:title, :description, :link, :desktop_picture_id, :mobile_picture_id]
      ].freeze

      PICTURE_KEYS = [ :desktop_picture_id, :mobile_picture ].freeze
      QUANTITY_AMOUNT = 5
      validates_numericality_of :columns, equal_to: 1, on: :update

      def self.styles
        { desktop: '435x415#', mobile: '435x415#', thumb: '300x156#' }
      end

      def init
        return unless super
        item = { title: nil, description: nil, link: nil, desktop_picture_id: nil, mobile_picture_id: nil }
        self.columns = 1
        self.items = [item] * self.class::QUANTITY_AMOUNT
        self.setup[:items] = items
      end

      def display_in_main_container?
        true
      end

      def items
        @items ||= self.setup[:items].map { |hash| OpenStruct.new(hash) }
      end

      def slides
        items.first(items.count - 1)
      end

      def banner
        items.last
      end
    end
  end
end

## +Breadcrumb+ Class, used to build breadcrumb in different situations
module Mkp
  class Bread<PERSON>rumb
    def initialize(product)
      @product = product
    end

    def to_a
      network = @product.shop.network.downcase

      params = {
        network: network
      }

      models = []
      items = []

      # items << BreadcrumbItem.new(
      #   'Home',
      #   Rails.application.routes.url_helpers.mkp_catalog_root_path(params)
      # )

      if @product.genders.size == 1
        models << (gender = @product.genders.first)
        items << BreadcrumbItem.new(
          gender.name,
          UrlMapperHelper.absolute_mapper_path(models, network)
        )
      end

      if (category = @product.category).present?
        category_path = category.path
        category_path.each do |ancestor|
          category_models = models + [ ancestor ]
          items << BreadcrumbItem.new(
            ancestor.name,
            UrlMapperHelper.absolute_mapper_path(category_models, network)
          )
        end
      end

      items << BreadcrumbItem.new(@product.title)
    end

    def adapt
      to_a.map { |x| x.to_h }
    end
  end

  class BreadcrumbItem
    def initialize(name, href = nil)
      @name = name
      @href = href
    end

    def to_h
      {
        name: @name,
        href: @href
      }
    end
  end
end

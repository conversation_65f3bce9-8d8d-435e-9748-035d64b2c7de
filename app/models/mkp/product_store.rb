module Mkp
  class ProductStore < ActiveRecord::Base
    belongs_to :product
    belongs_to :store

    #validates :points, presence: true, numericality: true
    validates :visa_puntos_equivalence, numericality: { greater_than: 0 }, allow_nil: true
    delegate :name, to: :store, allow_nil: true, prefix: true
    delegate :title, to: :product, allow_nil: true, prefix: true

    enum status: [:pending, :approved, :rejected]

    scope :not_approved, -> { where("status <> ?", Mkp::ProductStore.statuses[:approved]) }

  end
end

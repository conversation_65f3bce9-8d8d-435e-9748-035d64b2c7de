module Mkp
  class Gender < ActiveRecord::Base
    include ::Concerns::HasSlugOnRedis

    has_ancestry orphan_strategy: :rootify
    extend FriendlyId
    friendly_id :name, use: [:slugged, :history]

    has_and_belongs_to_many :products, join_table: 'genders_on_products'
    has_and_belongs_to_many :categories, join_table: 'categories_on_genders'

    scope :by_network, ->(network) { where(network: network) }

    def self.roots_in_network(network)
      roots.where('network = ?', network)
    end

    def is_kid?
      %w(chicos chicas boys girls).include?(slug)
    end
  end
end

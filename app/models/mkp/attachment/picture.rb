module Mkp
  module Attachment
    class Picture < ActiveRecord::Base
      self.table_name = :mkp_pictures

      attr_accessor :styles

      belongs_to :product, unscoped: true

      has_attached_file :photo,
                        processors: [:thumbnail, :paperclip_optimizer],
                        convert_options: {all: '-sampling-factor 4:2:0 -strip -quality 80 -colorspace sRGB'}

      validates_attachment :photo,
                           content_type: { content_type: %w(image/bmp image/gif image/jpeg image/png application/octet-stream) },
                           size: { in: 0..10.megabytes }

      after_initialize :generate_token

      after_commit :process_photo_async, on: :create

      after_create :update_status_from_change
      after_update :update_status_from_change
      before_destroy :update_status_from_change

      def update_status_from_change
        id_product = product_id.present? ? product_id : @changed_attributes["product_id"]
        return unless id_product.present?
        product = Mkp::ProductStore.find_by(product_id: id_product)
        product.update(status: 0) if product.present?
      end

      def self.unpublished_class
        UnpublishedPicture
      end

      def generate_token
        unless attribute_present?('token')
          self.token = Digest::MD5.hexdigest("#{rand}#{Time.now.to_i}")
        end
      end

      def self.styles
        {
          st: '100x100#', # small-thumb (cropped)
          t:  '130x130>', # thumbnail (cropped if bigger than specified)
          tm: '212x200>',
          m:  '300x300#', # medium (cropped)
          ml: '450x450>', # medium-large (cropped if bigger than specified)
          l:  '720x720>', # large (cropped if bigger than specified)
        }
      end

      def url(style = :l)
        photo.url(style)
      end

      def unpublished_photo(style = :l, option)
        path_definition = UnpublishedPicture.attachment_definitions[:photo][option]
        Paperclip::Interpolations.interpolate(path_definition, self.photo, style)
      rescue
        nil
      end

      protected

      def processes_photos_async?
        true
      end

      private

      def process_photo_async
        # Leave this text commented for future references
        # if processes_photos_async? && processing
        #   PictureUploadWorker.perform_in(20.seconds, self.class.to_s, id)
        # end

        if processing
          self.photo = File.open(self.unpublished_photo(:original, :path))
          self.processing = false
          self.save!
          touch_picture_owner
          refresh_feed
        end
      end

      def touch_picture_owner
        return if not self.respond_to?(:owner)
        self.owner.respond_to?(:final_touch) ? self.owner.final_touch : self.owner.touch
      end

      def refresh_feed
        return if not self.respond_to?(:owner)
        return if not self.owner.respond_to?(:author)

        Rails.logger.info("Refreshing feed of user [#{self.owner.author.id}]")
      end
    end
  end
end

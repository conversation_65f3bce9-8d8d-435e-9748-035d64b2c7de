module Mkp
  module Attachment
    class UnpublishedPicture < Picture
      has_attached_file :photo,
                        storage: :filesystem,
                        path: ':rails_root/public/system/temp_pictures/:token_:style.:extension',
                        url: '/system/temp_pictures/:token_:style.:extension',
                        styles: { m: '300x300#' },
                        convert_options: { m: '-quality 80', all: '-sampling-factor 4:2:0 -strip -quality 80 -colorspace sRGB' }

      # Publishes this picture and returns it as an instance of the specified
      # type.
      def publish(as_type)
        update_attribute(:type, as_type.to_s)
        published_picture = as_type.find(id)
        published_picture.update_attribute(:processing, true)
        published_picture
        # todo please remove this rescue
      rescue
        nil
      end

      protected

      def processes_photos_async?
        false
      end
    end
  end
end

module Mkp
  class Warehouse < BaseAddress
    has_one :zone, class_name: 'Zone', foreign_key: :warehouse_id
    attr_accessor :network

    validates :retardment, presence: true, :if => :pickup?
    validates :open_hours, presence: true, :if => :pickup?
    validates :telephone,  presence: true, :if => 'Network[network].requires_telephone?'

    belongs_to :shop, class_name: 'Shop', foreign_key: :addressable_id
    has_many :zones, class_name: 'Mkp::Zone'
    has_many :shipping_methods, through: :zone

    scope :for_pickup, -> { where('mkp_addresses.pickup' => true) }

    after_destroy :notify_shop
    after_initialize :set_addressable_type

    TELEPHONE_VALIDATOR = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/

    def pickup?
      pickup or false
    end

    def network
      shop.network
    end

    def notify_shop
      shop.shipping_method_or_address_destroyed
    end

    def set_addressable_type
      self.addressable_type = 'Mkp::Shop'
    end

    def full_name
      "#{company} (#{first_name})"
    end

    def company
      shop.title
    end

    def phone_valid?
      return true if country != 'US'
      !! telephone.match(/^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/)
    end
  end
end

class Mkp::Package < ActiveRecord::Base
  belongs_to :product

  validates :length_unit, inclusion: { in: Mkp::Unit::Length.all,
                        message: "%{value} is not a valid option." },
                        if: "length.present? || width.present? || height.present?"

  validates :mass_unit, inclusion: { in: Mkp::Unit::Mass.all,
                        message: "%{value} is not a valid option." },
                        if: "weight.present?"

  validates :length, :width, :height, :weight,
            numericality: true,
            presence: true

  after_update :update_status_from_change

  def update_status_from_change
    id_product = product_id.present? ? product_id : @changed_attributes["product_id"]
    return unless id_product.present?
    product = Mkp::ProductStore.find_by(product_id: id_product)
    product.pending! if product.present?
  end

  %i[length height width weight].each do |attr|
    define_method("with_default_#{attr}") do
      data = send(attr)
      data < 2.0 ? 2.0 : data
    end
  end

  def to_unit(convert_length_unit, convert_mass_unit)
    {
      length: with_default_length.send(length_unit).to.send(convert_length_unit).value,
      width: with_default_width.send(length_unit).to.send(convert_length_unit).value,
      height: with_default_height.send(length_unit).to.send(convert_length_unit).value,
      weight: with_default_weight.send(mass_unit).to.send(convert_mass_unit).value
    }
  end
end

module Mkp
  class BaseAddress < ActiveRecord::Base
    include Concerns::<PERSON><PERSON><PERSON>roy

    self.table_name = 'mkp_addresses'

    validates :address,    presence: true
    validates :city,       presence: true
    validates :country,    presence: true
    validates :state,      presence: true
    validates :zip,        presence: true
    validates :first_name, presence: true

    default_scope { where('mkp_addresses.deleted_at IS NULL').order('mkp_addresses.priority ASC, mkp_addresses.created_at DESC') }

    module EasypostFormat
      module_function

      def for(address)
        {
          name: address.full_name,
          street1: address.address,
          street2: address.address_2,
          city: address.city,
          state: address.state,
          zip: address.zip,
          country: address.country
        }.merge(phone_extra(address)).merge(warehouse_extras(address))
      end

      def phone_extra(address)
        address.telephone = Network[address.country].marketplace_customer_phone if address.telephone.blank?
        { phone: address.telephone.to_s.gsub(/\D/, '') }
      end

      def warehouse_extras(address)
        return {} if address.type    != 'Mkp::Warehouse' ||
                     address.country != 'US'
        {
          company: address.company
        }
      end

    end

    module PostmasterFormat
      module_function

      def for(address)
        { contact:  address.full_name.try(:first, 35), # Postmaster limit of 35 chars
          line1:    address.address,
          city:     address.city,
          state:    address.state,
          zip_code: address.zip.strip,
          phone_no: address.sanitize_telephone
        }
      end
    end
  end
end

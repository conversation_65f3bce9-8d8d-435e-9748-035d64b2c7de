module Mkp
  class VariantCost < ActiveRecord::Base
    scope :current_costs, -> { where('date_from >= :today AND date_to >= :today', today: Time.now) }

    validates :cost, :date_from, :date_to, :variant_id, presence: true
    validates_numericality_of :cost, greater_than: 0
    validate :date_from_greater_date_to?

    belongs_to :variant

    private
    def date_from_greater_date_to?
      return if date_from.nil? && date_to.nil?
      if date_to < date_from
        errors.add(:date_from, I18n.t('mkp.variant_costs.errors.date_from') )
      end
    end
  end
end

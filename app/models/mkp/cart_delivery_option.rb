module Mkp
  class CartDeliveryOption < ActiveRecord::Base
    belongs_to :cart
    belongs_to :mkp_shops, :class_name => 'Mkp::Shop'

    validates :charge, :title, :carrier, :shop_id, presence: true

    scope :selected, -> { where(selected: true) }

    def mark_selected!
      update_attribute(:selected, true)
    end

    def mark_unselected!
      update_attribute(:selected, false)
    end
  end
end

module Mkp
  # This module gives access to system-defined colors
  # to be used as variants' properties.
  module ProductColors
    def self.value_of(color_name, network, color_config = nil)
      color_config ||= DefaultColorConfig

      color_config[network].each do |hex, name|
        return { name: name, hex: hex.to_s } if color_name == name
      end

      nil
    end

    def self.generic_hex_for(hex)
      color = ::Paleta::Color.new(:hex, hex.delete('#'))
      generic = nil
      similarity = nil
      generic_hexes.each do |_hex|
        _color = ::Paleta::Color.new(:hex, _hex.delete('#'))
        _similarity = color.similarity(_color)
        if generic.nil? || similarity > _similarity
          generic = _hex
          similarity = _similarity
        end
      end
      generic
    end

    def self.all(network, color_config = nil)
      color_config ||= DefaultColorConfig
      color_config[network].map { |hex, name| { hex: hex, name: name } }
    end

    private

    def self.generic_hexes
      @@generic_colors ||= I18n.t('color').keys.map(&:to_s)
    end

    module DefaultColorConfig
      def self.[](network)
        locale = Network[network].locale
        I18n.t('color', locale: locale)
      end
    end
  end
end

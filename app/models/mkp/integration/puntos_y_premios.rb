require 'uri'
require 'open-uri'
require 'net/http'
require 'json'

module Mkp
  module Integration
    class PuntosYPremios
      def initialize
        env = Rails.env
        @logger_pyp ||= Logger.new("#{Rails.root}/log/PyP.log")
        @gateway_url_api = "#{PYP_RECARGA_URL}/api/v1"
        @catalog_url_api = "#{PYP_CATALOG_URL}/api/v1"
        @manufacturer = Mkp::Manufacturer.find_by_slug 'beyond'
        @store = Mkp::Store.find 21
        @equivalence = @store.visa_puntos_equivalence
        @credentials = {
          "pypbm" => {
            shop_name: APP_CONFIG[env]['pypbm']['shop_name'],
            catalog_token: APP_CONFIG[env]['pypbm']['catalog_token'],
            catalog_microsite: APP_CONFIG[env]['pypbm']['catalog_microsite'],
            redemption_id: APP_CONFIG[env]['pypbm']['redemption_id'],
            redemption_token: APP_CONFIG[env]['pypbm']['redemption_token']
          },
          "pypbm-g" => {
            shop_name: APP_CONFIG[env]['pypbm-g']['shop_name'],
            catalog_token: APP_CONFIG[env]['pypbm-g']['catalog_token'],
            catalog_microsite: APP_CONFIG[env]['pypbm-g']['catalog_microsite'],
            redemption_id: APP_CONFIG[env]['pypbm-g']['redemption_id'],
            redemption_token: APP_CONFIG[env]['pypbm-g']['redemption_token']
          }
        }
      end

      def logger_pyp
        @logger_pyp
      end

      def update_product(product)
        @credentials.each do |_, value|
          get_update_product(value, product)
          sku_to_import = "PYP#{@product["code"]}"
          producto = Mkp::Variant.find_by(sku: sku_to_import).product
          producto.variants.each do |variant|
            if PypImportedProduct.find_by(sku: variant.sku)
              unless variant.visible?
                variant.product.set_approved_for_stores(true)
                variant.update_attributes(visible: true)
                producto.recreate_variants_visibility
              end
            else
              variant.product.set_approved_for_stores(true)
              variant.update_attributes(visible: false)
            end
            producto.solr_index
          end
        end
      end

      def request_categories(shop_credentials, uri)
        body = {token: shop_credentials[:catalog_token]}

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE
        request = Net::HTTP::Get.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json
        JSON.parse(http.request(request).body)
      end

      def  initialize_category_product(shop_credentials, type, id )
        @pyp_categories = {} unless @pyp_categories
        @pyp_subcategories = {} unless @pyp_subcategories
        if type == "categories"
          response = request_categories(shop_credentials, URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/#{type}/#{id}"))
          @pyp_categories[id] = response["category"]["name"]
          @pyp_categories
        else
          return unless id
          category = @pyp_categories.keys.first
          response = request_categories(shop_credentials, URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/categories/#{category}/#{type}/#{id}"))
          unless response["subcategory"]["name"].nil?
            @pyp_subcategories[id] = response["subcategory"]["name"]
          end
          @pyp_subcategories
        end
        logger_pyp.info("CATEGORIAS  IMPORTADAS: #{@pyp_categories} \n")
        logger_pyp.info("SUB-CATEGORIAS  IMPORTADAS: #{@pyp_subcategories} \n")

      end

      def get_update_product(shop_credentials, product)
        types = { "categories" => "category_id", "subcategories"=>"subcategory_id"}

        @shop = Mkp::Shop.find_by_title shop_credentials[:shop_name]
        return if @shop.nil?
        body = {
          token: shop_credentials[:catalog_token],
        }
        uri = URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/products/#{product}")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE
        request = Net::HTTP::Get.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json
        response = http.request(request).body

        return unless response.present?

        data = JSON.parse(response)["product"]
        %w[categories subcategories].each do |value|
          initialize_category_product(shop_credentials, value, data[types[value]] )
        end
        process_response(response, true)
        logger_pyp.info("CREDENCIAL : #{shop_credentials[:shop_name]}\n #{response}  ")
        logger_pyp.info("PRODUCTO OBTENIDO DEL RESPONSE : #{Time.zone.now}\n #{response}  ")

        @product = JSON.parse(response)["product"]

      end

      # ----------------------------------------------------------------------------------------------
      # Catalog Update
      # ----------------------------------------------------------------------------------------------
      def update_catalogs
        PypImportedProduct.destroy_all
        @credentials.each do |key, value|
          page_number = 1
          while page_number
            Rails.logger.info("Importando página #{page_number} a las #{Time.zone.now}")
            page_number = update_products(value, page_number)
          end
        end
        @credentials.each do |each, value|
          @shop = Mkp::Shop.includes(:products,:variants).find_by(title: value[:shop_name])
          @shop.products.each do |product|
            product.variants.each do |variant|
              if PypImportedProduct.find_by(sku: variant.sku)
                unless variant.visible?
                  variant.product.set_approved_for_stores(true)
                  variant.update_attributes(visible: true)
                  product.recreate_variants_visibility
                end
              else
                variant.product.set_approved_for_stores(true)
                variant.update_attributes(visible: false)
              end
              product.solr_index
            end
          end
        end
      end

      def update_products(shop_credentials, page_number)
        @shop = Mkp::Shop.find_by_title shop_credentials[:shop_name]
        return if @shop.nil?
        initialize_categories(shop_credentials)
        initialize_subcategories(shop_credentials)
        logger_pyp.info("CATEGORIAS IMPORTADAS(MASIVO) #{page_number} a las #{Time.zone.now}\n #{@pyp_categories} ")
        logger_pyp.info("SUBCATEGORIAS IMPORTADAS(MASIVO) #{page_number} a las #{Time.zone.now}\n #{@pyp_subcategories} ")

        body = {
          token: shop_credentials[:catalog_token],
          page_number: page_number
        }

        uri = URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/products?")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        request = Net::HTTP::Get.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json
        response = http.request(request).body
        process_response(response)
        logger_pyp.info("PRODUCTOS OBTENIDOS(MASIVO) #{page_number} A LAS  #{Time.zone.now}\n #{JSON.parse(response)} \n")
        JSON.parse(response)["meta"]["pagination"]["next_page"]
      end

      def initialize_categories(shop_credentials)
        @pyp_categories = {}
        body = {token: shop_credentials[:catalog_token]}
        uri = URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/categories?")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE
        request = Net::HTTP::Get.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json
        response = JSON.parse(http.request(request).body)
        response["categories"].each {|hash| @pyp_categories[hash["id"]] = hash["name"]}

        @pyp_categories
      end

      def initialize_subcategories(shop_credentials)
        @pyp_subcategories = {}
        body = {token: shop_credentials[:catalog_token]}
        @pyp_categories.each do |key, value|
          uri = URI("#{@catalog_url_api}/microsites/#{shop_credentials[:catalog_microsite]}/categories/#{key}/subcategories")
          http = Net::HTTP.new(uri.host, uri.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE
          request = Net::HTTP::Get.new(uri.request_uri)
          request["content-type"] = 'application/json'
          request.body = body.to_json
          response = JSON.parse(http.request(request).body)
          response["subcategories"].each {|hash| @pyp_subcategories[hash["id"]] = hash["name"]}
        end
        @pyp_subcategories

      end

      def delete_hash(hash, object)
        logger_pyp.info("*************************************************")
        output = hash.select  do |key,value|
          object.send(key.to_sym).to_s != value.to_s
          logger_pyp.info("VALOR ACTUAL AVENIDA: #{key}: #{object.send(key.to_sym).to_s}\n")
          logger_pyp.info("VALOR ACTUAL REQUEST PYP: #{key}: #{value.to_s}\n")
        end
        logger_pyp.info("*************************************************")
        output

      end

      # def points_price_approval(variant)
      #   variant[:points_price] != variant.points_price
      # end

      def transform_data(data, uniq = nil)
        uniq ? [{"product" => [data["product"]]}, "product"] : [data, "products"]
      end

      def process_response(response, unique = nil )
        error = ""
        data = JSON.parse(response)
        data, type = transform_data(data, unique)
        data[type].each do |product_data|
          sku_to_import = "PYP#{product_data["code"]}"
          product_attributes = build_product_attributes(product_data)
          logger_pyp.info("BUILD DE PRODUCTOS : #{product_attributes} \n")
          variant_attributes = build_variant_attributes(product_data, sku_to_import)
          logger_pyp.info("BUILD DE VARIANTES: #{variant_attributes} \n")
          if product_attributes[:category_id]
            variant = Mkp::Variant.find_by_sku sku_to_import
            ActiveRecord::Base.transaction do
              if variant
                logger_pyp.info("ESTADO ACTUAL DEL PRODUCTO(AVENIDA): #{variant.product.id} - #{variant.product.product_stores.map(&:status)}")
                variant_attributes = delete_hash(variant_attributes, variant)
                variant.update_attributes(variant_attributes) if variant_attributes
                product = variant.product
                product.update_attributes(deleted_at: nil) if product.deleted?
                package_attributes = product_attributes.delete(:packages_attributes)
                product_attributes = delete_hash(product_attributes, product)
                product.update_attributes(product_attributes) unless product_attributes.blank?
                logger_pyp.info("VALORES DEL PRODUCTO, POST ACTUALIZACION: #{product.inspect}")
                product.packages.delete_all
                logger_pyp.info("VALORES DEL PACKAGING: #{product_attributes}")
                package_attributes.each do |hash|
                  Mkp::Package.create(hash.merge(product_id: product.id))
                end
              else
                product = Mkp::Product.create(product_attributes)
                logger_pyp.info("PRODUCTO NUEVO CREADO: #{product.inspect} \n")
                variant = Mkp::Variant.new(variant_attributes.merge(product_id: product.id))
                variant.send(:build_gp_sku, [])
                variant.save!
                logger_pyp.info("VARIANTE NUEVA CREADA: #{variant.inspect} \n")
              end
              import_image(product_data, product)
              product.recreate_variants_visibility
              logger_pyp.info("ESTADO FINAL DEL PRODUCTO: #{variant.product.product_stores.map(&:status)}")

              product.solr_index
            end
            PypImportedProduct.create(sku: sku_to_import)
          else
            error += "El producto << #{product_data["name"]} >>no se puede importar/actualizar por que no existe la categoría"
            error += "<< #{@pyp_categories[product_data["category_id"]]}/#{@pyp_subcategories[product_data["subcategory_id"]]} >>"
            error += "<br>"
            logger_pyp.error(error)
            logger_pyp.info("El producto << #{product_data["name"]} >>no se puede importar/actualizar por que no existe la categoría \n
                             << #{@pyp_categories[product_data["category_id"]]}/#{@pyp_subcategories[product_data["subcategory_id"]]} >>")
          end
        end
        # Notify results of the import/sync asynchronously.
        if !error.blank?
          IntegrationMailer.misses_category(error.html_safe).deliver
        end
      end

      def build_product_attributes(product_data)
        transaction_type = product_data["product_type"] == "physical" ? :purchasable : :points

        {
          title: product_data["name"],
          description: product_data["description"] || product_data["name"],
          category_id: to_category_id(product_data),
          manufacturer_id: to_manufacturer_id(product_data),
          regular_price: regular_price(product_data),
          iva: product_data["iva"].to_f,
          available_on: Time.zone.now - 1.day,
          currency_id: @shop.currency.id,
          transaction_type: transaction_type,
          handle_stock: true,
          shop_id: @shop.id,
          packages_attributes: [{
                                  width: (product_data["width"].to_f * 10),
                                  height: (product_data["height"].to_f * 10),
                                  length: (product_data["length"].to_f * 10),
                                  weight: product_data["weight"].to_f,
                                  length_unit: Network[Network.default].length_unit,
                                  mass_unit: Network[Network.default].mass_unit
                                }]
        }
      end

      def build_variant_attributes(product_data, sku_to_import)
        hash = {
          sku: sku_to_import,
          quantity: product_data["current_stock"].to_i
        }
        if product_data["product_type"] == "digital"
          hash[:points_price] = @equivalence ? (regular_price(product_data).ceil / (@equivalence.to_f * (1 + (product_data["iva"].to_f) / 100))).ceil : 0
        end
        hash
      end

      def regular_price(product_data)
        product_data["price"].to_f
      end

      def to_category_id(product_data)
        subcategory_name = @pyp_subcategories[product_data["subcategory_id"]]
        category_name = @pyp_categories[product_data["category_id"]]
        categories = @store.categories.where(full_path_name: (subcategory_name ? "#{category_name}/#{subcategory_name}" : "#{category_name}"))
        categories.first.id if categories.any?
      end

      def to_manufacturer_id(product_data)
        if product_data["brand"]
          manufacturer_name = product_data["brand"]["name"]
          available_manufacturer = Mkp::Manufacturer.find_by_name manufacturer_name
          return available_manufacturer.id if available_manufacturer
        end

        @manufacturer.id
      end

      def name_image(image)
        image["name"]  || File.basename(URI.parse(image['url']).path).split('.').first
      end

      def import_image(product_data, product)
        if product_data["images"].present?
          styles = {
            st: '100x100#', # small-thumb (cropped)
            t:  '130x130>', # thumbnail (cropped if bigger than specified)
            m:  '300x300#', # medium (cropped)
            ml: '450x450>', # medium-large (cropped if bigger than specified)
            l:  '720x720>', # large (cropped if bigger than specified)
          }
          #::Mkp::Attachment::ProductPicture.destroy_all(product_id: product.id)
          view_order = 1
          product_data["images"].each do |image_data|
            @name =  name_image(image_data)
            unless ::Mkp::Attachment::ProductPicture.find_by(photo_name: @name, product_id: product.id).present?
              picture = ::Mkp::Attachment::ProductPicture
                          .new(product_id: product.id,
                               photo_name: @name,
                               photo: open(image_data["url"]),
                               view_order: view_order)
              picture.styles = styles
              if picture.save
                picture.update_attribute(:processing, false)
                view_order += 1
              end
            end
          end
        end
      end

      # ----------------------------------------------------------------------------------------------
      # Reserva de productos del catalogo
      # ----------------------------------------------------------------------------------------------

      def redemption(order, customer_email = nil)
        begin
          response = []
          order.suborders.each do |suborder|
            if shop_credentials = @credentials[suborder.items.first.product.shop.title.downcase]
              email = customer_email || suborder.customer.email
              response << basic_redemption(suborder, shop_credentials, email)
            end
          end
          response
        rescue
          {
            response_code: '-9',
            response_message: 'Error al intentar redimir la orden al proveedor'
          }
        end
      end

      def basic_redemption(suborder, shop_credentials, customer_email)
        @shop = Mkp::Shop.find_by_title shop_credentials[:shop_name]
        return if @shop.nil?
        items = []
        order_payments = suborder.order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint'])
        gateway = order_payments.first unless order_payments.blank?
        shipment_address = suborder.shipment.present? ? suborder.shipment.destination_address : nil

        suborder.items.each do |item|
          if @shop && item.product.shop == @shop
            variant_sku = item.variant.sku.dup
            importe_cliente = item.variant.product.points? ? 0 : item.total - (item.points * (item.point_equivalent * (1 + (item.iva / 100)))).round(0)
            importe_banco_sin_iva = (item.points * item.point_equivalent).round(2)

            items << {
              cod_producto: (variant_sku.slice! 3..variant_sku.size),
              cantidad: item.quantity,
              importe_cliente: importe_cliente,
              importe_banco: importe_banco_sin_iva,
              puntos: item.points,
              importe_cuota: (gateway ? (importe_cliente / gateway.installments) : 0).round(2),
              cantidad_cuotas: (gateway ? gateway.installments : '-')
            }
          end
        end

        if items.blank?
          {
            response_code: '1',
            response_message: 'OK',
          }
        else
          body = {
            #general
            catalog_id: shop_credentials[:redemption_id],
            fecha: (suborder.created_at).strftime("%d/%m/%Y"),
            id_canje: suborder.id,
            items: items,
            #Shipment
            calle: shipment_address.present? ? shipment_address.address : '',
            nro: shipment_address.present? ? shipment_address.street_number : '',
            piso: shipment_address.present? ? shipment_address.address_2 : '',
            dpto: shipment_address.present? ? shipment_address.address_2 : '',
            cp: shipment_address.present? ? shipment_address.zip : '',
            provincia: shipment_address.present? ? shipment_address.state : '',
            localidad: shipment_address.present? ? shipment_address.city : '',
            #relacion puntos y plata
            puntos_mas_pesos: (suborder.total_without_points > 0),
            #Decidir
            id_gateway: (order_payments.blank? ? '-' : order_payments.first.get_external_id({ shop_id: @shop.id })),
            #Cliente
            cuenta: suborder.customer.uuid,
            titular: suborder.customer.full_name,
            nro_documento: suborder.customer.doc_number || "",
            telefono: suborder.customer.telephone || "",
            email: customer_email,
            # Informacion no obligatoria/necesaria
            "beneficiario": {
              "tarjeta_numero": "-",
              "documento_numero": "-",
              "abonado_numero": "-",
              "documento_tipo": "-",
              "telefono_cod_area": "-",
              "telefono_numero": "-"},
            "observaciones": "",
            "extra_info": "",
            token: shop_credentials[:redemption_token]
          }

          uri = URI("#{@gateway_url_api}/catalogs/#{shop_credentials[:redemption_id]}/redemptions?")
          http = Net::HTTP.new(uri.host, uri.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(uri.request_uri)
          request["content-type"] = 'application/json'
          request.body = body.to_json

          response = http.request(request).body

          result = process_redemption(response)
          redemption_completed = true

          if response != "1"
            IntegrationMailer.redemtion_error(@shop.owner.email, result, body).deliver
            IntegrationMailer.redemtion_error('<EMAIL>', result, body).deliver
            IntegrationMailer.redemtion_error('<EMAIL>', result, body).deliver if Rails.env.production?
            IntegrationMailer.redemtion_error('<EMAIL>', result, body).deliver if Rails.env.production?
            redemption_completed = false
          end

          Rails.logger.info "PYP Redemption suborder: #{suborder.id}"
          Rails.logger.info "---------------------------------------"
          Rails.logger.info request.body
          Rails.logger.info "---------------------------------------"
          suborder.update_attribute(:external_redemption_completed, redemption_completed)

          ExternalRedemptionLog.create(suborder: suborder.id, redemption: request.body, response: result)

          result
        end
      end

      # ----------------------------------------------------------------------------------------------
      # Cancelación de reserva de productos del catalogo
      # ----------------------------------------------------------------------------------------------
      def cancel_redemption(order)
        begin
          order.suborders.each do |suborder|
            if shop_credentials = @credentials[suborder.items.product.shop.title.downcase]
              basic_cancel_redemption(order, shop_credentials)
            end
          end
        rescue
          {
            response_code: '-9',
            response_message: 'Error al procesar la orden'
          }
        end
      end

      def basic_cancel_redemption(order, shop_credentials)
        return if @shop.nil?
        items = []
        order.items.each do |item|
          if @shop && item.product.shop == @shop
            items << item
          end
        end

        unless items.blank?

          body = {
            token: shop_credentials[:redemption_token]
          }

          uri = URI("#{@gateway_url_api}/catalogs/#{shop_credentials[:redemption_id]}/redemptions/#{order.payment.gateway_object_id}/cancel")
          http = Net::HTTP.new(uri.host, uri.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(uri.request_uri)
          request["content-type"] = 'application/json'
          request.body = body.to_json

          response = http.request(request).body

          process_redemption(response)
        end
      end

      # ----------------------------------------------------------------------------------------------
      # Recarga de celulares
      # ----------------------------------------------------------------------------------------------

      def recharge(transaction_id, amount, company, user_code, full_name, phone_code, phone_number)
        shop_credentials = @credentials["pypbm"]
        body = {
          catalog_id: shop_credentials[:redemption_id],
          id_canje: transaction_id,
          cod_producto: product_code(amount, company),
          cuenta: user_code,
          titular: full_name,
          beneficiario: {
            telefono_cod_area: phone_code,
            telefono_numero: phone_number
          },
          token: shop_credentials[:redemption_token]
        }

        uri = URI("#{@gateway_url_api}/catalogs/#{shop_credentials[:redemption_id]}/redemptions?")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        request = Net::HTTP::Post.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json

        response = http.request(request).body

        process_redemption(response)
      end

      def process_redemption(response)
        case response
        when '1'
          {
            response_code: '1',
            response_message: 'OK',
          }
        when '-1'
          {
            response_code: '-1',
            response_message: 'Otro error',
          }
        when '-2'
          {
            response_code: '-2',
            response_message: 'No se encuentra el catálogo con ese catalog_id',
          }
        when '-3'
          {
            response_code: '-3',
            response_message: 'No se encuentra el producto con ese código en el catálogo',
          }
        when '-4'
          {
            response_code: '-4',
            response_message: 'catalog_id no puede estar en blanco',
          }
        when '-5'
          {
            response_code: '-5',
            response_message: 'Stock insuficiente en el catálogo',
          }
        when '-6'
          {
            response_code: '-6',
            response_message: 'El código de socio/cuenta no puede estar en blanco',
          }
        when '-7'
          {
            response_code: '-7',
            response_message: 'El código de canje está repetido',
          }
        when '-8'
          {
            response_code: '-8',
            response_message: 'No se encuentra el parámetro idCanje',
          }
        else
          {
            response_code: '-',
            response_message: 'No se pudo procesar el error',
          }
        end
      end

      def product_code(amount, company)
        case company
        when 'Movistar'
          case amount
          when '10'
            '210015'
          when '20'
            '210016'
          when '50'
            '210005'
          when '100'
            '210006'
          when '200'
            '210007'
          when '300'
            '210008'
          else
            nil
          end
        when 'Claro'
          case amount
          when '10'
            '210013'
          when '20'
            '210014'
          when '50'
            '210000'
          when '100'
            '210002'
          when '200'
            '210003'
          when '300'
            '210004'
          else
            nil
          end
        when 'Personal'
          case amount
          when '10'
            '210017'
          when '20'
            '210018'
          when '50'
            '210009'
          when '100'
            '210010'
          when '200'
            '210011'
          when '300'
            '210012'
          else
            nil
          end
        when 'Tuenti'
          case amount
          when '10'
            '210019'
          when '20'
            '210020'
          when '50'
            '11323'
          when '100'
            '11320'
          when '200'
            '11321'
          when '300'
            '11322'
          else
            nil
          end
        end
      end
    end
  end
end


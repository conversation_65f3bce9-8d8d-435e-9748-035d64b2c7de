module Mkp
  module Integration
    class GPMercadolibre < Base
      include Concerns::MercadolibreLogic

      VARIANT_UPDATABLE_PARAMS = [
        :quantity
      ].freeze

      AVAILABLE_STATUSES = [
        'active'
      ].freeze

      def save_authorization
        set_client
        auth = @client.generate_access_token

        self.data          = { uid: ::MELI['AR']['uid'] }
        self.account_name  = ::MELI['AR']['account_name']
        self.access_token  = auth.access_token
        self.refresh_token = auth.refresh_token
        self.expires       = true
        self.expires_at    = auth.expired_at
        self.scopes        = %w(offline_access read write)
        self.save
      end

      def create_meli_item(product, network)
        set_client

        product_attributes = product_hash(product, network)
        if product_attributes.is_a?(Hash)
          return @client.errors unless product_valid?(product_attributes)

          external_object = @client.create_item(product_attributes)

          create_integration_object(product, external_object)
        else
          variants = product.variants.with_stock
          product_attributes.each_with_index do |variant_attr, index|
            return @client.errors unless product_valid?(variant_attr)
            variant = variants[index]

            external_object = @client.create_item(variant_attr)

            create_integration_object(variant, external_object)
          end
        end
      end

      def delete_meli_item(product_id)
        set_client

        external_objects_clause = { integration_name: 'gpmercadolibre' }
        product = Mkp::Product.find(product_id)
        object = product.external_objects.where(external_objects_clause).first

        # for those products that only are integrated by their variants
        if object.present?
          object_id = object.external_id
          object.destroy
          @client.update_item_fields(object_id, status: 'closed')
        end

        product.variants.map do |variant|
          external_objects = variant.external_objects.where(external_objects_clause)
          if object.nil?
            external_objects.each do |external_object|
              @client.update_item_fields(external_object.external_id, status: 'closed')
            end
          end
          external_objects.destroy_all
        end

      end

      def product_hash(product, network)
        predicted_category = predict_category(product)
        has_variations = predicted_category['variations'].present?
        if has_variations
          attributes_to_publish = basic_product_hash(product, network, predicted_category)
          attributes_to_publish.tap do |options|
            options[:variations] = meli_variations(product, predicted_category)
            options[:title] = title_for_item(product, network)
            options[:pictures] = pictures(product)
          end
        else
          attributes_to_publish = []
          product.variants.with_stock.each do |variant|
            attributes_hash = basic_product_hash(product, network, predicted_category)
            attributes_hash.tap do |options|
              options[:title] = title_for_variant(variant, network)
              options[:available_quantity] =  variant.quantity
              options[:seller_custom_field] = variant.gp_sku
              if variant.picture.present?
                options[:pictures] = [{ source: "http:#{variant.picture.url}" }]
              else
                options[:pictures] = pictures(product)
              end
            end
            attributes_to_publish << attributes_hash
          end
        end
        attributes_to_publish
      end

      def predict_category(product)
        set_client

        @client.predict_category(::MELI['site_country'], title_for_predictor(product))
      end

      def basic_product_hash(product, network, predicted_category)

        currency = Mkp::Currency.find(Network[network].currency_id).identifier
        {
          category_id: predicted_category['id'],
          price: product.price.to_s,
          currency_id: currency,
          buying_mode: Network[network].gp_meli['buying_mode'],
          listing_type_id: Network[network].gp_meli['listing_type'],
          condition: Network[network].gp_meli['condition'],
          warranty: Network[network].gp_meli['warranty'],
          description: description_html(product, network),
          shipping: {
            mode: 'me2',
            local_pick_up: true,
            free_shipping: false,
            methods: nil,
          },
          accepts_mercadopago: true,
          non_mercado_pago_payment_methods: [],
          tags: ['immediate_payment']
        }
      end

      def pictures(product)
        product.pictures.map do |picture|
          { source: "http:#{picture.url}" }
        end
      end

      def description_html(product, network)
        options = {
          title: title_for_description(product),
          extra_title: Network[network].gp_meli['extra_title'],
          description: product.description_html,
          manufacturer_logo_url: "http:#{product.manufacturer.logo.url}",
          manufacturer_name: product.manufacturer.name,
          pictures_urls: product.pictures.first(2).map { |pic| "http:#{pic.url}" }
        }

        view = ActionView::Base.new(ActionController::Base.view_paths, {})
        template_path = 'app/views/mkp/integration/mercadolibre_description.html.slim'

        view.render(file: Rails.root.join(template_path), locals: options)
      end

      def product_valid?(attributes)
        set_client

        @client.item_valid?(attributes)
      end

      def meli_variations(product, predicted_category)
        those_without_stock, those_with_stock = product.variants.partition do |variant|
          variant.quantity <= 0
        end

        if those_without_stock.present?
          those_without_stock.each do |variant|
            product.errors.add(:variants_without_stock, variant.name)
          end
        end

        those_with_stock.map do |variant|
          combinations = attribute_combinations(variant, predicted_category)

          unless combinations.present?
            product.errors.add(:attributes_combination, variant.name) and next
          end

          {
            attribute_combinations: combinations,
            available_quantity: variant.quantity,
            price: product.price,
            seller_custom_field: variant.gp_sku
          }.tap do |attributes|
            if variant.picture.present?
              attributes[:picture_ids] = [ "http:#{variant.picture.url}" ]
            end
          end
        end.compact
      end

      def attribute_combinations(variant, predicted_category)
        variant.properties.each_with_object([]) do |(key, value), attributes_for_variants|
          variation_hash = predicted_category['variations'].detect do |variation|
            key = key.first if key.is_a? Array
            variation['type'] == key.to_s
          end
          return nil unless variation_hash.present?
          value_id = get_value_id(value, variation_hash)

          attributes_hash = {
            id: variation_hash['id']
          }
          if value_id.present?
            attributes_hash.tap { |hash_attrib| hash_attrib[:value_id] = value_id }
          else
            if key == :size
              attributes_hash.tap { |hash_attrib| hash_attrib[:value_name] = value }
            else
              attributes_hash.tap { |hash_attrib| hash_attrib[:value_name] = value[:name] }
            end
          end
          attributes_for_variants << attributes_hash
        end
      end

      def get_value_id(property, variation_hash)
        return unless variation_hash.present? && variation_hash.is_a?(Hash)
        case variation_hash['type']
        when 'size'
          meli_size_id(property, variation_hash)
        end
      end

      def meli_size_id(size, variation_hash)
        meli_size = variation_hash['values'].detect { |v| size.downcase == v['name'].downcase }
        meli_size.present? && meli_size['id']
      end

      def create_integration_object(internal_object, external_object)
        Mkp::Integration::Object.create do |object|
          object.integrable = internal_object
          object.integration_id = id
          object.integration_name = simple_type
          object.external_id = external_object.id
        end
        if internal_object.is_a?(Mkp::Product)
          external_object.variations.each do |variation|
            variant = internal_object.variants.detect {|v| v.gp_sku == variation['seller_custom_field'] }

            Mkp::Integration::Object.create do |object|
              object.integrable = variant
              object.integration_id = id
              object.integration_name = simple_type
              object.external_id = variation['id']
            end
          end
        end
      end

      def title_for_item(product, network)
        title = title_for_description(product)
        extra = " - #{Network[network].gp_meli['extra_title']}"

        title += extra if title.length < (60 - extra.length)

        title
      end

      def title_for_variant(variant, network)
        product = variant.product
        title = "#{product.title.strip} - #{product.manufacturer.name.strip} - #{variant.name.strip}"
        extra = " - #{Network[network].gp_meli['extra_title']}"
        if title.length < 60
          title += extra if title.length < (60 - extra.length)
        else
          title = "#{product.title.strip} - #{product.manufacturer.name.strip}"
          if title.length > 60
            title = "#{product.title.strip}"
          end
        end
        title
      end

      def title_for_description(product)
        "#{product.title.strip} - #{product.manufacturer.name.strip}"
      end

      def title_for_predictor(product)
        gender = product.genders.first.try(:name)
        title = product.title.strip

        [gender, title].compact.join(' - ')
      end

      def remote_sync(product, integration_object)
        set_client

        external_product_id = integration_object.external_id

        product.variants.each do |variant|
          variant.external_objects.where(integration_name: simple_type).each do |external_variant|
            @client.update_variations_fields(
              external_product_id,
              external_variant.external_id,
              { available_quantity: variant.quantity }
            )
          end
        end

        meli_product = integration_product_model.find([external_product_id]).first

        attributes = if meli_product.status == 'active' && product.total_stock <= 0
          { status: 'paused' }
        elsif meli_product.status == 'paused' && product.total_stock > 0
          { status: 'active' }
        end

        @client.update_item_fields(external_product_id, attributes) if attributes.present?
      end

      private

      def update_token
        response = @client.generate_access_token

        raise if response.respond_to?(:error) || response.access_token.blank?

        self.access_token = response.access_token
        self.expires_at = response.expired_at

        save
      rescue => exception
        handle_exception(exception, response)
      end

      def integration_product_model
        @_model ||= Lux::Integration::Mercadolibre::Product.new(self)
      end

      def sync_product(external_product)
        sync_variants(external_product)
      end

      def sync_variants(external_product)
        if external_product.available_properties.size == 1 && \
          external_product.available_properties.include?(:noproperty)
          ActiveRecord::Base.transaction do
            if (integration_object = objects.find_by_external_id(external_product.external_id)).present?
              variant = integration_object.integrable
              sync_each_variant(variant, external_product.variants.first)
            end
          end
        else
          ActiveRecord::Base.transaction do
            external_product.variants.each do |external_variant|
              if (integration_object = objects.find_by_external_id(external_variant.external_id)).present?
                variant = integration_object.integrable
                sync_each_variant(variant, external_variant)
              end
            end
          end
        end
      end

      def sync_each_variant(variant, external_variant)
        params_to_update = variant_attributes_to_update(variant, external_variant)

        variant.update_attributes(params_to_update) if params_to_update.present?
      end

    end
  end
end

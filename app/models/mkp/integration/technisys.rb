require 'uri'
require 'net/http'
module Mkp
  module Integration
    class Technisys
      def initialize(username, second_factor, password)
        @username = username
        @second_factor = second_factor
        @password = password
        @gateway_url_api = "#{TECHNISYS_URL}/api/v1/execute/session.login."
      end

      def log_in
        response = first_step
        response_2 = second_step(response["data"]["_exchangeToken"])
        response_3 = third_step(response_2["data"]["_exchangeToken"])
        response_3["code"] == "COR000I"
      end

      def first_step
        uri = URI("#{@gateway_url_api}step1")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        body = {
            "lang": "es",
            "_username": @username,
            "_secondFactor": @second_factor,
            "_captcha": "",
            "channel": "frontend"
        }

        request = Net::HTTP::Post.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request.body = body.to_json

        response = http.request(request)
        response = JSON.parse(response.body)
        response
      end

      def second_step(exchange_token)
        uri = URI("#{@gateway_url_api}step2")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        body = {
            "lang": "es",
            "_password": @password,
            "_captcha": ""
        }

        request = Net::HTTP::Post.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request["authorization"] = "exchange #{exchange_token}"
        request["charset"] = 'UTF-8'
        request.body = body.to_json

        response = http.request(request)
        response = JSON.parse(response.body)
        response
      end

      def third_step(exchange_token)
        uri = URI("#{@gateway_url_api}step3")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.verify_mode = OpenSSL::SSL::VERIFY_PEER #http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        body = {}

        request = Net::HTTP::Post.new(uri.request_uri)
        request["content-type"] = 'application/json'
        request["authorization"] = "exchange #{exchange_token}"
        request["charset"] = 'UTF-8'
        request.body = body.to_json

        response = http.request(request)
        response = JSON.parse(response.body)
        response
      end
    end
  end
end

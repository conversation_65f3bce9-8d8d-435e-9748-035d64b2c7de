module Mkp
  module Integration
    class Object < ActiveRecord::Base
      self.table_name = 'mkp_integration_objects'

      belongs_to :integrable, polymorphic: :true
      belongs_to :integration, class_name: 'Mkp::Integration::Base', foreign_key: :integration_id

      validates_uniqueness_of :external_id, scope: [:integrable_type, :integrable_id, :integration_id]

      scope :by_type, ->(type) { where(integrable_type: type) }

      def integrable
        integrable_type.constantize.unscoped.find(integrable_id)
      rescue
        super
      end

    end
  end
end

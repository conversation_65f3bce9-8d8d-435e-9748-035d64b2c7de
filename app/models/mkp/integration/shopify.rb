module Mkp
  module Integration
    class Shopify < Base
      before_destroy :unregister_webhooks

      WEBHOOKS_TOPICS = [
        'products/create',
        'products/update',
        'products/delete'
      ].freeze

      AVAILABLE_SCOPES = [
        :read_products,
        :write_products,
        :read_orders,
        :write_orders,
        :read_fulfillments,
        :read_shipping
      ].freeze

      AUTH_DATA_PARAMS = [
        :timestamp,
        :signature,
        :hmac
      ].freeze

      store :data, accessors: AUTH_DATA_PARAMS

      validates_presence_of :account_name
      validates_presence_of :scopes

      def setup_session_params(session, callback_url)
        session[:shopify] = {}
        session[:shopify][:callback_url] = callback_url
        session[:shopify][:integration_id] = id
        session[:shopify][:shop_id] = shop_id
        session[:shopify][:scope] = scopes
        session[:shopify][:site] = "#{account_name}.myshopify.com"
      end

      def save_authorization(params, env_auth)
        self.data         = data_params(params)
        self.code         = params['code']
        self.access_token = env_auth['credentials']['token']
        self.expires      = env_auth['credentials']['expires']
        self.save

        register_webhooks
      end

      def session
        return unless authorized?

        @shopify_session ||= ShopifyAPI::Session.new("#{account_name}.myshopify.com", access_token)
      end

      def activate_session
        @active_session ||= begin
          ShopifyAPI::Base.activate_session(session)
          true
        end
      end

      def deactivate_session
        @active_session &&= begin
          ShopifyAPI::Base.clear_session
          false
        end
      end

      def data_params(params)
        params.select { |k, _| AUTH_DATA_PARAMS.include?(k.to_sym) }
      end

      def remote_sync(variant_ids)
        return unless scopes.include?('write_products')

        activate_session

        variants = Mkp::Variant.find(variant_ids)

        variants.map do |variant|
          external_variant_id = variant.external_objects.first.try(:external_id)
          external_variant = ShopifyAPI::Variant.find(external_variant_id)

          if external_variant.present?
            external_variant.update_attributes(
              old_inventory_quantity: external_variant.inventory_quantity,
              inventory_quantity: variant.quantity
            )
          end
        end
      end

      def process_notification(notification)
        activate_session

        topic, payload = notification.values_at(:topic, :payload)
        external_product_id = payload['id']

        external_product = integration_product_model.find(external_product_id).first

        case topic
        when 'products/create'
          create_integrated_product(external_product)
        when 'products/update'
          if integrated_product_exists?(external_product)
            update_related_product(external_product)
            sync_variants(external_product)
          end
        when 'products/delete'
          delete_integration_product(external_product_id)
        end

      rescue NoMethodError => e
        handle_exception(e, {
          notification: notification,
          topic: topic,
          payload: payload,
          external_product_id: external_product_id,
          external_product: external_product
        })
      end

      private

      def register_webhooks
        activate_session

        protocol = (ON_STAGING || Rails.env.development?) ? 'http' : 'https'
        endpoint = Rails.application.routes.url_helpers.api_mkp_shopify_webhooks_url(
          protocol: protocol, integration_id: id, host: HOSTNAME
        )

        webhooks = WEBHOOKS_TOPICS.each_with_object([]) do |topic, stash|
          webhook = ShopifyAPI::Webhook.create({ address: endpoint, topic: topic, format: 'json' })

          stash << { id: webhook.id, topic: webhook.topic, format: webhook.format }
        end

        settings[:webhooks] = webhooks

        save!
      end

      def unregister_webhooks
        activate_session

        webhooks_ids = settings[:webhooks].map { |webhook| webhook[:id] }.join(',')

        ShopifyAPI::Webhook.find(:all, { params: { ids: webhooks_ids } }).each(&:destroy)
      end
    end
  end
end

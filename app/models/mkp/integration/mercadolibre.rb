module Mkp
  module Integration
    class Mercadolibre < Base
      include Concerns::MercadolibreLogic

      UNSYNCABLE_SHOP_IDS = [
        788,
        797,
        817,
        818,
        822,
        585,
        798,
        863,
        922
      ]

      attr_accessor :client

      before_destroy :revoke_access

      def setup_session_params(session, callback_url)
        session[:mercadolibre] = {}
        session[:mercadolibre][:callback_url] = callback_url
        session[:mercadolibre][:integration_id] = id
        session[:mercadolibre][:shop_id] = shop_id
      end

      def save_authorization(access_code)
        set_client
        auth = @client.authenticate(access_code)

        self.access_token  = auth.access_token
        self.refresh_token = auth.refresh_token
        self.expires       = true
        self.expires_at    = auth.expired_at
        self.scopes        = %w(offline_access read write)
        self.save
      end

      def save_user_data
        response = @client.get_my_user

        self.account_name = response.nickname
        self.data = {
          provider: 'mercadolibre',
          uid: response.id,
          info: {
            username: response.nickname,
            email: response.email,
            first_name: response.first_name,
            last_name: response.last_name,
            url: response.permalink
          },
          extra: {
            raw_info: response.as_json
          }
        }

        self.save
      end

      def save
        return true if super

        persist_data_info
        arrange_errors
        false
      end

      def revoke_access
        # set_client

        # @client.revoke_access(data[:uid])
      end

      def session
        return unless authorized?
      end

      def username
        data['info']['username']
      end

      def user_type
        @user_type ||= data['extra']['raw_info']['user_type']
      end

      def need_custom_configuration?
        brand?
      end

      def configuration_complete?
        return true unless need_custom_configuration?

        official_store_id.present?
      end

      def can_change_configuration?
        !syncing? && (!configuration_complete? || !objects.present?)
      end

      def official_stores
        return [] unless brand?
        return data['brands'] if data['brands'].present?

        add_official_stores_to_data! ? data['brands'] : []
      end

      def official_store_id
        return unless brand?
        settings['official_store'] && settings['official_store']['id']
      end

      def official_store_name
        return unless brand?
        settings['official_store'] && settings['official_store']['name']
      end

      def brand?
        return @is_brand unless @is_brand.nil?
        @is_brand = user_type == 'brand'
      end

      def handle_custom_configuration(configuration_params)
        if (store_id = configuration_params[:official_store_id].to_i).present?
          store_data = official_stores.detect{ |store| store[:id] == store_id }
          if store_data.present?
            self.settings['official_store'] = store_data
            self.account_name = "#{data['uid']}-#{store_data[:id]}"
          end
        end
      end

      def get_offer_info(product_code)
        product_discounts = get_product_discounts(product_code)
        return if product_discounts.blank?

        price_discount = product_discounts.find { |offer| offer['type'] == 'PRICE_DISCOUNT' }
        return if price_discount.blank?

        {
          sale_price: price_discount['top_price'].to_f,
          sale_on: price_discount['start_date'].to_datetime,
          sale_until: price_discount['finish_date'].to_datetime
        }
      end

      protected

      def persist_data_info
        update_column(:data, data) if self.persisted?
      end

      def arrange_errors
        if errors.keys.include?(:account_name)
          errors.delete(:account_name)
          errors.add(:base, I18n.t("lux.integrations.errors.account_name.#{user_type}", integration_name: 'MercadoLibre'))
        end
      end

      def handle_inactive_products(active_external_ids)
        return unless active_external_ids.any?

        inactive_objects = objects.where(integrable_type: Mkp::Product.name)
                                  .where('external_id NOT IN (?)', active_external_ids)

        inactive_objects.each do |inactive_object|
          item = get_item(inactive_object.external_id)

          # We only check about this because the status can be "paused" or "closed" only
          if item.status == 'paused'
            inactive_object.integrable.update_attributes(available_on: nil)
          elsif item.status != 'active'
            inactive_object.integrable.destroy
          end
        end
      end

      private

      def add_official_stores_to_data!
        new_data = data.dup.tap do |data|
          data['brands'] = get_official_stores(data['uid'])
        end
        update_attributes(data: new_data)
      end

      def get_product_discounts(product_code)
        response = get_req("/seller-promotions/items/#{product_code}")
        unless response[:status_code].eql? Rack::Utils::SYMBOL_TO_STATUS_CODE[:ok]
          Rails.logger.info('Could not get discount data')
          Rails.logger.info("STATUS_CODE: #{response[:status_code]}")
          Rails.logger.info("RESPONSE: #{response[:body]}")
          return
        end
        response[:body]
      end

    end
  end
end

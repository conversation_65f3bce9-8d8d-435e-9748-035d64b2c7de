require 'rest_client'
module Mkp
  module Integration
    module Brukman
      extend self
      KEYS = BRUKMAN_KEYS
      AUTH_URL = BRUKMAN_AUTH.freeze
      GATEWAY_URL = BRUKMAN_URL.freeze
      GATEWAY_URL_API = "#{GATEWAY_URL}/services/apexrest/ERPvs/RestApis".freeze
      attr_reader :suborder, :customer, :identification, :shipment, :payment, :to, :from

      def post_purchase(suborder)
        set_data(suborder)
        request_auth(:post, "#{GATEWAY_URL_API}/pedido", order_data.to_json)
      rescue RestClient::BadRequest => e
        nil
      rescue RestClient::UnprocessableEntity => e
        nil
      rescue RestClient::GatewayTimeout => e
        nil
      end

      private
      def set_data(suborder)
        @suborder = suborder
        @customer = suborder.customer
        @payment = suborder.payment
        @shipment = suborder.shipment
        @identification = create_identification
        @from = shipment.origin_address
        @to = shipment.destination_address
      end

      def create_identification
        data = { type: "DNI", number: "" }
        if payment.present? && payment.gateway_data[:payer].present?
          data = payment.gateway_data[:payer][:identification]
        end
        OpenStruct.new(data)
      end

      def token(params)
        @token ||= OpenStruct.new(params)
      end

      def header
        @header ||= {Authorization: "#{@token.token_type} #{@token.access_token}", "Content-type": "application/json"} if @token.present?
      end

      def authorization
        oauth unless @token.present?
      end

      def oauth
        response = request(:post, AUTH_URL, KEYS)
        token(response)
      end

      def request_auth(method, path_info, params = nil)
        authorization
        request(method, path_info, params)['response']
      end

      def request(method, path_info, params = nil)
        response = RestClient::Request.execute( method: method, url: path_info, payload: params, headers: header )
        JSON.parse(response)
      end

      def order_data
        {
          "identificador": "Avenida",
          "cliente": customer_data,
          "referencia_externa": suborder.public_id,
          "forma_de_envio": "",
          "sucursal": "",
          "productos": products_data
        }
      end

      def customer_data
        {
          "id_externo": customer.id.to_s,
          "nombre": to.first_name,
          "apellido": to.last_name,
          "mail": customer.email,
          "tipo_documento": identification.type,
          "nro_documento": identification.number,
          "categoria_iva": "RI",
          "telefono": to.telephone,
          "telefono_alternativo": to.telephone,
          "direccion_facturacion":  address,
          "direccion_envio":  address,
          "razon_social": "AVENIDA COMPRAS S.A.",
          "identificacion_tributaria": "30714144649"
        }
      end

      def address
        {
          "direccion": "#{to.address} #{to.street_number} #{to.address_2}",
          "localidad": to.city,
          "codigo_postal": to.zip,
          "provincia": to.state
        }
      end

      def products_data
        shipment.items.map do |item|
          a_price = item.sale_price || item.price
          {
            "sku": item.variant.sku,
            "cantidad": item.quantity.to_s,
            "precio": a_price.round(2).to_s,
            "descuento": "0"
          }
        end
      end
    end
  end
end

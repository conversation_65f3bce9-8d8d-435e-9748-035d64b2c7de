module Mkp
  class Question < ActiveRecord::Base
    belongs_to :user
    belongs_to :product,  unscoped: true
    has_one :answer,
            foreign_key: 'mkp_question_id',
            dependent: :destroy

    validates :description, presence: true, length: { maximum: 512 }
    validates :product_id,  presence: true
    validates :user_id,     presence: true

    after_create :send_merchant_notification

    scope :without_answer, -> { includes(:answer).where('mkp_answers.mkp_question_id IS NULL') }

    def read!
      self.read = true
      self.save
    end

    private

    def send_merchant_notification
      QuestionMerchantNotificationWorker.perform_in(10.seconds, self.id)
    end
  end
end

module Mkp
  class Member < ActiveRecord::Base
    belongs_to :customer
    has_many :cards, class_name: 'Mkp::Card', dependent: :delete_all
    enum nationality: [:argentino, :foreign]

    scope :enabled, -> { where( enabled: true ) }
    scope :without_card, -> { where.not(token: nil) }
    #Filterrific search
    scope :created_at_gte, ->(reference_time) { where('mkp_members.created_at >= ?', reference_time) }
    scope :created_at_lt, ->(reference_time) { where('mkp_members.created_at < ?', reference_time) }
    scope :with_enabled, ->(enabled) { where(enabled: is_e?(enabled) )}
    scope :search_query, ->(query) do
      query = query.to_s
      return if query.blank?
      terms = query.downcase.split(/\s+/)
      terms = terms.map{ |e| ('%' + e.gsub('*', '%') + '%').gsub(/%+/, '%')}
      customer_ids = Mkp::Customer.where('mkp_customers.doc_number LIKE ? OR mkp_customers.email LIKE ? OR mkp_customers.first_name LIKE ? OR mkp_customers.last_name LIKE ?', terms[0], terms[0], terms[0], terms[0]).ids
      where(customer_id: customer_ids)
    end
    filterrific available_filters: [
                  :created_at_gte,
                  :created_at_lt,
                  :with_enabled,
                  :search_query
                 ]

    after_save :build_credit_card
    before_save  MemberEncryptionWrapper.new

    private

    def self.is_e?(value)
      ActiveRecord::Type::Boolean.new.type_cast_from_database(value)
    end

    def build_credit_card
      if token.present? && cards.empty?
        Tdd::BuildCardWorker.perform_async(self.id)
      elsif cards.any? && token_was != token && token.present?
        cards.delete_all
        Tdd::BuildCardWorker.perform_async(self.id)
      end
    end
  end
end

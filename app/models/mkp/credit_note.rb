module Mkp
  class CreditNote < ActiveRecord::Base
    serialize :gateway_data, Hash
    serialize :gateway_request, Hash

    belongs_to :order, class_name: 'Order'
    belongs_to :suborder
    belongs_to :invoice

    #validate :invoice_present
    #validate :order_cancelled

    def generate!(order, suborder=nil)
      return unless self.persisted? && (credit_note = instance_gateway.credit_note_generate(order, suborder)).present?

      update_attributes!({
        gateway_object_id: credit_note.id,
        gateway_data: credit_note.data.merge(number: credit_note.number),
      })

      order.reload
    end

    def number
      @number ||= gateway_data[:number]
    end

    private

    def instance_gateway
      return unless attributes['gateway'].present?
      @gateway ||= "Gateways::Invoices::#{attributes['gateway']}".constantize
    rescue
    end

    def invoice_present
      errors.add(:order, 'The order should have a invoice') if order.invoices.empty?
    end

    def order_cancelled
      if suborder.present?
        if !suborder.cancelled? && order.credit_notes.flat_map(&:suborder_id).exclude?(suborder_id)
          errors.add(:suborder, 'The suborder has to be cancelled')
        end
      elsif ['cancelled','refunded'].exclude?(order.payment_status)
        errors.add(:order, 'The payment has to be cancelled or refunded')
      end
    end
  end
end

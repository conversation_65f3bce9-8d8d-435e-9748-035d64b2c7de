module Mkp
  module Brands<PERSON>ilter
    extend self

    AVAILABLE_FILTERS = [:categories, :sports]

    FilterItem = Struct.new(:id, :name, :slug, :count)

    def brands(manufacturer_ids)
      Manufacturer.select([:id, :name, :logo_file_name, :logo_updated_at, :slug])
                  .where(id: manufacturer_ids)
                  .sort_by!{ |manufacturer| manufacturer.name.downcase }
    end

    def filter(search)
      @search = search

      filter = AVAILABLE_FILTERS.each_with_object({}) do |kind, result|
        facet = get_facet(kind.to_s)
        next if facet.nil?
        result[kind] = { title: I18n.t("mkp.brands.filter.#{kind}"), items: [] }
        filter_items = get_filter_for_facet(kind.to_s)
        filter_items.each do |element|
          item = { id: element.id, name: element.name.html_safe, slug: element.slug }
          result[kind][:items] << item
        end
      end

      filter
    end

    def active_filter(params)
      if params[:c].present?
        Category.select(:name).find(params[:c])
      elsif params[:sp].present?
        Sport.select(:name).find(params[:sp])
      end
    end

    private

    def get_facet(facet_matcher)
      @search.facets.select{ |f| f.name =~ /#{facet_matcher}/ }.first
    end

    def get_filter_for_facet(facet_matcher)
      facet = get_facet(facet_matcher)
      counter = facet.rows.each_with_object({}){ |f, h| h[f.value] = f.count }
      items = self.send("get_#{facet_matcher}", counter.keys)
      items.map! do |item|
        FilterItem.new( item.id, item.name, item.slug, counter[item.id] )
      end
    end

    # This way we have to add all the get_#{filter_name} that we want
    def get_categories(ids)
      Category.select([:id, :name, :slug])
              .where(id: ids, ancestry: nil)
              .sort_by!{ |category| category.name.downcase }
    end

    def get_sports(ids)
      Sport.select([:id, :name, :slug])
           .where(id: ids.first(5))
           .sort_by! { |sport| sport.name.downcase }
    end

  end
end

module Mkp
  class Invoice < ActiveRecord::Base
		serialize :gateway_data, Hash
		serialize :gateway_request, Hash

    belongs_to :order, class_name: 'Order'

    validates :amount, presence: true
    validates :amount, numericality: true

    def generate!
      return unless (invoice = gateway.process(order)).present?

      update_attributes!({
        amount: invoice.total_charged,
        gateway_object_id: invoice.id,
        gateway_data: invoice.data.merge(number: invoice.number),
      })
      order.reload
      Accounting::InvoiceMailer.send_invoice_to_customer(gateway.get_invoice_url(order), order).deliver

    end

    def number
      @number ||= gateway_data[:number]
    end

    private

    def gateway
      return unless attributes['gateway'].present?
      @gateway ||= "Gateways::Invoices::#{attributes['gateway']}".constantize
    rescue
    end

	end
end

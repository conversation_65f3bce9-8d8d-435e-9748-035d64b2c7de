module Mkp
  class Guest < ActiveRecord::Base
    include UserMethods

    has_many :addresses,
             class_name: 'Address',
             as: :addressable,
             dependent: :destroy

    has_many :carts,
             class_name: '<PERSON>t',
             as: :customer,
             dependent: :destroy

    has_many :orders,
             class_name: 'Order',
             as: :customer

    serialize :braintree_data, Hash
    serialize :mercadopago_data, Hash

    validates :email, presence: true
    validates :network, presence: true
    validate :email_format
    scope :search, ->(query) do
      return if query.blank?

      num_or_conds = 3
      terms = query.downcase.split(/\s+/).map do |e|
        (e.gsub('*', '%') + '%').gsub(/%+/, '%')
      end
      sql = terms.map do
        '(LOWER(mkp_guests.first_name) LIKE ? OR LOWER(mkp_guests.last_name)'\
        ' LIKE ? OR LOWER(mkp_guests.email) LIKE ?)'
      end.join(' AND ')
      where(sql, *terms.map { |e| [e] * num_or_conds }.flatten)
    end

    def cart(network)
      Cart.cart(carts, network)
    end

    def provider
      'guest'
    end

    # def telephone
    #   return addresses.first.telephone if addresses.present?
    # end

    protected

    def email_format
      errors.add(:base, 'Invalid Email') unless EmailUtils.valid_email_addresses?(email)
    end
  end
end

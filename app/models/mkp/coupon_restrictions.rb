module Mkp
  # Represents restrictions on the applicability of a coupon: which categories
  # and manufacturers it can be applied on, and which users are allowed to apply
  # it.
  class CouponRestrictions
    def initialize(hash)
      hash_with_integers = convert_ids_to_integer(hash)
      @restrictions_hash = HashWithIndifferentAccess.new(hash_with_integers)
    end

    def applies_to?(product)
      raise Mkp::Coupon::InvalidProductError.new(name: product.title) \
        unless includes_id?(product.id, 'products')
      raise Mkp::Coupon::InvalidCategoryError.new(name: product.category.full_path_name) \
        unless includes_category?(product.category)
      raise Mkp::Coupon::InvalidManufacturerError.new(name: product.manufacturer.name) \
        unless includes_id?(product.manufacturer.id, 'manufacturers')
      raise Mkp::Coupon::InvalidShopError.new(name: product.manufacturer.name) \
        unless includes_id?(product.shop.id, 'shops')

      true
    end

    def what_does_it_apply_to?(product)
      inclusion_list = @restrictions_hash["included_products"]
      return 'product' if inclusion_list && inclusion_list.include?(product.id)

      inclusion_list = @restrictions_hash["included_manufacturers"]
      return 'manufacturer' if inclusion_list && inclusion_list.include?(product.manufacturer.id)

      inclusion_list = @restrictions_hash["included_shops"]
      return 'shop' if inclusion_list && inclusion_list.include?(product.shop.id)

      if (inclusion_list = @restrictions_hash[:included_categories])
        inclusion_list.map!(&:to_i)
        if inclusion_list.include?(product.category.id) || category_descends_from_any_of?(product.category.id, inclusion_list)
          return 'category'
        end
      end

      'not_apply'
    end


    def can_be_applied_by?(user)
      raise Mkp::Coupon::UserExcludedError \
        unless includes_id?(user.id, 'users')
      true
    end

    def to_hash
      @restrictions_hash
    end

    private

    def convert_ids_to_integer(restrictions_hash)
      restrictions_hash.each_with_object({}) do |(list_name, list), result|
        result[list_name] = list.is_a?(Array) ? list.map(&:to_i) : list.split(',').map(&:to_i)
      end
    end

    def includes_category?(category)
      category_id = category.presence && category.id

      if (inclusion_list = @restrictions_hash[:included_categories])
        inclusion_list.map!(&:to_i)

        if inclusion_list.include?(category_id) || category_descends_from_any_of?(category_id, inclusion_list)
          return true
        else
          return false
        end
      end

      if (exclusion_list = @restrictions_hash[:excluded_categories])
        exclusion_list.map!(&:to_i)

        if exclusion_list.include?(category_id) || category_descends_from_any_of?(category_id, exclusion_list)
          return false
        end
      end

      true
    end

    def includes_id?(id, list_name)
      inclusion_list = @restrictions_hash["included_#{list_name}"]
      return inclusion_list.include?(id) if inclusion_list

      exclusion_list = @restrictions_hash["excluded_#{list_name}"]
      return !exclusion_list.include?(id) if exclusion_list

      true
    end

    def category_descends_from_any_of?(category_id, categories_ids_to_match)
      ancestors_ids = Category.ancestors_of(category_id).pluck(:id)
      (ancestors_ids & categories_ids_to_match).present?
    end
  end
end

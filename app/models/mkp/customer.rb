module Mkp
  class Customer < ActiveRecord::Base
    include UserMethods
    # Include default devise modules.
    devise :database_authenticatable, :registerable,
           :recoverable, :rememberable, :trackable

    belongs_to :store
    has_many :addresses, class_name: 'Address', as: :addressable, dependent: :delete_all
    has_many :coupons
    has_one :wishlist
    has_one :member, class_name: 'Member', dependent: :delete
    has_one :subscription, class_name: 'Subscription', dependent: :delete
    has_many :carts, class_name: 'Mkp::Cart', as: :customer, dependent: :delete_all
    has_many :identities, foreign_key: :user_id, dependent: :delete_all
    has_many :sube_cards, foreign_key: :mkp_customer_id, dependent: :delete_all
    has_many :orders, dependent: :delete_all
    enum gender: [:male, :female]
    enum doc_type: [:DNI, :DU, :LC, :LE, :PAS, :CUIT, :CUIL]
    enum macro_category: [:regular, :selecta]

    validates :email, uniqueness: {scope: :store}
    validates :password, :password_confirmation, presence: true, on: :create
    validates :password, confirmation: true
    validates_presence_of :store
    validates_presence_of :subscription, :if => :is_tdd?

    before_create :build_default_wishlist

    accepts_nested_attributes_for :addresses, allow_destroy: true, reject_if: :all_blank
    accepts_nested_attributes_for :member, allow_destroy: true, reject_if: :all_blank
    accepts_nested_attributes_for :subscription, allow_destroy: true

    scope :search, ->(query) do
      return if query.blank?
      num_or_conds = 3
      terms = query.downcase.split(/\s+/).map do |e|
        (e.gsub('*', '%') + '%').gsub(/%+/, '%')
      end
      sql = terms.map do |term|
        "(LOWER(mkp_customers.first_name) LIKE ? OR LOWER(mkp_customers.last_name) LIKE ? OR LOWER(mkp_customers.email) LIKE ?)"
      end.join(' AND ')
      where(sql, *terms.map{ |e| [e] * num_or_conds }.flatten)
    end

    def providers
      identities.map(&:provider)
    end

    def points
      return 0 unless user_identify.presence
      SystemPoints::User.new(self).points
    end

    def is_select_user?
      selecta?
    end

    private

    def build_default_wishlist
      build_wishlist
      true
    end

    def sp_create
      SystemPoints::Worker::GenerateUserWorker.perform_async(self.id) if store.system_point_availability?
    end

    def is_tdd?
      store.id.eql?(14)
    end

    def is_member?
      return unless store.id.eql?(14)
      return unless doc_number.present? && gender.present?
      response = Boca::Client.post_member_validation(self)
      response.try(:[], "es_socio")
    end
  end
end

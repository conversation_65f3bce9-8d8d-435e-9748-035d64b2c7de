module Mkp
  class ShippingMethod < ActiveRecord::Base
    include Concerns::<PERSON><PERSON><PERSON><PERSON>

    belongs_to :zone, class_name: 'Zone', unscoped: true

    validates :zone_id, presence: true
    validates :service, presence: true
    validates :title, presence: true

    delegate :warehouse, to: :zone
    delegate :shop, to: :zone

    validate :setup_required

    store :setup, accessors: [:carriers, :price, :speeds, :states]

    default_scope { where('mkp_shipping_methods.deleted_at IS NULL') }

    after_destroy :notify_shop
    before_validation :set_title, if: :easypost?

    SERVICES = [
      'Easypost',
      'Express',
      'Fixed',
      'Oca'
    ]

    # This each creates all the variables/methods for the services
    # i.e. For 'Fixed' will create:
    # Constant: FIXED
    # Method: fixed?
    SERVICES.each do |service_name|
      const_set(service_name.upcase, service_name)
      define_method(service_name.downcase + '?') { service == service_name }
    end

    def has_multiple_options?
      easypost?
    end

    def has_label?
      easypost?
    end

    def enable_to_ship?(from_country, to_country)
      return true unless easypost?

      is_international = from_country != to_country

      if is_international
        speeds.any? { |speed| Gateways::Labels::Easypost::INTERNATIONAL_SPEEDS.include?(speed) }
      else
        speeds.any? { |speed| Gateways::Labels::Easypost::DOMESTIC_SPEEDS.include?(speed) }
      end
    end

    protected

    def setup_required
      if express? && (price.blank? || price.to_f < 0)
        errors.add(:setup, 'Please fill price number')
      end
    end

    private

    def notify_shop
      zone.shop.shipping_method_or_address_destroyed
    end

    def set_title
      self.title = 'Speed of Delivery Options'
    end
  end
end

module Mkp
  class Card < ActiveRecord::Base
    include RecurrentPayment::Base
    belongs_to :member

    def security_number
      member.code.try(:decrypt, STRING_SECURITY)
    end

    def number
      member.token.try(:decrypt, STRING_SECURITY)
    end

    def self.type_cards
      brands = %w(visa mastercard amex)
      cards = brands.map do |brand|
        Mkp::Card.new(brand: brand, card_type: 'CREDIT')
      end
      cards << Mkp::Card.new(brand: 'visa', card_type: 'DEBIT')
    end
  end
end

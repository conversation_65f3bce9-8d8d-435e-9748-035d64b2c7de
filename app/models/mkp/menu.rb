module Mkp
  class Menu < ActiveRecord::Base
    has_paper_trail
    include ::Concerns::Avenidable

    serialize :structure
    scope :active_by_network, ->(network) { where(active: true, network: network) }

    validates :name, :url, :network, presence: true

    class << self
      def timestamp
        @timestamp ||= Menu.maximum(:updated_at).to_i
      end

      def get(country_code)
        Rails.cache.fetch ['avenida_menu3', country_code, timestamp].join('/') do
          items = Menu.select('slug, name_display, display_order, structure, url')
                      .where('active = ? AND network = ?', true, country_code)
                      .order('mkp_menus.display_order ASC')
                      .all

          arranged_items = []
          items.each do |item|
            decorated_item = {
              slug:  item.slug,
              title: item.name_display,
              url:   item.url,
              order: item.display_order,
              childs: [],
            }

            if item.structure.present?
              if item.structure.key?(:menu_display)
                decorated_item = self.brand_menu_builder(item, decorated_item)
                decorated_item.store(:menu_display, 'icon_menu')
              else
                decorated_item = self.category_menu_builder(item, decorated_item)
              end
            end

            arranged_items << decorated_item
          end

          arranged_items
        end
      end

      # There is a need of refactor this
      # This method shares functionality with the
      # We need to add some specs
      # self.get method
      def prepare_menu(store_id)
        items = Menu.select('slug, name_display, display_order, structure, url')
          .where('active = ? AND network = ? AND store_id = ?', true, "AR", store_id)
          .order('mkp_menus.display_order ASC')

        arranged_items = []
        items.each do |item|
          decorated_item = {
            slug:  item.slug,
            title: item.name_display,
            url:   item.url,
            order: item.display_order,
            childs: [],
          }

          if item.structure.present?
            if item.structure.key?(:menu_display)
              decorated_item = self.brand_menu_builder(item, decorated_item)
              decorated_item.store(:menu_display, 'icon_menu')
            else
              decorated_item = self.category_menu_builder(item, decorated_item)
            end
          end

          arranged_items << decorated_item
        end

        arranged_items
      end

      def set(network = 'AR')
        ## PARA LOS GENEROS PADRE QUE MUESTRO EN EL MENU ##
        gender_items = Hash.new(0)
        if network.downcase == 'us'
          gender_items[:men]   = ::Mkp::Gender.find_by_name("Men")
          gender_items[:women] = ::Mkp::Gender.find_by_name("Women")
          gender_items[:kids]  = ::Mkp::Gender.find_by_name("Kids")
        else
          gender_items[:hombres] = ::Mkp::Gender.find_by_name("Hombres")
          gender_items[:mujeres] = ::Mkp::Gender.find_by_name("Mujeres")
          gender_items[:ninos]  = ::Mkp::Gender.find_by_name("Niños")
        end

        ## ITERO SOBRE LOS GENEROS PADRE QUE MUESTRO EN EL MENU ##
        gender_items.each do |key, gender|
          if gender.present?
            menu_item = self.find_by_slug(gender.slug)
            if menu_item.blank?
              menu_item = self.new
              menu_item.name = gender.name
              menu_item.name_display = gender.name
              menu_item.slug = gender.slug
              menu_item.url = UrlMapperHelper.absolute_mapper_path(gender, network.downcase)
              menu_item.network = network
            end

            tree = gender.categories

            if gender.is_root?
              tree = Category.categories_for_gender(gender, network).all
            end

            menu_item.structure = self.build_categories_structure(tree, 0, gender, network)
            menu_item.save
          end
        end

        ## PARA LOS TIPOS DE PRODUCTO PADRE QUE MUESTRO EN EL MENU ##
        categories = Category.find(:all,
                      conditions: ['mkp_categories.network = ? AND mkp_categories.active = ?', network, true],
                      joins: 'RIGHT JOIN mkp_products ON mkp_categories.id = mkp_products.category_id',
                      order: 'mkp_categories.display_order ASC',
                      group: 'mkp_categories.id')

        pt_groups = Hash.new(0)
        categories.each do |pt|
          r = pt.root
          if pt_groups.key?(r.slug)
            pt_groups[r.slug][:childs] << pt
          else
            pt_groups[r.slug] = Hash[:childs => []]
            pt_groups[r.slug][:childs] << pt
            menu_item = self.find_by_slug(r.slug)
            if menu_item.blank?
              menu_item = self.new
              menu_item.name = r.name
              menu_item.name_display = r.name
              menu_item.slug = r.slug
              menu_item.url = UrlMapperHelper.absolute_mapper_path(r, network.downcase)

              menu_item.network = network
              menu_item.save
            end
          end
        end

        pt_groups.each do |key, ptg|
          menu_item = self.find_by_slug(key)
          menu_item.structure = self.build_categories_structure(ptg[:childs], 1, nil, network)
          menu_item.save
        end

        ## FOR MARCAS/BRANDS ##
        if network.downcase == 'us'
          menu = self.find_by_slug('brands')
        else
          menu = self.find_by_slug('marcas')
        end
        unless menu.nil?
          menu[:structure] = self.build_manufacturers_structure(network)
          menu.save
        end
      end
    end

    def self.build_mailer_menu(network)
      mail_menu_setup = Network.for(network).mail_menu_setup
      self.build_mailer_menu_with_network(network, mail_menu_setup['limit'], mail_menu_setup['extra'])
    end

    def self.build_mailer_menu_with_network(network, limit, name)
      menu = Mkp::Menu.active_by_network(network).order("display_order asc").limit(limit)
      menu <<  Mkp::Menu.find_by_slug(name) if name.present?
      menu.each do |item|
        item.url = "http://#{item.url}"
      end unless menu.all?(&:blank?)
    end


    def structure
      super || {}
    end

    private

    def self.build_categories_structure(treeable, level, model, network)
      structure = Hash.new(0)

      treeable.each do |t|
        if level == 0
          first = t.root
          second = (t.depth > 1) ? t.ancestors.to_depth(t.depth)[1] : t
        else
          first = (t.depth > level) ? t.ancestors.to_depth(t.depth)[level] : t
          second = (t.depth > (level+1)) ? t.ancestors.to_depth(t.depth)[level + 1] : ((t.depth == (level+1)) ? t : nil)
        end

        if structure.key?(first.slug)
          unless second.nil?
            if !structure[first.slug][:childs].key?(second.slug)
              structure[first.slug][:childs][second.slug] = Hash[:id => second.id, :slug => second.slug, :name => second.name, :order => second.display_order]
              models = model.present? ? [second, model] : second
              structure[first.slug][:childs][second.slug][:url] = UrlMapperHelper.absolute_mapper_path(models, network.downcase)
            end
          end
        else
          models = model.present? ? [first, model] : first
          structure[first.slug] = Hash[:id => first.id, :slug => first.slug, :name => first.name, :childs => {}, :order => first.display_order]
          structure[first.slug][:url] = UrlMapperHelper.absolute_mapper_path(models, network.downcase)
          unless second.nil?
            models = model.present? ? [second, model] : second
            structure[first.slug][:childs][second.slug] = Hash[:id => second.id, :slug => second.slug, :name => second.name, :order => second.display_order]
            structure[first.slug][:childs][second.slug][:url] = UrlMapperHelper.absolute_mapper_path(models, network.downcase)
          end
        end
      end
      structure
    end

    def self.build_manufacturers_structure(network)
      search = Mkp::Variant.search(include: [product: [:manufacturer]]) do
        with :network, network
        with :deleted, false
        with :shop_visible, true
        with :shop_deleted, false
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now
        facet :manufacturer_id, limit: -1, sort: :count
      end

      manufacturer_ids = search.facet(:manufacturer_id).rows.map(&:value).take(24)
      brands = Mkp::BrandsFilter.brands(manufacturer_ids)

      structure = { menu_display: 'icon_menu', childs: [] }

      brands.each do |b|
        hash = {
          id: b.id,
          slug: b.slug,
          name: b.name,
          icon: b.logo.url(:st),
        }
        structure[:childs] << hash
      end

      structure
    end

    def self.brand_menu_builder(item, decorated_item)
      childs = item.structure[:childs]
      childs.each do |sorted_item_submenu|
        decorated_submenu = {
          id:     sorted_item_submenu[:id],
          name:   sorted_item_submenu[:name],
          slug:   sorted_item_submenu[:slug],
          icon:   sorted_item_submenu[:icon]
        }
        decorated_item[:childs] << decorated_submenu
      end
      decorated_item
    end

    def self.category_menu_builder(item, decorated_item)
      childs = item.structure.values

      childs.sort! { |a,b| a[:slug] <=> b[:slug] }
      childs.each do |sorted_item_submenu|
        decorated_submenu = {
          id:     sorted_item_submenu[:id],
          name:   sorted_item_submenu[:name],
          slug:   sorted_item_submenu[:slug],
          url:    sorted_item_submenu[:url],
          childs: []
        }

        if sorted_item_submenu[:childs].present?
          _childs = sorted_item_submenu[:childs].values.sort do |a,b|
            a[:slug] <=> b[:slug]
          end
          decorated_submenu[:childs] = _childs
        end
        decorated_item[:childs] << decorated_submenu
      end
      decorated_item
    end

  end
end

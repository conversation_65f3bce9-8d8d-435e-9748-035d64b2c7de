module Mkp
  class ShipmentLabel < ActiveRecord::Base
    self.table_name = :mkp_shipment_labels

    serialize :gateway_data
    serialize :activity_log, Array

    belongs_to :shipment

    scope :active, -> { where('cancelled_at IS NULL') }
    scope :inactive, -> { where('cancelled_at IS NOT NULL') }

    def purchase!
      label_info = actual_gateway.purchase(shipment, id, courier.downcase.to_sym) if courier.present?
      return nil if label_info.nil?
      return update_label(label_info) unless label_info.class == Array

      update_label(label_info.shift)
      label_info.map{ |label| self.dup.update_label(label) }
    end

    def update_label(label_info)
      update_attributes!({
        tracking_number: label_info.tracking_code,
        courier: label_info.selected_rate.carrier,
        gateway_object_id: label_info.id,
        gateway_data: label_info.to_hash.deep_symbolize_keys ,
        price: label_info.selected_rate.rate.to_f
      })
    end

    def cancel!
      return update_attributes!(cancelled_at: Time.now) unless actual_gateway.present? && gateway_object_id.present?
      if (cancelled_payload = actual_gateway.cancel(gateway_object_id)).present?
        update_attributes!({
          cancelled_at: Time.now,
          gateway_data: gateway_data.merge({
            cancelled_payload: cancelled_payload.to_hash.deep_symbolize_keys
          })
        })
      end
    end

    def ready?
      return true unless courier != "other"
      tracking_number.present?
    end

    def tracking_url
      settings = Couriers.get_settings_for(courier)
      return '' unless settings.present?
      settings.direct_url ? "#{settings.url + tracking_number}" : settings.url
    end

    def cancellable?
      return false unless gateway.present?
      actual_gateway.respond_to? :cancel
    end

    def url(format = :pdf)
      return unless gateway.present? && gateway_data.present?
      actual_gateway.present? ? actual_gateway.get_url(gateway_data, format) : gateway_data['label_url']
    end

    def extra_docs
      return unless gateway.present? && gateway_object_id.present?
      actual_gateway.present? ? actual_gateway.get_extra_docs(gateway_object_id, gateway_data) : nil
    end

    def update_status(status)
      return unless gateway.present?
      status = actual_gateway.get_status(status)
      Mkp::StatusChange::EntityStatusManage.status_change(shipment,status)
    end

    def thermal_formats?
      return false unless actual_gateway.present?
      actual_gateway.allow_thermal_formats?
    end

    def icon
      Couriers.get_settings_for(courier).try(:icon)
    end

    private

    def actual_gateway
      return unless gateway.present?
      @actual_gateway ||= "Gateways::Labels::#{gateway}".constantize
    rescue
    end
  end
end

module Mkp
  class Shipment < ActiveRecord::Base
    has_paper_trail
    serialize :extra_info, Hash
    serialize :destination_address, OpenStruct
    serialize :origin_address, OpenStruct
    serialize :gateway_data, Hash

    belongs_to :suborder, class_name: 'Mkp::Suborder'
    has_and_belongs_to_many :items,
                            class_name: 'Mkp::OrderItem',
                            join_table: :mkp_shipment_items,
                            foreign_key: :shipment_id,
                            association_foreign_key: :order_item_id

    has_many :labels,
             class_name: 'Mkp::ShipmentLabel',
             foreign_key: :shipment_id,
             dependent: :destroy

    has_many :entity_status_changes,
             class_name: 'Mkp::StatusChange::EntityStatusChange',
             as: :entity,
             dependent: :destroy
    alias_attribute :status_changes, :entity_status_changes

    belongs_to :sale_item, polymorphic: true

    # esta validación falla para envios viejos donde no esta la doble relación consistente.
    # validates :suborder, :items, presence: true, if: -> {items.all {|item| suborder.items.flatten.include? item}}

    scope :charged, -> { where('bonified_amount > 0') }

    STATUS = %w[unfulfilled in_process shipped delivered cancelled returned not_delivered].freeze
    CANCELLABLE_STATUS = %w[unfulfilled in_process delivered].freeze
    PICKABLE_STATUS = %w[unfulfilled delivered]

    STATUS.each do |status|
      scope status, -> { where(status: status) }

      define_method("#{status}?".to_sym) do
        attributes['status'] == status
      end
    end

    def self.options_for_select
      STATUS.map { |state| [state.titleize, state] }
    end

    searchable do
      integer :id

      string :status

      time :created_at, trie: true

      boolean :fulfilled_by_gp do
        fulfilled_by_gp?
      end

      text :suborder_public_ids

      string :store_id do
        try(:order).try(:store_id).presence
      end

      text :customer_data do
        if order.present? && (customer = order.customer).present?
          [customer.full_name, customer.email].join(' ')
        end
      end

      integer :payment_status do
        order.payment_status if order.present?
      end

      integer :label_ids, multiple: true do
        labels.map(&:id).compact
      end

      text :shipment_id do
        # Keeping this because of https://github.com/sunspot/sunspot/issues/331
        # and until the PR https://github.com/sunspot/sunspot/pull/784 is merged
        # or resolved in anyway
        attributes['id'].to_s
      end

      text :shipment_gateway_object_id do
        gateway_object_id.to_s if gateway_object_id.present?
      end

      text :order_title do
        order.title if order.present?
      end

      time :order_created_at, trie: true do
        order.created_at if order.present?
      end
    end

    def in_process?
      status == 'in_process' && has_labels?
    end

    def processed_at
      return label.created_at if gateway.blank?

      gateway.processed_at(gateway_data)
    end

    def has_bonification?
      bonified_amount > 0
    end

    def has_labels?
      labels.active.present?
    end

    def label
      labels.active.last
    end

    def destination_address
      return if super == OpenStruct.new

      super
    end

    alias address destination_address

    def origin_address
      return if super == OpenStruct.new

      super
    end

    def fulfilled_by_gp?
      return false if suborders.blank?

      processed_with_gateway? || suborders.all?(&:fulfilled_by_gp?)
    end

    def suborders
      items.map(&:suborder).uniq
    end

    def order
      items.first.order if items.present?
    end

    def pickeable?
      true # hardcoded, logic must be overridden
    end

    def related_shops
      items.flat_map { |item| item.product.shop }.uniq
    end

    def process_with_gateway
      return if gateway.blank?

      gateway.process(self)
    end

    def processed_with_gateway?
      gateway.present?
    end

    def gateway_name
      attributes['gateway'] || '-'
    end

    def is_pickup?
      extra_info[:has_pickup].present? && extra_info[:has_pickup]
    end

    def outgoing?
      direction == 'outgoing'
    end

    def suborder_public_ids
      suborders.map(&:public_id).join(' ')
    end

    def shipment_kind_label
      case shipment_kind
      when 'exchange_refund' then 'Cambio - cliente a proveedor'
      when 'exchange_change' then 'Cambio - proveedor a cliente'
      when 'refund' then 'Devolución'
      when 'virtual' then 'Virtual'
      when 'pickup' then 'Pickup'
      else
        'Envio'
      end
    end

    def virtual?
      shipment_kind == 'virtual'
    end

    def pickable?
      shipment_kind == 'pickup'
    end

    def not_delivered?
      status != 'delivered'
    end

    def courier_name
      if has_labels?
        label.courier
      else
        if gateway_data && gateway_data[:carrier].present?
          gateway_data[:carrier]
        else
          gateway_name
        end
      end
    end

    def status_last_updated_at
      date = updated_at.strftime('%d-%m-%Y %H:%M')
      if status_changes.any?
        date = status_changes.order(:created_at).last.created_at.strftime('%d-%m-%Y %H:%M')
      end

      date
    end

    def shop_email
      suborders.first.shop.owner.email
    end

    def notify_shipped_to_customer
      Mkp::ShipmentMailer.delay.notify_shipped_to_user(self)
    end

    def notify_delivered_to_customer
      Mkp::ShipmentMailer.delay.notify_delivered_to_customer(self)
    end

    def notify_delivered_to_shop
      Mkp::ShipmentMailer.delay.notify_delivered_to_shop(self)
    end

    def notify_not_delivered_to_customer
      Mkp::ShipmentMailer.delay.notify_not_delivered_to_customer(self)
    end

    def notify_not_delivered_to_shop
      Mkp::ShipmentMailer.delay.notify_not_delivered_to_shop(self)
    end

    def destination_full_name
      "#{address.first_name} #{address.last_name}"
    end

    def destination_full_address
      full_address = "#{address.address} #{address.street_number}"
      full_address << ", #{address.city}, #{address.state}, #{address.country}"
    end

    def destination_street_number
      "#{address.address} #{address.street_number}"
    end

    def destination_city
      address.city
    end

    def destination_state
      address.state
    end

    def pickable_and_not_delivered?
      pickable? && not_delivered?
    end

    def billing_address
      address.billing_address
    end

    private

    def gateway
      return @gateway if @gateway.present?

      if attributes['gateway'].present?
        if attributes['gateway'].downcase == 'krabpack'
          @gateway ||= "Avenida::Krabpack".constantize
        else
          @gateway ||= "Gateways::Shipments::#{attributes['gateway']}".constantize
        end
      end
    rescue StandardError
    end
  end
end

module Mkp
  class CarrierStore < ActiveRecord::Base
    has_paper_trail
    validates_presence_of :zip_from
    validates_presence_of :zip_to
    validates_presence_of :gateway
    validates_numericality_of :weight_min, less_than: Proc.new { |c| c.weight_max }, allow_blank: true
    validates_length_of :delivery_message, maximum: 150

    enum operative: [:door_to_door, :door_to_local]

    belongs_to :store
    belongs_to :shop

    delegate :name, to: :shop, allow_nil: true, prefix: false

    scope :with_zip, ->(zip) { where("? BETWEEN zip_from AND zip_to", zip) }
    scope :with_weight, ->(weight) { where("? BETWEEN weight_min AND weight_max", weight) }
    scope :with_shop_id, ->(shop_id) { where(shop_id: shop_id) }

    def icon
      Couriers.get_settings_for(gateway).try(:icon)
    end
  end
end


module Mkp
  class Export < ActiveRecord::Base
    has_attached_file :csv_file
    do_not_validate_attachment_file_type :csv_file

    belongs_to :store, foreign_key: :store_id, class_name: 'Mkp::Store', required: false # rubocop:disable Rails/InverseOf
    belongs_to :shop, foreign_key: :shop_id, class_name: 'Mkp::Shop', required: false

    enum export_type: { products: 0, orders: 1, invoice_items: 2 }
    #delegate :token, to: [:store, :shop]

    enum owner: {
        pioneer: 0,
        lux: 1
    }

    def exist_url?
      return false unless url?

      path = "public/#{url.split('/').last}"
      File.exist?(path)
    end

    def token
      store.present? ? "#{self.id}_#{store.token}" : "#{self.id}_#{shop.stores.first.token}"
    end
  end
end

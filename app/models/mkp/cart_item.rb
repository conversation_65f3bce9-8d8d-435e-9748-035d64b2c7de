module Mkp
  class CartItem < ActiveRecord::Base
    belongs_to :cart
    belongs_to :variant, unscoped: true
    belongs_to :currency, class_name: 'Currency'
    has_many :payments

    validates :quantity, presence: true
    validates :quantity, numericality: { only_integer: true, greater_than: 0 }
    validates :variant_id, presence: true
    validates :cart_id, presence: true

    delegate :product, to: :variant
    delegate :title, :name, to: :variant

    def hs_tariff_number
      ''
    end

    def name
      variant.title
    end

    def on_sale?
      ProductSaleDetector.is_on_sale?(variant.product)
    end

    def price
      variant.product.regular_price
    end

    def sale_price
      ProductSaleDetector.calculate(variant.product) if on_sale?
    end

    def shop
      variant.shop
    end

    def total
      quantity * ( on_sale? ? sale_price : price )
    end

    def update_quantity_and_attrs(new_quantity, variant)
      return if quantity == new_quantity.to_i

      update_attribute(:quantity, new_quantity)
    end
  end
end

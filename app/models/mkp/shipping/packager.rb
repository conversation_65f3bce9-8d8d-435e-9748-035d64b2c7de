module Mkp
  module Shipping
    class Packager
      DEFAULT_MASS_UNIT   = :pounds.freeze
      DEFAULT_LENGTH_UNIT = :inches.freeze
      DEFAULT_VOLUME_UNIT = :cubic_meters.freeze

      DEFAULT_WEIGHT_VALUE = 1.freeze
      DEFAULT_LENGTH_VALUE = 8.freeze

      OVERRIDES_MANDATORY_KEYS = [:weight, :height, :width, :length, :mass_unit, :length_unit]

      def initialize(items, override_setup = {})
        @override_package = override_values_on_default_units(override_setup)
        @items            = items
        @box              = box_with_default_units
        @weight           = weight_on_default_units
      end

      # Return by default:
      # - weights in POUNDS
      # - sizes in   INCHES
      # - volume in  CUBIC_METERS
      def get_package_values(opts = {})
        mass_unit   = opts[:mass]   || DEFAULT_MASS_UNIT
        length_unit = opts[:length] || DEFAULT_LENGTH_UNIT
        volume_unit = opts[:volume] || DEFAULT_VOLUME_UNIT
        {
          height: @box[:height].to.send(length_unit).value.to_f.round(2),
          length: @box[:length].to.send(length_unit).value.to_f.round(2),
          width:  @box[:width].to.send(length_unit).value.to_f.round(2),
          volume: @box[:volume].to.send(volume_unit).value.to_f.round(5),
          weight: @weight.to.send(mass_unit).value.to_f.round(2)
        }
      end

      private

      def override_values_on_default_units(setup = {})
        return unless setup.present? && OVERRIDES_MANDATORY_KEYS.all?{ |k| setup[k].present? }
        mass_unit = valid_unit(:mass, setup[:mass_unit])
        length_unit = valid_unit(:distance, setup[:length_unit])
        return if mass_unit.nil? || length_unit.nil?

        height = unitless_length_value_on_default_unit(setup[:height].send(length_unit))
        width  = unitless_length_value_on_default_unit(setup[:width].send(length_unit))
        length = unitless_length_value_on_default_unit(setup[:length].send(length_unit))
        volume = unitless_volume_value_on_default_unit(height, width, length)

        {
          volume: volume,
          height: height,
          width:  width,
          length: length,
          weight: unitless_weight_value_on_default_unit(setup[:weight].send(mass_unit))
        }
      end

      def bigger_box
        @items.map do |item|
          item_dimensions(item)
        end.max_by{|box| box[:volume]}
      end

      def box_with_default_units
        box = @override_package.present? ? @override_package : bigger_box
        {
          volume: box[:volume].send(DEFAULT_VOLUME_UNIT),
          height: box[:height].send(DEFAULT_LENGTH_UNIT),
          width:  box[:width].send(DEFAULT_LENGTH_UNIT),
          length: box[:length].send(DEFAULT_LENGTH_UNIT)
        }
      end

      def item_dimensions(item)
        product = item.variant.product

        height = unitless_length_value_on_default_unit(product.height_with_unit)
        width  = unitless_length_value_on_default_unit(product.width_with_unit)
        length = unitless_length_value_on_default_unit(product.length_with_unit)
        volume = unitless_volume_value_on_default_unit(height, width, length)
        {
          volume: volume,
          height: height,
          width:  width,
          length: length
        }
      end

      def weight_on_default_units
        box_weight = @override_package.present? ? @override_package[:weight] : items_weight

        box_weight.send(DEFAULT_MASS_UNIT)
      end

      def items_weight
        @items.map do |item|
          product = item.variant.product
          weight_value_per_unit = unitless_weight_value_on_default_unit(product.weight_with_unit)

          weight_value_per_unit * item.quantity.to_i
        end.compact.sum.to_f
      end

      def unitless_length_value_on_default_unit(measure_with_unit)
        if measure_with_unit.value <= 0
          DEFAULT_LENGTH_VALUE.to_f
        else
          measure_with_unit.to.send(DEFAULT_LENGTH_UNIT).value.to_f
        end
      end

      def unitless_weight_value_on_default_unit(weight_with_unit)
        if weight_with_unit.value <= 0
          DEFAULT_WEIGHT_VALUE.to_f
        else
          weight_with_unit.to.send(DEFAULT_MASS_UNIT).value.to_f
        end
      end

      def unitless_volume_value_on_default_unit(height, width, length)
        height_value_in_meters = height.send(DEFAULT_LENGTH_UNIT).to.meters.value.to_f
        width_value_in_meters = width.send(DEFAULT_LENGTH_UNIT).to.meters.value.to_f
        length_value_in_meters = length.send(DEFAULT_LENGTH_UNIT).to.meters.value.to_f

        (height_value_in_meters * width_value_in_meters * length_value_in_meters).to_f
      end

      def valid_unit(category, unit_name)
        return unless unit_name.present?
        Alchemist.library.unit_names(category).detect{ |unit| unit == unit_name.to_sym }
      end
    end
  end
end

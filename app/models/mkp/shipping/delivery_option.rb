module Mkp
  module Shipping
    class DeliveryOption
      def self.build(id)
        id_parts = id.split(':')
        type = id_parts.shift

        if type == 'shipping'
          Shipping.build(id_parts)
        elsif type == 'pickup'
          Pickup.build(id_parts)
        else
          raise ArgumentError, 'ID must start with either shipping or pickup'
        end
      end

      def initialize(attributes = {})
        attributes.each { |name, value| send("#{name}=", value) }
      end

      def pickup?
        is_a?(Pickup)
      end

      def shipping?
        is_a?(Shipping)
      end

      class Shipping < DeliveryOption
        attr_accessor :charge,
                      :title,
                      :service_level,
                      :carrier,
                      :shop_id,
                      :service_level_real,
                      :external_shipment_id,
                      :external_rate_id

        def initialize(options = {})
          super(options)
        end

        def self.build(id_parts)
          service_level = id_parts.second
          carrier = id_parts.third
          new(service_level: service_level,
              carrier: carrier)
        end

        alias_method :shipment_id, :external_shipment_id
        alias_method :rate_id, :external_rate_id

        def id
          result = service_level.present? ? "#{service_level}" : ""
          result += ":#{carrier}" if carrier
          result += ":#{charge}" if charge
          result
        end

        def to_s
          result = service_level.present? ? "#{service_level}" : ""
          result += " #{carrier}" if carrier
          result += ":#{charge}" if charge
          result
        end

        def as_json(options)
          {
            charge: charge,
            title: title,
            service_level: service_level,
            carrier: carrier,
            shop_id: shop_id,
            service_level_real: service_level_real,
            external_shipment_id: external_shipment_id,
            external_rate_id: external_rate_id
          }
        end

      end

      class Pickup < DeliveryOption
        attr_accessor :charge,
                      :title,
                      :address,
                      :gateway_data,
                      :service_level_real,
                      :carrier

        def self.build(gateway_data)
          charge = gateway_data[:amount],
          address = gateway_data[:pickup_point_address],
          title =  "Punto Pickit: #{gateway_data[:pickup_point_name]}"
          gateway_data = gateway_data
          carrier = gateway_data[:carrier]

          new(charge: charge,
              address: address,
              gateway_data: gateway_data,
              carrier: carrier,
              service_level_real: nil)
        end

        def id
          "pickup:#{title}"
        end

        def to_s
          "Pickup Option - #{address}"
        end
      end
    end
  end
end

module Mkp
  module Shipping
    class SuborderKrabpackMapper
      attr_accessor :suborders, :zip, :options

      def initialize(order, zip, choosen_delivery_options)
        @order = order
        @suborders = order.suborders
        @options = choosen_delivery_options
        @zip = zip
      end

      def generate
        set_suborders_price
      end

      private

      def set_suborders_price
        suborders.map do |suborder|
          delivery_option = choosen_option(suborder.shop_id)
          suborder_builder(suborder, delivery_option)
        end
      end

      def choosen_option(shop_id)
        options.find{|option| option['shop_id'] == shop_id}
      end

      def suborder_builder(suborder, delivery_option)
        charge_amount = delivery_option.pickup ? 0 : delivery_option['charge']
        OpenStruct.new( items: suborder.items,
                        shop_id: suborder.shop_id,
                        warehouse: suborder.shop.warehouses.first,
                        charged_amount: charge_amount,
                        choosen_option: delivery_option )
      end

    end
  end
end

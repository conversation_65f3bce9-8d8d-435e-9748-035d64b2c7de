module Mkp
  module Shipping
    class SuborderMapper
      attr_accessor :suborders, :zip, :delivery_options, :total_weight, :base_price, :choosen_option

      def initialize(order, zip, choosen_delivery_options)
        @order = order
        @zip = zip
        @choosen_option = choosen_delivery_options.first
        @base_price = choosen_option[:price]
        @suborders = order.suborders
        @total_weight = 0
      end

      def generate
        return set_suborders_price if suborders.count == 1 || base_price == 0
        suborders_price_processor
      end

      private

      def set_suborders_price
        suborders.map do |suborder|
          suborder_builder(suborder, base_price)
        end
      end

      def suborders_price_processor
        delivery_options.map do |delivery|
          #total = delivery[:price] > 0 ? calculate_price_by_weight(delivery[:weight]) : 0
          suborder_builder(delivery[:suborder], delivery[:price])
        end
      end

      def suborder_builder(suborder, price)
        OpenStruct.new( items: suborder.items,
                        shop_id: suborder.shop.id,
                        warehouse: suborder.shop.warehouses.first,
                        charged_amount: price,
                        choosen_option: choosen_option )
      end

      def calculate_price_by_weight(weight)
        (( base_price * weight ) / total_weight ).round(2)
      end

      def delivery_options
        @delivery_options ||= suborders.map do |suborder|
          shop_id = suborder.shop_id
          weight = suborder.get_suborder_weight
          suborder.store.get_delivery_option(zip, [weight], [shop_id]).tap do |delivery|
            self.total_weight += weight if delivery[:price] > 0
            delivery[:suborder] = suborder
            delivery[:weight] = weight
          end
        end
      end
    end
  end
end

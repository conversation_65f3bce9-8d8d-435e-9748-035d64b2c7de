module Mkp
  module Shipping
    class Packages

      attr_accessor :suborder, :items, :opts, :sku
      def initialize(items)
        @items = items
        @suborder = items.first.suborder
      end

      def get_package_values(opts = {})
        @opts = opts
        return simple_package unless is_multi_packages?
        multi_packages
      end

      def total_weight
        @total_weight ||= items.to_a.sum(0, &:total_weight)
      end

      def volume
        volume_value(suborder.get_suborder_height, suborder.get_suborder_width, suborder.get_suborder_length)
      end

      private

      def simple_package
        total = suborder.total
        package = {}.tap do |hash|
          hash[:height] = suborder.get_suborder_height
          hash[:width] = suborder.get_suborder_width
          hash[:length] = suborder.get_suborder_length
          hash[:weight] = suborder.get_suborder_weight
        end
        [ params_package(package, total) ]
      end

      def multi_packages
        items.map do |item|
          total = total_cost(item)
          item.product.packages.map do |package|
            build_package = package.to_unit(opts[:length], opts[:mass])
            params_package(build_package, total, item)
          end
        end.flatten
      end

      def params_package(package, total, item = nil)
        {
          "alto" => package[:height].to_f / 100,
          "ancho" => package[:width].to_f / 100,
          "largo" => package[:length].to_f / 100,
          "peso" => package[:weight].to_f,
          "valor_declarado" => (total).round.to_f,
          "cantidad" => item.try(:quantity) || 1
        }.tap do |hash|
          hash["sku"] = item.try(:variant).try(:sku) || build_sku
        end
      end

      def build_sku
        @sku ||= suborder.items.map(&:variant).map(&:sku).join(", ")
      end

      def total_cost(item)
        item.product.packages.one? ? item.total : ( item.total / item.product.packages.count )
      end

      def is_multi_packages?
        is_packages? || has_many?
      end

      def has_many?
        items.any?{ |item| item.quantity > 1 } || items.count > 1
      end

      def limit_weight?
        total_weight > 7.0
      end

      def is_packages?
        items.any?{ |item| !item.product.packages.one? }
      end

      def volume_value(height, width, length)
        height = height.millimeters.to.meters.value.to_f
        width = width.millimeters.to.meters.value.to_f
        length = length.millimeters.to.meters.value.to_f
        (height * width * length).send(:cubic_meters).to_f
      end
    end
  end
end

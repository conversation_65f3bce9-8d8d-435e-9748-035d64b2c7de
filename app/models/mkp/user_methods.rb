module Mkp
  module UserMethods
    def full_name
      "#{first_name} #{last_name}".titleize
    end

    def get_or_create_regular_cart(network)
      carts.where('type is null').where(network: network).first || carts.create!(network: network)
    end

    def login
      first_name
    end

    def type
      self.class.to_s
    end

    def avatar
      hash = Digest::MD5.hexdigest(email)
      Avatar.new("http://www.gravatar.com/avatar/#{hash}?default=404")
    end
  end
end

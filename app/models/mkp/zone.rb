module Mkp
  class Zone < ActiveRecord::Base
    include Concerns::<PERSON><PERSON><PERSON><PERSON>

    belongs_to :shop,           class_name: 'Shop'
    belongs_to :warehouse,      class_name: 'Warehouse'
    has_many :shipping_methods, class_name: 'ShippingMethod'

    validates :warehouse_id, presence: true

    serialize :countries, Array

    default_scope { where('mkp_zones.deleted_at IS NULL') }

    accepts_nested_attributes_for :shipping_methods

    def include_country_code?(country_code)
      countries.empty? || countries.include?(country_code)
    end

    def shipping_methods_for(country_code)
      shipping_methods.map do |method|
        unless method.easypost?
          method
        else
          method.enable_to_ship?(warehouse.country, country_code) ? method : nil
        end
      end.compact
    end

  end
end

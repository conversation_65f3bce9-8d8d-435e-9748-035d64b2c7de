module Mkp
  class Promotion < ActiveRecord::Base
    include ::Concerns::Avenidable

    serialize :condition, Hash
    serialize :action, Hash

    validates :name, :display_name, :starts_at, :expires_at, :condition, :action, :condition, presence: true
    validates_uniqueness_of :name, :display_name, scope: :network
    validate :shipping_action_only_percent

    scope :by_network, -> (network) { where(network: network) }
    scope :active, -> { where(active: true) }
    scope :allow_coupon, -> { where(allow_coupon: true) }
    scope :by_store, ->(store_id) { store_id.nil? ? avenidable : where(store_id: store_id) }
    scope :today, ->(network) {
      by_network(network).active.where("starts_at <= ? AND expires_at >= ?",
        Time.now.to_datetime.beginning_of_day, Time.now.to_datetime.end_of_day
      )
    }

    OPERATORS = {
      equal:                 '==',
      not_equal:             '!=',
      less_than:             '<',
      greater_than:          '>',
      less_or_equal_than:    '<=',
      greater_or_equal_than: '>='
    }

    CONDITIONS_SETTINGS = {
      items_quantity:              { name: 'Quantity of Total Items', method: :items_quantity, has_operators: true },
      item_quantity:               { name: 'Quantity of N Items', method: :item_quantity, arguments: :products, has_operators: true },
      item_quantity_per_user:      { name: 'Quantity of N Items per User', method: :item_quantity, arguments: :products, has_operators: true },
      manufacturer_items_quantity: { name: 'Quantity of Items of N Manufacturers', method: :manufacturer_items_quantity, arguments: :manufacturers, has_operators: true },
      manufacturer_items_subtotal: { name: 'Subtotal of Items of N Manufacturers', method: :manufacturer_items_subtotal, arguments: :manufacturers, has_operators: true },
      category_items_quantity:     { name: 'Quantity of Items of N Categories', method: :category_items_quantity, arguments: :categories, has_operators: true },
      category_items_subtotal:     { name: 'Subtotal of Items of N Categories', method: :category_items_subtotal, arguments: :categories, has_operators: true },
      order_subtotal:              { name: 'Order Subtotal', method: :order_subtotal, has_operators: true },
      landings:                    { name: 'From Landings', method: :landings, arguments: :landings, has_operators: false }
    }

    ACTIONS = {
      order_subtotal:     { name: 'on Subtotal', method: :order_subtotal },
      cheaper_item:       { name: 'on Cheaper Item', method: :cheaper_item },
      involved_items:     { name: 'on Involved Items', method: :involved_items },
      free_item_involved: { name: 'on Free Product', method: :free_item_involved },
      shipping:           { name: 'on Shipping', method: :shipping }
    }

    ACTIONS_TYPE = [:fixed, :percent]

    def discount_amount(amount)
      if action[:type] == 'fixed'
        amount > action[:amount].to_f ? action[:amount].to_f : amount
      else
        amount * (action[:amount].to_f / 100)
      end
    end

    private

    def shipping_action_only_percent
      if action[:type] == 'fixed' && action[:method] == 'shipping'
        errors.add(:action, "can't add actions with fixed type and shipping method. Only percent type is allowed with shipping method")
      end
    end
  end
end

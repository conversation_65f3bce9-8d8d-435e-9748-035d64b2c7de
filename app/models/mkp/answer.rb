module Mkp
  class Answer < ActiveRecord::Base
    include ::Concerns::GeneratesNotifications

    belongs_to :user
    belongs_to :question, foreign_key: 'mkp_question_id'

    validates :description,     presence: true
    validates :mkp_question_id, presence: true
    validates :user_id,         presence: true

    generates_notification_on_create :answered

    def send_user_notification
      AnswerUserNotificationWorker.perform_async(self.id)
    end
  end
end

class Mkp::PaymentPromotion < ActiveRecord::Base
  enum brand: [:amex, :cabal, :cabal_debito, :credimas, :diners, :maestro, :mastercard, :naranja, :visa, :visa_debito, :visa_recargable, :mastercard_debito, :nativa]

  belongs_to :bank

  def total_amount(amount)
    return amount if active
    (amount * coefficient.to_f).round(2)
  end

  def total_installments(amount)
    (total_amount(amount) / installment).round(2)
  end
end


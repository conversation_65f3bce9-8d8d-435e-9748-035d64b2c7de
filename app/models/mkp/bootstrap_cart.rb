module Mkp
  class BootstrapCart
    extend CartCookieAndSessionManagement

    def self.cookie_load(cookie_cart, cookie_cart_owner, network, cart, actual_user_id)
      cookie_parsed = (get_cookie_parsed_by_network(cookie_cart, network) if cookie_cart.present?) || []
      cookie_owner  = (get_cookie_owner_by_network(cookie_cart_owner, network) if cookie_cart_owner.present?) || nil
      cart_parsed   = (get_parsed_cart(cart) if cart.present?) || []

      simple_items = if actual_user_id.present?
                       # Ok, this visitor looks like a logged in User
                       if are_equal?(cookie_parsed, cart_parsed)
                          # If what it's in the cookie is the same of what we had persisted
                          # let's use any of them, it's the same
                          cookie_parsed
                       else
                         # Ok, so the cookie_parsed and cart_parsed are different
                         # let's see if he is the owner of the cart
                         if (cookie_owner.blank? || cookie_owner == actual_user_id) && actual_user_id != 1
                           # If there is no cookie_owner setup, we asume that he just logged in
                           # or maybe it's a new user just signing up
                           # If the cookie_owner is not blank, let's check if the actual_user is the onwer
                           # If any of those are true we should update the persisted data with the
                           # information that's in the cookie and return that data
                           # also, we guarantee that it's not eshop user
                           if cookie_parsed.present?
                             cart.merge_items(cookie_parsed)
                             get_parsed_cart(cart.reload)
                           else
                             cart_parsed
                           end
                         else
                           # Ok, somehow there is a cookie_owner and it's not the actual_user_id
                           # we asume that in this case the visitor is changing from one "actor" to another
                           # So we priorize the persisted cart of this new "actor" or "character" of the user
                           cart_parsed
                         end
                       end
                     else
                       # So the visitor is navigating anonymously
                       # so the items are only set in and by the cookie and not persisted
                       cookie_parsed
                     end

      items = extract_relevant_data(simple_items)
      new(items, simple_items)
    end

    def initialize(items, simple_items)
      @items = items
      @simple_items = prune_no_longer_available(simple_items)
    end

    def items
      @items ||= []
    end

    def simple_items
      @simple_items ||= []
    end

    private

    def items_ids
      return @items_ids unless @items_ids.nil?
      @items_ids = items.map{|item| item[:id]}
    end

    def prune_no_longer_available(simple_items)
      simple_items.select do |item|
        items_ids.include?(item[:variant_id])
      end
    end

  end
end

# frozen_string_literal: true

module Mkp
  module StatusChange
    # Class to manage status changes for shipments
    class ShipmentStatusManage
      class << self
        def status_change(entity, status)
          send("#{status}!", entity) if respond_to?("#{status}!", :include_private)
          post_status_change_actions(entity)
        end

        private

        def post_status_change_actions(entity)
          entity.items.each do |item|
            processed_status = ProcessOrderItemStatus.new(item, entity.status).perform
            Mkp::StatusChange::EntityStatusManage.status_change(item, processed_status)
          end
          InvoiceItem.update_shipment_status(entity)
        end

        def unfulfilled!(entity)
          return if entity.delivered? || entity.cancelled?

          entity.update!(status: 'unfulfilled') unless entity.unfulfilled?

          entity
        end

        def in_process!(entity)
          entity.update!(status: 'in_process') unless entity.in_process?

          entity
        end

        def shipped!(entity)
          Rails.logger.info('-**********************SHIPPED EMAIL********************************-')
          Rails.logger.info(entity)
          unless entity.shipped?
            entity.update!(status: 'shipped')
            entity.notify_shipped_to_customer
          end
          Rails.logger.info(entity.shipped?)
          Rails.logger.info(entity)
          entity
        end

        def delivered!(entity)
          return entity if entity.delivered?

          entity.update!(status: 'delivered')
          notify_delivered(entity) unless entity.virtual?
          entity.suborders.each do |suborder|
            suborder.shipment_delivered
            InvoiceItem.update_fulfilled_at(suborder, Time.zone.now)
            InvoiceItem.create_return_from(suborder) if suborder.status == 'returned'
          end
          entity
        end

        def cancelled!(entity)
          if entity.unfulfilled? || entity.in_process? || entity.shipped? || entity.not_delivered?
            entity.update!(status: 'cancelled') unless entity.cancelled?
          end

          entity
        end

        def not_delivered!(entity)
          if entity.unfulfilled? || entity.in_process? || entity.shipped?
            unless entity.not_delivered?
              entity.update!(status: 'not_delivered')
              entity.notify_not_delivered_to_customer
              entity.notify_not_delivered_to_shop
            end
          end

          entity
        end

        def returned!(entity)
          entity.update!(status: 'returned') unless entity.returned?

          entity
        end

        def notify_delivered(entity)
          entity.notify_delivered_to_customer
          entity.notify_delivered_to_shop
        end
      end
    end
  end
end

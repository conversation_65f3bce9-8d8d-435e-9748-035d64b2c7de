module Mkp
  class Review < ActiveRecord::Base
    belongs_to :product
    has_many :votes

    MAX_STARS = 5
    REVIEWED_AS = { 'correct' => false,
                    'inappropriate' => true }

    validates :product_id,  presence: true
    validates :stars,       presence: true, inclusion: 1..MAX_STARS, numericality: { only_integer: true }
    validates :description, presence: true, length: { maximum: 2500 }
    validates :title,       presence: true, length: { maximum: 80 }

    validates_presence_of :email, :if => lambda { |r| r.customer_id.nil? }

    scope :curated, -> { where(curated: true) }
    scope :not_reported_except_by, ->(user_id) do
      if user_id.present?
        where('reported_by IS NULL OR reported_by = ?', user_id)
      else
        where('reported_by IS NULL')
      end
    end
    scope :appropriate, -> { where('inappropriate = 0 OR inappropriate IS NULL') }

    store :data, accessors: [:stored_reported_at,
                             :stored_reported_by,
                             :report_notified,
                             :reviewed_as,
                             :reviewed_at,
                             :reviewed_by]

    def reviewed_as(as, by)
      self.stored_reported_at = reported_at
      self.stored_reported_by = reported_by
      self.reviewed_by = by
      self.reviewed_at = Time.now
      self.reviewed_as = as
      self.reported_at = nil
      self.reported_by = nil
      self.inappropriate = REVIEWED_AS[as]
      save
    end

    def network
      product.shop.network
    end

    def report(user_id)
      self.reported_at = Time.now
      self.reported_by = user_id
      self.inappropriate = nil

      save
    end

    def reported_at_time
      reported_at || stored_reported_at
    end

    def reported_by_user
      user_id = reported_by || stored_reported_by
      return false if not user_id
      ::User.find(user_id)
    end

    def reported_by?(user)
      return false if not user
      user.id == reported_by
    end

    def revert_report(user_id)
      self.reported_at = nil
      self.reported_by = nil

      save
    end

    def report_notifications_sent!
      self.report_notified = true
      save
    end

    def voted_by?(user = nil)
      return false if not user
      votes.where(user_id: user.id).count > 0
    end

    class User < Review
      belongs_to :user,
                 class_name: '::User',
                 foreign_key: 'customer_id'

      validates :product_id, uniqueness: { scope: [:customer_id, :type] }

      before_create Proc.new { self.curated = true }
    end

    class Guest < Review
      belongs_to :guest,
                  class_name: 'Mkp::Guest',
                  foreign_key: 'customer_id'

      alias_method :user, :guest

      validates_presence_of :guest
      validate :uniqueness_by_guest_email, on: :create

      private

      def uniqueness_by_guest_email
        return if guest.nil?
        email    = guest.email
        reviewed = Review::Guest.includes(:guest)
                                .where(product_id: product_id)
                                .where('mkp_guests.email = ?', email)
                                .any?

        errors.add(:customer_id,
                   "The guest's email already made a review") if reviewed
      end
    end
  end
end

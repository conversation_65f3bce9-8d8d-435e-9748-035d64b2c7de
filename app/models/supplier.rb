class Supplier < ActiveRecord::Base
  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :shop, class_name: 'Mkp::Shop'

  before_save :update_slug

  def store_name
    store.name if store
  end

  def shop_name
    shop.name if shop
  end

  def current_supplier_stock
    current = SupplierStock.where(supplier: self, store: self.store).order('created_at desc').first
    current || SupplierStock.create(supplier: self, stock: 0, store: self.store, consumed_stock: 0)
  end

  def update_slug
    self.slug = shop.slug if shop
  end

end

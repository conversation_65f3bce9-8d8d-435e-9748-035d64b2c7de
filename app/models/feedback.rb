class Feedback < ActiveRecord::Base
  belongs_to :customer, polymorphic: true

  validates :data, :network, :type, presence: true

  class Checkout < Feedback
    validates :customer_id, :customer_type, :destination_id, :purchase_items, presence: true

    store :data, accessors: [:purchase_items, :destination_id]

    def self.generate(customer, cart_items, destination_id)
      feedback = self.new
      feedback.customer       = customer
      feedback.destination_id = destination_id
      feedback.network        = cart_items.first.shop.network
      feedback.purchase_items = cart_items.map { |item| [item.variant_id, item.quantity] }
      feedback.save
      feedback
    end
  end
end

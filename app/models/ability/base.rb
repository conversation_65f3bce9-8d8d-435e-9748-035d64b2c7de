class Ability::Base < Ability

  MODELS = ActiveRecord::Base.descendants.collect(&:name)

  def initialize(user)
    super
    return can(:manage, :all) if user.role.is_a?('administrator')
    user.role.permissions.enabled.each do |permission|
      section = (MODELS.include?(permission.section) && permission.section.constantize) || permission.section
      send(:can, permission.access.to_sym, section)
    end
  end
end

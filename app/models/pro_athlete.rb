class ProAthlete < User
  has_and_belongs_to_many :brands,
                          join_table: 'brands_users',
                          foreign_key: 'user_id'

  has_and_belongs_to_many :products,
                          class_name: '::Mkp::Product',
                          join_table: 'products_users',
                          foreign_key: 'user_id'

  has_one :profile,
          class_name: '::Social::Profile::User',
          dependent: :destroy,
          foreign_key: :user_id
end

# Represents a notification sent to a user, about a new event related to him.
# This class is not intended for instantiation: use its subclasses instead.
class Notification < ActiveRecord::Base
  validates :actor_id,  presence: true
  validates :user_id,   presence: true

  belongs_to :actor, class_name: '::User'
  belongs_to :user

  serialize :event

  scope :unread, lambda { where(read: false) }

  def self.of(user)
    includes(actor: [profile: :avatar])
    .where(user_id: user.id)
    .order('notifications.created_at DESC')
  end

  def self.unread_count_of(user)
    where(user_id: user.id).where(read: false).count
  end

  def read!
    update_attribute(:read, true)
  end

  private

  def self.build_post_title(post)
    if post.is_a?(Social::Whatsup)
      post.body
    elsif post.is_a?(Social::Album)
      post.title
    end
  end

  # Represents the notification to a user that received a comment on one of
  # his posts (whatsup or album).
  class CommentedPost < Notification
    [:post_title, :post_id, :post_type].each do |attr|
      define_method(attr) { event[attr] }
    end

    def self.send!(comment)
      create! do |n|
        n.user_id = comment.target.author_id
        n.actor_id = comment.user_id
        n.event = {
          post_title: build_post_title(comment.target),
          post_id: comment.target_id,
          post_type: comment.target_type
        }
      end
    end

    def target
      comment = Social::Comment.where(target_id: post_id,
                                      target_type: post_type).first
      comment && comment.target
    end
  end

  # Represents the notification to a user that started to being followed by
  # another one.
  class Followed < Notification
    def self.send!(follow)
      create! do |n|
        n.user_id = follow.followed_id
        n.actor_id = follow.follower_id
      end
    end

    def self.previously_notified?(follow)
      exists?(user_id: follow.followed_id, actor_id: follow.follower_id)
    end
  end

  # Represents the notification to a user that a new comment was added to a
  # post he had previously commented.
  class CommentedSamePost < Notification
    [:post_title, :post_id, :post_type].each do |attr|
      define_method(attr) { event[attr] }
    end

    def self.send!(comment, user)
      create! do |n|
        n.user = user
        n.actor_id = comment.user_id
        n.event = {
          post_title: build_post_title(comment.target),
          post_id: comment.target_id,
          post_type: comment.target_type
        }
      end
    end

    def target
      comment = Social::Comment.where(target_id: post_id,
                                      target_type: post_type).first
      comment && comment.target
    end
  end

  # Represents the notification to a user that received an answer to a question
  # he made about a product.
  class Answered < Notification
    def self.send!(answer)
      create! do |n|
        n.user_id = answer.question.user_id
        n.actor_id = answer.user_id
        n.event = {
          answer_id: answer.id
        }
      end
    end

    def product
      answers = Mkp::Answer.where(id: answer_id)
      answers.first.question.product if answers.any?
    end

    private

    def answer_id
      event[:answer_id]
    end
  end

  # Represents the notification to a user that received a favorite in one of
  # his posts.
  class Favorited < Notification
    [:title, :favoritable_id, :favoritable_type].each do |attr|
      define_method(attr) { event[attr] }
    end

    def self.send!(favorite)
      create! do |n|
        n.user_id = favorite.favoritable.author_id
        n.actor_id = favorite.user_id
        n.event = {
          title: build_post_title(favorite.favoritable),
          favoritable_id: favorite.favoritable_id,
          favoritable_type: favorite.favoritable_type
        }
      end
    end

    def self.previously_notified?(favorite)
      if notifications = self.where(user_id: favorite.favoritable.author.id,
                                    actor_id: favorite.user.id).presence
        notifications.detect do |n|
          n.favoritable_id == favorite.favoritable_id &&
            n.favoritable_type == favorite.favoritable_type
        end
      end
    end

    def favoritable
      favorite = Favorite.where(favoritable_id: favoritable_id,
                                favoritable_type: favoritable_type).first
      favorite && favorite.favoritable
    end
  end

  class Mentioned < Notification
    [:post_title, :post_id, :post_type].each do |attr|
      define_method(attr) { event[attr] }
    end

    def self.send!(mention)
      create! do |n|
        n.user = mention.to
        n.actor_id = mention.from.id
        n.event = {
          post_title: mention.on.body,
          post_id: mention.on.id,
          post_type: mention.on.class.name
        }
      end
    end

    def mention
      Social::Mention.new(on: post_type.constantize.find(post_id),
                          from: User.find(actor),
                          to: user)
    end

    def target
      post = post_type.constantize.find(post_id)
      case post
        when Social::Whatsup then post
        when Social::Comment then post.target
      end
    end
  end
end

class WhitelistConfiguration < ActiveRecord::Base
  belongs_to :store, :class_name => 'Mkp::Store'
  validates :validation_strategy, :user_strategy, presence: true, :if => :active

  def validation_strategy_class
    @validation_strategy_class ||= "WhitelistStrategies::#{self.read_attribute(:validation_strategy)}".constantize
  end

  def user_strategy_class
    @user_strategy_class ||= "WhitelistStrategies::#{self.read_attribute(:user_strategy)}".constantize
  end
end

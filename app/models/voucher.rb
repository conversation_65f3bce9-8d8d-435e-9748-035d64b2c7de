class Voucher < ActiveRecord::Base

  SUPPLIERS = [ :avenida, :dexter ]

  enum supplier: SUPPLIERS

  belongs_to :product, class_name: 'Mkp::Product'

  has_one :setting_coupon
  has_many :gift_cards
  has_many :coupons, through: :gift_cards

  #La carga del cupon se hara desde este metodo
  #store 14 TDDBoca
  def amount(store_id = 14)
    store_points = product.points_by_store(store_id)
    (store_points.zero? && setting_coupon&.amount) || store_points
  end

  def create_gift_card_to_item!(item)
    raise 'Not Setting' unless setting_coupon.present?
    setting_coupon.build_coupon!(item)
  end
end

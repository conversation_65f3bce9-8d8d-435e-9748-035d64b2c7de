class LoginAvailableValidator < ActiveModel::EachValidator
  def self.available?(login)
    !blacklisted?(login) && !taken?(login)
  end

  def validate_each(record, attribute, value)
    if !self.class.available_for?(record, value)
      error_message = options[:message] || I18n.t('errors.messages.taken')
      record.errors[attribute] << error_message
    end
  end

  private

  def self.available_for?(user, login)
    !blacklisted?(login) && !taken_by_other?(user, login)
  end

  def self.taken_by_other?(user, login)
    other_user = User.find_by_login(login)
    other_user && other_user != user
  end

  def self.taken?(login)
    User.find_by_login(login).present?
  end

  def self.blacklisted?(login)
    LOGIN_BLACKLIST.include?(login)
  end
end
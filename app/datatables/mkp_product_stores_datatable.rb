class MkpProductStoresDatatable < AjaxDatatablesRails::Base

  def view_columns
    @view_columns ||= {
      product:    { source: "Mkp::Product.title",  cond: :start_with, searchable: true, orderable: true },
      store:      { source: "Mkp::Store.name",     cond: :start_with, searchable: true, orderable: true },
      points:     { source: "Mkp::ProductStore.points",      cond: :start_with, searchable: true, orderable: true },
      created_at: { source: "Mkp::ProductStore.created_at",     cond: :start_with, searchable: true, orderable: true },
      id:         { source: "Mkp::ProductStore.id" }
    }
  end

  def data
    records.map do |record|
      product_store = record
      {
        store: product_store.store.try(:name) || '',
        product: product_store.product.try(:title) || '',
        points: product_store.points,
        created_at: product_store.created_at,
        id: record.id,
      }
    end
  end

  private

  def store_ids
    options[:store_ids].any? ? options[:store_ids] : Mkp::Store.all.ids
  end

  def get_raw_records
    Mkp::ProductStore.where(store_id: store_ids).includes(:product).references(:product).includes(:store).references(:store)
  end
end

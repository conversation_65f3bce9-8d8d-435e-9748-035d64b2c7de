class MkpMemberDatatable < AjaxDatatablesRails::Base
  extend Forwardable

  def_delegators :@view, :link_to, :mail_to, :tdd_member_path, :edit_tdd_member_path, :content_tag

  def view_columns
    @view_columns ||= {
      email:      { source: "Mkp::Customer.email", cond: :start_with, searchable: true, orderable: true },
      full_name:  { source: "Mkp::Customer.last_name", cond: :start_with, searchable: true, orderable: true },
      doc_number: { source: "Mkp::Customer.doc_number", cond: :start_with, searchable: true, orderable: true },
      origin:     { source: "Mkp::Member.origin", cond: :start_with, searchable: true, orderable: true },
      created_at: { source: "Mkp::Member.created_at", orderable: true, cond: :start_with },
      amount:     { source: "Service.amount", searchable: false, orderable: false },
      enabled:    { source: "Mkp::Member.enabled" },
      actions:    { source: "Mkp::Member.id" },
    }
  end

  def data
    records.map do |record|
      customer = record.customer
      {
        email: mail_to(customer.email),
        full_name: customer.full_name,
        doc_number: customer.doc_number,
        origin: link_to("#{(customer.member.try(:origin) || "--")[0..19]}", '', title: "#{customer.member.try(:origin) || "--"}"),
        created_at: customer.created_at.in_time_zone("Monrovia").strftime('%Y-%m-%d'),
        amount: "$#{customer&.subscription&.amount || 0 }",
        enabled: customer.member.enabled ? 'Si' : 'No',
        actions: [
          link_to('', edit_tdd_member_path(record), {class: 'fa fa-edit mx-2'}),
          link_to(content_tag(:i, '', class: 'fa fa-trash px-2'), tdd_member_path(record), action: :destroy, confirm: 'Are you sure?', method: :delete, class: 'px-2', title:'Delete')
        ].join('').html_safe
      }
    end
  end

  def from
    options[:from] || Date.today-1.month
  end

  def to
    options[:to] || Date.today
  end

  private

  def get_raw_records
    Mkp::Member.where('mkp_members.created_at >= ? AND mkp_members.created_at < ?', from, to).includes(customer: {subscription: :service}).references(:customer)
  end

end

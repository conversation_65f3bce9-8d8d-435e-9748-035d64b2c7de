class MkpCarrierStoreDatatable < AjaxDatatablesRails::Base

  def view_columns
    @view_columns ||= {
      shop: { source: "Mkp::Shop.title", cond: :start_with, searchable: true, orderable: true },
      gateway: { source: "Mkp::CarrierStore.gateway", cond: :start_with, searchable: true, orderable: true },
      zip_from: { source: "Mkp::CarrierStore.zip_from", cond: :start_with, searchable: true, orderable: true },
      zip_to: { source: "Mkp::CarrierStore.zip_to", cond: :start_with, searchable: true, orderable: true },
      weight_min: { source: "Mkp::CarrierStore.weight_min", cond: :start_with, searchable: true, orderable: true },
      weight_max: { source: "Mkp::CarrierStore.weight_max", cond: :start_with, searchable: true, orderable: true },
      operative: { source: "Mkp::CarrierStore.operative", cond: :start_with, searchable: true, orderable: true },
      delivery_message: { source: "Mkp::CarrierStore.delivery_message", cond: :start_with, searchable: true, orderable: true },
      id: { source: "Mkp::CarrierStore.id" }
    }
  end

  def data
    records.map do |record|
      {
        shop: record.shop.try(:title) || '',
        gateway: record.icon || record.gateway,
        zip_from: record.zip_from,
        zip_to: record.zip_to,
        weight_min: record.weight_min,
        weight_max: record.weight_max,
        delivery_message: record.delivery_message,
        price: record.price,
        operative: record.operative,
        id: record.id
      }
    end
  end

  private

  def get_raw_records
    Mkp::CarrierStore.where(store_id: params[:store_id]).includes(:shop).references(:shop)
  end
end

class PaymentsDatatable < AjaxDatatablesRails::Base
  extend Forwardable

  def_delegators :@view, :link_to, :mail_to, :tdd_payment_path, :edit_tdd_payment_path, :tdd_generate_invoice_path, :new_tdd_payment_invoice_path

  def view_columns
    @view_columns ||= {
      id:           { source: "Payment.id", cond: :start_with, searchable: true, orderable: true },
      member_id:    { source: "Mkp::Member.id", cond: :start_with, searchable: true, orderable: true },
      email:        { source: "Mkp::Customer.email", cond: :start_with, searchable: true, orderable: true },
      doc_number:   { source: "Mkp::Customer.doc_number", cond: :start_with, searchable: true, orderable: true },
      status:       { source: "Payment.status", cond: :start_with, searchable: true, orderable: true },
      brand:        { source: "Mkp::Card.brand" },
      card_type:    { source: "Mkp::Card.card_type" },
      amount:       { source: "Payment.amount" },
      period:       { source: "Payment.period", cond: :start_with, searchable: true, orderable: true },
      recurrent:    { source: "Payment.recurrent" },
      billable:     { source: "Payment.billable" },
      invoice_date: { source: "Invoice.created_at" },
      invoice_number: { source: "Invoice.number", searchable: true, orderable: true },
      error:        { source: "Payment.error", cond: :start_with, searchable: true, orderable: true },
      created_at:   { source: "Payment.created_at", searchable: true },
      actions:      { source: "Payment.id" }
    }
  end

  def data
    records.map do |payment|
      card = payment.subscription&.customer&.member&.cards&.first
      {
        id:           payment.id,
        member_id:    payment.subscription&.customer&.member&.id,
        email:        mail_to(payment.subscription&.customer&.email),
        doc_number:   payment.subscription&.customer&.doc_number,
        status:       payment&.status,
        amount:       payment&.amount,
        brand:        card&.brand,
        card_type:    card&.card_type,
        period:       payment&.period,
        recurrent:    (payment.recurrent === false) ? "Primer pago" : "Debito Aut.",
        billable:     payment&.billable,
        invoice_date: payment&.invoice&.created_at&.strftime('%Y-%m-%d %H:%m'),
        invoice_number: payment&.invoice&.number,
        error:        payment&.error,
        created_at:   payment&.created_at.strftime('%Y-%m-%d'),
        actions:      build_actions(payment)
      }
    end
  end

  def generate_invoice(pay)
    link_to('Facturar', tdd_generate_invoice_path(pay), {method: :post, class: 'button success tiny right'}) if pay.send(:can_invoice?)
  end

  private

  def build_actions(payment)
    links = [link_to('', edit_tdd_payment_path(payment.id), {class: 'fa fa-edit mx-2'})]
    links << link_to('', new_tdd_payment_invoice_path(payment), {class: 'fa fa-file mx-2', title: "Crear Factura"}) if payment.can_invoice?
    links.join('').html_safe
  end

  def from
    from = (options[:from] || Date.today - 5.year).to_s
  end

  def to
    to = (options[:to] || Date.today).to_s
  end

  def status
    status = options[:status] 
  end

  def get_raw_records
    Payment.created_at_gte(from)
           .created_at_lt(to)
           .includes(:invoice, subscription: {customer: {member: :cards}})
           .references(:suscription).order(id: :desc)
  end
end

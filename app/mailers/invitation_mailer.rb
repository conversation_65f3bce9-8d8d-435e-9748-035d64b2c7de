class InvitationMailer < Mailer
  def deliver_invitation_emails(email_addresses_string, sender, message)
    email_addresses = EmailUtils.split_email_addresses(email_addresses_string)

    email_addresses.each do |email_address|
      invitation_email(email_address, sender, message).deliver
    end
  end

  private

  def invitation_email(to, sender, message)
    @message = message
    @network = sender.network || Network.default

    mail(to: to,
         subject: 'Invitation to join Avenida.com',
         template_name: 'invitation_email')
  end
end

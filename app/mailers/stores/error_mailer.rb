class Stores::<PERSON>rro<PERSON><PERSON>ailer < ::<PERSON>er
  def throw_error_in_store(message, backtrace, store)
    @message = message
    @backtrace = backtrace
    @current_store = store

    ExceptionNotifier.notify_exception(StandardError.new, data: {
        error: message,
        backtrace: backtrace,
        store: store
    })
    #mail(from: from_store, to: "<EMAIL>", subject: "Error in #{@current_store.try(:title)}, #{@message}")
  end
end

class FeedbackMailer < Mailer
  include NetworkExposure
  include CurrencyExposure

  def checkout(feedback, network_admin)
    @network_admin = network_admin
    @network       = network_admin.network
    @destination   = Mkp::Address.find(feedback.destination_id)
    @customer      = feedback.customer
    @locale        = locale_to_use(network_admin.network)
    @items = feedback.purchase_items.map do |purchase_item|
      variant = Mkp::Variant.find(purchase_item[0])
      Mkp::CartItem.new(variant: variant, quantity: purchase_item[1])
    end

    mail(to: network_admin.email,
         subject: I18n.t('feedback_mailer.checkout.subject', locale: @locale))
  end

  def current_currency_format
    return super unless Network[@network].thin?
    super + ' '
  end
end

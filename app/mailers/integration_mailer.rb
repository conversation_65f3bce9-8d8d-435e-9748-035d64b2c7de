class IntegrationMailer < Mailer
  EMAIL_DEVELOPERS = '<EMAIL>'.freeze
  EMAIL_AYUDA = 'Avenida.com <<EMAIL>>'.freeze
  EMAIL_MACRO = '<EMAIL>, <EMAIL>, danie<PERSON><PERSON><PERSON><EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>'.freeze

  def emails_for_env(shop, internal_mail = nil)
    if Rails.env.production?
      return { to: shop.owner.email + internal_mail.to_s, cc: shop.managers.map(&:email) }
    end

    {
      to: EMAIL_DEVELOPERS,
      cc: EMAIL_DEVELOPERS
    }
  end

  def emails_for_env_misses
    return EMAIL_DEVELOPERS unless Rails.env.production?

    EMAIL_MACRO
  end

  def import_and_sync(integration, new_products)
    @integration_name = integration.type.split('::').last
    @new_products = new_products
    network = integration.shop.network
    shop = integration.shop
    I18n.locale = locale_to_use(network)
    receiver = emails_for_env(shop)
    mail(
      from: EMAIL_AYUDA,
      subject: t('integration_mailer.import_and_sync.subject', integration_name: @integration_name),
      to: receiver[:to],
      cc: receiver[:cc]
    )
  end

  def products_without_category(integration, products_without_category)
    shop = integration.shop
    errors = products_without_category.map do |each|
      "Titulo: #{each.to_hash[:title]} - Descripción: #{each.to_hash[:description]}"
    end
    body = errors.join('<br>').html_safe
    receiver = emails_for_env(shop)
    mail(
      from: EMAIL_AYUDA,
      subject: 'Listado de productos NO Importados: No se encontro la categoría',
      body: body,
      to: receiver[:to],
      cc: receiver[:cc]
    )
  end

  def products_without_manufacturer(integration, products_without_manufacturer)
    shop = integration.shop
    errors = products_without_manufacturer.map { |each| "Titulo: #{each.to_hash[:title]} - Descripción: #{each.to_hash[:description]}" }
    body = errors.join('<br>').html_safe
    receiver = emails_for_env(shop, ', <EMAIL>')
    mail(
      from: EMAIL_AYUDA,
      subject: 'Listado de productos NO Importados: No se encontro la marca del producto',
      body: body,
      to: receiver[:to],
      cc: receiver[:cc]
    )
  end

  def misses_category(body)
    mail(
      to: emails_for_env_misses,
      body: body,
      content_type: 'text/html',
      from: EMAIL_AYUDA,
      subject: 'Error al sincronizar catalogo PyP'
    )
  end


  def fulfil_pending_shipments(body)
    mail(
      to: '<EMAIL>',
      body: body,
      content_type: 'text/html',
      from: EMAIL_AYUDA,
      subject: 'Error al generar shipments con la tarea <<gp:shipments:fulfil_pending_shipments>>'
    )
  end

  def redemtion_error(email, result, body)
    body_mail = "Se obtuvo la siguiente respuesta: #{result} <br> cuando se envió el siguiente body: #{body}.".html_safe

    mail(
      to: email,
      body: body_mail,
      content_type: 'text/html',
      from: EMAIL_AYUDA,
      subject: 'Error al notificar una venta'
    )
  end
end

class ImportLabelMailer < Mailer
  EMAIL_DEVELOPERS = '<EMAIL>'.freeze #TODO: Cambiar emails en variables GLOBALES (sacarlos)
  EMAIL_AYUDA = 'Avenida.com <<EMAIL>>'.freeze #TODO: Cambiar emails en variables GLOBALES (sacarlos)
  def notify_errors(shop, row_errors)
    mail(
      from: EMAIL_AYUDA,
      subject: t('import_label_mailer.notify_errors.subject'),
      body: t('import_label_mailer.notify_errors.body.errors', row_errors: row_errors),
      to: shop.owner.email,
      cc: EMAIL_DEVELOPERS
    )
  end

  def notify_success(shop)
    mail(
      from: EMAIL_AYUDA,
      subject: t('import_label_mailer.notify_success.subject'),
      body: t('import_label_mailer.notify_success.body.success'),
      to: shop.owner.email,
      cc: EMAIL_DEVELOPERS
    )
  end
end
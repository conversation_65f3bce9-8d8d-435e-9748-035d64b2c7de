class InvoiceReportMailer < Mailer

  def invoice_report_export(report)
    export = Mkp::Export.find report.last_export_id
    subject = "Resultado del reporte #{report.name} - #{report.description}"

    data = {
      from: report.store.email,
      to: '<EMAIL>',
      body: "Adjunto resultado de exportar el reporte.",
      subject: subject
    }

    mail = mail(data)
    # en development mail.add_file("public/#{export.url.split('/').last}")
    mail.add_file("#{export.csv_file.url}")
    mail
  end
end
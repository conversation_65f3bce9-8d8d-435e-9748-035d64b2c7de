class NotificationMailer < Mailer
  helper 'social/feed'

  def mentioned(notification)
    action_description = t('notification_mailer.mentioned.subject',
                           login: notification.actor.login,
                           item_class_name: t("notification_mailer.mentioned.#{notification.mention.decorate.on_name}"))

    email_notification(notification, true, action_description)
  end

  def commented_post(notification)
    action_description = t('notification_mailer.commented_post.subject',
                           login: notification.actor.login,
                           post_title: notification.event[:post_title].truncate(40))
    email_notification(notification, true, action_description)
  end

  def commented_same_post(notification)
    action_description = t('notification_mailer.commented_same_post.subject',
                           login: notification.actor.login,
                           user_login: notification.target.author.login,
                           post_title: notification.event[:post_title].truncate(40))
    email_notification(notification, true, action_description)
  end

  def favorited(notification)
    action_description = t('notification_mailer.favorited.subject',
                           login: notification.actor.login,
                           title: notification.event[:title].truncate(40))
    email_notification(notification, true, action_description)
  end

  def followed(notification)
    @follower = notification.actor

    if Notification::Followed.where('user_id = ? AND actor_id = ?', notification.actor_id, notification.user_id).present?
      action_description = t('notification_mailer.followed.back_subject',
                             full_name: notification.actor.full_name,
                             login: notification.actor.login)
      email_notification(notification, true, action_description)
    else
      action_description = t('notification_mailer.followed.subject',
                             login: notification.actor.login)
      email_notification(notification, false, action_description)
    end
  end

  # This could be privated/protected but it won't work with mail view
  def email_notification(notification, viaGoodPeople, action_description)
    sender = viaGoodPeople ? "\"#{notification.actor.full_name} (via Avenida.com)\" <<EMAIL>>" : 'Avenida.com <<EMAIL>>'
    recipient = notification.user
    @network = recipient.network
    I18n.locale = locale_to_use(@network)
    @notification = notification

    set_google_analytics_campaign('user-social')

    mail(from: sender,
         to: recipient.email,
         subject: action_description)
  end
end

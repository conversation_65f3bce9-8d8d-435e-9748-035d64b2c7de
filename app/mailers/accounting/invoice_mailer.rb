class Accounting::InvoiceMailer < ::Mailer
  def send_invoice_to_customer(url, order)
    @order = order
    email1 = order.customer.email
    email2 = "<EMAIL>"
    @url = url
    @current_store = order.store
    @logo_ref='mailers/invoice.png'
    store_title = @current_store.title.titleize

    mail(from: from_store, to: email1, subject: "Factura de tu compra en #{store_title}!", bcc: email2)
  end

  #tienda del deporte
  def notify_customer(invoice)
    @customer = invoice.customer
    @payment = invoice.payment
    @url = invoice.url
    @current_store = Mkp::Store.find(14)  #tddboca
    @logo_ref='mailers/invoice.png'
    store_title = 'Tarjeta del Deporte'

    mail(from: from_store, to: "<EMAIL>", subject: "Factura de tu pago en #{store_title}!", bcc: "<EMAIL>")
  end
end

class UserMailer < Mailer

  def welcome_email(user, locale)
    @user = user
    I18n.locale = locale

    set_google_analytics_campaign('userregistration')

    mail(to: user.email, subject: t('welcome.subject'))
  end

  def partial_signup_email(sign_up)
    @sign_up = sign_up

    sign_up_url_params = { profile: { first_name: @sign_up.first_name, last_name: @sign_up.last_name },
                           email: @sign_up.email,
                           host: HOSTNAME }

    sign_up_url_params[:avatar_url] = @sign_up.avatar.url(:m) if @sign_up.avatar
    @sign_up_url = sign_up_url(sign_up_url_params)


    mail(to: sign_up.email, subject: 'Welcome to Avenida.com')
  end

  def password_reset_email(user)
    layout_name = 'lux/password_reset_email'
    @edit_password_reset_url = edit_password_reset_url(user.perishable_token, host: HOSTNAME)
    I18n.locale = locale_to_use(user.network) if user.network.present?

    set_google_analytics_campaign('user-social')

    mail(to: user.email, subject: 'Reset your password on Avenida.com') do |format|
      format.html { render layout: layout_name }
    end
  end

  def brand_partial_signup_email(sign_up)
    layout_name = "v5/mailer/shop"
    set_google_analytics_campaign('brand')

    mail(to: sign_up.email, subject: 'Welcome to Avenida.com') do |format|
      format.html { render layout: layout_name }
    end
  end

  def brand_application_form_email(data, locale)
    layout_name = "v5/mailer/shop"
    I18n.locale = locale
    @data = data

    set_google_analytics_campaign('brand')

    mail(to: data[:email], subject: t('brand_application.subject')) do |format|
      format.html { render layout: layout_name }
    end
  end

  def brand_application_form_admin_email(data, locale)
    layout_name = "v5/mailer/shop"
    I18n.locale = locale
    @data = data

    set_google_analytics_campaign('brand')

    mail(to: t('brand_application.recipent'), subject: 'New Brand Application')  do |format|
      format.html { render layout: layout_name }
    end
  end
end

class Mkp::VoucherMailer < Mailer
  include NetworkExposure
  include CurrencyExposure

  helper Lux::LabelGatewaysHelper
  helper 'mailers/shipments'

  helper_method :marketplace_customer_email
  
  def customer_voucher(order)

    @customer       = order.customer
    @order          = order
    @locale         = locale_to_use(order.network)
    @transaction_id = order.purchase_id
    @network        = order.network
    @current_store  = order.store
    @logo_ref='mailers/order_confirmation.png'
    unless order.payment.try(:payment_method).eql?('ticket')
      @gateway = order.payment.try(:gateway)
    end
    subject =t('mkp.order_mailer.customer_purchase_confirmation.subject', locale: @locale)

    data = {
      from: from_store,
      to: @customer.email,
      subject: subject
    }
    mail(data)
  end

  def customer_reservation(order)
    @customer       = order.customer
    @order          = order
    @locale         = locale_to_use(order.network)
    @transaction_id = order.purchase_id
    @network        = order.network
    @current_store  = order.store
    @logo_ref='mailers/order_confirmation.png'
    subject =t('mkp.voucher_mailer.customer_reservation.subject', locale: @locale)

    data = {
      from: from_store,
      to: @customer.email,
      subject: subject
    }
    mail(data)
  end

end

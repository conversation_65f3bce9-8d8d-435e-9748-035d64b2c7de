class Mkp::Shop<PERSON>ailer < Mailer
  include NetworkExposure
  include CurrencyExposure
  include ActionView::Helpers::Text<PERSON>elper

  add_template_helper(Mkp::ApplicationHelper)

  # TODO: I18n this strings, on config/locale/mkp/mailer.
  def question_user(question)
    @question = question
    @shop = @question.product.shop

    if question.user.network.present?
      @network  = question.user.network
      I18n.locale = locale_to_use(@network)
    end


    mail(
      to: question.user.email,
      subject: t('mkp.shop_mailer.question_user.subject',
                 shop_title: @shop.title,
                 product_title: question.product.title)
    )
  end

  def question_shop(question)
    @question = question
    @network  = question.product.shop.network
    layout_name = 'v5/mailer/shop'
    shop = question.product.shop
    email = shop.owner.present? ? shop.owner.email : shop.merchants.first.email

    I18n.locale = locale_to_use(@network)

    set_google_analytics_campaign('brand')

    mail(
      to: email,
      subject: t('mkp.shop_mailer.question_shop.subject',
                 product_title: question.product.title)
    ) do |format|
      format.html { render layout: layout_name }
    end
  end

  def answer_shop(answer)
    @answer = answer
    @question = answer.question
    @shop = @question.product.shop
    @customer_name = customer_name(@question.user)

    if @question.user.network.present?
      @network  = @question.user.network
      I18n.locale = locale_to_use(@network)
    end

    set_google_analytics_campaign('useranswer')
    @recommended_variants = load_recommended.sample(4)

    mail(
      from: "\"#{@shop.title} (via Avenida.com)\" <<EMAIL>>",
      subject: t('mkp.shop_mailer.answer_shop.subject',
                  question_text: truncate(@question.description, length: 40)),
      to: @question.user.email
    )
  end

  def current_currency_format
    return super unless Network[@network].thin?
    super + ' '
  end

  def customer_name(customer)
    customer.first_name.presence || customer.last_name
  end

  def load_recommended
    product = @question.product
    id = product.id
    categories = product.categories
    network = @network
    categories.flat_map(&:path_ids).reverse.flat_map do |category_id|
      search = Mkp::Variant.search do
        without :product_id, id
        with :categories_ids, category_id
        with :deleted, false
        with :shop_visible, true
        with :shop_deleted, false
        with :display_variant, true
        with :network, network
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now
      end
      return search.results.uniq(&:product_id) if search.hits.length > 4
    end.compact
  end
end

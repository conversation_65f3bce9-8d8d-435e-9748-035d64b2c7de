module Mkp
  module Bna
    class ProviderMailer < Mailer
      default from: '<EMAIL>'.freeze

      def notify_order_booked(order)
        mailer_initializer(order)
        subject = 'Nueva reserva MiMoto'
        mail(to: @shop.owner.email, subject: subject)
      end

      def notify_order_approved(order)
        mailer_initializer(order)
        subject = "Reserva #{@order.id} aprobada"
        mail(to: @shop.owner.email, subject: subject)
      end

      def notify_order_posted(order)
        mailer_initializer(order)
        subject = "Aviso de pago de la reserva #{@order.id}"
        mail(to: @shop.owner.email, subject: subject)
      end

      def notify_order_cancelled(order)
        mailer_initializer(order)
        subject = "Reserva #{@order.id} cancelada"
        mail(to: @shop.owner.email, subject: subject)
      end

      def notify_order_declined(order)
        mailer_initializer(order)
        subject = "Reserva #{@order.id} no aprobada"
        mail(to: @shop.owner.email, subject: subject)
      end

      private

      def mailer_initializer(order)
        @order = order
        @customer = order.customer
        @current_store = order.store
        @shop = order.shops.first
        @variant = order.items.map(&:product).first.variants.first
        @bna_info = order.info_package
      end
    end
  end
end

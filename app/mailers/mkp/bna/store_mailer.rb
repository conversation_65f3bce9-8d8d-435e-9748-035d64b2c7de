module Mkp
  module Bna
    class StoreMailer < Mailer
      default from: '<EMAIL>'.freeze

      BNA_MAIN_HASH = '0085'.freeze

      def notify_order_booked(order, id_store)
        mailer_initializer(order, id_store)
        subject = "Nueva reserva MiMoto"
        mail(to: @bna_mail, subject: subject)
      end

      def notify_order_billed(order, id_store)
        mailer_initializer(order, id_store)
        subject = "Aviso facturación reserva #{@order.id}"
        mail(to: @bna_mail, subject: subject)
      end

      def notify_order_pending_cancellation(order, id_store)
        mailer_initializer(order, id_store)
        subject = "Reserva #{@order.id} solicitar cancelacion a sucursal"
        mail(to: @bna_mail, subject: subject)
      end

      def notify_order_delivered(order, id_store)
        mailer_initializer(order, id_store)
        subject = "Producto entregado - reserva #{@order.id}"
        mail(to: @bna_mail, subject: subject)
      end

      private

      def mailer_initializer(order, id_store)
        @order = order
        @id_store = id_store
        @bna_mail = get_bna_mail(id_store)
        @customer = order.customer
        @current_store = order.store
        @shop = order.shops.first
        @variant = order.items.map(&:product).first.variants.first
        @bna_info = order.info_package
      end

      def get_bna_mail(bna_hash)
        bna_mail = if bna_hash == BNA_MAIN_HASH
                     %w[<EMAIL> <EMAIL>]
                   else
                     office = Mkp::Bna::Office.where(token: bna_hash).first
                     office.email if office.present?
                   end

        bna_mail
      end
    end
  end
end

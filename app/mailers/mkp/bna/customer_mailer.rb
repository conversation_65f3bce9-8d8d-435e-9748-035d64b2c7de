module Mkp
  module Bna
    class CustomerMailer < Mailer
      default from: '<EMAIL>'.freeze

      def notify_order_booked(order)
        mailer_initializer(order)
        subject = 'Confirmación de reserva'
        mail(to: @customer.email, subject: subject)
      end

      def notify_order_approved(order)
        mailer_initializer(order)
        subject = "Reserva #{@order.id} aprobada"
        mail(to: @customer.email, subject: subject)
      end

      def notify_order_delivered(order)
        mailer_initializer(order)
        subject = "Producto entregado - reserva #{@order.id}"
        mail(to: @customer.email, subject: subject)
      end

      private

      def mailer_initializer(order)
        @order = order
        @customer = order.customer
        @current_store = order.store
        @shop = order.shops.first
        @variant = order.items.map(&:product).first.variants.first
        @bna_info = order.info_package
      end
    end
  end
end

# frozen_string_literal: true

module Mkp
  # Send mails related to a purchase with VISA PUNTOS
  class PurchaseMailer < Mailer
    include NetworkExposure
    include CurrencyExposure

    helper Lux::LabelGatewaysHelper
    helper 'mailers/shipments'

    helper_method :marketplace_customer_email

    def sube_purchase_success(purchase, channel, card_number)
      @purchase       = purchase
      @customer       = purchase.customer
      @channel        = channel
      @card_number    = card_number
      @locale         = locale_to_use('AR')
      @network        = 'AR'
      @current_store  = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.sube_purchase_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: @purchase.email,
        subject: subject
      }
      mail(data)
    end

    def sube_purchase_error(purchase, card_number, status)
      @purchase       = purchase
      @customer       = purchase.customer
      @card_number    = card_number # "XXXX XXXX XXXX #{card_number[12..14]}X"
      @status         = status
      @locale         = locale_to_use('AR')
      @network        = 'AR'
      @current_store  = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.sube_purchase_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: @purchase.email,
        subject: subject
      }
      mail(data)
    end

    def voucher_exchange_success(purchase, variant, email)
      @purchase       = purchase
      @customer       = purchase.customer
      @variant        = variant
      @locale         = locale_to_use('AR')
      @network        = 'AR'
      @current_store  = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.voucher_exchange_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: email,
        subject: subject
      }
      mail(data)
    end

    def recarga_celular_purchase_success(purchase, phone_code, phone_number,
                                         phone_company, email)
      @purchase       = purchase
      @customer       = purchase.customer
      @phone_code = phone_code
      @phone_number = phone_number
      @phone_company = phone_company
      @locale = locale_to_use('AR')
      @network = 'AR'
      @current_store = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.recarga_celular_purchase_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: email,
        subject: subject
      }
      mail(data)
    end

    def aerolineas_purchase_success(purchase)
      @purchase       = purchase
      @customer       = purchase.customer
      @locale         = locale_to_use('AR')
      @network        = 'AR'
      @current_store  = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.aerolineas_purchase_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: @purchase.email,
        subject: subject
      }
      mail(data)
    end

    def points_exchange_success(purchase, variant, email)
      @purchase       = purchase
      @customer       = purchase.customer
      @variant        = variant
      @locale         = locale_to_use('AR')
      @network        = 'AR'
      @current_store  = purchase.store
      @logo_ref = 'mailers/order_confirmation.png'
      subject = t('mkp.purchase_mailer.points_exchange_success.subject',
                  locale: @locale)

      data = {
        from: from_store,
        to: email,
        subject: subject
      }
      mail(data)
    end
  end
end

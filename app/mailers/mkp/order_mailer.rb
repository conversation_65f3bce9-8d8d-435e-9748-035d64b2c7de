class Mkp::OrderMailer < Mailer
  include NetworkExposure
  include CurrencyExposure

  helper Lux::LabelGatewaysHelper
  helper 'mailers/shipments'

  helper_method :marketplace_customer_email

  def customer_purchase_confirmation(order)
    set_customer_data(order)
    @order = order
    @shipments = order.shipments
    unless order.payment.try(:payment_method).eql?('ticket')
      @gateway = order.payment.try(:gateway)
    end
    subject =t('mkp.order_mailer.customer_purchase_confirmation.subject', locale: @locale)

    data = {
      from: from_store,
      to: @customer.email,
      subject: subject
    }
    mail(data)
  end

  def customer_paid_confirmation(order)
    set_customer_data(order)
    subject = t('mkp.order_mailer.customer_paid_confirmation.subject', order_title: order.title, locale: @locale)

    set_google_analytics_campaign('userpayment')

    data = {
      from: from_store,
      to: @customer.email,
      subject: subject
    }
    mail(data)
  end

  def shop_fulfillment_notification(suborders, merchant)
    @seller          = merchant
    @customer        = suborders.first.customer
    @suborders       = suborders
    @suborder        = @suborders.first
    @shop_fulfilling = suborders.first.shop.fulfillment_shop
    @network         = merchant_network
    @locale          = locale_to_use(@network)
    @current_store   = @suborder.store
    layout_name     = 'v5/mailer/shop'

    set_google_analytics_campaign('brand')

    mail(
         from: from_store,
         to: @seller.email,
         subject: I18n.t('mkp.order_mailer.shop_fulfillment_notification.subject',
                         shops_title: suborders.map(&:shop).map(&:title).join(' - '),
                         locale: @locale)) do |format|
        format.html { render layout: layout_name }
    end
  end

  def customer_purchase_cancellation_notification(order, network, coupon)
    @order = order
    @customer = @order.customer
    @network = network
    @current_store = @order.store
    @coupon = coupon
    @logo_ref='mailers/order_cancellation.png'

    I18n.locale = locale_to_use(@network)
    subject = I18n.t('mkp.order_mailer.customer_purchase_cancellation_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    data = {
      from: from_store,
      to: @customer.email,
      subject: subject
    }
    mail(data)
  end

  def customer_purchase_refund_notification(order, network)
    @order = order
    @customer = @order.customer
    @network = network
    @current_store = @order.store
    @logo_ref='mailers/order_cancellation.png'

    I18n.locale = locale_to_use(@network)
    subject = I18n.t('mkp.order_mailer.customer_purchase_refund_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    data = {
        from: from_store,
        to: @customer.email,
        subject: subject
    }
    mail(data)
  end

  def customer_purchase_change_notification(order, network)
    @order = order
    @customer = @order.customer
    @network = network
    @current_store = @order.store
    @logo_ref='mailers/order_cancellation.png'

    I18n.locale = locale_to_use(@network)
    subject = I18n.t('mkp.order_mailer.customer_purchase_change_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    data = {
        from: from_store,
        to: @customer.email,
        subject: subject
    }
    mail(data)
  end

  def network_admin_purchase_cancellation_notification(order, network, coupon)
    layout_name = 'v5/mailer/shop'

    @network_admins = Pioneer::Admin.by_network(network).to_be_notified
    @order = order
    @current_store = @order.store

    I18n.locale = locale_to_use(network)
    subject = I18n.t('mkp.order_mailer.network_admin_purchase_cancellation_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    network_admin_emails = @network_admins.map(&:email)
    network_admin_emails = "<EMAIL>" unless network_admin_emails.present?

    data = {
      from: from_store,
      to: network_admin_emails,
      subject: subject
    }
    mail(data) { |f| f.html { render layout: layout_name } }
  end

  def network_admin_purchase_refund_notification(order, network)
    layout_name = 'v5/mailer/shop'

    @network_admins = Pioneer::Admin.by_network(network).to_be_notified
    @order = order
    @current_store = @order.store

    I18n.locale = locale_to_use(network)
    subject = I18n.t('mkp.order_mailer.network_admin_purchase_refund_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    network_admin_emails = @network_admins.map(&:email)
    network_admin_emails = "<EMAIL>" unless network_admin_emails.present?

    data = {
        from: from_store,
        to: network_admin_emails,
        subject: subject
    }
    mail(data) { |f| f.html { render layout: layout_name } }
  end

  def network_admin_purchase_change_notification(order, network)
    layout_name = 'v5/mailer/shop'

    @network_admins = Pioneer::Admin.by_network(network).to_be_notified
    @order = order
    @current_store = @order.store

    I18n.locale = locale_to_use(network)
    subject = I18n.t('mkp.order_mailer.network_admin_purchase_change_notification.subject', store: @order.store.title.titleize, order_title: order.title)

    network_admin_emails = @network_admins.map(&:email)
    network_admin_emails = "<EMAIL>" unless network_admin_emails.present?

    data = {
        from: from_store,
        to: network_admin_emails,
        subject: subject
    }
    mail(data) { |f| f.html { render layout: layout_name } }
  end

  def network_admin_paid_confirmation(order, network_admin)
    @network_admin  = network_admin
    @customer       = order.customer
    @order          = order
    @locale         = locale_to_use(order.network)
    @network        = order.network
    @transaction_id = order.purchase_id
    @shops_titles   = order.suborders.map{|suborder| suborder.shop.title}.join(', ')
    @current_store  = @order.store
    subject = I18n.t('mkp.order_mailer.network_admin_paid_confirmation.subject', store: @order.store.title.titleize,order_title: order.title, locale: @locale)

    data = {
      from: from_store,
      to: @network_admin.email,
      subject: subject
    }
    mail(data)
  end

  def network_admin_purchase_confirmation(order, network_admin)
    @network_admin  = network_admin
    @customer       = order.customer
    @order          = order
    @locale         = locale_to_use(order.network)
    @network        = order.network
    @transaction_id = order.purchase_id
    @shops_titles   = order.suborders.map{|suborder| suborder.shop.title}.join(', ')
    @current_store  = @order.store

    set_google_analytics_campaign('networkadmin')
    subject = I18n.t('mkp.order_mailer.network_admin_purchase_confirmation.subject', store: @order.store.title.titleize, order_title: order.title, locale: @locale)

    data = {
      from: from_store,
      to: @network_admin.email,
      subject: subject
    }
    mail(data)
  end

  def shop_paid_confirmation(suborder, recipient)
    if recipient.is_a?(String)
      @recipient = recipient
    else
      @seller = recipient
      @recipient = @seller.email
    end

    return if @recipient.include?('@avenida.com')

    layout_name = 'v5/mailer/shop'

    @customer = suborder.customer
    @suborder = suborder
    @shipment = suborder.shipment
    @shop = suborder.shop
    @network = merchant_network
    @locale = locale_to_use(@network)
    @transaction_id = suborder.purchase_id
    @current_store  = @suborder.store

    @packing_list_url = packing_list_url
    @shipment_label_url = shipment_label_url
    @shipment_label_extra_docs_url = shipment_label_extra_docs_url

    set_google_analytics_campaign('brand')

    mail(from: from_store,
         to: @recipient,
         subject: I18n.t('mkp.order_mailer.shop_paid_confirmation.subject',
         store: suborder.store.title.titleize,
         order_title: suborder.title, locale: @locale)) do |format|
           format.html { render layout: layout_name }
    end
  end

  def shop_reminder_notification(suborder, recipient, timeframe)
    layout_name = 'v5/mailer/shop'

    recipients = recipient.is_a?(String) ? recipient : recipient.email

    @suborder = suborder
    @shipment = suborder.shipment
    @shop = suborder.shop
    @network = merchant_network
    @locale = locale_to_use(@network)
    @transaction_id = suborder.purchase_id
    @timeframe = timeframe
    @current_store  = @suborder.store

    @packing_list_url = packing_list_url
    @shipment_label_url = shipment_label_url
    @shipment_label_extra_docs_url = shipment_label_extra_docs_url

    set_google_analytics_campaign('brand')

    mail(from: from_store,
         to: recipients,
         subject: I18n.t("mkp.order_mailer.shop_reminder_notification.subject_#{timeframe}",
         suborder_id: suborder.public_id, locale: @locale)) do |format|
           format.html { render layout: layout_name }
    end
  end

  protected

  def merchant_network
    if Network[@suborder.shop.network].thin?
      Network.default
    else
      @suborder.order.network
    end
  end

  def current_currency_format
    return super unless Network[@network].thin?
    super + ' '
  end

  def full_current_network
    Network[@network].thin? ? Network.default : @network
  end

  def marketplace_customer_email
    Network[full_current_network].marketplace_customer_email
  end

  private
  def set_customer_data(order)
    @customer       = order.customer
    @order          = order
    @locale         = locale_to_use(order.network)
    @transaction_id = order.purchase_id
    @network        = order.network
    @current_store  = order.store
    @logo_ref='mailers/order_confirmation.png'
  end

  def packing_list_url
    lux.defused_shipment_packing_list_url(defused_parameters)
  end

  def shipment_label_url
    lux.defused_shipment_label_url(defused_parameters)
  end

  def shipment_label_extra_docs_url
    lux.defused_shipment_label_extra_docs_url(defused_parameters)
  end

  def communication_token
    @shipment.present? ? encryptor.encrypt_and_encode(@shipment.id) : ''
  end

  def defused_parameters
    { suborder_id: @suborder.id, token: communication_token }
  end

  def encryptor
    NaiveTokenEncryptor.new(@suborder.encryptor_key, @suborder.encryptor_initialization_vector)
  end
end

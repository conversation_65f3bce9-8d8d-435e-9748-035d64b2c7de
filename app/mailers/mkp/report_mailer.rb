class Mkp::ReportMailer < Mailer
  layout 'v5/mailer/shop'

  def network_admin_notification(review, admin)

    @review         = review
    @admin          = admin
    @network        = @admin.network
    @locale         = locale_to_use(@network)

    mail(to: @admin.email,
         subject: t('mkp.report_mailer.network_admin_notification.subject',
                    locale: @locale))
  end

  def shop_admin_notification(review, shop_admin)

    @review     = review
    @shop_admin = shop_admin
    @network    = review.network
    @locale     = locale_to_use(@network)

    set_google_analytics_campaign('brand')

    mail(to: @shop_admin.email,
         subject: t('mkp.report_mailer.shop_admin_notification.subject',
                    locale: @locale, product: @review.product.title))
  end
end

class Mkp::In<PERSON><PERSON><PERSON><PERSON><PERSON> < Mailer
  def out_of_stock_reminder(variant_id)
    variant = Mkp::Variant.find(variant_id)

    @variant_name = if variant.properties.key?(:noproperty)
      t('mkp.inventory_mailer.out_of_stock_reminder.no_property')
    else
      variant.name
    end

    @product = variant.product
    @shop_admin = @product.shop.admins.first

    network = @product.network

    I18n.locale = locale_to_use(network)

    mail(
      from: "Avenida.com <<EMAIL>>",
      subject: t('mkp.inventory_mailer.out_of_stock_reminder.subject', variant_title: variant.title),
      to: "<EMAIL>"
    )
  end
end

require 'base64'

class SystemCommunication::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> < Mailer
  def with_items_only(system_communication)
    @customer = system_communication.customer
    @customer_name = customer_name
    set_display_icons
    load_items(system_communication.items)
    set_locale_to_use
    load_id
    load_recommended
    set_url('userleftcart01')


    build_mail
  end

  def selecting_address(system_communication)
    @customer = system_communication.customer
    @customer_name = customer_name
    load_items(system_communication.items)
    set_locale_to_use
    load_id
    load_recommended
    set_url('userleftcart02')


    build_mail
  end

  def selecting_payment(system_communication)
    @customer = system_communication.customer
    @customer_name = customer_name
    load_items(system_communication.items)
    set_locale_to_use
    set_display_icons
    load_id
    load_recommended
    set_url('userleftcart03')


    build_mail
  end

  def reviewing(system_communication)
    @customer = system_communication.customer
    @customer_name = customer_name
    load_items(system_communication.items)
    set_locale_to_use
    load_id
    load_recommended
    load_coupon(system_communication.coupon_id)
    set_url('userleftcart04')


    build_mail
  end

  protected

  def build_mail
    caller_method_name = caller[0].match(/`([^']*)'/)[1]
    subject = t('system_communication.left_cart_mailer' \
                ".#{caller_method_name}.subject",
                name: customer_name,
                title: @items.first.variant.title,
                locale: @locale)

    mail(to: @customer.email,
         subject: subject)
  end

  def customer_name
    @customer.first_name.presence ||
    @customer.last_name
  end

  def load_id
    id = ''
    @items.each do |item|
      id << "#{item.variant.id}+#{item.quantity}/"
    end
    @id = Base64.encode64(id)
  end

  def load_items(items)
    @items = []
    items.each do |variant_id, quantity|
      @items << OpenStruct.new(quantity: quantity,
                               variant: Mkp::Variant.unscoped.find(variant_id))
    end
  end

  def set_locale_to_use
    @network = @items.first.variant.product.network
    I18n.locale = locale_to_use(@network)
  end

  def set_url(utm_campaign)
    @cart_url = if GOOGLE_ANALYTICS.present?
      @google_analytics_params = google_analytics_params
      set_google_analytics_campaign(utm_campaign)
      mkp_cart_url(@network.downcase, @id)
    else
      mkp_cart_url(@network.downcase, @id)
    end
  end

  def load_coupon(coupon_id)
    @coupon = Mkp::Coupon::Network.find(coupon_id)
  end

  def load_recommended
    @recommended = Mkp::Product.joins(:variants)
                               .active
                               .with_stock
                               .by_network(@network)
                               .random(4)
  end

  def set_display_icons
    @display_checkout_icons = true
  end
end

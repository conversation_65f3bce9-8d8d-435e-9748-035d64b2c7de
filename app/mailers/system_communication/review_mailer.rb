class SystemCommunication::ReviewMailer < Mailer
  def feedback(communication)
    @purchase_id = if communication.suborder_id.present?
      suborder = Mkp::Suborder.find(communication.suborder_id)
      suborder.order.purchase_id
    elsif communication.shipment_id.present?
      items = Mkp::Shipment.find(communication.shipment_id).items
      items.first.order.purchase_id
    end

    @communication = communication
    @variants = Mkp::Variant.unscoped.find(communication.variants_ids)
    @network = communication.network.downcase
    @customer_name = customer_name

    set_locale_to_use
    set_brand
    set_urls

    set_google_analytics_campaign('userfeedback')

    subject = t('system_communication.review.subject', name: @customer_name, count: @variants.count)
    mail(to: @communication.customer.email, subject: subject)
  end

  def thanks(communication)
    @communication = communication
    @network = @communication.network
    @coupon = Mkp::Coupon.find(communication.coupon_id)

    review_communication = SystemCommunication::Review.find(communication.review_communication_id)

    shipment = Mkp::Shipment.find(review_communication.shipment_id)
    suborder = shipment.suborders.first

    @brand = if (brand = suborder.shop.brand)
      brand unless brand.type == 'SocialUser'
    end

    @brand_url = brand_url
    @google_analytics_params = google_analytics_params

    load_recommended
    set_locale_to_use

    set_google_analytics_campaign('userfeedback')

    subject = t('system_communication.review_mailer.thanks.subject', name: customer_name)
    mail(to: @communication.customer.email, subject: subject)
  end

  protected

  def load_recommended
    @recommended = Mkp::Product.with_stock.active.by_network(@network).sample(4)
  end

  def set_locale_to_use
    I18n.locale = locale_to_use(@communication.network)
  end

  def customer_name
    customer = @communication.customer
    customer.first_name.presence || customer.last_name
  end

  def set_brand
    @brand = brands.detect { |b| b.type == "Brand" }
  end

  def brands
    @brands ||= @variants.map(&:product).map(&:shop).flat_map(&:brand).uniq
  end

  def set_urls
    attributes = {
      order_id: @purchase_id,
      token: @communication.token,
      cid: @communication.id,
      network: @network.downcase
    }

    @review_url = new_mkp_order_orders_review_url(attributes)
    @brand_url = brand_url
  end

  def brand_url
    "http://#{HOSTNAME}/#{@brand.login}?#{google_analytics_params}" if @brand
  end
end

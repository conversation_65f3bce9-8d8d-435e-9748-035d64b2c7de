class SystemCommunication::Merchant<PERSON>ailer < Mailer
  layout "v5/mailer/shop"

  def communication(communication)
    admin = Pioneer::Admin.find(communication.sender_id)

    @body = communication.body
    @network = communication.network.downcase

    I18n.locale = locale_to_use(@network)

    if @body.blank?
      raise StandardError.new("Somehow the @body in the SystemCommunication::Merchant communication ID: #{communication.id} is empty")
    else
      mail(to: communication.recipient,
           cc: communication.cc_recipients,
           from: t('system_communication.merchant_mailer.from',
                   name: admin.first_name,
                   email: admin.email),
           subject: communication.subject)
    end
  end
end

<!DOCTYPE html>
<html>
  <head>
    <title>icons glyphs preview</title>

    <style>
      /* Page Styles */

      * {
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        background: #fff;
        color: #444;
        font: 16px/1.5 "Helvetica Neue", Helvetica, Arial, sans-serif;
      }

      a,
      a:visited {
        color: #888;
        text-decoration: underline;
      }
      a:hover,
      a:focus { color: #000; }

      header {
        border-bottom: 2px solid #ddd;
        margin-bottom: 20px;
        overflow: hidden;
        padding: 20px 0;
      }

      header h1 {
        color: #888;
        float: left;
        font-size: 36px;
        font-weight: 300;
      }

      header a {
        float: right;
        font-size: 14px;
      }

      .container {
        margin: 0 auto;
        max-width: 1200px;
        min-width: 960px;
        padding: 0 40px;
        width: 90%;
      }

      .glyph {
        border-bottom: 1px dotted #ccc;
        padding: 10px 0 20px;
        margin-bottom: 20px;
      }

      .preview-glyphs { vertical-align: bottom; }

      .preview-scale {
        color: #888;
        font-size: 12px;
        margin-top: 5px;
      }

      .step {
        display: inline-block;
        line-height: 1;
        position: relative;
        width: 10%;
      }

      .step .letters,
      .step i {
        -webkit-transition: opacity .3s;
        -moz-transition: opacity .3s;
        -ms-transition: opacity .3s;
        -o-transition: opacity .3s;
        transition: opacity .3s;
      }

      .step:hover .letters { opacity: 1; }
      .step:hover i { opacity: .3; }

      .letters {
        opacity: .3;
        position: absolute;
      }

      .characters-off .letters { display: none; }
      .characters-off .step:hover i { opacity: 1; }

      
      .size-12 { font-size: 12px; }
      
      .size-14 { font-size: 14px; }
      
      .size-16 { font-size: 16px; }
      
      .size-18 { font-size: 18px; }
      
      .size-21 { font-size: 21px; }
      
      .size-24 { font-size: 24px; }
      
      .size-36 { font-size: 36px; }
      
      .size-48 { font-size: 48px; }
      
      .size-60 { font-size: 60px; }
      
      .size-72 { font-size: 72px; }
      

      .usage { margin-top: 10px; }

      .usage input {
        font-family: monospace;
        margin-right: 3px;
        padding: 2px 5px;
        text-align: center;
      }

      .usage .point { width: 150px; }

      .usage .class { width: 250px; }

      footer {
        color: #888;
        font-size: 12px;
        padding: 20px 0;
      }

      /* Icon Font: icons */

      @font-face {
  font-family: "icons";
  src: url("./icons.eot");
  src: url("./icons.eot?#iefix") format("embedded-opentype"),
       url("./icons.woff") format("woff"),
       url("./icons.ttf") format("truetype"),
       url("./icons.svg#icons") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "icons";
    src: url("./icons.svg#icons") format("svg");
  }
}

      [data-icon]:before { content: attr(data-icon); }

      [data-icon]:before,
      .i-badge-charitable:before,
.i-badge-charitable-bg:before,
.i-badge-community:before,
.i-badge-community-bg:before,
.i-badge-ethical:before,
.i-badge-ethical-bg:before,
.i-badge-sustainable:before,
.i-badge-sustainable-bg:before,
.i-bell:before,
.i-brand-ambassador:before,
.i-brand-supporter:before,
.i-carrito:before,
.i-categories:before,
.i-chat:before,
.i-checkbox-off:before,
.i-checkbox-on:before,
.i-close:before,
.i-comunidad:before,
.i-counter-pic:before,
.i-counter-txt:before,
.i-counter-video:before,
.i-e-shop:before,
.i-flag:before,
.i-flecha-down:before,
.i-flecha-right:before,
.i-high-five:before,
.i-icon-trash-bin:before,
.i-info:before,
.i-installments:before,
.i-like:before,
.i-log-in:before,
.i-log-out:before,
.i-logo-facebook:before,
.i-logo-hexagono:before,
.i-logo-twitter:before,
.i-lupa:before,
.i-mail:before,
.i-menu:before,
.i-phone:before,
.i-play:before,
.i-plus:before,
.i-post:before,
.i-preferencias:before,
.i-radio-off:before,
.i-radio-on:before,
.i-registro:before,
.i-returns:before,
.i-secure:before,
.i-shipping:before,
.i-shipping-round:before,
.i-shop:before,
.i-social-facebook:before,
.i-social-foursquare:before,
.i-social-instagram:before,
.i-social-pinterest:before,
.i-social-tumblr:before,
.i-social-twitter:before,
.i-social-vimeo:before,
.i-social-youtube:before,
.i-star:before,
.i-tilde:before {
        display: inline-block;
  font-family: "icons";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
      }

      .i-badge-charitable:before { content: "\f114"; }
.i-badge-charitable-bg:before { content: "\f134"; }
.i-badge-community:before { content: "\f115"; }
.i-badge-community-bg:before { content: "\f135"; }
.i-badge-ethical:before { content: "\f116"; }
.i-badge-ethical-bg:before { content: "\f136"; }
.i-badge-sustainable:before { content: "\f117"; }
.i-badge-sustainable-bg:before { content: "\f137"; }
.i-bell:before { content: "\f100"; }
.i-brand-ambassador:before { content: "\f118"; }
.i-brand-supporter:before { content: "\f119"; }
.i-carrito:before { content: "\f101"; }
.i-categories:before { content: "\f102"; }
.i-chat:before { content: "\f124"; }
.i-checkbox-off:before { content: "\f13a"; }
.i-checkbox-on:before { content: "\f139"; }
.i-close:before { content: "\f133"; }
.i-comunidad:before { content: "\f103"; }
.i-counter-pic:before { content: "\f11a"; }
.i-counter-txt:before { content: "\f11b"; }
.i-counter-video:before { content: "\f11c"; }
.i-e-shop:before { content: "\f104"; }
.i-flag:before { content: "\f111"; }
.i-flecha-down:before { content: "\f12c"; }
.i-flecha-right:before { content: "\f12d"; }
.i-high-five:before { content: "\f11d"; }
.i-icon-trash-bin:before { content: "\f13d"; }
.i-info:before { content: "\f13e"; }
.i-installments:before { content: "\f13f"; }
.i-like:before { content: "\f112"; }
.i-log-in:before { content: "\f106"; }
.i-log-out:before { content: "\f107"; }
.i-logo-facebook:before { content: "\f131"; }
.i-logo-hexagono:before { content: "\f125"; }
.i-logo-twitter:before { content: "\f132"; }
.i-lupa:before { content: "\f108"; }
.i-mail:before { content: "\f12e"; }
.i-menu:before { content: "\f109"; }
.i-phone:before { content: "\f127"; }
.i-play:before { content: "\f11e"; }
.i-plus:before { content: "\f11f"; }
.i-post:before { content: "\f10a"; }
.i-preferencias:before { content: "\f10b"; }
.i-radio-off:before { content: "\f13c"; }
.i-radio-on:before { content: "\f13b"; }
.i-registro:before { content: "\f10c"; }
.i-returns:before { content: "\f140"; }
.i-secure:before { content: "\f128"; }
.i-shipping:before { content: "\f129"; }
.i-shipping-round:before { content: "\f141"; }
.i-shop:before { content: "\f10d"; }
.i-social-facebook:before { content: "\f120"; }
.i-social-foursquare:before { content: "\f12f"; }
.i-social-instagram:before { content: "\f121"; }
.i-social-pinterest:before { content: "\f122"; }
.i-social-tumblr:before { content: "\f130"; }
.i-social-twitter:before { content: "\f123"; }
.i-social-vimeo:before { content: "\f12a"; }
.i-social-youtube:before { content: "\f12b"; }
.i-star:before { content: "\f113"; }
.i-tilde:before { content: "\f138"; }
    </style>

    <!--[if lte IE 8]><script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->

    <script>
      function toggleCharacters() {
        var body = document.getElementsByTagName('body')[0];
        body.className = body.className === 'characters-off' ? '' : 'characters-off';
      }
    </script>
  </head>

  <body class="characters-off">
    <div id="page" class="container">
      <header>
        <h1>icons contains 61 glyphs:</h1>
        <a onclick="toggleCharacters(); return false;" href="#">Toggle Preview Characters</a>
      </header>

      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-charitable" class="i-badge-charitable"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-charitable" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf114;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-charitable-bg" class="i-badge-charitable-bg"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-charitable-bg" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf134;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-community" class="i-badge-community"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-community" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf115;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-community-bg" class="i-badge-community-bg"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-community-bg" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf135;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-ethical" class="i-badge-ethical"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-ethical" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf116;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-ethical-bg" class="i-badge-ethical-bg"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-ethical-bg" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf136;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-sustainable" class="i-badge-sustainable"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-sustainable" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf117;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-badge-sustainable-bg" class="i-badge-sustainable-bg"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-badge-sustainable-bg" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf137;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-bell" class="i-bell"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-bell" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf100;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-brand-ambassador" class="i-brand-ambassador"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-brand-ambassador" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf118;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-brand-supporter" class="i-brand-supporter"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-brand-supporter" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf119;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-carrito" class="i-carrito"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-carrito" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf101;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-categories" class="i-categories"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-categories" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf102;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-chat" class="i-chat"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-chat" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf124;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-checkbox-off" class="i-checkbox-off"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-checkbox-off" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13a;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-checkbox-on" class="i-checkbox-on"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-checkbox-on" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf139;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-close" class="i-close"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-close" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf133;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-comunidad" class="i-comunidad"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-comunidad" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf103;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-counter-pic" class="i-counter-pic"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-counter-pic" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11a;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-counter-txt" class="i-counter-txt"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-counter-txt" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11b;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-counter-video" class="i-counter-video"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-counter-video" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11c;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-e-shop" class="i-e-shop"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-e-shop" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf104;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-flag" class="i-flag"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-flag" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf111;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-flecha-down" class="i-flecha-down"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-flecha-down" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12c;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-flecha-right" class="i-flecha-right"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-flecha-right" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12d;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-high-five" class="i-high-five"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-high-five" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11d;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-icon-trash-bin" class="i-icon-trash-bin"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-icon-trash-bin" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13d;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-info" class="i-info"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-info" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13e;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-installments" class="i-installments"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-installments" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13f;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-like" class="i-like"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-like" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf112;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-log-in" class="i-log-in"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-log-in" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf106;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-log-out" class="i-log-out"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-log-out" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf107;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-logo-facebook" class="i-logo-facebook"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-logo-facebook" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf131;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-logo-hexagono" class="i-logo-hexagono"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-logo-hexagono" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf125;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-logo-twitter" class="i-logo-twitter"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-logo-twitter" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf132;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-lupa" class="i-lupa"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-lupa" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf108;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-mail" class="i-mail"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-mail" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12e;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-menu" class="i-menu"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-menu" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf109;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-phone" class="i-phone"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-phone" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf127;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-play" class="i-play"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-play" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11e;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-plus" class="i-plus"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-plus" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf11f;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-post" class="i-post"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-post" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf10a;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-preferencias" class="i-preferencias"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-preferencias" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf10b;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-radio-off" class="i-radio-off"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-radio-off" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13c;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-radio-on" class="i-radio-on"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-radio-on" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf13b;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-registro" class="i-registro"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-registro" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf10c;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-returns" class="i-returns"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-returns" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf140;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-secure" class="i-secure"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-secure" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf128;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-shipping" class="i-shipping"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-shipping" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf129;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-shipping-round" class="i-shipping-round"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-shipping-round" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf141;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-shop" class="i-shop"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-shop" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf10d;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-facebook" class="i-social-facebook"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-facebook" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf120;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-foursquare" class="i-social-foursquare"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-foursquare" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12f;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-instagram" class="i-social-instagram"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-instagram" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf121;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-pinterest" class="i-social-pinterest"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-pinterest" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf122;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-tumblr" class="i-social-tumblr"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-tumblr" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf130;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-twitter" class="i-social-twitter"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-twitter" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf123;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-vimeo" class="i-social-vimeo"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-vimeo" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12a;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-social-youtube" class="i-social-youtube"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-social-youtube" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf12b;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-star" class="i-star"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-star" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf113;" />
        </div>
      </div>
      
      <div class="glyph">
        <div class="preview-glyphs">
          <span class="step size-12"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-14"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-16"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-18"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-21"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-24"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-36"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-48"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-60"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span><span class="step size-72"><span class="letters">Pp</span><i id="i-tilde" class="i-tilde"></i></span>
        </div>
        <div class="preview-scale">
          <span class="step">12</span><span class="step">14</span><span class="step">16</span><span class="step">18</span><span class="step">21</span><span class="step">24</span><span class="step">36</span><span class="step">48</span><span class="step">60</span><span class="step">72</span>
        </div>
        <div class="usage">
          <input class="class" type="text" readonly="readonly" onClick="this.select();" value=".i-tilde" />
          <input class="point" type="text" readonly="readonly" onClick="this.select();" value="&amp;#xf138;" />
        </div>
      </div>
      

      <footer>
        Made with love using <a href="http://fontcustom.com">Font Custom</a>.
      </footer>
    </div>
  </body>
</html>

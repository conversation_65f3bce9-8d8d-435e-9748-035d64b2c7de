gp.Mkp.Product = gp.App.Model.extend({
  urlRoot: '/api/shop/products',

  validation: {
    title: {
      validate: function(title) {
        if( !_.isString(title) ||  !title.length ){
          return "Title can't be blank."
        }
      }
    },
    description: true
  },

  price: function(){
    return this.get('sale_price') || this.get('regular_price') || 0
  },

  getPicture: function(id){
    id = +id
    return _.find(this.get('pictures'), function(p){
      return (+p.id) === id
    })
  },

  getVariant: function(id){
    id = +id
    return _.find(this.get('variants'), function(v) {
      return (+v.id) === id
    })
  },

  getPictureWhereColor: function(color){
    if( !color ) return null
    return _.find(this.get('pictures'), function(p){
      return p.color === color
    })
  },

  availablePropertyValuesWhere: function(property, properties) {
    var values = []
    var propertiesNames = _.keys(properties)

    _.each(this.get('variants'), function(variant) {
      if (typeof variant.properties[property] === 'undefined') return

      var p = _.pick(variant.properties, propertiesNames)
      // Becuase the way we are dealing with the colors and the _.isEqual()
      if( _.contains(propertiesNames, 'color') ){
        p.color = p.color.slug_name
      }
      if (_.isEqual(p, properties)) {
        value = variant.properties[property]
        values.push(value)
      }
    })

    return _.uniq(values)
  },

  variantsWhere: function(properties) {
    var that = this

    if (!properties) {
      return this.get('variants')
    }

    return _.filter(this.get('variants'), function(variant) {
      return _.every(properties, function(value, key) {
        var variant_value = variant.properties[key]
        // Becuase the way we are dealing with the colors and the _.isEqual()
        variant_value = typeof variant_value === 'string' ? variant_value : variant_value.slug_name
        return _.isEqual(variant_value, value.toString())
      })
    })
  },

  availableQuantityFor: function(properties) {
    return _.some(this.variantsWhere(properties), function(variant) {
      return !!variant.quantity
    })
  }

})

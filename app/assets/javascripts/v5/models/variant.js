gp.Mkp.Variant = gp.App.Model.extend({
  urlRoot: '/api/shop/variants',

  price: function(){
    var attrs = this.attributes.product
    return (+(attrs.sale_price || attrs.regular_price)).toFixed(2)
  },

  picture: function(){
    var pic = {}
    pic.id = this.get('picture_id')
    pic.thumb = this.get('picture_thumb')
    return pic ? pic : null
  },

  onSale: function(){
    return !!this.attributes.product.sale_price
  }
})

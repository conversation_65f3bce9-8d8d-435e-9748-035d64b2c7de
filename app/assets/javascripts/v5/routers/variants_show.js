;if( gp.controller.variants && gp.action.show && Modernizr.history ) $(function(){
  var productUrl = $('head link[rel="canonical"]').first()
  if( !productUrl.length ) return
  productUrl = productUrl.attr('href')

  var Router = Backbone.Router.extend({
    routes: {
      '': 'showProduct',
      '-:color': 'showColor'
    },

    showProduct: function() {
      app.navigate('-' + gp.product.color_slugs[gp.selectedColor], { trigger: true })
    },

    showColor: function(color) {
      window._gaq && _gaq.push(['_trackPageview', window.location.pathname])
    }
  })

  Backbone.history.start({
    pushState: true,
    hashChange: false,
    root: productUrl
  })

  // Fix: Backbone always appends trailing slash, we have to remove it.
  Backbone.history.root = productUrl

  var app = new Router()
  app.listenTo(gp.pubSub, 'product:properties:change:color', function(color) {
    var color_slug = ( typeof color === 'string' ) ? color : color.slug_name
    if( _.contains(_.keys(gp.product.color_slugs), color_slug) ){
      app.navigate('-' + color_slug, { trigger: true, replace: true })
    }
  })

  $document.on('click', 'a[href^="' + productUrl + '"]', function(e) {
    if (e.altKey || e.ctrlKey || e.metaKey || e.shiftKey) return

    e.preventDefault()

    return false
  })
});

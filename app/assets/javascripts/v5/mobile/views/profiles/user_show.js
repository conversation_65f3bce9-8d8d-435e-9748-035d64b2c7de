;if( gp.controller.profiles && (gp.action.brand_show ||gp.action.nonprofit_show) ) ( function(){
  var $mobileResume = $('.user-resume-mobile')
  if( typeof $mobileResume === 'undefined' || $mobileResume.hasClass('with-1') ) return

  var $points = $mobileResume.find('.points span')

  var slider = new IScroll($mobileResume[0], {
    momentum: false,
    click: true,
    snap: '.scroll-item',
    scrollX: true,
    scrollY: false,
    eventPassthrough: true
  })

  var $activePoint = $points.filter('.active')

  var updatePoints = _.debounce(function(){
    var $point = $($points.get(slider.currentPage.pageX))
    if( $activePoint.is($point) ) return
    $activePoint.removeClass('active')
    $activePoint = $point.addClass('active')
  }, 15)

  slider.on('scrollEnd', updatePoints)
})()

// Menu
$document.on('click', '.mobile-header .h-btn-menu', gp.Views['partials/menu'].open)
$document.on('click', '.menu-wrapper-overlay', gp.Views['partials/menu'].close)

// Search
$document.on('click', '.mobile-header .h-btn-search', function(e){
  gp.Views['partials/menu'].open()
  gp.Views['partials/menu'].search_focus()
  return e.stop()
})

// Create post
$document.on('click', '.mobile-header .h-btn-create', function(e){
  gp.Views['partials/whatsups_form'].open()
  gp.Views['partials/menu'].close()
  return e.stop()
})

// Login
$document.on('click', '#mobile-login', function(e){
  gp.Views['partials/login_dropdown'].open()
  gp.Views['partials/menu'].close()
  return e.stop()
})

// Cart <PERSON>
;(function(){
  if( !gp.cart ) return

  var $el = $('.mobile-header .h-btn-cart')

  $document.on('click', '.mobile-header .h-btn-cart', gp.Views['partials/cart'].open)

  var $count = $el.find('.h-btn-count')
  var onCountChange = _.debounce(function(){
    var count = gp.cart.cartCount()
    $count.attr('data-count', count).html(count)
  }, 100)

  gp.cart.on('cart:change', onCountChange)
})()

// Search
//$('.search-mobile').toggle();
$document.on('click', '.mobile-header .h-btn-login, #search-mobile', function(e){
  gp.Views['partials/search'].open()
  return e.stop()
})
$document.on('click', '.mobile-header .h-btn-login, #close-search', function(e){
  gp.Views['partials/search'].close()
  return e.stop()
})

// Expand categories by default
$('.shop-by-category').click();

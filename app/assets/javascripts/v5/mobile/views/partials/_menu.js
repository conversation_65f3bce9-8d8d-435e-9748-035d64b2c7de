;(function(){
  var V = gp.Views['partials/menu'] = {}

  var $el = V.$el = $('.menu-wrapper')

  V.open = function() { $el.addClass('active') }
  V.close = function() { $el.removeClass('active') }
  V.search_focus = function() { $el.find('.mobile-search-bar input[name=query]').delay(250).focus() }
})()

// Categories
;(function(){
  var active = 'active'

  var $mainMenu = gp.Views['partials/menu'].$el
  var $shopCats = $mainMenu.find('.shop-by-category')
  var $cats = $mainMenu.find('.mobile-categories-menu')

  $mainMenu.on('click', '.shop-by-category', function(e){
    $shopCats.add($cats).toggleClass(active)
    e.stopPropagation(); e.preventDefault()
    return false
  })

  var $catActive
  $mainMenu.on('click', '.category-item.with-submenu .category-item-title', function(e){
    var $el = $(e.currentTarget)
    $el = $el.add($el.parent())
    if( $catActive && $catActive.is($el) ) {
      $catActive.removeClass(active)
      $catActive = null
    } else {
      if( $catActive ) {
        $catActive.removeClass(active)
        $catActive = null
      }
      $el.addClass(active)
      $catActive = $el
    }
    e.stopPropagation(); e.preventDefault()
    return false
  })
})()

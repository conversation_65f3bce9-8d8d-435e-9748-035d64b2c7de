// Feature Variants Scroll
;if( gp.controller.landings ) (function(){

  var $featureVariants = $('.featured-variants-cmpnt .scroll-wrapper')
  var $featuredSports = $('.cols5-generic-v1-vnrs .scroll-wrapper')

  if( typeof $featureVariants !== 'undefined' ) {
    $featureVariants.each(function() {
      new IScroll(this, {
        momentum: true,
        scrollX: true,
        scrollY: false,
        eventPassthrough: true
      })
    })
  }
  if( typeof $featuredSports !== 'undefined' ) {
    $featuredSports.each(function() {
      new IScroll(this, {
        momentum: true,
        scrollX: true,
        scrollY: false,
        eventPassthrough: true
      })
    })
  }
})()

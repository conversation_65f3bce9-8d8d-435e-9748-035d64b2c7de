/**
 * v5 Form Helper
**/
;(function(){
  var F = gp.Helpers.v5.form = {}
  // Complete<PERSON> agreed with this blog post about the email regex validation
  // http://slainer68.wordpress.com/2010/09/02/rails-3-html-5-and-client-side-forms-validations-using-validator
  F.validation_patterns = {
    'email': /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+(com|net|com.ar|gov|gob|org|es)))$/i
  }

  var t = I18n.withScope('js.v5.helpers.form')

  // Function to validate a field, based on classes and the  HTML5 attributes (data-*)
  // @returns false if there were no errors or an array of the errors
  F.validate = function($field){
    var errors = []
    ;[
      F.validate.required,
      F.validate.validation
    ].forEach(function(validator){
      var e = validator($field)
      if( e ) errors.push(e)
    })

    return !!errors.length ? errors : false
  }

  F.validate.required = function($field){
    if( ( $field.hasClass('required') || $field.prop('required') ) && !$field.val() ) {
      return 'required'
    }
    return false
  }

  F.validate.validation = function($field){
    var pattern = $field.data('pattern') || $field.attr('pattern')
    var validation = $field.data('validation') || $field.attr('validation')

    // Fallback if there is no pattern found and if it's a predefined
    if ( !pattern && !!validation ){
      pattern = F.validation_patterns[validation]
    }

    if( pattern && !RegExp(pattern).test($field.val()) ) {
      return 'validation'
    }
    return false
  }

  F.ajaxify = function($form, beforeSubmit){
    var _makeCall = function(){
      $form.trigger('form:submit')
      var dataType = $form.data('type') || 'json'
      $.ajax({
        url: $form.attr('action'),
        type: $form.attr('method'),
        data: $form.formParams(),
        dataType: dataType,
        cache: false
      }).done(function(data){
        $form.trigger('form:done', data)
      }).fail(function(xhr){
        $form.trigger('form:fail', xhr)
      }).always(function(){
        $form.trigger('form:always', gp.Helpers.v5.array.argsToArray(arguments))
      })
    }

    $form.on('submit', function(e){
      e.preventDefault(), e.stopPropagation()
      if( typeof beforeSubmit === 'function' ) {
        if( !beforeSubmit() ) return false
      }
      _makeCall()
      return false
    })

    return $form
  }

})()

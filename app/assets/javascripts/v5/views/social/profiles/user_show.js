;if( gp.controller.profiles && (gp.action.brand_show || gp.action.nonprofit_show) ) (function(){
  var $self = $('#profiles-v5-brand_show')

  var userFollowButton = new gp.Views['partials/social/follow_btn']({
    el: $self.find('.user-resume .follow-btn')
  })

  gp.App.View.autoInstantiate(gp.Views['posts/list'])

  $self.find('.user-resume .simple-avatar img').on('click', function(e){
    var $img = $(e.currentTarget)
    $.magnificPopup.open({
      items: {
        src: $img.attr('src')
      },
      type: 'image'
    })
  })
})()

;(function(){

var V = gp.Views['partials/notifications'] = gp.App.View.extend({
  itemTemplate: null,
  $list: null,

  events: {
    'click .read-all': 'readAll'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'onC',
      'open',
      'close',
      'reset',
      'add',
      'readAll'
    )

    this.$list = this.$('.notifications-items-list')
    this.itemTemplate = this.template('notifications-item')

    this.onC('reset', this.reset)
    this.onC('add', this.add)

    this.reset()
    this.$el.removeClass('loading')
  },

  onC: function(event, callback){
    return this.listenTo(this.collection, event, callback)
  },

  open: function(){
    var self = this

    $.magnificPopup.open({
      showCloseBtn: false,
      enableEscapeKey: false,
      mainClass: 'notifications-popup',
      items: {
        src: this.$el,
        type: 'inline'
      },

      callbacks: {
        open: function(){
          gp.Helpers.lockScroll.lock(self.$list)
        },
        close: gp.Helpers.lockScroll.unlock
      }
    })

    return this
  },

  close: function(){
    $.magnificPopup.close()
    return this
  },

  reset: function(){
    var that = this
    this.$list.find('.notifications-item').remove()
    this.collection.each(function(n){ that.add(n) })
  },

  add: function(n){
    var obj = n.toJSON()
    obj.extraClass = obj.read ? '' : 'unread'
    var $notif = this.itemTemplate(obj)
    var $text = $notif.find('.text')
    $notif.html(gp.Helpers.string.unscapeHTML($notif.html()))
    this.$list.append($notif)
    this.$el.removeClass('empty')
  },

  readAll: function(){
    this.collection.readAll()
  }
},{

  open: function() {
    var v = new gp.Views['partials/notifications']({
      el: $('#notifications .notifications'),
      collection: gp.notifications
    })

    gp.Views['partials/notifications'] = v

    return v.open()
  }

})

})()
$document.on('click', '.favorite-btn', function(e){
  if( gp.Helpers.loginModal(I18n.t('javascripts.partials.favorite_button.need_login'), { v5: true }) ) {
    e.preventDefault()
    e.stopPropagation()
    return false
  }

  var $el = $(e.currentTarget)
  var favorited = $el.hasClass('favorited')

  if( favorited ) {
    $el.removeClass('favorited')
    gp.favorites.unfavorite($el.data('url'), $el.data('type'))
      .fail(function(){
        $el.addClass('favorited')
      })
  } else {
    $el.addClass('favorited')
    gp.favorites.favorite($el.data('url'), $el.data('type'))
      .fail(function(){
        $el.removeClass('favorited')
      })
  }
})
;(function(){
  $(document).ready(function() {
    $tracking = $("#tracking_order");
    $tracking.change(function(){
      $tracking.removeClass('error')
    })
    $tracking.keypress(function (event) {
      if (!(event.which == 13 || event.which == 10)) return;
      val = $tracking.val().trim()
      val = val.charAt(0) === '#' ? val.substring(1) : val;
      $.get($tracking.data('url') + val)
        .done(function(res){
          var popup = $.magnificPopup.open({
            items: {
              src: '<div style="padding:10px;width:100%;max-width:600px;background-color:white;margin:0 auto;">' + res + '</div>', // can be a HTML string, jQuery object, or CSS selector
              type: 'inline'
            }
          });
        })
        .fail(function(res){
          $tracking.addClass('error')
        })
      event.preventDefault();
    })
  })
})()
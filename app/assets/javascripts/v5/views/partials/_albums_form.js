;(function(){
  var V = gp.Views['partials/albums_form'] = gp.App.View.extend({
    events: {
      'opened': 'onShow',
      'submit form#new_social_album': 'submit'
    },

    initialize: function(){
      _.bindAll(
        this,
        'open',
        'close',
        'onShow',
        'submit',
        'disableSubmitButton',
        'enableSubmitButton',
        'initAddMoreImages',
        'addImageFiles',
        'addImageFile',
        'setSportTags'
      )

      var self = this
      this.show()

      this.$hiddens = this.$('.hiddens')
      this.setSportTags()

      gp.Helpers.file.init()
        .done(function(){
          self.uploader = self.autoInstantiate(gp.Views['partials/pictures_uploader'])[0]
          if( self.options && self.options.preloadedFiles ){
            self.addImageFiles(self.options.preloadedFiles)
          }
          self.initAddMoreImages()
        })
        .fail(function(){
          alert('Your device is not compatible for uploading media.')
        })
      return this
    },

    open: function(){
      var self = this

      $.magnificPopup.open({
        showCloseBtn: false,
        enableEscapeKey: false,
        mainClass: 'albums-form-popup',
        items: {
          src: this.$el,
          type: 'inline'
        }
        // ,
        // callbacks: {
        //   open: function(){
        //     gp.Helpers.lockScroll.lock(self.$list)
        //   },
        //   close: gp.Helpers.lockScroll.unlock
        // }
      })

      return this
    },

    close: function(){
      $.magnificPopup.close()
      return this
    },

    onShow: function(){
      var that = this
      _.defer(function(){
        that.$('form input[type="text"], form textarea').first().focus()
      })
    },

    submit: function(e){
      if ( this.$el.hasClass('loading') ) {
        e.preventDefault()
        return false
      }
      var params = this.$('form').formParams().social_album
      if(!params.unpublished_picture_attributes) {
        alert( I18n.t('javascripts.social.whatsups.no_pictures') )
        e.preventDefault()
        return false
      }
      var model = new gp.Social.Album({
        title: params.title
      })
      if ( !model.isValid() ) {
        var errors = _.values(model.validationError).join(' ')
        alert(errors)
        e.preventDefault()
        return false
      }
    },

    disableSubmitButton: function() {
      this.$('.post-item').prop('disabled', true)
    },

    enableSubmitButton: function() {
      this.$('.post-item').prop('disabled', false)
    },

    initAddMoreImages: function(){
      var that = this
      var fileOptions = V.fileOptions
      var $addImages = this.$('.add-photos')
      var fileListener = gp.Helpers.file.listen($addImages, fileOptions)

      if( !fileListener ) {
        alert('There was an error creating the album.')
      }

      fileListener
        .on('files:selected', function(files){
          that.addImageFiles(files)
        })
        .on('files:error:maxFileSize', function(file, maxFileSize){
          var msg = I18n.t(
            'javascripts.partials.form.upload_file_too_big',
            { max: maxFileSize }
          )
          alert(msg)
        })
        .on('files:error:accept', function(file, types){
          var msg = I18n.t(
            'javascripts.partials.form.file_type_not_allowed',
            { types: types.join(', ') }
          )
          alert(msg)
        })
    },

    addImageFiles: function(files){
      if( !files ) return
      if( files.length ) {
        for( var i = 0; i < files.length; i++ ) {
          this.addImageFile(files[i])
        }
      } else if( files.type ) {
        this.addImageFile(files)
      }
    },

    _imageFilesUploadingOrQueue: 0,
    _imageFilesDoneAndReady: 0,
    addImageFile: function(file){
      var that = this
      this._imageFilesUploadingOrQueue++
      this.disableSubmitButton()

      var $id = $('<input/>', {
        type: 'hidden',
        name: 'social_album[unpublished_picture_attributes][][id]'
      })
      var $description = $('<input/>', {
        type: 'hidden',
        name: 'social_album[unpublished_picture_attributes][][description]'
      })

      var $picHiddens = $id.add($description)

      $picHiddens.appendTo(this.$hiddens)

      var $file = this.uploader.add(file)
      var done = false

      var _checkAbilityToSubmit = function() {
        if ( that._imageFilesDoneAndReady >= 1 && that._imageFilesUploadingOrQueue === 0 ) {
          that.enableSubmitButton()
        } else if ( that._imageFilesDoneAndReady === 0 ) {
          that.disableSubmitButton()
        }
      }

      $file
        .on('file:done', function(e, data){
          that._imageFilesDoneAndReady++
          that._imageFilesUploadingOrQueue--
          $id.val(data.picture.id)
          done = true
          _checkAbilityToSubmit()
        })
        .on('file:fail', function(e, data){
          that._imageFilesUploadingOrQueue--
          $picHiddens.remove()
          alert( I18n.t('javascripts.partials.form.named_upload_error', { file: file.name }) )
          _checkAbilityToSubmit()
        })
        .on('file:remove', function(e, data){
          if ( done ) {
            that._imageFilesDoneAndReady--
          }
          if ( that._imageFilesUploadingOrQueue > 0) {
            that._imageFilesUploadingOrQueue--
          }
          $picHiddens.remove()
          _checkAbilityToSubmit()
        })
        .on('input', '[name="unpublished_picture[description]"]', function(e){
          $description.val( $(e.currentTarget).val() )
        })
    },

    setSportTags: function(){
      var $select = this.$('#social_album_sport_ids')

      $select.find('option').each(function(i, el){
        var $el = $(el)
        $el.text('#'+$el.text())
      })

      $select.selectize()
    }
  },{
    fileOptions: {
      accept: ['image/bmp','image/gif','image/jpeg','image/png'],
      multiple: true,
      maxFileSize: 24
    },

    open: function() {
      gp.Views['partials/albums_form'] = new V({
        el: $('#albums-form .albums-form')
      })

      return gp.Views['partials/albums_form'].open()
    }
  })
})()

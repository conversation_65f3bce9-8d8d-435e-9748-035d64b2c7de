// Newsletter Suscription
;(function(){
  var $el = $('.newsletter-subscription')
  var $form = $el.find('form')
  var $done = $el.find('.done-msg')

  gp.Helpers.form.ajaxify($form)
    .on('form:submit', function(){
      $el.addClass('loading')
    })
    .on('form:done', function(){
      $form.fadeOut(function(){
        $done.fadeIn()
      })
    })
    .on('form:fail', function(){
      alert('There was an error, try again later.')
    })
    .on('form:always', function(){
      $el.removeClass('loading')
    })
})()
gp.Views['partials/file_uploader'] = gp.App.View.extend({
  className: 'file-uploader',

  intialize: function(options) {
    _.bindAll(this, 'render', 'add')
  },

  render: function() {
    this.$itemTemplate = this.template('uploader-item')
    this.$msg = this.$('.file-uploader-msg')

    return this
  },

  add: function(file) {
    var that = this

    // Load the template for the uplaoder file item list
    var $item = this.$itemTemplate({
      progress: 0,
      thumb: '',
      name: file.name
    })
    var $thumb = $item.find('.file-uploader-item-thumb')
    var $progressPercentage = $item.find('.file-uploader-item-progress-percentage')
    var $progressBar = $item.find('.file-uploader-item-progress-bar')

    // Get the thumbnail
    gp.Helpers.file.getThumb(file)
      .done(function(img){
        var $img = $(img).css({
          width: '100%',
          height: '100%'
        })
        $thumb.html($img)
      })
      .fail(function(){
        $item.addClass('no-thumb')
      })

    // Append the file item to the list
    this.$el.append($item)

    var url = this.$el.data('url')
    var field = 'unpublished_picture[photo]'
    // var field = this.options.field || 'unpublished_picture[photo]'
    var type = this.$el.data('type')
    var params = { type: type }

    var uploader = gp.Helpers.file.upload(url, field, file, params)
      .progress(function(file, percentage){
        if( +percentage === 100 ) percentage = 99
        $progressBar.css('width', percentage+'%')
        $progressPercentage.html(percentage+'%')

        $item.trigger('file:progress', [percentage])
      })
      .done(function(file, data){
        $item.addClass('complete')
        $progressBar.css('width', '100%')
        $progressPercentage.html('100%')

        $item.trigger('file:done', [data])
        $item.trigger('file:always', $item)
      })
      .fail(function(file, e){
        if( e.type !== 'abort' ) {
          that.$msg.html( I18n.t('javascripts.partials.form.upload_error') )
          $item.trigger('file:fail', [e])
          $item.trigger('file:always', $item)
        }
        _.defer(function(){
          $item.remove().off()
        })
      })

    $item.on('click', '.file-uploader-item-cancel', function(){
      if( uploader.state() !== 'resolved' ) {
        uploader.abort()
      }
      $item.trigger('file:remove')
      $item.trigger('file:always', $item)
      $item.remove().off()
    })

    return $item
  }
})

//= require social/whatsups/whatsup
;(function(){
  var View = gp.App.View.extend({
    events: {
      // It will be rendered when foundation reveal triggers open
      'open'                                       : 'render',
      'opened'                                     : 'formToggle',
      'click [type="submit"]'                      : 'submitForm',
      'click .facebook-share'                      : 'toggleFacebookShare',
      'click .twitter-share'                       : 'toggleTwitterShare',
      'click .metadata-container .metadata .close' : 'metaRemove',
      'submit form'                                : 'disableSubmitButton'
    },

    initialize: function(){
      var self = this

      _.bindAll(
        this,
        'show',
        'hide',
        'submitForm',
        'formToggle',
        'loadMediaUploader',
        'uploadPic',
        'toggleFacebookShare',
        'toggleTwitterShare',
        'listenForMeta',
        'onMetaFound',
        'metaRemove',
        'disableSubmitButton',
        'enableSubmitButton',
        'setSportTags'
      )

      this.$hiddens = this.$('.hiddens')
      this.$mediaBtns = this.$('.add-img, .create-album')
      this.$uploader = this.$('.file-uploader')
      this.$uploaderMsg = this.$uploader.find('.file-uploader-msg')

      this.$body = this.$('textarea[name="social_whatsup[body]"]')
      this.$metaItem = this.$('.metadata-container')

      gp.Helpers.mentions.listen(this.$body)

      this.listenForMeta()
      this.setSportTags()

      gp.Helpers.file.init()
        .done(function(){
          self.loadMediaUploader()
          self.initCreateAlbumButton()
        })
        .fail(function(){
          alert('Your device is not compatible for uploading media.')
        })

      this.rendered = true

      return this
    },

    show: function(){
      var self = this

      $.magnificPopup.close()
      $.magnificPopup.open({
        showCloseBtn: false,
        enableEscapeKey: false,
        mainClass: 'whatsups-form-popup',
        items: {
          src: this.$el,
          type: 'inline'
        },
        callbacks: {
          open: function(){
            gp.Helpers.lockScroll.lock(self.$list)
          },
          close: gp.Helpers.lockScroll.unlock
        }
      })
    },

    hide: function(){
      $.magnificPopup.close()
    },

    initCreateAlbumButton: function(){
      var that = this
      var $createAlbum = this.$('.create-album')
      $createAlbum.on('click', function(e){
        gp.Views['partials/albums_form'].open()
        return e.stop()
      })
    },

    submitForm: function(e){
      if ( this.$el.hasClass('loading') ) {
        e.preventDefault()
        return false
      }
      var model = new gp.Social.Whatsup({
        sport_id: this.$('[name="social_whatsup[sport_id]"]').val(),
        picture_id: this.$('[name="social_whatsup[unpublished_picture_id]"]').val(),
        body: this.$('[name="social_whatsup[body]"]').val()
      })
      if ( !model.isValid() ) {
        var errors = _.values(model.validationError).join(' ')
        alert(errors)
        e.preventDefault()
        return false
      }
      if ( this.mediaUploader ) {
        this.mediaUploader.remove()
      }
    },

    formToggle: function(){
      this.$('textarea:first').focus()
    },

    mediaUploader: null,
    loadMediaUploader: function(){
      var self = this
      var fileOptions = {
        accept: self.picTypes,
        multiple: false
      }
      var $uplaoderBtn = this.$('.add-img')
      var fileListener = gp.Helpers.file.listen($uplaoderBtn, fileOptions)

      this.mediaUploader = fileListener

      fileListener
        .on('files:selected', function(file){
          if ( gp.Helpers.file.validates(file, 'maxSize', 10) ) {
            self.uploadPic(file)
            fileListener.disable()
          } else {
            alert( I18n.t('javascripts.partials.form.upload_file_too_big', { max: 10 }) )
          }
        })
        .on('files:error:accept', function(file, types){
          alert( I18n.t('javascripts.partials.form.file_type_not_allowed', {types: types.join(', ')}) )
        })
        .on('files:error:multiple', function(){
          alert( I18n.t('javascripts.partials.form.multiple_files_not_allowed') )
        })
        .on('disabled', function(){
          self.$mediaBtns.hide()
        })
        .on('enabled', function(){
          self.$mediaBtns.show()
        })
    },

    picTypes: ['image/bmp','image/gif','image/jpeg','image/png'],
    picUploader: null,
    uploadPic: function(file){
      var that = this
      if( !this.picUploader ) {
        this.picUploader = new gp.Views['partials/file_uploader']({
          el: this.$('#whatsups-pic-uploader')
        }).render()
        this.picUploader.showing = false
      }

      if( this.picUploader.showing ) return

      this.picUploader.show()

      function cancel(){
        that.$el.removeClass('loading')
        that.enableSubmitButton()
        that.mediaUploader.enable()
        that.picUploader.hide()
      }

      var $picHidden = that.$hiddens.find('[name="social_whatsup[unpublished_picture_id]"]')
      if ( !$picHidden.length ) {
        $picHidden = $('<input type="hidden" name="social_whatsup[unpublished_picture_id]">')
        $picHidden.appendTo(that.$hiddens)
      }

      var $file = this.picUploader.add(file)
        .on('file:done', function(e, data){
          $picHidden.val(data.picture.id)
          that.$el.removeClass('loading')
          that.enableSubmitButton()
        })
        .on('file:fail', function(e, data){
          cancel()
          $picHidden.remove()
          alert( I18n.t('javascripts.partials.form.upload_error') )
        })
        .on('file:remove', function(e, data){
          cancel()
          $picHidden.remove()
        })

      that.$el.addClass('loading')
      that.disableSubmitButton()
    },

    toggleFacebookShare: function() {
      var that = this
      var facebookShare = this.$('.facebook-share')

      if (facebookShare.hasClass('active')) {
        facebookShare.removeClass('active')
        $('input[name="facebook_share"]').remove()
      }else {
        if (facebookShare.data('can_write') !== true) {
          gp.Helpers.authorizeFacebook(function(authentication) {
            if (authentication) {
              facebookShare.addClass('active')

              $.post('/authentications', {authentication: authentication}, function(response) {
                $('<input type="hidden" name="facebook_share" value="true">').appendTo(that.$hiddens)
              }, 'json')
            }
          }, true)
        } else {
          facebookShare.addClass('active')
          $('<input type="hidden" name="facebook_share" value="true">').appendTo(that.$hiddens)
        }
      }
    },

    toggleTwitterShare: function() {
      var that = this
      var twitterShare = this.$('.twitter-share')
      if (twitterShare.hasClass('active')) {
        twitterShare.removeClass('active')
        $('input[name="twitter_share"]').remove()
      }else {
        if (twitterShare.data('can_write') !== true) {
          gp.Helpers.authorizeTwitter(function(authentication) {
            if (authentication) {
              twitterShare.addClass('active')

              $.post('/authentications', {authentication: authentication}, function(response) {
                $('<input type="hidden" name="twitter_share" value="true">').appendTo(that.$hiddens)
              }, 'json')
            }
          }, true)
        } else {
          twitterShare.addClass('active')
          $('<input type="hidden" name="twitter_share" value="true">').appendTo(that.$hiddens)
        }
      }
    },

    listenForMeta: function(){
      gp.Helpers.parser.listenForItem(this.$body)
      this.$body.one('parser:item:found', this.onMetaFound)
    },

    onMetaFound: function(e, item){
      var that = this, url = item.url
      this.$metaItem.addClass('loading')
      gp.Helpers.scrap(url)
        .done(function(data){
          var $meta = $(data)
          $meta.prepend('<span class="close">X</span>')
          that.$metaItem.append($meta)

          var $metaValue = that.$hiddens.find('[name="social_whatsup[metadata][url]"]')
          if ( !$metaValue.length ) {
            $metaValue = $('<input type="hidden" name="social_whatsup[metadata][url]">')
            $metaValue.appendTo(that.$hiddens)
          }
          $metaValue.val(url)
        })
        .always(function(){
          that.$metaItem.removeClass('loading')
        })
    },

    metaRemove: function(e){
      this.$hiddens.find('[name="social_whatsup[metadata][url]"]').remove()
      this.$metaItem.find('.metadata').remove()
      this.listenForMeta()
    },

    disableSubmitButton: function() {
      $('.post-item').prop('disabled', true)
    },

    enableSubmitButton: function() {
      $('.post-item').prop('disabled', false)
    },

    setSportTags: function() {
      var $select = this.$('.sport-selector select')

      $select.find('option').each(function(i, el){
        var $el = $(el)
        $el.text('#'+$el.text())
      })

      $select.selectize()
    }
  })

  var V = gp.Views['partials/whatsups_form'] = {}

  var view
  V.init = _.once(function() {
    view = new View({
     collection: gp.cart,
     el: $('#whatsups-form .whatsups-form')
    })
  })

  V.open = function() {
    V.init()
    view.show()
  }

  V.close = function() {
    view.hide()
  }
})()

$(function(){
  var header = gp.Views['partials/header']
  var $search = header.$el.find('.header-search')
  var $form = $search.find('form')
  var $input = $search.find('input[type="search"]')

  var V = gp.Views['partials/header_search'] = {}

  var inited = false
  V.init = function(o){
    if( inited ) return V
    inited = true

    V.initTypeahead()

    $input.on('focusin', V.setActive)
    $input.on('focusout', V.unsetActive)
  }

  // Counter function
  // It returns true when the count is over zero
  var fetchCounter = (function(){
    var items = []

    var counter = function(){
      return items.length > 0 ? true : false
    }

    counter.addItem = function(item){
      if( items.indexOf(item) === -1 ) items.push(item)
      if( counter() ) $search.addClass('loading')
    }

    counter.removeItem = function(item){
      var index = items.indexOf(item)
      if( index > -1 ) items.splice(index, 1)
      if( !counter() ) $search.removeClass('loading')
    }

    return counter
  })()

  // Adapter wrapper, to be able to call the fetching
  // and generate a loading state
  var wrapWithCounter = function(source){
    return function(query, cb){
      var _cb = function(){
        fetchCounter.removeItem(source)
        return cb.apply(this, arguments)
      }

      fetchCounter.addItem(source)
      return source.call(this, query, _cb)
    }
  }

  var _collections = {}
  V.getCollection = function(name){
    if( _collections[name] ) return _collections[name]

    var c = _collections[name] = new Bloodhound({
      name: name,
      remote: {
        url: '/api/suggestions?query=%QUERY',
        filter: function(r){ return r[name] || [] },
        ajax: {
          data: { network: gp.network }
        }
      },
      datumTokenizer: function(d){
        return Bloodhound.tokenizers.whitespace(d.value)
      },
      queryTokenizer: Bloodhound.tokenizers.whitespace
    })

    c.initialize()

    return c
  }

  V.categoriesDataset = function(){
    return {
      name: 'categories',
      source: wrapWithCounter(V.getCollection('categories').ttAdapter()),
      templates: {
        header: gp.Helpers.templates('header-search-categories-header', $search),
        suggestion: gp.Helpers.templates('header-search-categories-item', $search, { variable: 'data' })
      }
    }
  }

  V.suggesterDataSet = function(){
    return {
      name: 'suggester_search',
      source: wrapWithCounter(V.getCollection('suggester_search').ttAdapter()),
      templates: {
        suggestion: gp.Helpers.templates('header-search-suggester-item', $search, { variable: 'data' })
      }
    }
  }

  V.usersDataset = function(type){
    return {
      name: type,
      source: wrapWithCounter(V.getCollection(type).ttAdapter()),
      templates: {
        header: gp.Helpers.templates('header-search-'+type+'-header', $search),
        suggestion: gp.Helpers.templates('header-search-users-item', $search, { variable: 'data' })
      }
    }
  }

  V.variantsDataset = function(){
    return {
      name: 'variants',
      source: wrapWithCounter(V.getCollection('variants').ttAdapter()),
      templates: {
        header: gp.Helpers.templates('header-search-variants-header', $search),
        suggestion: gp.Helpers.templates('header-search-variants-item', $search, { variable: 'data' })
      }
    }
  }

  V.categoriesFacetsDataset = function(){
    return {
      name: 'categories_facets',
      source: wrapWithCounter(V.getCollection('categories_facets').ttAdapter()),
      templates: {
        header: gp.Helpers.templates('header-search-categories-facet-header', $search),
        suggestion: gp.Helpers.templates('header-search-categories-facet-item', $search)
      }
    }
  }

  V.manufacturersFacetsDataset = function(){
    return {
      name: 'manufacturer_facets',
      source: wrapWithCounter(V.getCollection('manufacturer_facets').ttAdapter()),
      templates: {
        header: gp.Helpers.templates('header-search-manufacturers-facet-header', $search),
        suggestion: gp.Helpers.templates('header-search-manufacturers-facet-item', $search)
      }
    }
  }

  V.initTypeahead = function(){
    $input.typeahead(
      {
        hint: true,
        highlight: true,
        minLength: 1
      },
      V.suggesterDataSet(),
      V.categoriesDataset(),
      V.usersDataset('brands'),
      V.usersDataset('nonprofits'),
      V.variantsDataset(),
      V.categoriesFacetsDataset(),
      V.manufacturersFacetsDataset()
    )

    $input.on('typeahead:selected', function(e, data, collection){
      if (data.url) {
        document.location = data.url
      } else {
        $form.off( 'submit' )
        $form.on( 'submit' ,{data: data, collection: collection}, V.submitForm )
        $form.submit()
      }
    })

    // Hack to open it when it gets focus
    // https://github.com/twitter/typeahead.js/issues/798
    $input.on('typeahead:opened', function() {
      var initial = $input.val()
      var e = $.Event('keydown')
      e.keyCode = e.which = 40

      setTimeout(function(){
        $input.trigger(e)
        if( $input.val() != initial ) $input.val('')
      }, 150)

      return true
    });

    // Hide the suggestions when the header hides
    header.on('hide', function(){
      $input.typeahead('close')
    })

    window.$input = $input
  }

  V.submitForm = function(evt){
    if ( typeof(evt.data.collection) === 'undefined' ) {
      var $items = $(evt.data.items_selector)
      if ($items.length > 0) {
        var query = $(evt.currentTarget).find('input[name=query]').val().toLowerCase()
        var suggestion = $items.find('a').first().find('span').text().toLowerCase()
        if(query == suggestion){
          $(evt.currentTarget).attr('action', $items.find('a').attr('href'))
          $(evt.currentTarget).find(":input").attr('disabled', 'disabled')
        }
      }
    } else {
      if ( evt.data.collection === 'categories' || evt.data.collection === "manufacturer_facets"){
        $(evt.currentTarget).find(":input").attr('disabled', 'disabled')
      }
    }
  }

  V.init()

  $form.on( 'submit', { items_selector: 'div.tt-dataset-suggester_search .item, div.tt-dataset-categories .item' }, V.submitForm)
})

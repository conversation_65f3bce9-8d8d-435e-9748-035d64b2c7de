gp.Views['partials/rate_stars'] = gp.App.View.extend({
  events: {
    'click .star': 'clickStar'
  },

  initialize: function(options){
    _.bindAll(
      this,
      '_drawRate'
    )
    this.rate = parseInt(this.$el.attr('data-stars'))
    this.rateMax = parseInt(this.$el.attr('data-max-stars'))
    this.rateShowing = this.rate

    this.$input = options.$input
  },

  clickStar: function(e){
    var $evt = $(e.currentTarget)
    this.setRate($evt.data('stars'))
    this.showRate($evt.data('stars'))
  },

  showRate: function(rate){
    this.rateShowing = rate
    this.drawRate()
  },

  hideRate: function(rate){
    if( this.rateShowing === rate ) this.rateShowing = this.rate
    this.drawRate()
  },

  setRate: function(rate){
    this.rate = rate
    this.drawRate()
  },

  requested: false,
  drawRate: function(){
    if( !this.requested ){
      this.requested = true
      requestAnimationFrame(this._drawRate)
    }
  },

  _drawRate: function(){
    this.el.className = this.el.className.replace(/\bstars\-[0-9]\b/, 'stars-'+this.rateShowing)
    this.$input.val(this.rate)
    this.requested = false
  }
})

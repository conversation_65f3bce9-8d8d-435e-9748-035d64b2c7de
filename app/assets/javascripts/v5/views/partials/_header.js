$(function(){
  var H = gp.Views['partials/header'] = {
    $el: $('header')
  }

  _.extend(H, Backbone.Events)

  gp.pubSub.on('login:open', function() {
    gp.Views['partials/login_dropdown'].open()
  })
  $document.on('click', 'header .login', function(){
    gp.pubSub.trigger('login:open')
  })

  $document.on('click', '.user-menu .i-post', function(){
    gp.Views['partials/whatsups_form'].open()
  })
})

// Notifications Button
$(function(){
  if( !gp.notifications ) return

  $document.on('click', '.notifications-btn', function(){
    gp.Views['partials/notifications'].open()
  })
  var $el = $('.notifications-btn')

  var $count = $el.find('[data-count]')
  var onCountChange = _.debounce(function(){
    var count = gp.notifications.unreadCount()
    $count.attr('data-count', count).html(count)
  }, 100)

  onCountChange()
  gp.notifications.on('change:unreadCount', onCountChange)
})

// Cart Button
$(function(){
  if( !gp.cart ) return

  var $el = $('.user-menu .i-carrito')

  $document.on('click', '.user-menu .i-carrito', gp.Views['partials/cart'].open)

  var $count = $el.find('[data-count]')
  var onCountChange = _.debounce(function(){
    var count = gp.cart.cartCount()
    $count.attr('data-count', count).html(count)
  }, 100)

  onCountChange()
  gp.cart.on('cart:change', onCountChange)
})

// Fixed Positioning
if( Modernizr.csstransforms ) $(function(){
  var header = gp.Views['partials/header']
  var el = header.$el[0]

  if( !el ) return

  new Headroom(el, {
    tolerance: {
      down: 5,
      up: 5
    },
    offset: 100,
    classes : {
      initial: 'fixed',
      pinned: 'pinned',
      unpinned: 'unpinned',
      top: 'top',
      notTop: 'not-top'
    },
    onPin: function() {
      header.trigger('show')
    },
    onUnpin: function() {
      header.trigger('hide')
    }
  }).init()
})

// Categories
if( Modernizr.touch ) $(function(){
  var linksClass = '.header-categories .category.with-childs .category-link';

  $document.on('click', linksClass, function(e){
    var $link = $(e.currentTarget);
    var $category = $link.parent();

    $category.toggleClass('active');

    function close(){
      $category.toggleClass('active')
    }

    if( $category.hasClass('active') ) {
      $document.one('click', close)
    }
  });
});

var toggleCategoryLink = function(action, target) {
  var method = action == 'mouseover' ? 'addClass' : 'removeClass';

  $(target).parent().find('.category-link').each(function(){
    $(this)[method]('text-black');
  })
};

$document.on('mouseover', '.subcategories-wrapper', function(e) {
  toggleCategoryLink('mouseover', e.currentTarget);
});

$document.on('mouseout', '.subcategories-wrapper', function(e) {
  toggleCategoryLink('mouseout', e.currentTarget);
});



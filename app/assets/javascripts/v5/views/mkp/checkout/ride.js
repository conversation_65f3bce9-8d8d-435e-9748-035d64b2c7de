;if( gp.controller.checkout && gp.action.ride ) (function(){
  var ensureFreshCheckout = function() {
    if (cookie.get('checkout_completed')) {
      cookie.remove('checkout_completed')
      window.location.reload(true)
    }
  }

  ensureFreshCheckout()

  gp.Views['checkout/ride'] = gp.App.View.extend({
    initialize: function(){
      _.bindAll(
        this,
        'paymentLoaded',
        'addressChanged',
        'deliveryChanged',
        'paymentReady',
        'paymentFail',
        'couponChanged',
        'pickupChanged',
        'checkoutSubmit',
        'set_gp_checkout_object',
        'couponChangedDone',
        'couponChangedRemove'
      )
      var ride = this
      var $self = this.$el
      var $addressOptions = $self.find('.shipping .addresses .address-option')

      this.params = {}
      this.choosenAddress = false
      this.choosenDelivery = false
      this.choosenPayment = false
      this.paymentCover = false

      this.set_params()
      this.set_gp_checkout_object({
        addressOptionsCount: $addressOptions.length
      })

      // Initialize Pickup Selector
      var $pickupSelector = $self.find('.pickup-selector')
      if ($pickupSelector.length) {
        this.pickupSelector = new gp.Views['checkout/pickup/selector']({
          el: $pickupSelector
        })
      }

      // Initialize Pickup Location Form
      var $pickupLocation = $self.find('.pickup-location')
      if ($pickupLocation.length) {
        this.pickupLocation = new gp.Views['checkout/pickup/location']({
          el: $pickupLocation
        })
      }

      // Initialize the Addresses
      _($addressOptions).each(function(address){
        new gp.Views['checkout/shipping/address']({
          el: address
        })
      })

      // Initialize the Address Form
      this.addressForm = new gp.Views['checkout/shipping/address_form']({
        el: $self.find('.shipping .address .address-form')
      })

      this.deliveryOptions = new gp.Views['checkout/shipping/methods']({
        el: $self.find('.shipping .methods .delivery-options')
      })

      // Initialize the Summary Box
      this.summaryBox = new gp.Views['checkout/summary']({
        el: $self.find('.summary'),
        ride: this
      })

      // Initialize the Coupon
      this.coupon = new gp.Views['checkout/coupon']({
        el: $self.find('.coupon')
      })

      // Pub/Sub Events (need to explicit bind the "this")
      gp.pubSub.on({
        'checkout:payment_gateway:loaded': this.paymentLoaded,
        'checkout:address:change':         this.addressChanged,
        'checkout:delivery:change':        this.deliveryChanged,
        'checkout:payment:ready':          this.paymentReady,
        'checkout:payment:fail':           this.paymentFail,
        'checkout:coupon:done':            this.couponChangedDone,
        'checkout:coupon:remove':          this.couponChangedRemove,
        'checkout:pickup:change':          this.pickupChanged,
        'checkout:submit':                 this.checkoutSubmit,

      })
    },

    set_params: function(){
      var that = this
      window.location.search.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(str,key,value){
        that.params[key] = value
      })
    },

    set_gp_checkout_object: function(options){
      var self = this
      gp.checkout = options || {}
      gp.checkout.coupon = {
        discount: 0,
        percent: 0,
        amount: 0,
        handlingDiscount: function(){
          var coupon = gp.checkout.coupon
          var handlingDiscount = 0
          if (coupon.amount > 0) {
            var availableAmount = parseFloat(coupon.amount - coupon.discount)
            var handlingAmount = self.getDeliveryCharge() + self.getTaxesCharge()

            if (handlingAmount > 0) {
              handlingDiscount = (availableAmount > handlingAmount) ? handlingAmount : availableAmount
            }
          }

          return parseFloat(handlingDiscount)
        }
      }
      gp.checkout.network = gp.network
      gp.checkout.fid = this.params.fid
      gp.checkout.ready_to_purchase = false
      gp.checkout.is_pickup = false
      gp.checkout.paymentCovered = false
      gp.checkout.deliveryCharge = 0
      gp.checkout.taxesCharge = 0
      gp.checkout.subtotal = 0
      gp.checkout.pickit = {
        default:{
          address1:"1295 Charleston Road",
          country: "US",
          state: "CA",
          city: "Mountain View",
          zip_code: "94043"
        }
      }
      gp.checkout.promotion = {
        discount: 0,
        name: ''
      }

      gp.checkout.total = function(){
        var total = parseFloat((gp.checkout.subtotal
                    + ride.getTaxesCharge()
                    + ride.getDeliveryCharge()
                    - gp.checkout.coupon.discount
                    - gp.checkout.promotion.discount
                    - gp.checkout.coupon.handlingDiscount()).toFixed(2))
        total = total > 0 ? total : 0
        gp.checkout.paymentCovered = total == 0
        return total
      },

      gp.checkout.financingCosts = function() {
        var costs = gp.checkout.financed_total - gp.checkout.total()
        return costs
      }
    },

    paymentLoaded: function(){
      // Initialize the Payment Stuff
      var gateway = gp.checkout.payment.name
      var payment = new gp.Views['checkout/payment/'+gateway]({
        el: this.$el.find('.payment .methods .'+gateway),
        ride: this
      })
    },

    addressChanged: function() {
      this.choosenAddress = gp.checkout.currentAddressId
      this.checkIfReadyToPurchase()
    },

    deliveryChanged: function(id){
      this.choosenDelivery = id
      this.checkIfReadyToPurchase()
    },

    paymentReady: function(){
      this.choosenPayment = true
      this.checkIfReadyToPurchase()
    },

    paymentFail: function(){
      this.choosenPayment = false
      this.checkIfReadyToPurchase()
    },

    couponChangedDone: function(data) {
      var handlingAmount = this.getDeliveryCharge() + this.getTaxesCharge()
      var coupon = data.response.coupon
      gp.checkout.coupon.discount = parseFloat(coupon.discount)
      gp.checkout.coupon.percent = coupon.percent ? parseFloat(coupon.percent) : 0
      gp.checkout.coupon.amount = coupon.amount ? parseFloat(coupon.amount) : 0
      gp.pubSub.trigger('checkout:subtotal:updatediscount')
      gp.pubSub.trigger('checkout:subtotal:updatehandling')
      gp.pubSub.trigger('checkout:total:update')
      this.couponChanged();
    },

    couponChangedRemove: function() {
      gp.checkout.coupon.discount = 0
      gp.checkout.coupon.percent = 0
      gp.checkout.coupon.amount = 0
      gp.pubSub.trigger('checkout:subtotal:updatediscount')
      gp.pubSub.trigger('checkout:subtotal:updatehandling')
      gp.pubSub.trigger('checkout:total:update')
      this.coupon.resetForm()
      this.couponChanged();
    },

    couponChanged: function(){
      this.checkIfReadyToPurchase()
    },

    readyAddress: function(){
        return this.choosenAddress && this.addressForm.allRequieredFieldsValid()
    },

    readyDelivery: function(){
        return ((gp.checkout.is_pickup && gp.checkout.pickit_quotation) || (!gp.checkout.is_pickup && this.choosenDelivery))
    },

    readyToPurchase: function(){
        return this.readyAddress() && this.readyDelivery() && (this.choosenPayment || gp.checkout.paymentCovered)
    },

    checkIfReadyToPurchase: function(){
      if(gp.checkout.is_pickup){
        gp.checkout.ready_to_purchase = this.choosenAddress && gp.checkout.pickitCotizationId &&
          (this.choosenPayment || gp.checkout.paymentCovered)
      }else{
        gp.checkout.ready_to_purchase = this.choosenAddress && this.choosenDelivery &&
          (this.choosenPayment || gp.checkout.paymentCovered)
      }
    },

    checkoutSubmit: function(){
        var self = this;
      this.summaryBox.$form.find('#fid').val(gp.checkout.fid)
      this.summaryBox.$form.append("<input type='hidden' name='address_id' value='" + gp.checkout.currentAddressId +"' />")
      this.summaryBox.$form.append("<input type='hidden' name='is_pickit' value='" + (gp.checkout.is_pickup ? '1' : '0') +"' />")
      this.summaryBox.$form.append("<input type='hidden' name='pickit_quotation_id' value='" +gp.checkout.pickitCotizationId+"' />")
      if(gp.checkout.pickit_quotation) this.summaryBox.$form.append("<input type='hidden' name='pickup_point_id' value='" +gp.checkout.pickit_quotation.pickup_point_id+"' />")
      if(gp.checkout.is_pickup){
        this.addressForm.updatePickitData(function(){
          self.summaryBox.$form.submit()
        })
      }else {
          this.summaryBox.$form.submit()
      }
    },

    getDeliveryCharge:function(){
      if(gp.checkout.is_pickup && gp.checkout.pickit_quotation){
        return gp.checkout.pickit_quotation.amount
      }
      if(!gp.checkout.is_pickup){
        return  gp.checkout.deliveryCharge
      }
      return 0;
    },

    getTaxesCharge:function(){
      if(!gp.checkout.is_pickup){
        return  gp.checkout.taxesCharge
      }
      return 0;
    },


    pickupChanged: function () {
      var action = gp.checkout.is_pickup ? 'addClass' : 'removeClass'

      var $shipping = this.$el
        .find('.shipping > .address, .shipping > .methods')
      var $pickup = this.$el
        .find('.shipping > .pickup-location')

      if (gp.checkout.is_pickup) {
        this.$el.find('.shipping .methods').addClass('hidden')
        $pickup.removeClass('hidden')
        this.addressForm.showAddressFields(false)
        $pickup.find('input[type="text"]:visible').first().focus()
      } else {
        this.addressForm.showAddressFields(true)
        this.$el.find('.shipping .methods').removeClass('hidden')
        $pickup.addClass('hidden')
        $shipping.find('input[type="text"]:visible').first().focus()
      }

      gp.pubSub.trigger('checkout:delivery:update')
    }
  })

  // Instantiate the ride view
  var ride = new gp.Views['checkout/ride']({
    el: $('#checkout-v5-ride')
  })
})()

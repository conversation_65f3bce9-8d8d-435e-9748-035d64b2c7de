gp.Views['checkout/shipping/methods'] = gp.App.View.extend({

  events: {
  },

  initialize: function(){
    _.bindAll(
      this,
      'createDeliveryOptions',
      'fetching',
      'deliveryOptionsDone',
      'setDeliveryOption'
    )
    // Vars
    this.endpoint = '/api/mkp/checkout/delivery_options'
    this.deliveryOptions = {}
    this.createDebouncedDeliveryOptions = _.debounce(this.createDeliveryOptions, 800)

    // Local Events (need to explicit bind the "this")
    this.$el.on({
      'methods:fetch:done': this.deliveryOptionsDone,
      'methods:fetch:fail': this.deliveryOptionsFailed
    })

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('checkout:address:change checkout:items:remove checkout:coupon:done checkout:coupon:remove', this.createDebouncedDeliveryOptions)
    gp.pubSub.on({
      'shipping:methods:fetching': this.fetching,
      'checkout:delivery:change': this.setDeliveryOption
    })
  },

  createDeliveryOptions: function(){
    if(gp.checkout.is_pickup) return;
    var that = this,
        url  = this.getUrl(),
        addressId = gp.checkout.currentAddressId
    if( addressId ) {
      gp.pubSub.trigger('shipping:methods:fetching')
      $.ajax({
        url: url,
        data: { address_id: addressId },
        dataType: 'json',
        type: 'post',
        cache: false
      }).done(function(){
        that.$el.trigger('methods:fetch:done', arguments)
        gp.pubSub.trigger('shipping:methods:fetched')
      }).fail(function(){
        that.$el.trigger('methods:fetch:fail', arguments)
      })
    }
  },

  fetching: function(){
    var template = '<div class="notifications"><strong>MESSAGE</strong></div>'
    var element = template.replace('MESSAGE', I18n.t('js.v5.mkp.checkout.shipping_methods.loading'))
    this.$el.html('').append(element)
  },

  getUrl: function(params){
    var url = this.endpoint
    if( typeof params !== 'undefined' && params.action ){
      url += '/' + params.action
    }
    url += '?fid=' + gp.checkout.fid
    return url
  },

  deliveryOptionsDone: function(evt, data){
    var that = this
    var options = []
    var taxes = data.response.taxes
    var title = data.response.title

    this.$el.html('')

    this.deliveryOptions = data.response.delivery_options

    _(this.deliveryOptions).each(function(option, key){
      var option = new gp.Views['checkout/shipping/delivery_option']({
        id: key,
        data: option,
        title: title,
        taxes: taxes
      })

      that.$el.append(option.el)

      options.push(option)
    })

    var firstOption = _(options).first()

    this.setDefaultDeliveryOption(firstOption)
  },

  deliveryOptionsFailed: function(evt, data){
    this.$el.html('').append('<div class="error">ERROR</div>')
  },

  setDefaultDeliveryOption: function(option){
    option.$el.click()
  },

  setDeliveryOption: function(){
    var that = this,
        url  = this.getUrl({ action: 'set' }),
        deliveryId = arguments[0]
    if(arguments[0] === null) return;
    gp.pubSub.trigger('delivery:set:init')
    $.ajax({
        url:      url,
        data:     { delivery_id: deliveryId },
        dataType: 'json',
        type:     'post',
        cache:    false
    }).done(function(){
      that.$el.trigger('delivery:set:done', arguments)
    }).fail(function(){
      that.$el.trigger('delivery:set:fail', arguments)
    })

  }

})

gp.Views['checkout/shipping/delivery_option'] = gp.App.View.extend({
  template: _.template("<i class='i-radio-off'></i><h5>$<%= total %><span> - <%= title %></span></h5>"),
  className: 'option',

  events: {
    'click': 'clickOption'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'changeDeliveryOption',
      'paying',
      'getTitles'
    )

    // Vars
    this.data = options.data
    this.totals = this.getTotals()
    this.ids = this.getIds()

    this.title = this.getTitles(/*options.title*/)

    this.taxes = parseFloat(options.taxes)
    this.total = parseFloat(this.getTotal())

    this.render()
    // Pub/Sub Events (need to explicit bind the "this")
     gp.pubSub.on({
      'checkout:delivery:change': this.changeDeliveryOption,
      'checkout:pay': this.paying
    })
  },

  paying: function(){
    this.$el.off()
  },

  clickOption: function(evt){
    gp.pubSub.trigger('checkout:delivery:change', this.id, this.total, this.taxes)
  },

  changeDeliveryOption: function(deliveryOptionId, totalCharge){
    var radio = this.$el.find('i')
    if( deliveryOptionId === this.id ) {
      if ( !this.choosenOption ) {
        this.choosenOption = true
        radio.addClass('i-radio-on').removeClass('i-radio-off')
      }
    } else {
      if ( this.choosenOption ) {
        this.choosenOption = false
        radio.addClass('i-radio-off').removeClass('i-radio-on')
      }
    }
  },

  getTotals: function(){
    return this.data.map(function(service){
      if( service.bonificated_price ){
        return parseFloat(service.price)
      } else {
        return parseFloat(service.delivery_option.charge)
      }
    })
  },

  getTotal: function(){
    return this.totals.reduce(function(memo, subtotal){ return memo + subtotal }, 0)
  },

  getTitles: function() {
    var args = _.compact(arguments)
    if( args.length > 0 ){
      var title = ''
      var option = this.data[0]
      var estimated_delivery_time = arguments[0]

      if( option.bonificated_price ){
        title = '<span class="bonification"><span class="old-price">$'+parseFloat(option.bonificated_price).toFixed(2)+'</span>';

        if(option.display_name) {
          title += option.display_name;
        }
        else if( parseFloat(option.price) === 0 ){
          title += I18n.t('js.v5.mkp.checkout.shipping_methods.bonification')
        }
        title += '</span> | '
      }

      title += estimated_delivery_time

      return [title]
    } else {
      // Leaving this untouched for US.
      return this.data.map(function(service){
        var title = ''
        if( service.bonificated_price ){
          title = '<span class="bonification"><span class="old-price">$'+parseFloat(service.bonificated_price).toFixed(2)+'</span>'
          if( parseFloat(service.price) === 0 ){
            title += I18n.t('js.v5.mkp.checkout.shipping_methods.bonification')
          }
          title += '</span> | '
        }

        title += service.delivery_option.title

        return title
      })
    }
  },

  getIds: function(){
    return this.id.split('-')
  },

  render: function(){
    this.$el.attr('id', this.id)
    this.$el.html(this.template({
      total: this.total.toFixed(2),
      title: this.title.join(' - ')
    }))

    return this
  }

})

gp.Views['checkout/payment/mercadopago'] = gp.App.View.extend({
  creditCards: {
    fieldsMap: {
      cc_card_number:           'cardNumber',
      cc_cardholder_name:       'cardholderName',
      cc_expiration_month:      'cardExpirationMonth',
      cc_expiration_year:       'cardExpirationYear',
      cc_cvv:                   'securityCode',
      cc_cardholder_doc_type:   'docType',
      cc_cardholder_doc_number: 'docNumber'
    }
  },

  events: {
    'click .creditcards i, .creditcards .header':               'chooseCreditcards',
    'click .tickets i, .tickets .header':                       'chooseTickets',
    'keyup    .creditcard-form .card-number input':             'cardNumberChange',
    'focusout .creditcard-form input.validate':                 'validateField',
    'focusout .tickets-form input.validate':                    'validateField',
    'focusout .tickets-form input, .tickets-form select':       'checkRequiredField',
    'focusout .creditcard-form input, .creditcard-form select': 'checkRequiredField',
    'focusin  input':                                           'checkAddressRequiredFields',
    'click .installments-more-info':                            'infoModal',
    'change .installments .select-wrapper':                     'updateTaxes'
  },

  initialize: function(){
    this.MERCADOPAGO_TICKET_LIMIT = 4500.0

    _.bindAll(
      this,
      'changeCCNumberFieldType',
      'checkAllRequiredFields',
      'displayInstallmentOptions',
      'checkAddressRequiredFields',
      'getInstallments',
      'setCardHolderName',
      'tokenizePayment',
      'itemsChanged'
    )

    var self = this
    var active_payments = gp.checkout.payment.active_payments()
    var creditCards = { available: _(active_payments).select(function(p){ return p.payment_type_id === 'credit_card' }) }
    var tickets = _(active_payments).select(function(p){ return p.payment_type_id === 'ticket' })

    var _activatePaymentOptions = function(creditCards, tickets) {
      var element = self.$el
      var ticketSelect = self.$ticketsForm.find('.ticket-type select')
      var ticketOptions = ['']

      _(tickets).each(function(p){
        element.find('.active-payments .tickets .header').append('<span class="payment-icon"><img alt="'+ p.name +'" src="'+ p.secure_thumbnail +'" /></span>')
        ticketOptions.push({ value: p.id, name: p.name })
      })

      ticketSelect.selectize({
        onChange: function(value){
          gp.pubSub.trigger('checkout:payment:ready')
        },
        allowEmptyOption: true,
        labelField: 'name',
        options: ticketOptions,
        create: false,
      })

      self.evaluateTicketOptionVisible()
    }

    // Vars
    this.gateway_name = 'mercadopago'
    this.fieldsWithErrors = []
    this.readyToTokenize = false
    this.selectedOption = 'creditcards'
    this.client = gp.checkout.payment.client
    this.getDebounceInstallments = _.debounce(this.getInstallments, 650)
    this.paymentData = { gateway: this.gateway_name }

    this.ccNumber = ''
    this.ccBin = ''

    // jQuery Vars
    this.$creditcardsOption = this.$el.find('.payment-option.creditcards')
    this.$ticketsOption = this.$el.find('.payment-option.tickets')

    this.$creditcardForm = this.$el.find('.creditcard-form')
    this.$ticketsForm = this.$el.find('.tickets-form')

    this.$errors =         this.$creditcardForm.find('.errors')
    this.$requiredCCFields = this.$creditcardForm.find('input.required')
    this.$requiredTicketFields = this.$ticketsForm.find('input.required')
    this.$ccFields =       this.$creditcardForm.find('input[id^="cc_"], select[id^="cc_"]')
    this.$ccNumberField =  this.$ccFields.filter('input#cc_card_number')
    this.$mpFields =       this.$creditcardForm.find('.mp-fields input')

    // Local Events
    this.$ccNumberField.on('focusout focusin', this.changeCCNumberFieldType)

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'checkout:missing_data': this.checkAllRequiredFields,
      'checkout:payment:tokenize': this.tokenizePayment,
      'checkout:customer:full_name:change': this.setCardHolderName,
      'checkout:coupon:done checkout:coupon:remove': this.getInstallments,
      'checkout:items:change': this.itemsChanged,
      'checkout:delivery:change':  this.getInstallments
    })

    // Init operations
    _activatePaymentOptions(creditCards, tickets)
    this.$creditcardForm.find('select').selectize()
    this.$ticketsForm.find('select').selectize()
    this.enableForm(true)
  },

  evaluateTicketOptionVisible: function(){
    if( gp.checkout.total() > this.MERCADOPAGO_TICKET_LIMIT ){
      this.$ticketsOption.hide()
    } else{
      this.$ticketsOption.show()
    }
  },

  chooseCreditcards: function(){
    this.selectedOption = 'creditcards'
    this.checkSelectedOption()
    gp.pubSub.trigger('checkout:total:update')
  },

  chooseTickets: function(){
    this.selectedOption = 'tickets'
    this.checkSelectedOption()
    this.readyToTokenize = true
    this.fieldsWithErrors = []
    gp.pubSub.trigger('checkout:total:remove')
  },

  toggleForm: function(){
    if (!this.$ticketsOption.is(':visible')) return;

    if( this.selectedOption === 'creditcards' ){
      this.$creditcardForm.slideDown();
      this.$ticketsForm.slideUp();
      this.$ticketsForm.find('select')[0].selectize.clear(true)
      this.$requiredCCFields[0].focus()
    } else {
      this.$creditcardForm.slideUp();
      this.$ticketsForm.slideDown();
      this.$ticketsForm.find('select')[0].selectize.focus()
    }
  },

  checkSelectedOption: function(){
    this.toggleForm()
    if( this.selectedOption === 'creditcards' ){
      this.$creditcardsOption.find('i').addClass('i-radio-on').removeClass('i-radio-off')
      this.$ticketsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off')
    } else {
      this.$creditcardsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off')
      this.$ticketsOption.find('i').addClass('i-radio-on').removeClass('i-radio-off')
    }
  },

  changeCCNumberFieldType: function(evt){
    if( evt.type === 'focusin'){
      this.$ccNumberField.attr('type', 'text')
    } else {
      this.$ccNumberField.attr('type', 'password')
    }
  },

  checkAllRequiredFields: function(){
    var that = this,
        fieldsToCheck = []
    if( this.selectedOption === 'creditcards' ){
      fieldsToCheck = this.$requiredCCFields
    } else {
      fieldsToCheck = this.$requiredTicketFields
    }
    _(fieldsToCheck).each(function(field){
      var $field = $(field)
      var errors = gp.Helpers.v5.form.validate($field)
      that.handleFieldErrors( $field, errors )
    })
  },

  tokenizePayment: function(){
    var that = this
    this.enableForm(false)

    // Decidir 2.0 check gateway
    if(this.gateway_name === 'mercadopago') {
      if( this.selectedOption === 'tickets' ){
        var _getTicketPaymentObject = function(){
          var ticket_type = that.$ticketsForm.find('.ticket-type select').val(),
              doc_type = that.$ticketsForm.find('.doc-type select').val(),
              doc_number = that.$ticketsForm.find('.doc-number input').val()
          return {
            gateway: that.gateway_name,
            payment_method_id: ticket_type,
            document_type: doc_type,
            document_number: doc_number
          }
        }
        gp.pubSub.trigger( 'checkout:pay',  _getTicketPaymentObject() )
      } else {
        var _grabCreditCardData = function(){
          var fieldsData = {}
          var fieldsMap = that.creditCards.fieldsMap
          _(that.$ccFields).each(function(field){ fieldsData[field.id] = field.value })
          _(fieldsData).each(function(d,k){ that.$mpFields.filter('#'+fieldsMap[k]).val( d ) })
        }()
        var _getCreditCardPaymentObject = function(response){
          var full_data = _(that.paymentData).extend(response)
          var __parsedData = function(data){
            var installments = that.$creditcardForm.find('.installments select').val()
            return {
              gateway: data.gateway,
              token: data.id,
              installments: installments,
              payment_method_id: data.payment_method_id,
              issuer_id: data.issuer_id
            }
          }
          return __parsedData(full_data)
        }
        var _callbackTokenizeCard = function(status, response) {
          if( status === 200 || status === 201 ){
            gp.pubSub.trigger( 'checkout:pay', _getCreditCardPaymentObject(response) )
          } else {
            var error_title =  I18n.t('js.v5.mkp.checkout.payment.error.'+response.error)
            that.$errors.append('<span class="title">'+error_title+'</span>')
            _(response.cause).each(function(e){
              var error_text = I18n.t('js.v5.mkp.checkout.payment.error.codes.'+e.code)
              that.$errors.append('<span class="error">'+error_text+'</span>')
            })
            gp.pubSub.trigger('checkout:payment:fail')
            that.$errors.show()
            that.enableForm(true)
          }
        }
        this.client.createToken( this.$creditcardForm.find('.mp-fields'), _callbackTokenizeCard );
      }
    } else if (this.gateway_name === 'decidir') {
      var settings = {
        "async": true,
        "crossDomain": true,
        "url": gp.decidir.url,
        "method": "POST",
        "headers": {
          "apikey": gp.decidir.public_key,
          "content-type": "application/json",
          "cache-control": "no-cache"
        },
        "processData": false,
        "data": "{\"card_number\":\""+$('#cc_card_number').val()+"\",\"card_expiration_month\":\""+ ($('#cc_expiration_month').val().length === 1 ? ("0" + $('#cc_expiration_month').val()) : $('#cc_expiration_month').val()) +"\",\"card_expiration_year\":\""+$('#cc_expiration_year').val().substring(2,4)+"\",\"security_code\":\""+$('#cc_cvv').val()+"\",\"card_holder_name\":\""+$('#cc_cardholder_name').val().split(".").join("")+"\",\"card_holder_identification\":{\"type\":\""+$('#cc_cardholder_doc_type').val().toLowerCase()+"\",\"number\":\""+$('#cc_cardholder_doc_number').val()+"\"}}"
      };

      var paymentObject = function(response) {
        return {
          gateway: "decidir",
          token: response.id,
          transaction_id: 123,
          installments: $('#cc_installments').val(),
          payment_method_id: that.detectCreditBrand($('#cc_card_number').val()),
          issuer_id: "1",
          bin: response.bin,
          document_number: $('#cc_cardholder_doc_number').val(),
          document_type: $('#cc_cardholder_doc_type').val().toLowerCase()
        }
      };

      $.ajax(settings).done(function (response) {
        if(response.status === "active") {
          gp.pubSub.trigger( 'checkout:pay', paymentObject(response) )
        } else {
          gp.pubSub.trigger('checkout:payment:fail')
          that.$errors.append('<span class="error">Ocurrio un error al procesar la tarjeta. Por favor intente de nuevo</span>')
          that.$errors.show()
          that.enableForm(true)
        }

      }).fail(function() {
        gp.pubSub.trigger('checkout:payment:fail')
        that.$errors.append('<span class="error">Ocurrio un error al procesar la tarjeta. Por favor intente de nuevo</span>')
        that.$errors.show()
        that.enableForm(true)
      });
    }
  },

  enableForm: function(status){
    this.$creditcardForm.find('form input').each(function(){ this.disabled = !status })
    this.$creditcardForm.find('form select').each(function(){
      status ? $(this)[0].selectize.enable() : $(this)[0].selectize.disable()
    })
    if( !status ){
      this.$errors.hide().text('')
    }
  },

  cardNumberChange: function(evt){
    var $currentTarget = $(evt.currentTarget)
    this.ccNumber = $currentTarget.val().replace(/[ .-]/g, '')
    if( this.binChanged() && this.ccNumber.length >= 6 ){
      this.getDebounceInstallments()
    } else if( this.ccNumber.length < 6 ){
      this.cleanUpInstallmentsOptions(true)
    }
  },

  // Decidir 2.0
  detectCreditBrand: function (number) {
    var re = {
      31: /^(4026|417500|4405|4508|4844|4913|4917)\d+$/,
      // maestro: /^(5018|5020|5038|5612|5893|6304|6759|6761|6762|6763|0604|6390)\d+$/,
      1: /^4[0-9]{12}(?:[0-9]{3})?$/,
      15: /^5[1-5][0-9]{14}$/,
      65: /^3[47][0-9]{13}$/,
      8: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/
    }

    for(var key in re) {
      if(re[key].test(number)) {
        return key
      }
    }
  },

  getInstallments: function(){
    if( this.ccBin.length >= 6 ) {

      // Decidir 2.0 Check if credit card number is in our decidir
      if( this.checkIfBinIsDecidir(this.ccBin.substring(0, 6), gp.bines) ){
        this.gateway_name = 'decidir';
        this.displayDecidirOptions();
      } else {
        this.gateway_name = 'mercadopago';
        this.client.getInstallments( { bin: this.ccBin, amount: gp.checkout.total() }, this.displayInstallmentOptions )
      }
    }
  },

  // Decidir 2.0
  checkIfBinIsDecidir: function(currentBin, bines) {
    var returned = false;

    $.each(bines, function(key, value){
      if(value.bin === currentBin) {
        returned = true;
      }
    });

    return returned;
  },

  // Decidir 2.0
  displayDecidirOptions: function(){
    this.cleanUpInstallmentsOptions(false);
    var installmentsSelect = this.$creditcardForm.find('.installments select')[0].selectize;
    var currentBin = this.ccBin.substring(0, 6);
    var binObject;
    var installments = [];

    $.each(gp.bines, function(key, value){
      if(value.bin === currentBin) {
        binObject = value;
      }
    });

    $.each(binObject.installments, function(key, value){
      var intValue = parseInt(value);

      installments.push({
        discount_rate: 0,
        installment_amount: gp.checkout.total() / intValue,
        installment_rate: 0,
        installments: intValue,
        labels: ["CFT_0,00%|TEA_0,00%"],
        max_allowed_amount: 250000,
        min_allowed_amount: 0,
        recommended_message: intValue + " " + (intValue === 1 ? "cuota" : "cuotas") +" de $ " + gp.checkout.total() / intValue + " (" + gp.checkout.total() + ")",
        total_amount: gp.checkout.total()
      });
    });

    installmentsSelect.addOption(this.buildInstallmentsSelectOptions(installments));
    installmentsSelect.on('blur', function(){ self.checkRequiredField } );
    installmentsSelect.refreshOptions(false);
    installmentsSelect.setValue('1');
  },

  itemsChanged: function(){
    this.evaluateTicketOptionVisible()
  },

  setCardHolderName: function(name){
    var $input = this.$el.find('.full-name input')
  },

  binChanged: function(){
    var bin = this.ccNumber.slice(0, 6)
    if( bin === this.ccBin ){
      return false
    } else {
      this.ccBin = bin
      return true
    }
  },

  displayInstallmentOptions: function(status, response){
    this.cleanUpInstallmentsOptions(false)
    var installmentsSelect = this.$creditcardForm.find('.installments select')[0].selectize
    if( status === 200 ) {
      this.paymentData.payment_method_id = response[0].payment_method_id
      this.paymentData.payment_type_id = response[0].payment_type_id
      this.paymentData.issuer = { id: response[0].issuer.id, name: response[0].issuer.name }
      installmentsSelect.addOption(this.buildInstallmentsSelectOptions(response[0].payer_costs))
      installmentsSelect.on('blur', function(){ self.checkRequiredField } )
    } else {
      installmentsSelect.addOption([{ text: "Hay un problema con el número de tarjeta que ingresaste", value: 1}])
    }
    installmentsSelect.refreshOptions(false)
    installmentsSelect.setValue('1')
  },

  buildInstallmentsSelectOptions: function(installments){
    var self = this
    return _(installments).map(function(plan){
      return { value: plan.installments, text: plan.recommended_message, cft: self.getCftParsed(plan.labels[0]), installment_rate: plan.installment_rate, financed_total: plan.total_amount}
    })
  },

  getCftParsed: function(cft){
    cft = cft.replace(/_/g, " ");
    cft = cft.replace("|", " | ");
    return cft
  },

  cleanUpInstallmentsOptions: function(setDefault){
    var installmentsSelect = this.$creditcardForm.find('.installments select')[0].selectize
    installmentsSelect.clear(true)
    installmentsSelect.clearOptions()
    installmentsSelect.off('blur')
    if( setDefault ){
      installmentsSelect.addOption([{ text: "Ingresá tu tarjeta para conocer las cuotas disponibles", value: 1 }])
      installmentsSelect.refreshOptions(false)
      installmentsSelect.setValue('1')
    }
  },

  validateField: function(evt){
    var $currentTarget = $(evt.currentTarget)
    var errors = gp.Helpers.v5.form.validate($currentTarget)

    this.handleFieldErrors( $currentTarget, errors )
  },

  handleFieldErrors: function($field, errors){
    var that = this
    var $wrapper = $field.parent('.input-wrapper')
    var $label = this.$el.find("label[for='"+$field.attr('id')+"']")

    var _addErrors = function(){
       var hasErrors = $wrapper.hasClass('with-errors')
       if( !hasErrors ){
        that.fieldsWithErrors.push($field.attr('id'))
        $wrapper.addClass('with-errors')
        $label.addClass('with-errors')
       }
    }
    var _removeErrors = function(){
       var hasErrors = $wrapper.hasClass('with-errors')
       if( hasErrors ){
        that.fieldsWithErrors = _.without(that.fieldsWithErrors, $field.attr('id'))
        $wrapper.removeClass('with-errors')
        $label.removeClass('with-errors')
       }
    }

    if( errors ){
      _addErrors()
    } else {
      _removeErrors()
    }
  },

  checkRequiredField: function(){
    var fieldsToCheck = []
    if( this.selectedOption === 'creditcards' ){
      fieldsToCheck = this.$requiredCCFields
    } else {
      fieldsToCheck = this.$requiredTicketFields
    }
    this.readyToTokenize = _(fieldsToCheck).all(function(f){ return $(f).val().trim() !== '' })
    if( this.readyToTokenize && this.fieldsWithErrors.length === 0 ){
      gp.pubSub.trigger('checkout:payment:ready')
    }
  },

  checkAddressRequiredFields: function(){
    gp.pubSub.trigger('checkout:address:missing_data')
  },

  infoModal: function(){
    $.magnificPopup.close()

    var src =
    '<div style="padding:10px;width:100%;max-width:830px;height:1451px;background-color:white;margin:0 auto;">'+
      '<iframe class="mfp-iframe" '+
        'style="display:block;width:100%;height:100%;" '+
        'src="https://www.mercadopago.com/mla/credit_card_promos.htm" '+
        'frameborder="0" allowfullscreen>'+
      '</iframe>'+
    '</div>'

    $.magnificPopup.open({
      items: {
        src: src,
        type: 'inline'
      }
    });

    return true
  },

  updateTaxes: function(evt){
    var $el = $(evt.currentTarget).find('select')[0]
    var selectedValue = $el.value
    gp.checkout.cft = $el.selectize.options[selectedValue].cft
    gp.checkout.financed_total = $el.selectize.options[selectedValue].financed_total
    gp.pubSub.trigger('checkout:total:update')
  },

  removeTaxes: function(evt){
    gp.pubSub.trigger('checkout:total:remove')
  }
})

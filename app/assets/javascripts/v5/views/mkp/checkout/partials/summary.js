gp.Views['checkout/summary'] = gp.App.View.extend({
  events: {},
  initialize: function(options){
    _.bindAll(
      this,
      'clickBuy',
      'itemsRemoved',
      'deliveryChanged',
      'removeCartItem',
      'paymentFail',
      'payItPlease',
      'updateDeliveryCost'
    )

    var $self = this.$el

    this.cartItemsEndpoint = '/api/mkp/checkout/cart_items'
    this.ride = options.ride
    this.$form = this.$el.find('#checkout-pay-form')
    this.$payButton = this.$form.find('.pay-button')
    this.$cartItems = this.$el.find('.cart-item')
    this.$removeItemButtons = this.$el.find('.remove-item')

    // Initialize the Subtotal
    this.subtotal = new gp.Views['checkout/summary/subtotal']({
      el: $self.find('.subtotal'),
      deliveryCharge: 0,
      taxesCharge: 0,
      discountAmount: 0
    })
    // Initialize the Total
    this.total = new gp.Views['checkout/summary/total']({
      el: $self.find('.total')
    })

    // Local Events
    this.$payButton.on('click', this.clickBuy)
    this.$removeItemButtons.on('click', this.removeCartItem)

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'checkout:items:remove':    this.itemsRemoved,
      'checkout:delivery:change': this.deliveryChanged,
      'checkout:delivery:update': this.updateDeliveryCost,
      'checkout:payment:fail':    this.paymentFail,
      'checkout:pay':             this.payItPlease
    })
  },

  clickBuy: function(evt){
    if(this.ride.readyToPurchase() ) {
      this.$payButton.off('click')
      this.$payButton.addClass('disabled')
      this.$payButton.find('.button').text(I18n.t('js.v5.mkp.checkout.summary.buying'))
      if (gp.checkout.paymentCovered) {
        gp.pubSub.trigger('checkout:submit')
      } else {
        gp.pubSub.trigger('checkout:payment:tokenize',{preventDoubleTrigger: false})
      }
    } else {
      gp.pubSub.trigger('checkout:missing_data')
    }
  },

  payItPlease: function(paymentObject){
    var that = this
    if( paymentObject ){
      var _appendPaymentData = function(){
        var $container = $(that.$form.find('> div')[0])
        _(paymentObject).each(function(v,k){
          $container.append('<input type="hidden" value="'+v+'" name="payment['+k+']">')
        })
      }()
    }
    gp.pubSub.trigger('checkout:submit')
  },

  removeCartItem: function(ev){
    var that = this

    var currentItem = $(ev.currentTarget).closest('.cart-item')
    var variantId = currentItem.data('variant-id')

    $.ajax({
      url: this.getCartItemUrl('remove'),
      data: { variant_id: variantId },
      dataType: 'json',
      type: 'post',
      cache: false
    }).done(function(data){
      that.$cartItems.filter(function(e) {
        return $(this).data('variant-id') == variantId
      }).remove()

      if (that.$el.find('.cart-item').length == 1) {
        that.$cartItems.find('.remove-item').remove()
      }

      gp.pubSub.trigger('checkout:items:remove', data)
    }).fail(function(){
      alert('Sorry! Cannot remove item!')
    })
  },

  itemsRemoved: function(data){
    gp.checkout.subtotal = data.response.subtotal
    gp.checkout.promotion.name = data.response.promotion.name
    gp.checkout.promotion.discount = data.response.promotion.discount
    this.subtotal.updateSubtotalAmount()
    this.subtotal.updatePromotionDiscount()
    this.total.updateTotal()
    gp.pubSub.trigger('checkout:items:change')
  },

  deliveryChanged: function(){
    var deliveryCost = arguments[1],
        taxes        = arguments[2]
    gp.checkout.deliveryCharge = deliveryCost
    gp.checkout.taxesCharge = taxes
    this.updateDeliveryCost()
  },

  updateDeliveryCost: function(){
    this.subtotal.updateDeliveryCost(this.ride.getDeliveryCharge(), this.ride.getTaxesCharge())
    this.subtotal.updateHandlingDiscount()
    this.total.updateTotal()
  },

  applyCoupon: function(data){

  },

  removeCoupon: function(){

  },

  paymentFail: function(){
    gp.checkout.ready_to_purchase = false
    this.$payButton.removeClass('disabled')
    this.$payButton.find('.button').text(I18n.t('js.v5.mkp.checkout.summary.buy'))
    this.$payButton.on('click', this.clickBuy)
  },

  getCartItemUrl: function(action){
    return this.cartItemsEndpoint + '/remove' + '?fid=' + gp.checkout.fid
  }
})

gp.Views['checkout/summary/subtotal'] = gp.App.View.extend({
  initialize: function(options){
    gp.checkout.subtotal = parseFloat(this.$el.data('subtotal'))
    gp.checkout.coupon.discount = parseFloat(this.$el.data('discount'))
    gp.checkout.promotion.discount = parseFloat(this.$el.data('promotion_discount'))
    gp.checkout.promotion.name = this.$el.data('promotion_name')

    this.updateDeliveryCost(options.deliveryCharge, options.taxesCharge)
    this.updateDiscount(options.discountAmount)
    this.updatePromotionDiscount(gp.checkout.promotion.discount)

    gp.pubSub.on({
      'checkout:subtotal:updatediscount': this.updateDiscount,
      'checkout:subtotal:updatehandling': this.updateHandlingDiscount,
    })
  },

  updateSubtotalAmount: function(){
    subtotalAmount = gp.checkout.subtotal
    this.$el.find('.subtotal_amount .price').text('$' + subtotalAmount.toFixed(2))
  },

  updateDeliveryCost: function(deliveryCost, taxesCharge){
    this.$el.find('.delivery_cost .price').text('$' + deliveryCost.toFixed(2))
    this.$el.find('.taxes .price').text('$' + taxesCharge.toFixed(2))
  },

  updateDiscount: function(){
    var $discount = $('.subtotal').find('.discount')
    if( gp.checkout.coupon.discount === 0) {
      $discount.hide()
    } else {
      $discount.find('.price').text('- $' + gp.checkout.coupon.discount.toFixed(2))
      $discount.show()
    }
  },

  updatePromotionDiscount: function(){
    var $promotionDiscount = this.$el.find('.promotion')
    if( gp.checkout.promotion.discount === 0 ) {
      $promotionDiscount.hide()
    } else {
      $promotionDiscount.find('h4').text(gp.checkout.promotion.name)
      $promotionDiscount.find('.price').text('- $' + gp.checkout.promotion.discount.toFixed(2))
      $promotionDiscount.show()
    }
  },

  updateHandlingDiscount: function(){
    var $handlingDiscount = $('.subtotal').find('.handling-discount')
    if( gp.checkout.coupon.handlingDiscount() === 0) {
      $handlingDiscount.hide()
    } else {
      $handlingDiscount.find('.price').text('- $' + gp.checkout.coupon.handlingDiscount().toFixed(2))
      $handlingDiscount.show()
    }
  }
})

gp.Views['checkout/summary/total'] = gp.App.View.extend({
  initialize: function(){
    _.bindAll(
      this,
      'updateTotal',
      'removeTaxes'
    )

    gp.pubSub.on({
      'checkout:total:update': this.updateTotal,
      'checkout:total:remove': this.removeTaxes
    })
  },

  // XXX: I know. Haters gonna hate.
  updateTotal: function(data){

    this.$el.find('.order_total .price_with_financing').text('$' + gp.checkout.total().toFixed(2))

    if (gp.checkout.cft) {
      var finance_data = gp.checkout.cft.split(' | ')

      var cft = finance_data[0]
      var tea = finance_data[1]

      this.$el.find('.order_total .taxes span.cft').text(cft)
      this.$el.find('.order_total .taxes span.tea').text(tea)
      this.$el.find('.price_with_financing').text('$' + gp.checkout.total().toFixed(2))
      this.$el.find('.order_total .fees_and_interests span').text('$' + gp.checkout.financingCosts().toFixed(2))
      this.$el.find('.order_total .financed_price span').text('$' + gp.checkout.financed_total)
      this.$el.find('.order_total h3.order_price').text('Total contado')
      this.$el.find('.order_total h3.order_price').removeClass('order_price').addClass('order_price_with_financing')
      this.$el.find('.order_total .price').removeClass('price').addClass('price_with_financing')
      this.$el.find('.order_total').css('padding-bottom', '0')

      this.$el.find('.order_total .financing_information').show()
    }

    if(data !== undefined && data.notRunAuthorize) {
      // Do nothing
    } else {
      gp.pubSub.trigger('checkout:total:authorize')
    }
  },

  removeTaxes: function(){
    this.$el.find('.order_total h3.order_price_with_financing').removeClass('order_price_with_financing').addClass('order_price')
    this.$el.find('.order_total .financing_information').hide()
  }
})

gp.Views['checkout/coupon'] = gp.App.View.extend({
  events: {
    'click .coupon-form > h3': 'openCouponSection',
    'click .apply-coupon-button': 'applyCoupon',
    'click .remove-coupon': 'removeCoupon',
    'submit .coupon-form > form': 'submitForm'
  },

  initialize: function(){
    _.bindAll(
      this,
      'applyCoupon'
    )

    this.$couponForm = this.$el.find('.coupon-form')
    this.$couponDisplay = this.$el.find('.coupon-display')

    this.$form = this.$couponForm.find('form')
    this.$couponField = this.$form.find('#coupon-code')
    this.$couponErrors = this.$couponForm.find('#coupon-errors')

    // pubSub Events
    gp.pubSub.on('checkout:items:remove', this.applyCoupon)
  },

  openCouponSection: function(evt){
    var $button = $(evt.currentTarget)
    $button.hasClass('open') ? $button.removeClass('open') : $button.addClass('open')
    this.$form.toggle()
  },

  applyCoupon: function(){
    if (this.$couponField.val().length > 0){
      var that = this,
          url  = this.getUrl(this.$form)
      gp.pubSub.trigger('checkout:coupon:applying')
      $.ajax({
        url: url,
        data: this.$form.formParams(),
        dataType: 'json',
        type: 'post',
        cache: false
      }).done(function(data){
        that.couponDone(data)
        gp.pubSub.trigger('checkout:coupon:done', data)
      }).fail(function(xhr){
        if( gp.checkout.coupon.discount > 0 ){
          gp.pubSub.trigger('checkout:coupon:remove')
        }
        var errors = xhr.status === 422 || xhr.status === 404 ? xhr.responseJSON.response.errors : [ I18n.t('js.v5.mkp.checkout.summary.invalid_coupon') ]
        that.showErrors(errors)
      })
    }
  },

  couponDone: function(data){
    var $couponApplied = this.$couponDisplay.find('.coupon-applied')
    this.$couponErrors.hide()
    this.$couponForm.hide()
    $couponApplied.text(this.$couponField.val())
    this.$couponDisplay.show()
  },

  removeCoupon: function(){
    var that = this,
        $removeForm = this.$couponDisplay.find('form'),
        url  = this.getUrl($removeForm)
    gp.pubSub.trigger('checkout:coupon:removing')
    $.ajax({
      url: url,
      dataType: 'json',
      type: 'delete',
      cache: false
    }).done(function(data){
      that.resetForm()
      gp.pubSub.trigger('checkout:coupon:remove')
    }).fail(function(xhr){
      // TODO.
    })
  },

  resetForm: function() {
    this.$form.get(0).reset()
    this.$couponForm.show()
    this.$couponDisplay.hide()
  },

  submitForm: function(evt){
    evt.preventDefault()
    this.applyCoupon()
  },

  showErrors: function(errors){
    var errorHtml = ''
    _(errors).each(function(error){
      errorHtml += '<li>' + error + '</li>'
    })
    this.$couponErrors.html(errorHtml)
    this.$couponErrors.show()
  },

  getUrl: function($form){
    var url = $form.attr('action')
    url += '?fid=' + gp.checkout.fid
    return url
  }
})

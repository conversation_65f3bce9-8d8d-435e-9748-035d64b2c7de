gp.Views['checkout/payment/todopago'] = gp.App.View.extend({
  events: {
    'click .creditcards i, .creditcards .header':               'chooseCreditcards',
    'click .tickets i, .tickets .header':                       'chooseTickets',
    'click .billetera i, .billetera .header':                     'chooseBilletera',
    'keyup    .creditcard-form .card-number input': 'cardNumberChange',
    'focusout .creditcard-form input.validate': 'validateField',
    'focusout .tickets-form input, .tickets-form select':       'checkRequiredField',
    'focusout .creditcard-form input, .creditcard-form select': 'checkRequiredField',
    'focusin  input': 'checkAddressRequiredFields',
    'change .installments .select-wrapper': 'updateTaxes'
  },

  initialize: function (options) {
    var self = this;
    this.MERCADOPAGO_TICKET_LIMIT = 4500.0;

    _.bindAll(
        this,
        'checkAllRequiredFields',
        'checkAddressRequiredFields',
        'setCardHolderName',
        'itemsChanged',
        'changeCCNumberFieldType',
        'tokenizePayment',
        'getInstallments',
        'goForKeys'
    );

    // Address Form
    this.appendPaymentMethodToAddress();

    // Vars
    this.gateway_name = 'todopago';
    this.fieldsWithErrors = [];
    this.readyToTokenize = false;
    this.selectedOption = 'creditcards';
    this.client = gp.checkout.payment.client;
    this.paymentData = {gateway: this.gateway_name};
    this.getDebounceInstallments = _.debounce(this.getInstallments, 650);
    this.ride = options.ride

    this.ccNumber = '';
    this.ccBin = '';

    // jQuery Vars
    this.$ticketsOption = this.$el.find('.payment-option.tickets');
    this.$ticketsForm = this.$el.find('.tickets-form');
    this.$creditcardsOption = this.$el.find('.payment-option.creditcards');
    this.$creditcardForm = this.$el.find('.creditcard-form');
    this.$billeteraOption = this.$el.find('.payment-option.billetera');

    this.$errors = this.$creditcardForm.find('.errors');
    this.$requiredCCFields = this.$creditcardForm.find('input.required');
    this.$requiredTicketFields = this.$ticketsForm.find('input.required');
    this.$ccFields =       this.$creditcardForm.find('input[id^="cc_"], select[id^="cc_"]');
    this.$ccNumberField =  this.$ccFields.filter('input#cc_card_number');
    this.$mpFields =       this.$creditcardForm.find('.mp-fields input');
    this.ticketLoaded = false;
    this.decidirIsOn = false;

    // Local Events
    this.$ccNumberField.on('focusout focusin', this.changeCCNumberFieldType);

    var active_payments_2 = gp.checkout.payment_2.active_payments();
    var tickets = _(active_payments_2).select(function(p){ return p.payment_type_id === 'ticket' });

    // MercadoPago
    var _activatePaymentOptions = function(tickets) {
      var element = self.$el;
      $('#ticket_type').selectize();
      var ticketSelect = $('#ticket_type')[0].selectize;

      element.find('.active-payments .tickets .header').find('.payment-icon').each(function() { $(this).remove(); });
      _(tickets).each(function(p){
        element.find('.active-payments .tickets .header').append('<span class="payment-icon"><img alt="'+ p.name +'" src="'+ p.secure_thumbnail +'" /></span>');
        ticketSelect.addOption([{ value: p.id, text: p.name }])
      });

      self.evaluateTicketOptionVisible();
    };

    if ($('#ticket_type').length > 0) {
      _activatePaymentOptions(tickets);
    }

    self.$ticketsForm.find('select').selectize();

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'checkout:missing_data': this.checkAllRequiredFields,
      'checkout:payment:tokenize': this.tokenizePayment,
      'checkout:customer:full_name:change': this.setCardHolderName,
      'checkout:coupon:done checkout:coupon:remove': this.getInstallments,
      'checkout:items:change': this.itemsChanged,
      'checkout:delivery:change':  this.getInstallments,
      'checkout:total:authorize': this.goForKeys,
    });

    this.enableForm(false);

    // We need to check if goForKeys was fired
    setTimeout(function() {
      if(!todoPagoTokenized && $('#address_id').length > 0) {
        self.goForKeys();
      }
    }, 2000);
  },

  evaluateTicketOptionVisible: function(){
    if( gp.checkout.total() > this.MERCADOPAGO_TICKET_LIMIT ){
      this.$ticketsOption.hide()
    } else{
      this.$ticketsOption.show()
    }
  },

  changeCCNumberFieldType: function (evt) {
    if (evt.type === 'focusin') {
      this.$ccNumberField.attr('type', 'text')
    } else {
      this.$ccNumberField.attr('type', 'password')
    }
  },

  chooseCreditcards: function(){
    this.selectedOption = 'creditcards';
    this.checkSelectedOption();
    gp.pubSub.trigger('checkout:total:update');
  },

  chooseTickets: function(){
    this.selectedOption = 'tickets';
    this.checkSelectedOption();
    this.readyToTokenize = true;
    this.fieldsWithErrors = [];
    gp.pubSub.trigger('checkout:total:remove');
  },

  chooseBilletera: function() {
    this.selectedOption = 'billetera';
    this.checkSelectedOption();
    this.readyToTokenize = true;
    this.fieldsWithErrors = [];
    gp.pubSub.trigger('checkout:total:remove');
  },

  toggleForm: function(){
    if (!this.$ticketsOption.is(':visible')) return;

    if( this.selectedOption === 'creditcards' ){

      if( this.checkIfBinIsDecidir(this.ccBin.substring(0, 6), gp.bines) ) {
        this.gateway_name = 'decidir';
      } else {
        this.gateway_name = "todopago"
      }

      this.$creditcardForm.slideDown();
      this.$ticketsForm.slideUp();
      this.$ticketsForm.find('select')[0].selectize.clear(true);
    } else if( this.selectedOption === 'tickets') {
      this.$creditcardForm.slideUp();
      this.$ticketsForm.slideDown();
      this.$ticketsForm.find('select')[0].selectize.focus();

      this.gateway_name = "mercadopago"
    } else {
      this.$creditcardForm.slideUp();
      this.$ticketsForm.slideUp();
    }
  },

  checkSelectedOption: function(){
    this.toggleForm();

    if( this.selectedOption === 'creditcards' ){
      this.$creditcardsOption.find('i').addClass('i-radio-on').removeClass('i-radio-off');
      this.$ticketsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      this.$billeteraOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      $('.pay-button').show();
      $('#MY_btnPagarConBilletera').addClass('hide');

    } else if(this.selectedOption === 'tickets') {
      this.$ticketsOption.find('i').addClass('i-radio-on').removeClass('i-radio-off');
      this.$creditcardsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      this.$billeteraOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      $('.pay-button').show();
      $('#MY_btnPagarConBilletera').addClass('hide');
    } else {
      this.$billeteraOption.find('i').addClass('i-radio-on').removeClass('i-radio-off');
      this.$creditcardsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      this.$ticketsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off');
      $('.pay-button').hide();
      $('#MY_btnPagarConBilletera').removeClass('hide');
    }
  },

  checkAllRequiredFields: function () {
    var that = this,
        fieldsToCheck = [],
        fieldsToCheck = this.$requiredCCFields;

    _(fieldsToCheck).each(function (field) {
      var $field = $(field);
      var errors = gp.Helpers.v5.form.validate($field);
      that.handleFieldErrors($field, errors);
    })
  },

  getCftParsed: function (cft) {
    cft = cft.replace(/_/g, " ");
    cft = cft.replace("|", " | ");
    return cft;
  },

  tokenizePayment: function (data) {
    if(data.preventDoubleTrigger) return; // for  preventing not-expected double call
    data.preventDoubleTrigger = true;
    var self = this;
    this.enableForm(false);

    // We only accept mercado pago with tickets
    if(this.gateway_name === 'mercadopago') {
      var _getTicketPaymentObject = function(){
        var ticket_type = self.$ticketsForm.find('.ticket-type select').val(),
            doc_type = self.$ticketsForm.find('.doc-type select').val(),
            doc_number = self.$ticketsForm.find('.doc-number input').val();

        return {
          gateway: self.gateway_name,
          payment_method_id: ticket_type,
          document_type: doc_type,
          document_number: doc_number
        }
      };

      gp.pubSub.trigger( 'checkout:pay',  _getTicketPaymentObject() )
    } else if(this.gateway_name === 'todopago') {
      gp.doc_number = $('#nroDocTxt').val();
      gp.doc_type = self.$ticketsForm.find('.doc-type select').val();
      self.authorizeRequest();
    } else if (this.gateway_name === 'decidir') {
      var settings = {
        "async": true,
        "crossDomain": true,
        "url": gp.decidir.url,
        "method": "POST",
        "headers": {
          "apikey": gp.decidir.public_key,
          "content-type": "application/json",
          "cache-control": "no-cache"
        },
        "processData": false,
        "data": "{\"card_number\":\""+$('#numeroTarjetaTxt').val()+"\",\"card_expiration_month\":\""+ $('#mesTxt').val() +"\",\"card_expiration_year\":\""+ $('#anioTxt').val() +"\",\"security_code\":\""+$('#codigoSeguridadTxt').val()+"\",\"card_holder_name\":\""+$('#apynTxt').val().split(".").join("")+"\",\"card_holder_identification\":{\"type\":\""+$('#tipoDocCbx').val().toLowerCase()+"\",\"number\":\""+$('#nroDocTxt').val()+"\"}}"
      };

      var paymentObject = function(response) {
        return {
          gateway: "decidir",
          token: response.id,
          transaction_id: 123,
          installments: $('#promosCbx').val(),
          payment_method_id: self.detectCreditBrand($('#numeroTarjetaTxt').val()),
          issuer_id: "1",
          bin: response.bin,
          document_number: $('#nroDocTxt').val(),
          document_type: $('#tipoDocCbx').val().toLowerCase()
        }
      };

      $.ajax(settings).done(function (response) {
        if(response.status === "active") {
          gp.pubSub.trigger( 'checkout:pay', paymentObject(response) )
        } else {
          gp.pubSub.trigger('checkout:payment:fail');
          self.$errors.append('<span class="error">Ocurrio un error al procesar la tarjeta. Por favor intente de nuevo</span>');
          self.$errors.show();
          self.enableForm(true);
        }

      }).fail(function(response) {
        gp.pubSub.trigger('checkout:payment:fail');
        self.$errors.append('<span class="error">Ocurrio un error al procesar la tarjeta. Por favor intente de nuevo</span>');
        self.$errors.show();
        self.enableForm(true);
      });
    }
  },

  enableForm: function (status) {
    this.$creditcardForm.find('form input').each(function () {
      this.disabled = !status;
    });
    this.$creditcardForm.find('form select').each(function () {
      this.disabled = !status;
    });
    if (!status) {
      this.$errors.hide().text('');
    }
  },

  itemsChanged: function () {
    this.evaluateTicketOptionVisible();
  },

  setCardHolderName: function (name) {
    var $input = this.$el.find('.full-name input')
  },

  binChanged: function () {
    var bin = this.ccNumber.slice(0, 6);
    if (bin === this.ccBin) {
      return false;
    } else {
      this.ccBin = bin;
      return true;
    }
  },

  buildInstallmentsSelectOptions: function (installments) {
    var self = this;
    return installments.map(function (plan) {
      if (gp.first === undefined) {
        gp.first = plan.Id;
      }

      return {
        value: plan.Id,
        text: plan.Name + ' ($' + plan.Amount + ')',
        cft: self.getCftParsed("CFT_" + plan.CFT.toString().replace('.', ',') + "%|TEA_" + plan.TEA.toString().replace('.', ',') + "%"),
        installment_rate: plan.DirectRate,
        financed_total: plan.Amount
      };
    });
  },

  cleanUpInstallmentsOptions: function (setDefault) {
    var installmentsSelect = this.$creditcardForm.find('.installments select')[0].selectize;
    installmentsSelect.clear(true);
    installmentsSelect.clearOptions();
    installmentsSelect.off('blur');
    if (setDefault) {
      installmentsSelect.addOption([{text: "Ingresá tu tarjeta para conocer las cuotas disponibles", value: 1}]);
      installmentsSelect.refreshOptions(false);
      installmentsSelect.setValue('1');
    }
  },

  validateField: function (evt) {
    var $currentTarget = $(evt.currentTarget);
    var errors = gp.Helpers.v5.form.validate($currentTarget);

    this.handleFieldErrors($currentTarget, errors)
  },

  handleFieldErrors: function ($field, errors) {
    var that = this;
    var $wrapper = $field.parent('.input-wrapper');
    var $label = this.$el.find("label[for='" + $field.attr('id') + "']");

    var _addErrors = function () {
      var hasErrors = $wrapper.hasClass('with-errors');
      if (!hasErrors) {
        that.fieldsWithErrors.push($field.attr('id'));
        $wrapper.addClass('with-errors');
        $label.addClass('with-errors')
      }
    };

    var _removeErrors = function () {
      var hasErrors = $wrapper.hasClass('with-errors');
      if (hasErrors) {
        that.fieldsWithErrors = _.without(that.fieldsWithErrors, $field.attr('id'));
        $wrapper.removeClass('with-errors');
        $label.removeClass('with-errors')
      }
    };

    if (errors) {
      _addErrors()
    } else {
      _removeErrors()
    }
  },

  checkRequiredField: function () {
    var fieldsToCheck = [];
    if (this.selectedOption === 'creditcards') {
      fieldsToCheck = this.$requiredCCFields;
    } else if (this.selectedOption === 'tickets') {
      fieldsToCheck = this.$requiredTicketFields;
    } else {
      this.readyToTokenize = true;
    }

    if (!this.readyToTokenize) {
      this.readyToTokenize = _(fieldsToCheck).all(function (f) {
        return $(f).val().trim() !== '';
      });
    }

    if (this.readyToTokenize && this.fieldsWithErrors.length === 0) {
      gp.pubSub.trigger('checkout:payment:ready');
    }
  },

  checkAddressRequiredFields: function () {
    gp.pubSub.trigger('checkout:address:missing_data');
  },

  infoModal: function () {
    $.magnificPopup.close();

    var src =
        '<div style="padding:10px;width:100%;max-width:830px;height:1451px;background-color:white;margin:0 auto;">' +
        '<iframe class="mfp-iframe" ' +
        'style="display:block;width:100%;height:100%;" ' +
        'src="https://www.mercadopago.com/mla/credit_card_promos.htm" ' +
        'frameborder="0" allowfullscreen>' +
        '</iframe>' +
        '</div>';

    $.magnificPopup.open({
      items: {
        src: src,
        type: 'inline'
      }
    });

    return true;
  },

  updateTaxes: function (evt) {
    var $el = $(evt.currentTarget).find('select')[0];
    var selectedValue = $el.value;
    element = $el.selectedOptions[0]

    cft = element.getAttribute('cft');
    tea = element.getAttribute('tea');
    gp.checkout.cft = "CFT: " + cft + " | TEA: "+ tea
    gp.checkout.financed_total = element.getAttribute('newamount');
    gp.pubSub.trigger('checkout:total:update', {notRunAuthorize: true});
  },

  removeTaxes: function (evt) {
    gp.pubSub.trigger('checkout:total:remove');
  },

  cleanSelect: function (selector) {
    var select = this.$creditcardForm.find(selector)[0].selectize;
    select.clear(true);
    select.clearOptions();
    select.off('blur');
  },

  getInstallments: function () {
    var self = this;
    var installmentsBackup = $('#backup-promos');
    var installments = $('#promosCbx');

    if( this.ccBin.length >= 6 ) {
      if( this.checkIfBinIsDecidir(this.ccBin.substring(0, 6), gp.bines) ){
        this.gateway_name = 'decidir';

        if(!this.decidirIsOn) {
          installmentsBackup.html(installments.html());
        }

        this.decidirIsOn = true;
        this.displayDecidirOptions();
      } else {
        this.gateway_name = 'todopago';

        if(this.decidirIsOn) {
          installments.html(installmentsBackup.html());
        }

        this.decidirIsOn = false;
      }
    }
  },

  checkIfBinIsDecidir: function(currentBin, bines) {
    var returned = false;

    $.each(bines, function(key, value){
      if(value.bin === currentBin) {
        returned = true;
      }
    });

    return returned;
  },

  displayDecidirOptions: function(){
    var installments = $('#promosCbx');
    installments.html("");

    var currentBin = this.ccBin.substring(0, 6);
    var binObject;

    $.each(gp.bines, function(key, value){
      if(value.bin === currentBin) {
        binObject = value;
      }
    });

    $.each(binObject.installments, function(key, value){

      var intValue = parseInt(value);
      var text = intValue + " " + (intValue === 1 ? "cuota" : "cuotas") +" de $ " + (gp.checkout.total() / intValue).toFixed(2) + " ($" + gp.checkout.total().toFixed(2) + ")";

      installments.append('<option tea="0" cft="0" newamount="' + gp.checkout.total() + '" value="' + intValue + '">' + text + '</option>');
    });
  },

  detectCreditBrand: function (number) {
    var re = {
      31: /^(4026|417500|4405|4508|4844|4913|4917)\d+$/,
      // maestro: /^(5018|5020|5038|5612|5893|6304|6759|6761|6762|6763|0604|6390)\d+$/,
      1: /^4[0-9]{12}(?:[0-9]{3})?$/,
      15: /^5[1-5][0-9]{14}$/,
      65: /^3[47][0-9]{13}$/,
      8: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/
    }

    for(var key in re) {
      if(re[key].test(number)) {
        return key
      }
    }
  },

  authorizeRequest: function () {
    $('#MY_btnConfirmarPago').click();
  },

  cardNumberChange: function(evt){
    var $currentTarget = $(evt.currentTarget);
    this.ccNumber = $currentTarget.val().replace(/[ .-]/g, '');
    if( this.binChanged() && this.ccNumber.length >= 6 ){
      this.gateway_name = 'decidir';
      this.getDebounceInstallments()
    } else if( this.ccNumber.length < 6 ){
      this.gateway_name = 'todopago';
    }
  },

  appendPaymentMethodToAddress: function() {
    $('.address-form').find('form')
        .append('<input type="hidden" id="address_payment_method" name="payment_method" value="todopago" />')
        .append('<input type="hidden" id="address_payment_method" name="payment_total_amount" value="' + gp.checkout.total() + '" />');
  },

  goForKeys: function() {
    todoPagoTokenized = true;

    // Clean installments
    $('#promosCbx').html("");
    var fullName = $('#address_full_name').val();

    // Return if some fields are not filled out
    // if(!this.ride.addressForm.allRequieredFieldsValid()) {
    //   // showError("Por favor complete los campos de dirección de entrega")
    //   return false;
    // }

    var validateEmail = function(email) {
      var re = /\S+@\S+\.\S+/;
      return re.test(email);
    };

    var lastName = fullName.split(' ').splice(1).join(' ') === "" ? fullName.split(' ')[0] : fullName.split(' ').splice(1).join(' ');
    if(fullName.split(' ')[0].length < 2) {
      // showError("Nombre demasiado corto. Debe contenter más de 2 caracteres");
      return false;
    } else if (lastName.length < 2) {
      // showError("Apellido demasiado corto. Debe contenter más de 2 caracteres");
      return false;
    } else if(!validateEmail($('#address_email').val())) {
      // showError("Formato de email invalido");
      return false;
    }

    var stateMap = {
      'Capital Federal': 'C', 'Bs As Interior': 'B', 'GBA': 'B', 'Catamarca': 'K', 'Chaco': 'H',
      'Chubut': 'U', 'Córdoba': 'X', 'Corrientes': 'W', 'Entre Ríos': 'E',
      'Formosa': 'P', 'Jujuy': 'Y', 'La Pampa': 'L', 'La Rioja': 'F',
      'Mendoza': 'M', 'Misiones': 'N', 'Neuquén': 'Q', 'Río Negro': 'R',
      'Salta': 'A', 'San Juan': 'J', 'San Luis': 'D', 'Santa Cruz': 'Z',
      'Santa Fe': 'S', 'Santiago del Estero': 'G', 'Tierra del Fuego': 'V', 'Tucumán': 'T'
    };

    var data = {
      first_name: fullName.split(' ')[0],
      last_name: lastName,
      email: $('#address_email').val(),
      phone_number: $('#address_telephone').val(),
      address1: $('#address_address').val() + " " + $('#address_street_number').val() ,
      address2: $('#address_address_2').val(),
      country: $('#address_country').val(),
      city: $('#address_city').val(),
      zip_code: $('#address_zip').val(),
      state: stateMap[$('#address_state').val()],
      total: gp.checkout.total().toFixed(2),
      fid: gp.checkout.fid,
      authenticity_token: $('[name="authenticity_token"]').val()
    };

    if(gp.checkout.is_pickup){
      data.address1 = gp.checkout.pickit.default.address1
      data.country = gp.checkout.pickit.default.country
      data.city = gp.checkout.pickit.default.city
      data.zip_code = gp.checkout.pickit.default.zip_code
      data.state = gp.checkout.pickit.default.state
      data.is_pickit = "1"
    }

      $.ajax({
      url: '/ar/shop/checkout/authorize_payment',
      method: 'POST',
      data: data,
      success: function (data, textStatus, xhr) {
        if (data.status_code === "-1") {
          gp.publicRequestKey = data.public_request_key;
          gp.requestKey = data.request_key;

          window.TPFORMAPI.hybridForm.setItem({
            publicKey: gp.publicRequestKey,
            defaultMail: ""
          });

          // It seems like we're not able to use "enableForm" method in this context
          // I tried with this.enableForm, that.enableForm, self.enableForm and nothing works
          $('.creditcard-form').find('form input, form select').each(function () {
            this.disabled = false;
          });

          $('#emailTxt').val($('#address_email').val());
        } else {
          console.log("not -1")
        }
      },
      error: function () {
        console.log('error by error')
      }
    });
  }
});
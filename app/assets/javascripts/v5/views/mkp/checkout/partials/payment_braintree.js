gp.Views['checkout/payment/braintree'] = gp.App.View.extend({
  creditCards: {
    available: [
      {
        name: "Visa",
        secure_thumbnail: "https://www.braintreegateway.com/images/payment_instrument_images/visa.png"
      },
      {
        name: "MasterCard",
        secure_thumbnail: "https://www.braintreegateway.com/images/payment_instrument_images/mastercard.png"
      },
      {
        name: "American Express",
        secure_thumbnail: "https://www.braintreegateway.com/images/payment_instrument_images/american_express.png"
      },
      {
        name: "Discover",
        secure_thumbnail: "https://www.braintreegateway.com/images/payment_instrument_images/discover.png"
      },
      {
        name: "JC<PERSON>",
        secure_thumbnail: "https://www.braintreegateway.com/images/payment_instrument_images/jcb.png"
      }
    ],
    fieldsMap: {
      cc_number:            'number',
      cc_street_address:    { billingAddress: 'streetAddress' },
      cc_postal_code:       { billingAddress: 'postalCode' },
      cc_state:             { billingAddress: 'region' },
      cc_country:           { billingAddress: 'countryCodeAlpha2' },
      cc_cardholder_name:   'cardholderName',
      cc_expiration_date:   'expirationDate',
      cc_expiration_month:  'expirationMonth',
      cc_expiration_year:   'expirationYear',
      cc_cvv:               'cvv'
    }
  },

  events: {
    'click .creditcards.loaded i, .creditcards.loaded .header': 'chooseCreditcards',
    'click .paypal.loaded i, .paypal.loaded .header .title':    'choosePaypal',
    'click #braintree-paypal-button img':                       'clickPaypalButton',
    'click .paypal.loaded.loggedin #bt-pp-cancel':              'cancelPaypalAccount',
    'focusout input.validate':                                  'validateField',
    'focusout input':                                           'checkRequiredField',
    'focusin  input':                                           'checkAddressRequiredFields'
  },

  initialize: function(){
    _.bindAll(
      this,
      'changeCCNumberFieldType',
      'checkAllRequiredFields',
      'checkAddressRequiredFields',
      'tokenizePayment',
      'verifiedPayment',
      'failPaymentVerification'
    )

    // Vars
    this.gateway_name = 'braintree'
    this.endpoint = '/api/mkp/checkout/payment'
    this.readyToTokenize = false
    this.fieldsWithErrors = []
    this.selectedOption = 'creditcards'
    this.client = gp.checkout.payment.client

    // jQuery vars
    this.$form = this.$el.find('form')
    this.$errors = this.$form.find('.errors')

    this.$creditcardForm = this.$form.find('.creditcard-form')
    this.$requiredFields = this.$creditcardForm.find('input.required')
    this.$ccFields =       this.$creditcardForm.find('input[id^="cc_"], select[id^="cc_"]')
    this.$ccNumberField =  this.$creditcardForm.find('input#cc_number')

    this.$creditcardsOption = this.$el.find('.payment-option.creditcards')
    this.$paypalOption = this.$el.find('.payment-option.paypal')

    // Local Events
    this.$ccNumberField.on('focusout focusin', this.changeCCNumberFieldType)
    this.on('payment:verify', this.verifyPaymentMethod)
    this.on('payment:verification:success', this.verifiedPayment)
    this.on('payment:verification:fail', this.failPaymentVerification)

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'checkout:missing_data': this.checkAllRequiredFields,
      'checkout:payment:tokenize': this.tokenizePayment
    })

    this.setupSelects()

    this.activatePaymentOptions()
    this.enableForm(true)
  },

  setupSelects: function(){
    var that = this
    var $countrySelect = this.$creditcardForm.find('select#cc_country')
    var $stateSelect = this.$creditcardForm.find('select#cc_state')
    var createSetting = $stateSelect.children('option').length > 1 ? false : true

    if( $countrySelect.size() > 0 ){
      this.countrySelect = $countrySelect.selectize({
        selectOnTab: true,
        onChange: function(value) {
          if (!value.length) return
          var results = _(GEO_CONSTANTS.states[value]).map(function(v, k) { return { value: k, text: v } })
          that.stateSelect.clearOptions()
          that.stateSelect.disable()
          if( results.length > 0 ){
            that.stateSelect.settings.create = false
            that.stateSelect.settings.placeholder = 'Select State'
            that.stateSelect.addOption(results)
            that.stateSelect.refreshOptions()
          } else {
            that.stateSelect.settings.create = true
            that.stateSelect.settings.placeholder = 'State Name'
          }
          that.stateSelect.updatePlaceholder()
          that.stateSelect.enable()
        }
      })[0].selectize
    }
    this.stateSelect = $stateSelect.selectize({
      create: createSetting,
      persist: false,
      selectOnTab: true
    })[0].selectize
  },

  activatePaymentOptions: function(){
    var that = this
    var _activatePaypalOption = function(evt){
      gp.checkout.payment.braintree.setup(gp.checkout.payment.token, "paypal", {
        container: "paypal-container",
        enableShippingAddress: false,
        enableBillingAddress: true,
        onPaymentMethodReceived: function (paymentObj) {
          that.paypalPaymentObject = paymentObj
          gp.pubSub.trigger('checkout:payment:ready')
          that.$el.find('.active-payments .paypal').removeClass('loggedout').addClass('loggedin')
        }
      })
      that.$el.find('.active-payments .paypal').addClass('loaded').addClass('loggedout')
    }
    var _activateCreditCardOption = function(){
      that.$ccNumberField.removeAttr('disabled')
      that.$creditcardForm.find('select').selectize()
    }
    _(this.creditCards.available).each(function(p){
      that.$el.find('.active-payments .creditcards > .header').append('<span class="payment-icon "><img height="24px" alt="'+ p.name +'" src="'+ p.secure_thumbnail +'" /></span>')
    })
    this.$el.find('.active-payments .creditcards').addClass('loaded ')
    _activatePaypalOption()
    _activateCreditCardOption()
    this.checkSelectedOption()
  },

  checkSelectedOption: function(){
    if( this.selectedOption === 'creditcards' ){
      this.$creditcardsOption.find('i').addClass('i-radio-on').removeClass('i-radio-off')
      this.$paypalOption.find('i').removeClass('i-radio-on').addClass('i-radio-off')
    } else {
      this.$creditcardsOption.find('i').removeClass('i-radio-on').addClass('i-radio-off')
      this.$paypalOption.find('i').addClass('i-radio-on').removeClass('i-radio-off')
    }
  },

  checkAllRequiredFields: function(){
    var that = this
    _(this.$requiredFields).each(function(field){
      var $field = $(field)
      var errors = gp.Helpers.v5.form.validate($field)
      that.handleFieldErrors( $field, errors )
    })
  },

  tokenizePayment: function(){
    var that = this
    if( this.selectedOption === 'paypal' ){
      if( typeof this.paypalPaymentObject !== 'undefined' ){
        var paymentObj = this.paypalPaymentObject
        this.trigger('payment:verify', paymentObj)
      } else {
        gp.pubSub.trigger('checkout:payment:fail')
        this.choosePaypal()
      }
    } else {
      var ccDataObject = {
        options: { validate: true },
        billingAddress: { }
      }
      var _grabCreditCardData = function(){
        var fieldsData = {}
        var fieldsMap = that.creditCards.fieldsMap
        _(that.$ccFields).each(function(field){ fieldsData[field.id] = field.value })
        _(fieldsData).each(function(d,k){
          var property = fieldsMap[k]
          if( _(property).isObject() ){
            ccDataObject[_(property).keys()[0]][_(property).values()[0]] = d
          } else if( typeof property !== 'undefined' ) {
            ccDataObject[ property ] = d
          }
        })
      }()

      var _callbackTokenizeCard = function(err, nonce, paymentObj) {
        if( err ){ throw new Error('Oh, no!') }
        paymentObj.nonce = nonce
        that.trigger('payment:verify', paymentObj)
      }
      this.client.tokenizeCard( ccDataObject, _callbackTokenizeCard );
    }
    this.enableForm(false)
  },

  validateField: function(evt){
    var $currentTarget = $(evt.currentTarget)
    var errors = gp.Helpers.v5.form.validate($currentTarget)

    this.handleFieldErrors( $currentTarget, errors )
  },

  handleFieldErrors: function($field, errors){
    var that = this
    var $wrapper = $field.parent('.input-wrapper')
    var $label = this.$el.find("label[for='"+$field.attr('id')+"']")

    var _addErrors = function(){
       var hasErrors = $wrapper.hasClass('with-errors')
       if( !hasErrors ){
        that.fieldsWithErrors.push($field.attr('id'))
        $wrapper.addClass('with-errors')
        $label.addClass('with-errors')
       }
    }
    var _removeErrors = function(){
       var hasErrors = $wrapper.hasClass('with-errors')
       if( hasErrors ){
        that.fieldsWithErrors = _.without(that.fieldsWithErrors, $field.attr('id'))
        $wrapper.removeClass('with-errors')
        $label.removeClass('with-errors')
       }
    }

    if( errors ){
      _addErrors()
    } else {
      _removeErrors()
    }
  },

  checkRequiredField: function(evt){
    this.readyToTokenize = _(this.$requiredFields).all(function(f){ return $(f).val().trim() !== '' })

    if( this.readyToTokenize && this.fieldsWithErrors.length === 0 ){
      gp.pubSub.trigger('checkout:payment:ready')
    }
  },

  checkAddressRequiredFields: function(){
    gp.pubSub.trigger('checkout:address:missing_data')
  },

  chooseCreditcards: function(){
    this.selectedOption = 'creditcards'
    this.checkSelectedOption()
    this.showCreditcardsForm(true)
  },

  clickPaypalButton: function(evt){
    this.selectedOption = 'paypal'
    this.checkSelectedOption()
  },

  choosePaypal: function(evt){
    this.selectedOption = 'paypal'
    this.checkSelectedOption()
    if (this.$el.find('.active-payments .paypal').hasClass('loggedout')) {
      this.$el.find('#braintree-paypal-loggedout').addClass('paypal-button-container')
      this.$el.find('#braintree-paypal-button img').click()
    }
    this.showCreditcardsForm(false)
    return false
  },

  cancelPaypalAccount: function(evt){
    this.$el.find('.active-payments .paypal').addClass('loggedout').removeClass('loggedin')
    this.chooseCreditcards()
  },

  showCreditcardsForm: function(displayIt){
    if( displayIt ){
      this.$creditcardForm.slideDown();
    } else {
      this.$creditcardForm.slideUp();
    }
  },

  changeCCNumberFieldType: function(evt){
    if( evt.type === 'focusin'){
      this.$ccNumberField.attr('type', 'text')
    } else {
      this.$ccNumberField.attr('type', 'password')
    }
  },

  verifyPaymentMethod: function(paymentMethodObj) {
    var that = this,
        url  = this.getUrl('/verify')
    this.payment_method = paymentMethodObj
    this.payment_method.verified = false
    this.nonce = this.payment_method.nonce
    this.enableForm(false)
    gp.pubSub.trigger('payment:verification:started')
    $.ajax({
        url:      url,
        data:     {
          nonce: that.nonce,
          gateway: that.gateway_name,
          payment_method_type: that.payment_method.type
        },
        dataType: 'json',
        type:     'post',
        cache:    false
    }).done(function(data){
      that.trigger('payment:verification:success', data)
    }).fail(function(xhr){
      var errors = xhr.status === 422 ? xhr.responseJSON.errors : {}
      gp.pubSub.trigger('checkout:payment:fail')
      that.trigger('payment:verification:fail', errors)
    }).always(function(){
      gp.pubSub.trigger('payment:verification:ready')
    })
  },

  getUrl: function(params){
    var url = this.endpoint
    if( _.isString(params) ){
      url += params
    }
    url += '?fid=' + gp.checkout.fid
    return url
  },

  enableForm: function(status){
    this.$ccFields.each(function(){ this.disabled = !status })
    if( !status ){
      this.$errors.hide().text('')
    }
  },

  verifiedPayment: function(data){
    var response = data.response
    if( response && response.verified ){
      gp.pubSub.trigger('checkout:pay')
    }
  },

  failPaymentVerification: function(errors){
    var that = this
    var validationErrors = errors.validation || false
    var verificationErrors = errors.verification || false
    if( validationErrors ) {
      _(validationErrors).each(function(e){
        that.$errors.append('<span class="error">'+e.message+'</span>')
      })
    }
    if( verificationErrors ) {
      if( verificationErrors.processor_response_code === '1000' ){
        that.$errors.append('<span class="error">'+I18n.t('js.v5.mkp.checkout.payment.braintree.errors.'+verificationErrors.gateway_rejection_reason)+'</span>')
      } else {
        that.$errors.append('<span class="error">'+verificationErrors.processor_response_code+': '+verificationErrors.processor_response_text+'</span>')
      }
    }
    this.$errors.show()
    this.enableForm(true)
  }

})

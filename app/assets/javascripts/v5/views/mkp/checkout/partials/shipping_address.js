gp.Views['checkout/shipping/address'] = gp.App.View.extend({
  events: {
    'click .actions .edit':   'editAddress',
    'click .actions .delete': 'deleteAddress',
    'click':                  'clickOption'
  },

  initialize: function(){
    _.bindAll(
      this,
      'changeAddress',
      'editAddressFinish',
      'cancelNewAddress'
    )

    this.choosenAddress = false
    if( this.$el.hasClass('new-address-option') ){
      this.newAddressOption = true
    }
    this.data = this.getData()
    this.$addresses = this.$el.parent('.addresses')
    this.$actions = this.$el.find('.actions')

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'checkout:address:change': this.changeAddress,
      'checkout:address:edit:finish': this.editAddressFinish,
      'checkout:address:new:cancel': this.cancelNewAddress
    })
  },

  getData: function(){
    var $jsdata = this.$el.find('.jsdata').remove()
    return $jsdata.data()
  },

  clickOption: function(evt){
    if( !!this.newAddressOption ){
      gp.pubSub.trigger('checkout:address:new')
      this.$addresses.hide()
    } else {
      this.updateCurrentAddress(this.addressId())
      gp.pubSub.trigger('checkout:address:change')
    }
  },

  changeAddress: function(){
    var addressId = gp.checkout.currentAddressId
    var radio = this.$el.find('i')
    if( addressId === this.addressId() ) {
      if ( !this.choosenAddress ) {
        this.choosenAddress = true
        radio.addClass('i-radio-on').removeClass('i-radio-off')
      }
    } else {
      if ( this.choosenAddress ) {
        this.choosenAddress = false
        radio.addClass('i-radio-off').removeClass('i-radio-on')
      }
    }
  },

  editAddress: function(evt){
    this.$addresses.hide()
    gp.pubSub.trigger('checkout:address:edit', this.data)
    evt.stopPropagation()
  },

  editAddressFinish: function(addressId, data){
    if( addressId === this.addressId() ) {
      this.data = _.extend(this.data, data)
      this.refreshAddress()
      this.$addresses.fadeIn()
      this.updateCurrentAddress(addressId)
      gp.pubSub.trigger('checkout:address:change')
    }
  },

  cancelNewAddress: function(){
    this.$addresses.fadeIn()
  },

  refreshAddress: function(){
    var $fullLocation = this.$el.find('.full_location')
    var $fullName = this.$el.find('.full_name')
    var $country = this.$el.find('.country')

    $fullName.text(this.data.full_name)
    $fullLocation.text(this.data.full_location)
    $country.text(this.data.full_country)
  },

  deleteAddress: function(evt){
    var confirmation_html = '<div class="confirmation" style="height:0px">' +
      '<p>Are you sure?</p>' +
      '<span class="yes">' + I18n.t('js.v5.mkp.checkout.shipping_address.delete.yes') +'</span>' +
      '<span class="no">' + I18n.t('js.v5.mkp.checkout.shipping_address.delete.no') + '</span>' +
      '</div>'
    this.$el.append(confirmation_html)
    this.$el.find('.confirmation').show(180);
    this.$actions.slideUp(100)
    return false
  },

  addressId: function(){
    var id = this.$el.data('id') || (this.$el.attr('id') && this.$el.attr('id').split('_')[1])
    return id
  },

  updateCurrentAddress: function(addressId){
    gp.checkout.currentAddressId = addressId
  }
})

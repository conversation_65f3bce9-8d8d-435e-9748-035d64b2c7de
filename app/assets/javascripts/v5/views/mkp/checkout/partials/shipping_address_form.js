gp.Views['checkout/shipping/address_form'] = gp.App.View.extend({
  events: function(){
    if( this.$el.hasClass('hidden') ) return {}
    return this.newFormEvents()
  },

  newFormEvents: function(){
    return {
      'focusout input.validate':      'validateFieldEvt',
      'click .edit-controls .cancel': 'closeEdit'
    }
  },

  editFormEvents: function(){
    return {
      'focusout input.validate':      'validateFieldEvt',
      'click .edit-controls .cancel': 'closeEdit',
    }
  },

  initialize: function(){
    _.bindAll(
      this,
      'validateFieldEvt',
      'newAddress',
      'applyAddress',
      'validateAllFields',
      'paying',
      'closeEdit'
    )
    // Vars
    var self = this
    this.saveDebounced = _.debounce(this.save, 500)

    // jQuery vars
    this.$form = this.$el.find('form')
    this.$addressId = this.$form.find('#address_id')

    gp.pubSub.on({
      'checkout:address:new':          this.newAddress,
      'checkout:address:edit':         this.applyAddress,
      'checkout:missing_data':         this.validateAllFields,
      'checkout:address:missing_data': this.validateAllFields,
      'checkout:pay':                  this.paying
    })

    this.setupSelects()

    // hide address form if there is an user an adresses
    if( gp.user && gp.checkout.addressOptionsCount > 0 ){
      this.$el.hide()
      this.$el.removeClass('hidden')
    }

    this.enableForm(true)
  },

  setupSelects: function(){
    var that = this
    this.$form.find('select').prop('autocomplete','false')
    var $countrySelect = this.$addressFields().filter('select#address_country')
    var $stateSelect = this.$addressFields().filter('select#address_state')
    var createSetting = $stateSelect.children('option').length > 1 ? false : true

    if( $countrySelect.size() > 0 ){
      this.countrySelect = $countrySelect.selectize({
        selectOnTab: true,
        onChange: function(value) {
          if (!value.length) return
          var results = _(GEO_CONSTANTS.states[value]).map(function(v, k) { return { value: k, text: v } })
          that.stateSelect.clearOptions()
          that.stateSelect.disable()
          if( results.length > 0 ){
            that.stateSelect.settings.create = false
            that.stateSelect.settings.placeholder = 'Select State'
            that.stateSelect.addOption(results)
            that.stateSelect.refreshOptions()
          } else {
            that.stateSelect.settings.create = true
            that.stateSelect.settings.placeholder = 'State Name'
          }
          that.stateSelect.updatePlaceholder()
          that.stateSelect.enable()
        }
      })[0].selectize
    }
    this.stateSelect = $stateSelect.selectize({
      create: createSetting,
      persist: false,
      selectOnTab: true,
      onChange: function(value){
        if (value == 'Capital Federal'){
          $('#address_city').val('CABA')
        }else{
          $('#address_city').val('')
        }
        that.validateField($('#address_city')[0])
      }
    })[0].selectize

    this.$form.find('select.no-options').each(function(){
      $(this)[0].selectize.disable()
    })
  },

  // MODEL

  updateCurrentAddressId: function(addressId){
    gp.checkout.currentAddressId = addressId
    this.$addressId.val(addressId)
  },

  setAddress: function(data, id){
    this.setData(data);
    this.updateCurrentAddressId(id)
  },

  addressId: function(){
    return this.$addressId.val() != '' ? this.$addressId.val() : undefined
  },

  toUpdate: function(){
    return !!this.addressId()
  },

  getData: function(){
    var data = this.$form.formParams()
    if(data.address['street_number']){
      data.address['address'] = data.address['address'] + " " + data.address['street_number']
    }
    delete data['payment_method']
    delete data['payment_total_amount']
    data.address.id = this.addressId()
    return data;
  },

  setData:  function(data){
    this.savedDataObj = data;
  },

  savedData:  function(){
    return this.savedDataObj
  },

  // INTERACTION

  applyAddress: function(data){
    var that = this;
    this.setAddress(data)
    this.countrySelect.trigger('change', data.country)
    _(data).each(function(value, field){
      var $field = that.$addressFields().filter(':input[name$="['+field+']"]')
      if( $field.hasClass('selectized') && !$field.is("#address_country")){
        $field[0].selectize.setValue(value)
      } else {
        $field.val(value)
      }
    })
    this.displayIt()
    this.addEditControls()
  },

  newAddress: function(){
    this.delegateEvents(this.newFormEvents())
    this.displayIt()
    this.addNewControls()
  },

  save: function(){
    this.savePost(this.getData())
  },

  savePost: function(data){
    var that = this
    gp.pubSub.trigger('shipping:address:updating')
    this.enableForm(false)
    $.ajax({
      url:      this.getUrl(),
      data:     data,
      dataType: 'json',
      type:     this.toUpdate() ? 'put': 'post',
      cache:    false
    }).done(function(response){
      that.setAddress(data, response.response.id);
      gp.pubSub.trigger('checkout:address:change')
    }).fail(function(response){
      that.$form.trigger('form:fail', response)
    }).always(function(){
      that.enableForm(true)
      gp.pubSub.trigger('shipping:address:saved')
    })
  },

  getUrl: function(){
    var url = this.$form.attr('action')
    if( this.toUpdate()){
      url += '/' + this.addressId()
    }
    url += '?fid=' + gp.checkout.fid
    return url
  },

  // QUESTIONS

  dataChanged: function(){
    return !this.toUpdate() || !_.isEqual(this.getData(), this.savedData())
  },

  allRequieredFieldsValid: function(){
    return _(this.$requiredFields()).all(function(field){
      var $field = $(field)
      if ($field.parents(".select-wrapper").length > 0) {
        $field = $field.parents(".select-wrapper").find("select")
      }
      return gp.Helpers.v5.form.validate($field) === false
    })
  },

  // VALIDATIONS

  validateFieldEvt: function(evt){
    this.validateField(evt.currentTarget);
    if(this.allRequieredFieldsValid()) this.saveDebounced()
  },

  validateField: function(el){
    var $currentTarget = $(el)
    $currentTarget.val($currentTarget.val().trim())
    var errors = gp.Helpers.v5.form.validate($currentTarget)
    this.handleFieldErrors( $currentTarget, errors )
    return errors; //false if no error
  },

  validateAllFields: function(el){
    var that = this;
    _(this.$requiredFields()).each(function(el){
      that.validateField(el)
    })
  },

  // PICKIT

  updatePickitData: function(done){
    var data = this.getData();
    _.extend(data.address, {
      address:gp.checkout.pickit_quotation.pickup_point_address.split(',')[0],
      city:gp.checkout.pickit_quotation.pickup_point_address.split(',')[1].trim(),
      zip:gp.checkout.pickit_quotation.pickup_point_zip,
      state:gp.checkout.pickit_quotation.pickup_point_state,
      country: "AR"
    })
    this.save(data)
    gp.pubSub.on('shipping:address:saved', done);
  },

  // UI

  closeEdit: function(){
    this.$el.find('.edit-controls').remove()
    this.hideIt()
  },

  addEditControls: function(){
    var controls = '<div class="edit-controls">' +
      '<span class="cancel">' + I18n.t('js.v5.mkp.checkout.shipping_address_form.cancel') + '</span>' +
      '<span class="save">' + I18n.t('js.v5.mkp.checkout.shipping_address_form.save') + '</span>' +
      '</div>'
    this.$el.append(controls)
    this.delegateEvents(this.editFormEvents())
  },

  addNewControls: function(){
    var controls = '<div class="edit-controls">' +
      '<span class="cancel">' + I18n.t('js.v5.mkp.checkout.shipping_address_form.cancel') + '</span>' +
      '</div>'
    this.$el.append(controls)
    this.delegateEvents(this.newFormEvents())
  },

  handleFieldErrors: function($field, errors){
    var that = this
    var $wrapper = $field.parent('.input-wrapper, .select-wrapper')
    var $label = this.$el.find("label[for='"+$field.attr('id')+"']")

    var _addErrors = function(){
      var hasErrors = $wrapper.hasClass('with-errors')
      if( !hasErrors ){
        $wrapper.addClass('with-errors')
        $label.addClass('with-errors')
      }
    }
    var _removeErrors = function(){
      var hasErrors = $wrapper.hasClass('with-errors')
      if( hasErrors ){
        $wrapper.removeClass('with-errors')
        $label.removeClass('with-errors')
      }
    }

    if( errors ){
      _addErrors()
    } else {
      _removeErrors()
    }
  },

  showAddressFields: function(show){
    this.addressFieldsShown = show
    this.$form.find('.address-field').toggle(show)
  },

  displayIt: function(){
    this.$el.removeClass('hidden').fadeIn()
  },

  hideIt: function(){
    this.undelegateEvents()
    gp.pubSub.trigger('checkout:address:edit:finish', this.addressId, this.addressData)
    this.$el.hide()
    this.resetForm()
  },

  paying: function(){
    this.enableForm(false)
  },

  enableForm: function(status){
    this.$form.find('input').each(function(){ this.disabled = !status })
    this.$form.find('select:not(.no-options)').each(function(){
      status ? $(this)[0].selectize.enable() : $(this)[0].selectize.disable()
    })
  },

  resetForm: function(){
    this.$form[0].reset()
    this.$form.find('.with-errors').removeClass('with-errors')
  },

  $requiredFields: function(){
      return this.$form.find('>div').not(':hidden').find(':input.required')
  },

  $addressFields: function(){
    return this.$form.find(':input')
  }

})

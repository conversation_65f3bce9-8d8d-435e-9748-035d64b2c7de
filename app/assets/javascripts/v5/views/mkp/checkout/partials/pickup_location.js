gp.Views['checkout/pickup/location'] = gp.App.View.extend({
  state: {
    googleMapsLoaded: false,
    searchBox: null,
    pickupUserAddress: null,
    pickupLocations: null
  },

  newFormEvents: function(){
    return {
      'focusout input.validate':      'validateField'
    }
  },

  initialize: function () {
    _.bindAll(
      this,
      'onPickupLocationChange')

    this.$pickupSelectorWrapper = this.$el.find('.pickup-selector-wrapper')
    var $pickupSelect = this.$el.find('[name="cc[pickup_location]"]')

    this.pickupSelector = $pickupSelect.selectize({
      closeAfterSelect: true,
      onChange: this.onPickupLocationChange
    })[0].selectize

    this.render()
  },

  render: function () {
    if (!this.state.googleMapsLoaded) return this.initGoogleMaps()
    if (!this.state.searchBox) return this.initSearchBox()
    return this
  },

  initGoogleMaps: function () {
    var self = this

    var apiKey = document.querySelector('[name="google:maps"]')
      .getAttribute('content')

    $.ajax({
      url: 'https://maps.googleapis.com/maps/api/js' + gp.Helpers.url.toParams({
        libraries: 'places',
        key: apiKey
      }),
      dataType: 'script',
      cache: true,
      success: checkGoogleMapsLoaded
    })

    // Sometimes, script is loaded, but not the library.
    // So we have to give it some time.
    function checkGoogleMapsLoaded () {
      if (
        window.google &&
        google.maps &&
        google.maps.Map &&
        google.maps.places &&
        google.maps.places.SearchBox
      ) {
        self.state.googleMapsLoaded = true
        self.render()
      } else {
        window.setTimeout(checkGoogleMapsLoaded, 300)
      }
    }

    return this
  },

  initSearchBox: function () {
    var self = this

    var searchInput = this.$el
      .find('#cc_pickup_location_search')[0]

    var searchBox = new google.maps.places.SearchBox(searchInput)

    this.state.searchBox = searchBox

    searchBox.addListener('places_changed', function() {
      var place = searchBox.getPlaces()

      if (!place || place.length == 0) return

      place = place[0]

      self.state.pickupUserAddress = {
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng()
      }

      self.fetchPickupLocations()
    })

    return this
  },

  fetchPickupPoint: function(data){
    this.onPickupLocationChange(data)
  },

  fetchPickupLocations: function () {
    var self = this

    this.doFetchPickupLocations().done(function (response) {
      // self.state.pickupLocations = locations
      gp.checkout.pickitCotizationId = response.cotizacionId
        var src =
          '<div style="padding:10px;width:100%;max-width:830px;height:600px;background-color:white;margin:0 auto;">' +
          '<iframe class="mfp-iframe" ' +
          'style="display:block;width:100%;height:100%;" ' +
          'src='+response.urlLightBox+' ' +
          'frameborder="0" allowfullscreen>' +
          '</iframe>' +
          '</div>';

      var pickitIframe = $.magnificPopup.open({
        items: {
          src: src,
          type: 'inline'
        }
      });

      var closeFrame =  function(event){
        window.parent.$.magnificPopup.close()
        $.ajax({
          url: '/api/mkp/checkout/pickup/pickup_point',
          data: { quotationId: gp.checkout.pickitCotizationId, fid: gp.checkout.fid },
          type: 'post',
          success: function(data){self.fetchPickupPoint(data)},
          error: function(xhr, textStatus, error){
              alert('Error:' + xhr.responseJSON.data);
          }
        });
      }

      //window.addEventListener("message",closeFrame,false);

      $(window).on('message', null, pickitIframe, closeFrame)

      // self.pickupSelector.clearOptions()

      // if (!locations || locations.length === 0) {
      //   self.pickupSelector.disable()
      //   self.$pickupSelectorWrapper.addClass('hidden')
      //   return
      // }

      // self.pickupSelector.addOption(locations.map(function (location) {
      //   return {
      //     value: location.id,
      //     text: location.title
      //   }
      // }))

      // self.pickupSelector.enable()
      // self.$pickupSelectorWrapper.removeClass('hidden')
    }).fail(function (err) {
      alert('Lo sentimos! Se produjo un error, inténtalo mas tarde por favor.')
    })
  },

  /**
  * Mock Fetch of pickup locations
  * This function should be changed by an $.ajax call
  * that uses self.state.pickupUserAddress params
  */
  doFetchPickupLocations: function () {
    params = {
      items: gp.checkout_items,
      location: $('.pickup-location-search-wrapper #cc_pickup_location_search').val(),
      fid: gp.checkout.fid
    }
    return $.ajax({
      url: '/api/mkp/checkout/pickup/quotation',
      data: params,
      type: 'post',
      error: function(xhr, textStatus, error){
              alert('Text Error:' + xhr.responseJSON.data);
          }
    })



    var fetch = $.Deferred()

    // fetch.resolve([
    //   {
    //     id: '13m123m1k2l3',
    //     title: 'Un Local en Virrey del Pino y Cabildo',
    //     description: 'Av Cabildo, 2379 Belgrano, C.A. de Buenos Aires, 1426\n Lun a Vie 8:30 a 18 hs. | Sab 9 a 12:30 hs.',
    //     location: {
    //       lat: -34.564018,
    //       lng: -58.4480241
    //     }
    //   },
    //   {
    //     id: '1adsadaskldmkals',
    //     title: 'Un Local en Darwin 1154',
    //     description: 'Darwin, 1154 Palermo, C.A. de Buenos Aires, 1111\n Lun a Vie 8:30 a 18 hs. | Sab 9 a 12:30 hs.',
    //     location: {
    //       lat: -34.5900316,
    //       lng: -58.44014179999999
    //     }
    //   },
    //   {
    //     id: '12312312lkm31',
    //     title: 'Abasto Shopping',
    //     description: 'Corrientes 333, C.A. de Buenos Aires, 2222\n Lun a Vie 8:30 a 18 hs. | Sab 9 a 12:30 hs.',
    //     location: {
    //       lat: -34.6158238,
    //       lng: -58.4332985
    //     }
    //   }
    // ])

    return fetch.promise()
  },

  onPickupLocationChange: function (value) {

    gp.checkout.pickit_quotation = value

    this.$el.find('.pickup-location-info').html([
      '<div class="pickup-location-info-item">',
        '<h1><i class="i-radio-on"></i></h1>',
        '<h5>'+'$'+ value.amount +'<span>'+ ' - ' + value.pickup_point_name +'</span></h5>',
        '<p>' + value.pickup_point_address + '</p>',
        '<p>' + value.pickup_point_business + '</p>',
      '</div>'
    ].join(''))

    gp.pubSub.trigger('checkout:delivery:update')

    // gp.pubSub.trigger('checkout:delivery:change', null, value.amount, 0)
  }
})

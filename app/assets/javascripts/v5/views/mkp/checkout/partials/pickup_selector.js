gp.Views['checkout/pickup/selector'] = gp.App.View.extend({
  events: {
    'click [data-type]': 'handlePickupTypeClick'
  },

  selectedType: 'default',

  initialize: function () {
    _.bindAll(this, 'handlePickupTypeClick')

    this.$isPickup = this.$el.find('[name="cc[is_pickup]"]')
  },

  handlePickupTypeClick: function (evt) {
    var target = evt.currentTarget
    var type = target.getAttribute('data-type')

    if (type === this.selectedType) return

    this.selectedType = type

    gp.checkout.is_pickup = type === 'pickup'
    gp.pubSub.trigger('checkout:pickup:change')

    this.$isPickup.val(gp.checkout.is_pickup || '')
    this.$el.find('.active').removeClass('active')
    this.$el.find('[data-type=' + type + ']').addClass('active')
  }
})

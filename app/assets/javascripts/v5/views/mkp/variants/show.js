//= require_self
//= require v5/routers/variants_show

;if( gp.controller.variants && gp.action.show ) (function(){
  gp.Views['variants/show'] = gp.App.View.extend({
    events:{
      'click .product-rate .read-reviews': 'readReviews',
      // 'click .service.installments': 'installmentsModal'
    },

    initialize: function(){
      this.initProduct()
      this.initViewComponents()
    },

    initProduct: function(){
      gp.product.model = this.model
    },

    initViewComponents: function(){
      var $self = this.$el
      if( !gp.product.unavailable ){
        // Initialize the Actions
        new gp.Views['variants/partials/actions']({
          el: $self.find('.buybox .actions')
        })
        // Initialize the Properties
        new gp.Views['variants/partials/properties']({
          el: $self.find('.product-properties')
        })
      }
      // Initialize the Pictures
      new gp.Views['variants/partials/pictures']({
        el: $self.find('.product-main > .pictures')
      })
      // Initialize the Extras
      new gp.Views['variants/partials/extras']({
        el: $self.find('.product-extras')
      })
    },

    readReviews: function(){
      gp.pubSub.trigger('product:read:reviews')
    },

    installmentsModal: function(){
      $.magnificPopup.close()

      var src =
      '<div style="padding:10px;width:100%;max-width:830px;height:1451px;background-color:white;margin:0 auto;">'+
        '<iframe class="mfp-iframe" '+
          'style="display:block;width:100%;height:100%;" '+
          'src="https://www.mercadopago.com/mla/credit_card_promos.htm" '+
          'frameborder="0" allowfullscreen>'+
        '</iframe>'+
      '</div>'

      $.magnificPopup.open({
        items: {
          src: src,
          type: 'inline'
        }
      });

      return true
    }

  })

  // Instantiate the Product Show (of a Variant)
  gp.productView = new gp.Views['variants/show']({
    el: $('#variants-v5-show main'),
    model: new gp.Mkp.Product(gp.product)
  })
})();

// var toggleDescriptions = function(enabled, disabled) {
//   $('.' + enabled).addClass('active');
//   $('.' + disabled).removeClass('active');
//   $('#' + (enabled ==  'tab_title' ? 'product-description' : 'product-specs')).show();
//   $('#' + (enabled ==  'tab_title' ? 'product-specs' : 'product-description')).hide();
// };
//
// $('.tab_specs').click(function (){ toggleDescriptions('tab_specs', 'tab_title'); });
// $('.tab_title').click(function (){ toggleDescriptions('tab_title', 'tab_specs'); });

$('.toggle-info').click(function() {
  var thisToggle = this;

  $('.toggle-info').each(function() {
    $(this).removeClass('active');
    $('#' + $(this).data('target')).hide();
  });

  $('#' + $(thisToggle).data('target')).show();
  $(thisToggle).addClass('active');
});

gp.Views['variants/partials/quantity'] = gp.App.View.extend({
  events:{
    'change input': 'quantityChanged'
  },

  initialize: function(){
    _.bindAll(
      this,
      'variantChanged'
    );

    var $self = this.$el;
    var that = this;
    this.$quantityInput = $self.find('input');
    this.$stockMsg = $self.find('.stock .msg');
    this.$quantityInput.inputIncrementor();
    this.incrementor = this.$quantityInput.data('inputIncrementor');

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('product:variant:change', this.variantChanged);

    this.listenTo(gp.cart, 'cart:change', function() {
      if( typeof that.choosenVariant !== 'undefined' ){
        that.setQuantity(that.choosenVariant);
      }
    })
  },

  quantityChanged: function(){
    var quantity = this.$quantityInput.val();
    gp.pubSub.trigger('product:variant:quantity:change', quantity);
  },

  variantChanged: function(variant){
    this.choosenVariant = variant;
    if( variant ) {
      this.setQuantity(variant);
    }
  },

  setQuantity: function(variant) {
    message = '';

    if (!variant || !(+variant.quantity)) {
      this.incrementor.setMinMax(0, 0);
      message = this.t('out_of_stock')
    } else {
      var quantity = +variant.quantity;
      var quantityInCart = gp.cart.cartCountOf(variant);

      if (quantity <= 3) {
        message = this.t('only_left_msg', { quantity: quantity })
      }

      quantity -= quantityInCart;

      if( quantityInCart > 0 ){
        message += '<span class="comment">'+this.t('some_in_cart', { quantity: quantityInCart })+'</span>'
      }

      this.incrementor.setMinMax(!quantity ? 0 : 1, quantity)
    }

    this.quantityChanged();
    this.setMessage(message);
  },

  setMessage: function(text){
    if( typeof text === 'undefined' || text === '' ){
      this.$stockMsg.html('');
    } else {
      this.$stockMsg.html(text);
    }
  }
});

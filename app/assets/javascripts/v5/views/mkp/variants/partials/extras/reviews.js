gp.Views['variants/partials/extras/review_form'] = gp.App.View.extend({
  initialize: function(){
    _.bindAll(
      this,
      'validate',
      'submit',
      'done',
      'fail',
      'reset'
    )
    var $self = this.$el
    new gp.Views['partials/rate_stars']({
      el: $self.find('.rate-stars'),
      $input: $self.find('input[name*="[stars]"]')
    })

    this.$form = $self.find('form')
    if( this.$form.length > 0 ){
      this.$reviewInputs = this.$form.find('textarea, input.title')
      this.$submitBtn = this.$form.find('button')
      this.reset()
      this.$reviewInputs.val('')
      gp.Helpers.v5.form.ajaxify(this.$form, this.validate)

      this.$form.on({
        'form:submit': this.submit,
        'form:done': this.done,
        'form:fail': this.fail,
        'form:always': this.reset
      })
    }
  },
  validate: function(){
    var errors = false
    _.each(this.$reviewInputs, function(el){
      var $el = $(el)
      if( !$el.val() ){
        $el.addClass('with-error')
        errors = true
      } else {
        $el.removeClass('with-error')
      }
    })
    return !errors
  },
  submit: function(){
    this.$submitBtn.attr('disabled', 'disabled')
    this.$reviewInputs.attr('disabled', 'disabled')
  },
  done: function(_evt, data){
    this.showStatusMessage(data.message, 'done')
    this.$reviewInputs.val('')
    this.$form.off('form:submit form:done form:fail form:always')
    this.$form.remove()
    this.$el.find('.avatar-container').remove()
  },
  fail: function(_evt, xhr){
    this.showStatusMessage(data.message, 'fail')
  },
  reset: function(){
    this.$submitBtn.removeAttr('disabled')
    this.$reviewInputs.removeAttr('disabled')
  },

  showStatusMessage: function(message, className){
    if( this.$el.children('.status').length > 0 ){
      var $status = this.$el.children('.status')
      $status.attr('class', 'status');
      $status.html(message).addClass(className)
    } else {
      var status = "<div class='status "+className+"'>"+message+"</div>"
      this.$el.prepend(status)
    }
  }
})
gp.Views['variants/partials/extras/reviews_list'] = gp.App.View.extend({
  events: {
    'click .like-review': 'like',
    'click .report-review': 'report'
  },

  like: function(evt){
    var $like = $(evt.currentTarget)
    if( $like.data('liking') ) return
    $like.data('liking', true)

    var liked = $like.hasClass('active')
    $like.toggleClass('active')
    $.ajax({
      url: $like.data('url'),
      type: 'post',
      data: {
        _method: liked ? 'delete' : 'create'
      }
    }).fail(function(){
      $like.toggleClass('active')
    }).always(function(){
      $like.data('liking', false)
    })
  },

  report: function(evt){
    var $report = $(evt.currentTarget)
    if( $report.data('reporting') ) return
    $report.data('reporting', true)

    if(!$report.hasClass('active')){
      var confirm = window.confirm(I18n.t('js.v5.partials.review.report.confirm'))
      if( !confirm ) return
    }

    var reported = $report.hasClass('active')

    $report.toggleClass('active')
    $.ajax({
      url: $report.data('url'),
      type: 'post',
      data: {
        _method: reported ? 'delete' : 'create'
      }
    }).fail(function(){
      $report.toggleClass('active')
    }).always(function(){
      $report.data('reporting', false)
    })
  }
})

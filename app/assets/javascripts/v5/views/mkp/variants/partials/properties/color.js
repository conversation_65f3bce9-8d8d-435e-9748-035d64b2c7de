gp.Views['variants/partials/properties/color'] = gp.App.View.extend({
  listEvents: function(){
    return { 'click .color': 'clickColor' }
  },
  selectEvents: function(){
    return { 'change select': 'selectColor' }
  },

  initialize: function(options){
    _.bindAll(
      this,
      'chooseColor',
      'resetItems'
    )
    var $self = this.$el

    if( !!$self.find('select').length ){
      this.type = 'select'
      var selectOptions = {
        allowEmptyOption: false,
        selectOnTab: true
      }
      this.selectize = $self.find('select').selectize(selectOptions)[0].selectize
      this.delegateEvents(this.selectEvents())
    } else {
      this.type = 'list'
      this.$items = $self.find('.color')
      this.$selected = this.$items.filter('.selected')
      this.delegateEvents(this.listEvents())
    }
    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('product:picture:change', this.chooseColor)
    gp.pubSub.on('product:properties:color:reset', this.resetItems)
  },

  bootstrap: function(value){
    switch (this.type) {
      case 'select':
        this.selectize.clear(true)
        this.selectize.addItem(value, false)
        break
      case 'list':
        this.chooseColor(value)
        break
    }
  },

  resetItems: function(choosenProperties){
    var available = gp.product.model.availablePropertyValuesWhere('color', choosenProperties)
    switch (this.type) {
      case 'select':
        this.resetSelect(available, choosenProperties)
        break
      case 'list':
        this.resetList(available, choosenProperties)
        break
    }
  },
  resetSelect: function(available, choosenProperties){
    var availableOptions = _.map(available, function(property){
      var thisProperties = _.extend(_.clone(choosenProperties), { color: property.slug_name })
      var hasStock = gp.product.model.availableQuantityFor(thisProperties)
      if( hasStock ) {
        return { value: property.slug_name, text: property.name }
      } else {
        return ''
      }
    })
    availableOptions = _.compact(availableOptions)
    this.selectize.clear(true)
    this.selectize.clearOptions()
    this.selectize.addOption(availableOptions)
    this.selectize.refreshOptions(false)
    this.chooseColor(_.first(availableOptions).value)
  },
  resetList: function(available, choosenProperties){
    this.deselectOption()
    _.each(this.$items, function(color) {
      var $color = $(color)
      var value = $color.data('color')
      var thisProperties = _.extend(_.clone(choosenProperties), { color: value })
      var hasStock = gp.product.model.availableQuantityFor(thisProperties)

      if (_.contains(available, value.toString()) && hasStock) {
        $color.removeClass('disabled')
      } else {
        $color.addClass('disabled')
      }
    })
  },

  clickColor: function(evt){
    if (evt.altKey || evt.ctrlKey || evt.metaKey || evt.shiftKey) return
    evt.preventDefault()
    evt.stopPropagation()
    var $color = $(evt.currentTarget)
    this.chooseColor($color.data('color'))

    return false
  },

  selectColor: function(evt){
    var colorName = $(evt.currentTarget).val()
    this.chooseColor(colorName)
  },

  chooseColor: function(colorName) {
    switch (this.type) {
      case 'select':
        var selected = this.selectize.getItem(colorName)
        if( typeof selected !== 'undefined' ){
          this.selectize.addItem(colorName, false)
        }
        break
      case 'list':
        var selected = _.find(this.$items, function(item){
          return $(item).data('color') === colorName
        })
        if( typeof selected !== 'undefined' ){
          this.deselectOption()
          this.selectOption(selected)
        }
        break
    }
    if( typeof selected !== 'undefined' ){
      gp.pubSub.trigger('product:properties:change', colorName, 'color')
      gp.pubSub.trigger('product:properties:change:color', colorName)
    }
  },
  selectOption: function(selected){
    this.$selected = $(selected)
    this.$selected.addClass('selected')
  },
  deselectOption: function(){
    if( !!this.$selected.length ){
      this.$selected.removeClass('selected')
      this.$selected = []
    }
  }
})

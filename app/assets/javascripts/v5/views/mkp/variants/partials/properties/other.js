gp.Views['variants/partials/properties/other'] = gp.App.View.extend({
  events: {
    'change select': 'chooseOption'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'resetItems'
    )
    var $self = this.$el
    propertyName = options.propertyName
    this.propertyName = typeof propertyName === 'string' ? propertyName : propertyName.slug ;

    this.$select = $self.find('select')

    var selectOptions = {
      allowEmptyOption: false,
      selectOnTab: true
    }
    this.selectize = this.$select.selectize(selectOptions)[0].selectize
    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('product:properties:'+this.propertyName+':reset', this.resetItems)
  },

  bootstrap: function(value){
    this.selectize.clear(true)
    this.selectize.addItem(value, false)
  },

  resetItems: function(choosenProperties){
    var available = gp.product.model.availablePropertyValuesWhere(this.propertyName, choosenProperties)
    var availableOptions = _.map(available, function(propertyValue){
      return { value: propertyValue, text: propertyValue }
    })
    this.selectize.clear(true)
    this.selectize.clearOptions()
    this.selectize.addOption(availableOptions)
    this.selectize.refreshOptions(false)
    this.selectize.addItem(_.first(available), false)
  },

  chooseOption: function(evt){
    var value = $(evt.currentTarget).val()
    gp.pubSub.trigger('product:properties:change', value.toString(), this.propertyName)
  }
})

gp.Views['variants/partials/properties'] = gp.App.View.extend({
  t: I18n.withScope('js.v5.mkp.variants.partials.properties'),

  initialize: function(){
    _.bindAll(
        this,
        'propertyChanged'
    )
    var $self = this.$el
    var that = this

    if (!gp.product.unavailable && gp.product.total_stock) {
      this.availableProperties = gp.product.available_properties

      this.quantity = new gp.Views['variants/partials/quantity']({
        el: $self.find('.quantity-wrapper')
      })
      this.quantity.t = this.t

      if( !_.contains(this.availableProperties, 'noproperty') ){
        this.variantProperties = this.bootstrapVariantProperties()

        var properties = _.map(this.availableProperties, function(propertyName) {
          var componentName = _.contains(['color', 'size'], propertyName) ? propertyName : 'other'
          if (componentName == 'color' && typeof propertyName == 'string'){
            componentName == 'other'
          }
          var lookupElement = '.'+componentName+'-property'
          if( componentName === 'other' ) {
            finalPropertyName = typeof propertyName == 'string' ? propertyName : propertyName.slug
            lookupElement = '.'+finalPropertyName
          }
          return new gp.Views['variants/partials/properties/'+ componentName ]({
            el: $self.find(lookupElement),
            propertyName: propertyName
          })
        })

        // Pub/Sub Events (need to explicit bind the "this")
        gp.pubSub.on('product:properties:change', this.propertyChanged)
        properties[0].bootstrap(this.variantProperties[this.availableProperties[0]])
      } else {
        this.choosenVariant = _.first(gp.product.model.variantsWhere(this.variantProperties))
        gp.pubSub.trigger('product:variant:change', this.choosenVariant)
      }
    }
  },

  bootstrapVariantProperties: function(){
    var bootstrapVariant = gp.product.model.getVariant(gp.bootstrapVariant)
    var variantProperties = {}
    _.each(this.availableProperties, function(propertyName){
      if(propertyName === "color") {
        variantProperties[propertyName] = bootstrapVariant.properties.color.slug_name
      }
      else if (bootstrapVariant.properties[propertyName] !== undefined) {
        variantProperties[propertyName] = bootstrapVariant.properties[propertyName]
      }
    })

    return variantProperties
  },

  propertyChanged: function(value, property){

    var customProperty = false;
    _.each(this.availableProperties, function(value) {
      if (typeof value !== "string" && value.slug == property) {
        customProperty = true;
      }
    });

    if(customProperty)
    {
      this.variantProperties[property] = value;
      this.chooseVariant()
    }
    else
    {
      var indexOfNextProperty = _.indexOf(this.availableProperties, property) + 1
      var lengthOfProperties = this.availableProperties.length
      this.variantProperties[property] = value
      this.normalizeVariantProperties(indexOfNextProperty)

      if( indexOfNextProperty < lengthOfProperties ){
        this.unchooseVariant()
        var nextProperty = this.availableProperties[indexOfNextProperty]
        gp.pubSub.trigger('product:properties:'+nextProperty+':reset', this.variantProperties)
      } else {
        this.chooseVariant()
      }
    }
  },

  normalizeVariantProperties: function(index){
    var that = this
    _.each(this.availableProperties.slice(index), function(property){
      delete that.variantProperties[property]
    })
  },

  chooseVariant: function(){
    var x = [];
    _.each(this.availableProperties, function(property) {
      x.push(typeof property === "string" ? property : property.slug)
    });

    if( _.isEqual(_.keys(this.variantProperties), x) ) {
      this.choosenVariant = _.first(gp.product.model.variantsWhere(this.variantProperties))
      gp.pubSub.trigger('product:variant:change', this.choosenVariant)
    }
  },

  unchooseVariant: function(){
    this.choosenVariant = undefined
    gp.pubSub.trigger('product:variant:change', this.choosenVariant)
  }
})

gp.Views['variants/partials/extras'] = gp.App.View.extend({
  t: I18n.withScope('js.v5.mkp.variants.partials.extras'),

  events: {
    'click section > a': 'clickSection',
    'click .logged-in-required': 'openLogin'
  },

  initialize: function(){
    _.bindAll(
      this,
      'openReviews'
    )
    var $self = this.$el
    this.$sections = $self.find('.tabs > section')
    this.displaySection(this.$sections[0])

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('product:read:reviews', this.openReviews)
  },

  clickSection: function(evt){
    evt.preventDefault()
    var $sectionLink = $(evt.currentTarget)
    if( !$sectionLink.hasClass('active') ){
      this.displaySection($sectionLink.parent('section'))
    }
    return false
  },

  openLogin: function(){
    gp.pubSub.trigger('login:open')
  },

  openReviews: function(){
    this.displaySection(this.$sections.filter('.reviews')[0])
  },

  displaySection: function(section){
    var that = this
    if( typeof this.activeSection !== 'undefined' ){
      $(this.activeSection).children('a.tab_title').removeClass('active').next().toggleClass('open').toggle().hide()
    }
    $(section).children('a.tab_title').addClass('active').next().addClass('open').show()
    this.initializeComponents(section)
    this.activeSection = section
  },

  initializeComponents: function(section){
    _.each($(section).find('.jscomponent'), function(el){
      var $el = $(el)
      var name = $el.data('component')
      new gp.Views['variants/partials/extras/'+name]({
        el: el
      })
      $el.removeClass('jscomponent').removeAttr('data-component')
    })
  }
});

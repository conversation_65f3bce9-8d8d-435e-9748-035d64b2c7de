gp.Views['variants/partials/actions'] = gp.App.View.extend({
  t: I18n.withScope('js.v5.mkp.variants.partials.actions'),

  events: {
    'click .add-cart:not(.disabled :disabled)': 'addToCart'
  },

  initialize: function(){
    _.bindAll(
      this,
      'variantChanged',
      'quantityChanged'
    )
    var $self = this.$el
    $self.find('.add-cart').prop('disabled', false)

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'product:variant:change': this.variantChanged,
      'product:variant:quantity:change': this.quantityChanged
    })
  },

  variantChanged: function(variant){
    this.choosenVariant = variant
  },

  quantityChanged: function(quantity){
    this.choosenQuantity = quantity
  },

  addToCart: function(){
    var variant = this.choosenVariant
    var quantity = this.choosenQuantity
    if( !variant ) return alert(this.t('choose_product'))
    if( !quantity ) return alert(this.t('choose_quantity'))

    var cartItem = {
      variant_id: variant.id,
      quantity: quantity
    }
    variant.product = _.omit(gp.product.model.attributes, 'variants')
    variant = new gp.Mkp.Variant(variant)

    gp.cart.cartAdd(cartItem, variant)
    gp.pubSub.trigger('cart:add', cartItem, variant)

    gp.Views['partials/cart'].open()
  }
})

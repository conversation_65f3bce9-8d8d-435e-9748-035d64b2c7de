gp.Views['variants/partials/size_chart'] = gp.App.View.extend({
  initialize: function(){
    _.bindAll(
      this,
      'open',
      'close'
    )
    this.$el.removeClass('loading')
  },

  open: function(){
    var self = this
    $.magnificPopup.open({
      showCloseBtn: true,
      mainClass: 'size-chart-dropdown-popup',
      items: {
        src: self.$el,
        type: 'inline'
      },
      tLoading: I18n.t('js.v5.helpers.loadingModal.loading')
    })
  },

  close: function(){
    $.magnificPopup.close()
  }
})

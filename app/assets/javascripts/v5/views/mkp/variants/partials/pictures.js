gp.Views['variants/partials/pictures'] = gp.App.View.extend({
  initialize: function(){
    this.initSlider()
  },

  initSlider: function() {
    var that = this
    var $slider = this.$('.slider-wrapper')
    var picturesIndexes = {}
    var picturesColors = {}

    $slider.find('[data-picture-id]').each(function(i, el) {
      var $el = $(el)
      var id = $el.attr('data-picture-id')
      var pic = gp.product.model.getPicture(id)
      var color = pic && pic.color
      var index = $el.index()

      if (!color) return

      picturesIndexes[color] = index
      picturesColors[index] = color
    })

    var sliderOptions = {
      autoScaleSlider: true,
      autoScaleSliderWidth: 450,
      imgHeight: 500,
      loop: true,
      imageScaleMode: 'fit-if-smaller',
      navigateByClick: true,
      numImagesToPreload: 2,
      arrowsNav: true,
      arrowsNavAutoHide: true,
      arrowsNavHideOnTouch: true,
      keyboardNavEnabled: true,
      fadeinLoadedSlide: true,
      globalCaption: false,
      globalCaptionInside: false,
      controlNavigation: 'thumbnails'
    }

    if ($window.width() >= 768) {
      _.extend(sliderOptions, {
        autoScaleSliderHeight: 360,
        fullscreen: { enabled: true },
        thumbs: {
          autocenter: false,
          appendSpan: true,
          firstMargin: true,
          spacing: 10,
          orientation: 'vertical'
        }
      })
    } else {
      _.extend(sliderOptions, {
        autoScaleSliderHeight: 400,
        thumbs: {
          autocenter: false,
          appendSpan: true,
          firstMargin: true,
          spacing: 10,
          orientation: 'horizontal'
        }
      })
    }
    if (gp.selectedColor) {
      var selectedPic = gp.product.model.getPictureWhereColor(gp.selectedColor)
      if (selectedPic) {
        _.extend(sliderOptions, {
          startSlideId: picturesIndexes[selectedPic.color]
        })
      }
    }

    var slider = $slider.royalSlider(sliderOptions).data('royalSlider')
    this.slider = slider

    var _currentColor = function() {
      return picturesColors[slider.currSlideId]
    }

    slider.ev.on('rsAfterSlideChange', function() {
      if( that.changedViaColor ){
        that.changedViaColor = false
      } else {
        var color = _currentColor()
        if( typeof color !== 'undefined' ){
          gp.pubSub.trigger('product:picture:change', color)
        }
      }
    })

    gp.pubSub.on('product:properties:change:color', function(color) {
      var color_slug = ( typeof color === 'string' ) ? color : color.slug_name
      if (color_slug === _currentColor()) return

      that.changedViaColor = true
      var index = picturesIndexes[color_slug]
      if (!index && index !== 0 ) return
      slider.goTo(index)
    })

    return this
  }
})

gp.Views['variants/partials/properties/size'] = gp.App.View.extend({
  events: {
    'click .size-chart-button':              'openSizeChart',
    'click .size:not(.selected, .disabled)': 'clickSize'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'resetItems'
    )
    var $self = this.$el
    this.$items = $self.find('.size')
    this.$selected = this.$items.filter('.selected')

    this.$sizeChart = new gp.Views['variants/partials/size_chart']({
      el: $self.find('#size-chart .size-chart')
    })

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on('product:properties:size:reset', this.resetItems)
  },

  bootstrap: function(value){
    this.resetItems({})
    this.chooseSize(value)
  },

  resetItems: function(choosenProperties){
    var available = gp.product.model.availablePropertyValuesWhere('size', choosenProperties)
    this.deselectOption()

    _.each(this.$items, function(size) {
      var $size = $(size)
      var value = $size.data('size')
      var thisProperties = _.extend(_.clone(choosenProperties), { size: value })
      var hasStock = gp.product.model.availableQuantityFor(thisProperties)

      if (_.contains(available, value.toString()) && hasStock) {
        $size.removeClass('disabled')
      } else {
        $size.addClass('disabled')
      }
    })
  },

  openSizeChart: function(){
    this.$sizeChart.open()
  },

  clickSize: function(evt){
    var $size = $(evt.currentTarget)
    this.chooseSize($size.data('size'))
  },

  chooseSize: function(size) {
    var selected = _.find(this.$items, function(item){
      return $(item).data('size').toString() === size.toString()
    })
    this.deselectOption()
    this.selectOption(selected)
    gp.pubSub.trigger('product:properties:change', size.toString(), 'size')
  },
  selectOption: function(selected){
    this.$selected = $(selected)
    this.$selected.addClass('selected')
  },
  deselectOption: function(){
    if( !!this.$selected.length ){
      this.$selected.removeClass('selected')
      this.$selected = []
    }
  }
})

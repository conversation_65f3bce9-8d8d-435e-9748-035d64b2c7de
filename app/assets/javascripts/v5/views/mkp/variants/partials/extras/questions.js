gp.Views['variants/partials/extras/question_form'] = gp.App.View.extend({

  initialize: function(){
    _.bindAll(
      this,
      'validate',
      'submit',
      'done',
      'fail',
      'reset'
    )
    var $self = this.$el
    this.$form = $self.find('form')
    this.$questionInput = this.$form.find('textarea')
    this.$submitBtn = this.$form.find('button')
    this.reset()
    this.$questionInput.val('')
    gp.Helpers.v5.form.ajaxify(this.$form, this.validate)

    this.$form.on({
      'form:submit': this.submit,
      'form:done': this.done,
      'form:fail': this.fail,
      'form:always': this.reset
    })
  },
  validate: function(){
    if( !this.$questionInput.val() ){
      this.$questionInput.addClass('with-error')
      return false
    } else {
      this.$questionInput.removeClass('with-error')
    }
    return true
  },
  submit: function(){
    this.$submitBtn.attr('disabled', 'disabled')
    this.$questionInput.attr('disabled', 'disabled')
  },
  done: function(_evt, data){
    this.showStatusMessage(data.message, 'done')
    this.$questionInput.val('')
  },
  fail: function(_evt, xhr){
    this.showStatusMessage(data.message, 'fail')
  },
  reset: function(){
    this.$submitBtn.removeAttr('disabled')
    this.$questionInput.removeAttr('disabled')
  },

  showStatusMessage: function(message, className){
    if( this.$el.children('.status').length > 0 ){
      var $status = this.$el.children('.status')
      $status.attr('class', 'status');
      $status.html(message).addClass(className)
    } else {
      var status = "<div class='status "+className+"'>"+message+"</div>"
      this.$el.prepend(status)
    }
  }
})

gp.Views['catalog/partials/filter/option'] = gp.App.View.extend({
  showAndHideEvents: function(){
    return {
      'click .js-show-all': 'showAllItems',
      'click .js-hide-all': 'hideExtraItems'
    }
  },

  initialize: function(){
    var $self = this.$el
    var moreItems = typeof $self.data('show') !== 'undefined'
    if( moreItems ){
      var displayCount = $self.data('show')
      this.$list = $self.find('ul')
      this.$items = this.$list.find('> li')
      this.hideItems($self.find('.hide-for-seo'))
      this.sortItems(this.$items)
      this.$btns = $self.find('.js-show-all, .js-hide-all')
      this.delegateEvents(this.showAndHideEvents())
    }
  },

  hideItems: function(itemsToHide){
    this.$itemsToHide = itemsToHide.hide().removeClass('hide-for-seo')
  },

  sortItems: function(items){
    this.$sortedItems = $(_.clone(items))
    this.$sortedItems.sort(function(a, b) {
      var text = $(a).find('a').text().toLowerCase()
      var another_text = $(b).find('a').text().toLowerCase()

      return text.localeCompare(another_text)
    })
  },

  showAllItems: function(evt){
    this.$list.append(this.$sortedItems)
    this.$itemsToHide.add(this.$btns).toggle()
  },

  hideExtraItems: function(evt){
    this.$list.append(this.$items)
    this.$itemsToHide.add(this.$btns).toggle()
  }
})

gp.Views['catalog/partials/filter/price_option'] = gp.App.View.extend({
  events: {
    'submit form': 'submitForm'
  },

  initialize: function(){
    var $self = this.$el
    this.$minInput = $self.find('.price_wrapper input#pr-min')
    this.$maxInput = $self.find('.price_wrapper input#pr-max')
    this.$prInput = $self.find('form input#pr')
  },

  submitForm: function(evt){
    evt.preventDefault()
    evt.stopPropagation()
    var $form = $(evt.currentTarget)

    this.$prInput.val(this.price_range())
    var url = $form.attr('action').split('?').shift()
    var params = (location.search === '' ? '?' : location.search + '&')
    var price = "pr="+this.$prInput.val()
    window.location = (url + params + price)

    return false
  },

  price_range: function(){
    return this.min_price()+".."+this.max_price()
  },

  min_price: function(){
    var price = this.$minInput.val()
    return (price ? price : '0')
  },

  max_price: function(){
    return this.$maxInput.val()
  }
})

gp.Views['catalog/partials/filter'] = gp.App.View.extend({
  initialize: function(){
    var $self = this.$el
    var $filterOptions = $self.find('.filter-option')
    // Initialize the Filter Options
    _($filterOptions).each(function(option){
      var viewComponent = ''
      if( $(option).data('name') === 'price' ){
        viewComponent = 'catalog/partials/filter/price_option'
      } else {
        viewComponent = 'catalog/partials/filter/option'
      }
      new gp.Views[viewComponent]({
        el: option
      })
    })
  }
})

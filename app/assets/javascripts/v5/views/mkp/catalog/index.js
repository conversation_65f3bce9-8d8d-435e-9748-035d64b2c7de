;if( gp.controller.catalog && gp.action.index ) (function(){
  gp.Views['catalog/index'] = gp.App.View.extend({
    initialize: function(){
      var $self = this.$el
      // Initialize the Filter
      var filter = new gp.Views['catalog/partials/filter']({
        el: $self.find('.filter')
      })
      // Initialize the Sort
      var sort = new gp.Views['catalog/partials/sort']({
        el: $self.find('.sort')
      })

      $('.variants-list').find('a').each(function(e) {
          $(this).on('click', function(e) {
            if( e.which == 2 || e.shiftKey || e.ctrlKey || e.metaKey) {
              e.stopPropagation()
              e.preventDefault()
              window.open($(this).attr('href'));
           }
          })
      })

    }
  })

  // Instantiate the catalog
  var catalog = new gp.Views['catalog/index']({
    el: $('#catalog-v5-index main')
  })
})()

;if( gp.controller.landings ) (function(){
  gp.App.View.autoInstantiate(gp.Views['posts/list'])

  $('.hero-main-vnrs, .hero-component .main').each( function(x, slider){
    slider = $(slider)
    if( !slider.length ) return
    var $points = slider.find('.points')
    if( !$points.length ) return
    slider.find('.wrapper').cssSlider({
      timer: 6000,
      selectors: {
        item: 'article',
        paginatorItems: $points.find('> *'),
        prev: slider.find('.prev'),
        next: slider.find('.next')
      }
    })
  })
  function bannerSize(){
    $( ".hero-component .banner" ).height( $('.hero-component .main').height() );
  }
  $( window ).resize(bannerSize);
  bannerSize()
})()

;(function(){
  var self = gp.Views['posts/modal'] = {}

  self.getPost = function(postUrl){
    return $.ajax({
      url: postUrl,
      data: {
        xhr: true
      }
    }).pipe(function(response){
      return $(response)
    })
  }

  self.openPost = function($el, postUrl, picIndex){
    $.magnificPopup.close()
    $.magnificPopup.open({
      items: {
        src: $el,
        type:'inline'
      },
      closeBtnInside: true,
      tClose: I18n.t('javascripts.social.item_body.close'),
      closeMarkup: '<button title="%title%" class="mfp-close feed-item-modal-close"></button>',
      tLoading: I18n.t('javascripts.social.item_body.loading'),
      callbacks: {
        beforeOpen: function(){
          var popup = this
          history.pushState({route: postUrl}, undefined, postUrl)

          window.onpopstate = function() {
            popup.close()
          }
        },

        open: function(){
          window._gaq && _gaq.push(['_trackPageview', window.location.pathname])

          // Hide zoom cursor
          try {
            document.body.style.cursor = 'auto'
            window.scroll(window.scrollX, window.scrollY)
          } catch(e) {  }



          var $modal = this.content
          var type = $modal.data('type').toLowerCase()

          var options = {
            el: $modal[0]
          }

          if( typeof picIndex === 'number' ) options.itemActive = picIndex

          this.view = new gp.Views['posts/body_'+type](options)

          if( this.view.hasMedia ) $modal.addClass('has-media')
        },

        afterClose: function(){
          if( this.view ) this.view.close()
          window.onpopstate = null
          if( postUrl === location.href || postUrl === location.pathname ){
            history.back()
          }
        }
      }
    })
  }

  self.open = function(postUrl, picIndex){
    gp.Helpers.loadingModal()

    self.getPost(postUrl).done(function($el){
      self.openPost($el, postUrl, picIndex)
    })
  }
})()
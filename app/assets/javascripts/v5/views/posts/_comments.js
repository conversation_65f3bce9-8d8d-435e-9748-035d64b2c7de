gp.Views['posts/comments'] = gp.App.View.extend({
  className: 'comments',

  events: {
    'focusin input': 'onFocusin',
    'focusout input': 'onFocusout'
  },

  initialize: function(){
    _.bindAll(
      this,
      'render',
      'scrollListToBottom',
      'disableForm',
      'enableForm',
      'showLoginModal',
      'initForm',
      'onFocusin',
      'onFocusout'
    )
  },

  render: function(){
    var self = this
    self.$form = self.$('form')
    self.$body = self.$('input[name="social_comment[body]"]')
    self.$list = self.$('.comments-list')
    self.$inputs = self.$form.find('input[type="text"], button')

    if( gp.user ) gp.Helpers.mentions.listen(self.$body)

    self.initForm()
    self.scrollListToBottom()

    return this
  },

  scrollListToBottom: function(){
    this.$list.scrollTop(9999)
  },

  disableForm: function(){
    this.$inputs.attr('disabled', 'disabled')
  },

  enableForm: function(){
    this.$inputs.removeAttr('disabled')
  },

  showLoginModal: function(){
    var msg = I18n.t('javascripts.partials.comments.need_login')
    return gp.Helpers.loginModal(msg, { v5: true })
  },

  initForm: function(){
    var self = this

    if( !gp.user ){
      self.$form.on('submit', function(e){
        this.showLoginModal()
        return e.stop()
      })

      return
    }

    gp.Helpers.form.ajaxify(self.$form)
      .on('form:submit', function(){
        self.disableForm()
      })
      .on('form:done', function(e, html){
        var $html = $(html).hide()
        self.$list.append( $html.fadeIn() )
        self.scrollListToBottom()
        self.$form.find('[name="social_comment[body]"]').val('')
        self.$el.removeClass('empty')
        self.trigger('comment:add')
      })
      .on('form:fail', function(){
        alert('There was an error commenting the item.')
      })
      .on('form:always', function(){
        self.enableForm()
      })

    return false
  },

  _loginMsgShowed: false,
  onFocusin: function(){
    this.$el.addClass('focus')
    if( !this._loginMsgShowed ) {
      if( this.showLoginModal() ) {
        this._loginMsgShowed = true
      }
    }
  },

  onFocusout: function(){
    this.$el.removeClass('focus')
  }
})

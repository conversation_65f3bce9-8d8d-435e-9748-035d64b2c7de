gp.Views['posts/list'] = gp.App.View.extend({
  className: 'posts-list',

  events: {
    'click .share .i-social-facebook': 'shareOnFacebook',
    'click .share .i-social-twitter': 'shareOnTwitter',
    'click .share .i-social-pinterest': 'shareOnPinterest',
    'click .load-more-wide:not(.disabled) a.in-place': 'paginationNext',
    'click .show': 'openPost'
  },

  initialize: function(){
    _.bindAll(
      this,
      'listSort',
      'listItemShowQueueInit',
      'listItemShowQueueAdd',
      'listItemShowQueue',
      'listAdd',
      'paginationInit',
      'paginationShow',
      'paginationNext',
      'paginationTrack',
      'openPost',
      'shareOnFacebook',
      'shareOnTwitter',
      'shareOnPinterest'
    )

    this.parseTimes()
    this.listInit()
    this.paginationInit()
  },

  parseTimes: function($el){
    var $times = ($el || this.$el).find('[data-created-at]')
    rAF(function(){
      $times.each(function(i, el){
        var $el = $(el)
        var time = moment($el.data('created-at')).fromNow()
        $el.html(time)
      })
    })
  },

  listInit: function(){
    var self = this

    self.listItemShowQueueInit()

    self.$list = self.$('.posts-list-wrapper')

    self.list = new Masonry(self.$list[0], {
      itemSelector: 'article',
      containerStyle: null,
      visibleStyle: {},
      transitionDuration: 0,
      gutter: 20,
      isInitLayout: false,
      isResizeBound: false
    })

    self.listSortDebounced = _.debounce(function(){
      rAF(self.listSort)
    }, 50)

    var $items = $(this.list.getItemElements())
    self.listInitItems($items)

    rAF(function(){
      self.$list.addClass('inited')
    })

    $window.on('resize', self.listSortDebounced)
  },

  listInitItems: function($items){
    var self = this
    $items.css({ opacity: 0 })
    $items.each(function(){
      self.listInitItem($(this))
    })
  },

  listInitItem: function($item){
    var self = this

    var showed = false
    function show(){
      if( showed ) return
      showed = true
      self.listItemShowQueueAdd($item)
    }

    var timeout = setTimeout(show, 2500)

    $item.imagesLoaded()
      .always(function(){
        clearTimeout(timeout)
        show()
        self.listSortDebounced()
      })
  },

  listSort: function(){
    return this.list.layout()
  },

  $listItemShowQueue: null,

  listItemShowQueueInit: function(){
    this.$listItemShowQueue = $()
    this.listItemShowQueueDebounced = _.debounce(this.listItemShowQueue, 5)
  },

  listItemShowQueueAdd: function($item){
    this.$listItemShowQueue.push($item[0])
    this.listItemShowQueueDebounced()
  },

  listItemShowQueue: function(){
    var self = this
    rAF(function(){
      self.$listItemShowQueue.css({ opacity: 1 })
      self.$listItemShowQueue = $()
      self.listSortDebounced()
    })
  },

  listAdd: function(html){
    this.showLoader()
    var $items = $(html)
    this.$list.append($items)
    this.list.appended($items)
    this.listInitItems($items)
    this.parseTimes($items)
  },

  paginationInit: function(){
    this.$load = this.$('.load-more-wide')
    this.$loadBtn = this.$load.find('a')
    this.currentPage = +this.$loadBtn.attr('data-current-page')
    this.totalPages = +this.$loadBtn.attr('data-total-pages')
  },

  paginationShow: function(){
    if( this.currentPage < this.totalPages ){
      this.$load.show()
    } else {
      this.$load.hide()
    }
  },

  paginationNext: function(e){
    var self = this
    var path = self.$loadBtn.attr('data-path')
    e.preventDefault()

    var page = self.currentPage + 1
    self.$loadBtn.addClass('disabled')
    self.$loadBtn.html(
      I18n.t('javascripts.social.partials.feed_items_list.loading_more')
    )
    $.ajax({
      url: path,
      data: {
        page: page,
        v5: true
      },
      dataType: 'html',
      success: function(html) {
        self.listAdd(html)
        self.$loadBtn.attr('data-current-page', page)
        self.currentPage = page
        self.paginationShow()
      },
      complete: function(){
        self.$loadBtn.removeClass('disabled')
        self.$loadBtn.html(
          I18n.t('javascripts.social.partials.feed_items_list.load_more')
        )
      }
    })

    self.paginationTrack()
  },

  paginationTrack: function(){
    // Track pushState view on google analytics.
    if( window._gaq ) {
      var u = gp.Helpers.url
      var analyticsUrl = u.getParamsObject()
      analyticsUrl.page = page
      for(var action in gp.action);
      for(var controller in gp.controller);
      // Specify controller and action when the user is on root
      if( window.location.pathname === '/' ) {
        _.extend(analyticsUrl, {
          controller: controller,
          action: action
        })
      }
      analyticsUrl = window.location.pathname + u.toParams(analyticsUrl)
      _gaq.push(['_trackPageview', analyticsUrl])
    }
  },

  openPost: function(e){
    e.stop()

    var self = this
    var $link = $(e.currentTarget)
    var postUrl = $link.attr('href')
    var picIndex = +$link.data('pic-index') || null

    gp.Views['posts/modal'].open(postUrl, picIndex)

    return false
  },

  shareOnFacebook: function(e){
    var link = window.location.origin + $(e.currentTarget).data('link')

    var options = {
      link: link,
      caption: link
    }

    gp.Helpers.share.onFacebook(options)
  },

  shareOnTwitter: function(e){
    var $el = $(e.currentTarget)
    var options = {
      url: window.location.origin + $el.data('link'),
      text: $el.data('title')
    }

    gp.Helpers.share.onTwitter(options)
  },

  shareOnPinterest: function(e){
    var $el = $(e.currentTarget)

    var o = {
      media: $el.data('pic'),
      url: window.location.origin + $el.data('link')
    }

    gp.Helpers.share.onPinterest(o)
  }
})

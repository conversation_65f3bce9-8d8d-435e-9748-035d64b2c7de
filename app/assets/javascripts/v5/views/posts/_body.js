// TODO refactorizame el arrange.
gp.Views['posts/body'] = gp.App.View.extend({
  className: null,
  commentsView: null,
  hasMedia: false,

  events: {
    'click .facebook-share': 'shareOnFacebook',
    'click .twitter-share': 'shareOnTwitter',
    'submit .delete-post form': 'disableDeleteButton',
    'click .item-body-user-menu-button': 'userMenuBtnClick'
  },

  initialize: function(){
    _.bindAll(
      this,
      'close',
      'arrangeContent',
      'arrangeContentRAF',
      'unarrangeContent',
      'showSharesCount',
      'showFavoriteButton',
      'disableDeleteButton',
      'userMenuBtnClick',
      'shareOnFacebook',
      'shareOnTwitter',
      'initRecommendedProducts'
    )

    this.arrangeContentDebounced = _.debounce(this.arrangeContentRAF, 10)

    this.commentsView = this.autoInstantiate(gp.Views['posts/comments'])[0]
    this.followAuthorBtnView = this.autoInstantiate(gp.Views['partials/social/follow_btn'])[0]

    if( this.$el.hasClass('has-media') ) this.hasMedia = true

    if( this.hasMedia ) {
      this.$content = this.$('.item-body-content')
      this.$media = this.$('.item-body-media')
      if( this.$mediaImg() ) {
        this.$mediaImg().one('load', this.arrangeContent)
      }
      this.$comments = this.commentsView.$el

      this.$contents = this.$media.add(this.$content)
      this.arrangeContent()
      $window.on('resize', this.arrangeContentDebounced)
      this.listenTo(this.commentsView, 'comment:add', this.arrangeContentDebounced)

      _.delay(this.arrangeContentDebounced, 2500)
    }

    this.initDate()

    this.$userMenu = this.$('.item-body-user-menu')

    this.showSharesCount()

    this.$el.removeClass('loading')

    rAF(this.initRecommendedProducts)

    return this
  },

  close: function(){
    if( this.commentsView ) this.commentsView.close()
    if( this.favoriteButtonView ) this.favoriteButtonView.close()

    $window.off('resize', this.arrangeContentDebounced)
    this.unbind()
    return this
  },

  initRecommendedProducts: function(){
    var self = this
    var $el = self.$recommendedProducts = self.$('.recommended-products')
    if( !$el.length ) return
    var url = $el.attr('data-url')

    $.ajax({ url: url }).done(function(data){
      data = $.trim(data)
      if( !data ) return
      $el.append(data).show()
      self.arrangeContentDebounced()
    })
  },

  initDate: function(){
    var $date = this.$('[data-created-at]')
    var fromNow = moment($date.data('created-at')).fromNow()
    rAF(function(){
      $date.html(fromNow)
    })
  },

  _$mediaImg: null,
  $mediaImg: function(){
    if( !this._$mediaImg ){
      var $mediaImg = this.$media.find('>img')
      if( $mediaImg.length ) {
        this._$mediaImg = $mediaImg
        this.$mediaImgCopy = $('<img/>', {
          src: $mediaImg.attr('src')
        })
      }
    }
    return this._$mediaImg
  },

  $mediaItem: function(){
    return this.$mediaImg()
  },

  _arranged: false,
  arrangeContent: function(){
    if( !this.hasMedia ) return
    this.unarrangeContent()
    if( $window.width() < gp.Helpers.media.screens.medium ) return

    var topOffset = this.$el.offset().top - $window.scrollTop()
    var maxH = $window.height() - topOffset - 15
    if( this.$mediaImg() ) {
      var imgH = this.$mediaImgCopy[0].height
      if( imgH > maxH ) {
        this.$mediaImg().css({ maxHeight: maxH })
      }
    }

    var contentH = this.$content.outerHeight()
    var mediaH = this.$media.outerHeight()

    if( mediaH > contentH ) {
      this.$comments.css({
        marginBottom: mediaH - contentH
      })
    } else if( contentH > mediaH ) {
      this.$media.css({
        height: contentH
      })
      if( this.$mediaItem() ) {
        var mediaImgH = this.$mediaItem().outerHeight()
        if( mediaImgH && mediaImgH < contentH ) {
          this.$mediaItem().css({
           marginTop: ((contentH - mediaImgH) / 2) + 'px'
          })
        }
      }
    }

    this._arranged = true
  },

  arrangeContentRAF: function(){
    rAF(this.arrangeContent)
  },

  unarrangeContent: function(){
    if( !this._arranged ) return

    if( this.$mediaImg() ) {
      this.$mediaImg().css({
        maxHeight: ''
      })
    }

    if( this.$mediaItem() ) {
      this.$mediaItem().css({
        marginTop: ''
      })
    }
    this.$comments.css({
      marginBottom: ''
    })
    this.$contents.css({
      height: ''
    })

    this._arranged = false
  },

  showSharesCount: function(){
    var that = this
    var $actions = this.$('.social-actions')
    var $shares = $actions.find('.social-actions-shares')

    gp.Helpers.share.getCount()
      .done(function(count){
        if( !count ) {
          return $actions.fadeOut(that.arrangeContentDebounced)
        }
        var countText = 'javascripts.social.item_body.social_actions.shares'
        countText = I18n.t(countText, { count: count })
        $shares.find('.text').html(countText)
        $actions.fadeIn(that.arrangeContentDebounced)
      })
  },

  showFavoriteButton: function(){
    this.favoriteButtonView = this.autoInstantiate(gp.Social.FavoriteButtonView)[0]
  },

  disableDeleteButton: function(){
    this.$('.delete-post [type="submit"]').prop('disabled', true)
  },

  userMenuBtnClick: function(){
    this.$userMenu.toggleClass('active')
  },

  shareOnFacebook: function(e){
    var that = this
    gp.Helpers.share.onFacebook(null, function(){
      that.showSharesCount()
    })
    this.userMenuBtnClick()
    if( e ){
      e.preventDefault()
      e.stopPropagation()
    }
  },

  shareOnTwitter: function(e){
    var options = {
      url: window.location.origin + $(e.currentTarget).data('link'),
      text: $(e.currentTarget).data('title')
    }

    gp.Helpers.share.onTwitter(options)
    this.userMenuBtnClick()
    if( e ){
      e.preventDefault()
      e.stopPropagation()
    }
  }
})

;(function(){
  var Parent = gp.Views['posts/body'], parent = Parent.prototype

  gp.Views['posts/body_albums'] = Parent.extend({
    className: 'albums-body',

    initialize: function(){
      this.$slider = this.$('.item-body-media').cssSlider({
        selectors: {
          item: '.item',
          prev: '.navigation-left',
          next: '.navigation-right'
        }
      })
      this.slider = this.$slider.data('cssSlider')

      parent.initialize.apply(this, arguments)

      this.$slider.on('cssSlider:shown', this.onPictureShown)

      this.showPic(this.options.itemActive || 0)

      return this
    },

    $picturesDescriptions: null,
    onPictureShown: function(){
      if( !this.$picturesDescriptions ) {
        this.$picturesDescriptions = this.$('.album-picture-description')
      }

      this.$picturesDescriptions
        .removeClass('active')
        .filter('[data-pic-index="'+this.slider.showing()+'"]')
          .addClass('active')

      this.arrangeContentDebounced()
    },

    $mediaImg: function(){
      if( !this._$mediaImg || this._$mediaImg !== this.slider.$showing ){
        this._$mediaImg = this.slider.$showing()
        this.$mediaImgCopy = $('<img/>', {
          src: this._$mediaImg.attr('src')
        })
      }
      return this._$mediaImg
    },

    showPic: function(i){
      return this.slider.show(i)
    }
  })
})()

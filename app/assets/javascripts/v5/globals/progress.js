;if( window.NProgress ) (function(){
  if( !Modernizr.csstransforms ) return

  var $imgs = $('img, [style*=background-image]').filter(':visible')

  var started = isDone = false

  function start() {
    if( started ) return
    started = true
    NProgress.start()
    setTimeout(done, 3500)
  }

  function done() {
    if( isDone ) return
    isDone = true
    NProgress.done()
  }

  function inc(i) {
    if( isDone ) return
    NProgress.inc(i)
  }

  $window.load(done)

  if( !$imgs.length ) {
    NProgress.configure({
      showSpinner: false,
      trickleRate: .05,
      trickleSpeed: 150
    })

    start()

    return
  }

  var toInc = 1 / $imgs.length

  NProgress.configure({
    showSpinner: false
  })

  start()

  $imgs.imagesLoaded()
    .progress(function(){
      if( NProgress.status < .85 ) {
        inc(toInc)
      } else {
        done()
      }
    })
    .always(done)
})()

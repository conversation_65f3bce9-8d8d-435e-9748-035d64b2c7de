;(function(){
  var Analytics = function() {
    this.initialize.apply(this, arguments)
  }
  _.extend(Analytics.prototype, Backbone.Events, {
    initialize: function(){},

    initListClick: function(list_name, version){
      var that = this
      if( this.GAenable() ){
        var _clickVariant = function(evt){
          event.preventDefault()

          var id = $(this).data('id')
          var data = gp.lists[list_name][id]
          var target = evt.target
          var _getUrlLink = function(){
            var $target = $(target)
            var alternative_href = version === 'v5' ? $target.closest('article').data('url') : $target.closest('.variant').data('url')
            return target.href || alternative_href
          }

          var _hitCallback = function(){
            window.location.href = _getUrlLink()
          }
          that.addProduct(data)
          that.setAction('click', { 'list': list_name })
          that.sendEvent('Product List UX','click', that.eventLabel(target, data.name+' - List Item'), {
            'callback': _hitCallback
          })

          setTimeout(_hitCallback, 1000);
          return false
        }
        if( version === 'v5' ){
          var $list = $('.variants-list[data-name="'+list_name+'"]')
          $list.find('article').each(function(i, variant){
            $(variant).on('click', _clickVariant)
          })
        } else if( version === 'v4' ){
          var $list = $('.variants-list-module[data-name="'+list_name+'"]')
          $list.find('.variant').each(function(i, variant){
            $(variant).on('click', _clickVariant)
          })
        }
      }
    },

    initCartWatcher: function(){
      var that = this
      if( this.GAenable() ){
        var _data = function(item, variant){
          var product = variant.get('product')
          var price = product.sale_price || product.regular_price
          var sale_status = (typeof product.sale_price !== 'undefined') ? 'On Sale' : 'No'
          var data = {
            'id': product.id,
            'name': product.title,
            'category': product.category.full_path_name,
            'brand': product.manufacturer.name,
            'variant': variant.get('name'),
            'price': price,
            'quantity': item.quantity,
            'dimension2': item.variant_id,
            'dimension5': sale_status,
            'dimension6': product.shop.title
          }
          return data
        }
        var _addToCart = function(item, variant){
          that.addProduct(_data(item, variant))
          that.setAction('add')
          that.sendEvent('Product Detail UX','click', 'Add to Cart', { 'callback': false })
        }
        var _removeFromCart = function(item, variant){
          that.addProduct(_data(item, variant))
          that.setAction('remove')
          that.sendEvent('Cart Details UX','click', 'Remove from Cart', { 'callback': false })
        }
        gp.pubSub.on('cart:add', _addToCart)
        gp.pubSub.on('cart:remove', _removeFromCart)
      }
    },

    initCheckoutSteps: function(){
      var that = this
      if( this.GAenable() ){
        var _trackStepOption = function(step, option){
          that.setAction('checkout_option', { 'step': step, 'option': option })
          that.sendEvent('Checkout','Option', option ,{ 'callback': false })
        }
        var _trackStepChange = function(step, pageview){
          _(gp.checkout_items).each(function(data){
            that.addProduct(data)
          })
          that.setAction('checkout', { 'step': step })
          ga('send', 'pageview', pageview)
        }
        var _trackPaymentLoaded = function(){_trackStepOption(1, 'Payment Loaded')}
        var _trackAddressComplete = function(){_trackStepOption(1, 'Address Form Completed')}
        // Google Analytics Goal Funnel - Step 1: Address selection/creation.
        var _trackAddressChange = function(){_trackStepChange(2, '/virtual/checkout/shipping/address/ready')}
        var _trackDeliveryOptionsDisplay = function(){_trackStepOption(2, 'Delivery Options Available')}
        // Google Analytics Goal Funnel - Step 2: Delivery option selection.
        var _trackDeliveryChange = function(){_trackStepChange(3, '/virtual/checkout/shipping/delivery/ready')}
        // Google Analytics Funnel - Step 3: Payment option selection.
        var _trackPaymentReady = function(){_trackStepChange(4, '/virtual/checkout/payment/ready')}
        var _trackConfirmPurchase = function(){_trackStepOption(4, 'Confirm Order')}

        gp.pubSub.on('checkout:payment_gateway:loaded', _trackPaymentLoaded)
        gp.pubSub.on('shipping:address:saving', _trackAddressComplete)
        gp.pubSub.on('checkout:address:change', _trackAddressChange)
        gp.pubSub.on('shipping:methods:fetched', _trackDeliveryOptionsDisplay)
        gp.pubSub.on('checkout:delivery:change', _trackDeliveryChange)
        gp.pubSub.on('checkout:payment:ready', _trackPaymentReady)
        gp.pubSub.on('checkout:pay', _trackConfirmPurchase)
      }
    },

    GAenable: function(){
      return window.ga ? true : false
    },

    addProduct: function(data){
      ga('ec:addProduct', data)
    },

    setAction: function(){
      var action = arguments[0]
      var data = arguments[1]
      if( data ){
        ga('ec:setAction', action, data)
      } else {
        ga('ec:setAction', action)
      }
    },

    sendEvent: function(category, action, label, options){
      if( typeof options.callback === 'function'){
        ga('send', 'event', category, action, label, {
          hitCallback: options.callback
        })
      } else {
        ga('send', 'event', category, action, label)
      }
    },

    eventLabel: function(target, default_label){
      return $(target).data('label') || default_label
    }
  })

  gp.analytics = new Analytics()
})()

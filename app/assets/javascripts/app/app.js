//= require_self
//= require ./locale
//= require ./app.Model
//= require ./app.Collection
//= require ./app.View

;(function(){
  var root = this

  root.$window = $(window)
  root.$document = $(document)
  root.$body = $('body')
  root.$html = $('html')

  var gp = root.gp = root.gp || {}
  _.extend(gp, {
    App: {},
    Social: {},
    Mkp: {},
    Lux: {},
    Helpers: {},
    Views: {},
    debug: false,
    controller: {},
    action: {},
    user: false,
    network: null,
    mobile: root.$html.hasClass('mobile')
  })

  gp.pubSub = _.extend({}, Backbone.Events)

  // View if the server is on development
  if( $('meta[name="environment"][content="development"]').length
  ||  $('meta[name="environment"][content="test"]').length ) {
    gp.debug = true
  }

  // View if the server is on staging server
  if( $('meta[name="context"][content="staging"]').length ) {
    gp.onStaging = true
  }

  // Check if the user is logged in
  if( $('meta[name="user"]').length ) gp.user = true

  // Set Network
  var $network = $('meta[name="network"]')
  if( $network.length ){
    gp.network = $network.attr('content')
  }

  // Set Currency
  var $currencyFormat = $('meta[name="currency_format"]')
  if( $currencyFormat.length ){
    gp.currencyFormat = $currencyFormat.attr('content')
  }

  // Set controller and action variables.
  var method = $('body').attr('id').split('-')
  if( method.length === 3 ){
    gp.module = {}
    gp.module[method[0]] = true
    gp.controller[method[1]] = true
    gp.action[method[2]] = true
  } else {
    gp.controller[method[0]] = true
    gp.action[method[1]] = true
  }

  // Send token on every ajax call that is not GET
  gp.CSRFtoken = $('meta[name="csrf-token"]').attr('content')
  if( typeof gp.CSRFtoken !== 'string' || !gp.CSRFtoken.length ) {
    gp.CSRFtoken = null
  } else {
    $.ajaxPrefilter(function(options, originalOptions, jqXHR) {
      if( gp.Helpers.url.isSameDomain(options.url) ) {
        jqXHR.setRequestHeader('X-CSRF-Token', gp.CSRFtoken)
      }
    })
  }
})()

$(function(){
  ;(function(){
    if( !window.Foundation ) return
    if( Foundation.libs.reveal ) {
      _.extend(Foundation.libs.reveal.settings, {
        animationSpeed: 200,
        // On tablets the modal shouldnt be closed by clicking the overlay
        closeOnBackgroundClick: ($window.width() < 1024) ? false : true
      })
    }
    $document.foundation()
  })()

  $('.block-grid').fadeOut(0).fadeIn(0)
})

$(function(){
  Modernizr.load({
    test: Modernizr.input.placeholder,
    nope: ['https://cdnjs.cloudflare.com/ajax/libs/jquery-placeholder/2.0.7/jquery.placeholder.min.js'],
    callback: {
      'https://cdnjs.cloudflare.com/ajax/libs/jquery-placeholder/2.0.7/jquery.placeholder.min.js' : function(){
        $('input, textarea').placeholder();
      }
    }
  });
})

$(function(){
  if( window.FastClick ) FastClick.attach(document.body)
})

;(function(){
  var Parent = Backbone.Model, parent = Parent.prototype

  gp.App.Model = Parent.extend({
    paramRoot: null,
    _fetching: false,

    fetch: function(options) {
      var that = this
      options = options || {}

      this._fetching = true
      this.trigger('fetch:start', this)

      var success = options.success
      options.success = function() {
        that._fetching = false
        that.trigger('fetch:success', that)
        if ( typeof success === 'function' ) success(arguments)
      }

      var error = options.error
      options.error = function() {
        that._fetching = false
        that.trigger('fetch:error', that)
        if ( typeof error === 'function' ) error(arguments)
      }

      return parent.fetch.call(this, options)
    },

    isFetching: function() {
      return !!this._fetching
    },

    validate: function(attrs, options) {
      options || (options = {})
      var validation = options.validation || this.validation
      if ( !validation || typeof validation !== 'object') return
      attrs || (attrs = {})
      var that = this, errors = {}
      _.each(validation, function(v, attr){
        var value = attrs[attr], error = null
        if ( v === true ) {
          if ( !value ) {
            error = gp.Helpers.string.capitalize(attr) + " can't be blank."
          }
        } else if ( typeof v === 'function' ) {
          error = v.call(that, value, attrs)
        } else if ( typeof v === 'object' ) {
          if ( v.validate === true && !value ) {
            error = v.msg || gp.Helpers.string.capitalize(attr) + " can't be blank."
          } else if ( typeof v.validate === 'function' ) {
            error = v.validate.call(that, value, attrs)
          } else if( typeof value === 'object' ) {
            error = that.validate.call(that, value, {validation: v})
          }
        }
        if ( error ) errors[attr] = error
      })
      return _.any(errors) ? errors : undefined
    },

    // Allow to set paramRoot to be able to send the paramenters the 'Rails way'
    // i.e. { product: {...attributes...} }
    sync: function(method, model, options){
      if( this.paramRoot && (method == 'create' || method == 'patch' || method == 'update') ) {
        if( !options ) options = {}
        if( !options.attrs ) options.attrs = _.clone(this.attributes)
        var attrs = {}
        attrs[this.paramRoot] = options.attrs
        options.attrs = attrs
      }
      return parent.sync.call(this, method, model, options)
    }
  })
})()

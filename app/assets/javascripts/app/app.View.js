;(function(){
  var Parent = Backbone.View, parent = Parent.prototype;
  gp.App.View = Parent.extend({
    showing: true,

    initialize: function(options){
      options = options || {}
      _.defaults(options, {
        data: {}
      });

      _.bindAll(this, 'show', 'hide', 'toggle');
    },

    close: function(){
      this.unbind();
      return this;
    },

    remove: function(){
      this.hide();
      return parent.remove.apply(this, arguments);
    },

    show: function(){
      this.$el.show()
      this.showing = true
      this.trigger('show')
      return this
    },

    hide: function(){
      this.$el.hide()
      this.showing = false
      this.trigger('hide')
      return this
    },

    toggle: function(){
      this[this.showing ? 'hide' : 'show']()
    },

    showLoader: function(){
      this.$el.addClass('loading');
    },

    hideLoader: function(){
      this.$el.removeClass('loading');
    },

    autoInstantiate: function(View){
      return gp.App.View.autoInstantiate(View, this.$el)
    },

    template: function(template, options){
      return gp.Helpers.templates(template, this.$el, options)
    }
  },{
    autoInstantiate: function(View, context){
      var selector = View.prototype.className
      if ( !selector ) {
        if ( gp.debug ) {
          console.log('Cant auto instantiate a View without className: ', View)
        }
        return
      }

      var $els = $('.'+selector, context)
      var views = []

      $els.each(function(i, el){
        var $el = $(el)
        var view = new View({ el: $el }).render()
        if ( gp.debug ) $el.data('view', view)
        views.push(view)
      })

      return views
    }
  })
})()

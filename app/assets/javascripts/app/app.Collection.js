;(function(){
	var Parent = Backbone.Collection, parent = Parent.prototype;

	gp.App.Collection = Parent.extend({
		model: gp.App.Model,

		_fetching: false,
		fetchQueue: undefined,

		initialize: function() {
			_.bindAll(this, 'fetch', 'isFetching')
		},

		fetch: function(options) {
			var that = this;
			options = options || {};

			var success = options.success;
			options.success = function() {
				var queue = that.fetchQueue;
				that.fetchQueue = undefined;
				if( queue ) {
					parent.fetch.call(that, queue);
				} else {
					that._fetching = false;
					that.trigger('fetch:success', that);
					if( _.isFunction(success) ) success(arguments);
				}
			}

			var error = options.error;
			options.error = function() {
				var queue = that.fetchQueue;
				that.fetchQueue = undefined;
				if( queue ) {
					parent.fetch.call(that, queue);
				} else {
					that._fetching = false;
					that.trigger('fetch:error', that);
					if( _.isFunction(error) ) error(arguments);
				}
			}

			if( this.isFetching() ) {
				this.fetchQueue = options;
				return true;
			} else {
				this._fetching = true;
				this.trigger('fetch:start', this);
				return parent.fetch.call(this, options);
			}
		},

		isFetching: function() {
			return !!this._fetching;
		}

		/**
		* No está andando. It's not working. Это не работает.
		**/
		/*
		updateModels: function(options) {
			var that = this;
			if( this.length ) {
				var c = new this.constructor();
				options = options || {};
				options.data = options.data || {};
				_.extend(options.data, {
					ids: c.pluck('id')
				});
				var success = options.success;
				options.success = function() {
					if( c.length !== that.length ) gp.error('Bad response from server on updateModels.');
					c.each(function(model, i){
						if( that.models[i].id !== model.id ) gp.error('Bad response from server on updateModels.');
						that.models[i].set(model.toJSON());
					});
					if( _.isFunction(success) ) success.apply(this, arguments);
				}

				c.fetch(options);
				return true;
			} else {
				return false;
			}
		}
		*/
	})
})()

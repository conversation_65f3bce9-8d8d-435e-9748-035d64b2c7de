/**
* External APIS Helper by @mjlescano
* Intended to use for communication of other apis thru the client
*/
;(function(){
  var A = gp.Helpers.externalAPIS = {}

   A.Youtube = {
    userExists: function(user){

      var df = jQuery.Deferred()
      $.ajax({
        url: 'https://www.googleapis.com/youtube/v3/channels',
        type: 'GET',
        data: {
          key: 'AIzaSyCoIhFEFpLksXkPqn5ziK7Hlc8MAiNq5zo',
          part: 'id',
          forUsername: user
        }
      }).done(function(data){
        if( data && data.pageInfo && +data.pageInfo.totalResults > 0 ){
          df.resolve()
        } else {
          $.ajax({
            url: 'https://www.googleapis.com/youtube/v3/channels',
            type: 'GET',
              data: {
              key: 'AIzaSyCoIhFEFpLksXkPqn5ziK7Hlc8MAiNq5zo',
              part: 'id',
              id: user
            }
          }).done(function(data){
            if( data && data.pageInfo && +data.pageInfo.totalResults > 0 ){
              df.resolve()
            } else {
              df.reject()
            }
          }).fail(function(){
            df.reject()
          })
        }
      }).fail(function(){
        df.reject()
      })
      return df.promise()
    }

  }

   A.Instagram = {
    userExists: function(user){
      var df = jQuery.Deferred()
      $.ajax({
        url: '/profile/external_exists',
        data: {
          user: user,
          type: 'Instagram'
        },
        type: 'GET'
      }).done(function(isValid){
        df[isValid ? 'resolve' : 'reject']()
      }).fail(function(){
        df.reject()
      })
      return df.promise()
    }
  }

   A.Vimeo = {
    userExists: function(user){
      var df = jQuery.Deferred()
      $.ajax({
        url: 'http://vimeo.com/api/v2/'+user+'/info.json',
        type: 'GET'
      }).done(function(data){
        df.resolve()
      }).fail(function(){
        df.reject()
      })
      return df.promise()
    }
  }

})()

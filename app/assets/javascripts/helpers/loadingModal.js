;(function(){
  gp.Helpers.loadingModal = function(msg){
    msg = msg || I18n.t('js.v5.helpers.loadingModal.loading')

    $.magnificPopup.close()
    $.magnificPopup.open({
      items: { src: $('<span/>') },
      type: 'inline',
      modal: true
    })

    $.fn.magnificPopup('updateStatus', 'loading', msg)
    $.magnificPopup.instance.container.find('.mfp-preloader')
      .addClass('mfp-prevent-close')
  }
})()

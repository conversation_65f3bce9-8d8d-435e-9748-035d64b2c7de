gp.Helpers.olarkChat = function(){
  var t = I18n.withScope('javascripts.helpers.olarkChat')

  var $placeholder = $('<div>', {
    'class': 'olark-chat-placeholder',
    text: t('placeholder'),
    css: {
      'z-index': 9999,
      'position': 'fixed',
      'bottom': '-35px',
      'right': '15px',
      'display': 'inline-block',
      'border-radius': '3px 3px 0 0',
      'padding': '7px 35px',
      'box-shadow': 'rgba(0, 0, 0, 0.5) 0px 0px 8px 1px',
      'margin': '0 auto',
      'color': 'white',
      'text-align': 'center',
      'font-size': '14px',
      'cursor': 'pointer',
      'background-color': '#ED1C24'
    }
  })

  $placeholder.one('click', function(){
    rAF(function(){
      $placeholder.css({
        width: $placeholder.outerWidth(),
        color: '#bbb'
      }).html(t('loading'))
    })

    gp.Helpers.olarkChat.load()
      .done(function(){
        rAF(function(){
          $placeholder.animate({ bottom: '-35px' }, function(){ $placeholder.remove() })
        })
      })
  })

  rAF(function(){
    $body.append($placeholder)
    $placeholder.animate({ bottom: '0' })
  })
}

gp.Helpers.olarkChat.load = function(){
  return $.ajax({
    url: '/widgets/olark_chat',
    data: { network: gp.network },
    dataType: 'html'
  }).done(function(html){
    rAF(function(){ $body.append(html) })
  })
}

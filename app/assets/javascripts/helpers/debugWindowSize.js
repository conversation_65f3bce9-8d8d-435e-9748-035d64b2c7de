;(function(){
  if( !gp.debug ) return false

  var $size, showing = false

  var initialize = _.once(function() {
    showing = false
    $size = $('<div/>', {
      css: {
        position: 'fixed',
        bottom: '5px',
        right: '5px',
        padding: '0 6px 2px',
        'z-index': 9999,
        'border-radius': '3px',
        color: 'white',
        'font-weight': 'bold',
        background: 'rgba(25,25,25,0.5)',
        display: 'none'
      }
    }).prependTo( $('body') )
  })

  function onResize() {
    $size.html( $window.width()+'x'+$window.height() )
    window.setTimeout(function(){
      $window.one('resize', onResize)
    }, 10)
  }

  gp.Helpers.debugWindowSize = function(){
    initialize()

    if( showing ) {
      $size.fadeOut()
      $window.off('resize', onResize)
      showing = false
    } else {
      $size.fadeIn()
      onResize()
      showing = true
    }
  }
})()
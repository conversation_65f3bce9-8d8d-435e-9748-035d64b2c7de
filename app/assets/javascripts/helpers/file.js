/** Helper to work with files
*/
;(function(){
  var F = gp.Helpers.file = {}

  var inited = false
  F.init = function(options){
    var deferred = new $.Deferred()

    if( inited ) return deferred.resolve().promise()

    deferred.done(function(){ inited = true })

    window.FileAPI = _.extend({
      staticPath: '/FileAPI/dist/',
      // html5: false, // Uncomment this line to test Flash uploader
      debug: gp.debug
    }, options)

    yepnope({
      load: [
        '/FileAPI/dist/FileAPI.min.js',
        '/FileAPI/plugins/FileAPI.exif.js'
      ],
      complete: function(err){
        if( err || !(FileAPI.support.html5 || FileAPI.support.flash) ){
          deferred.reject(err)
        } else {
          deferred.resolve()
        }
      }
    })

    return deferred.promise()
  }

  F.inited = function(){ return inited }
})()



/** File Selector Listener
 * Appends an invisible inuput[type=file] on top of everything else inside
 * the passed jQuery objects.
 * This will make the elements act as a file selector.
 * events triggered on $el:
 *   'files:selected' (files:FileList)
 *   'files:error:multiple' (files:FileList)
 *   'files:error:maxFileSize' (files:FileList, maxFileSize:int)
 *   'files:error:accept' (files:FileList, accept:Array)
 *
 * @param {jQuery} $el
 * @param {object} options
 * @return {jQuery}
 */
;(function(){
  var F = gp.Helpers.file

  var defaults = {
    inputCss: {
      position: 'absolute',
      top: 0,
      left: 0,
      opacity: 0,
      filter: 'alpha(opacity = 0)',
      progid: 'DXImageTransform.Microsoft.Alpha(Opacity=0)',
      width: '100%',
      height: '100%',
      margin: 0,
      padding: 0,
      'z-index': 2,
      cursor: 'pointer'
    },
    accept: false, // Array of allowed Mime types, i.e: ['image/jpeg','image/png']
    maxFileSize: false, // Max size per file in MB, i.e: 10
    multiple: true
  }

  F.listen = function($el, options){
    if( !F.inited() ) return null

    var fl = _.extend({}, Backbone.Events)
    var o = _.extend({}, defaults, options)

    function init() {
      $el.data('fileListener', fl)
        .addClass('js-fileapi-wrapper')

      if( $el.css('position') === 'static' ) {
        $el.css('position', 'relative')
      }

      fl.$input = $('<input/>', {
        type: 'file',
        accept: o.accept ? o.accept.join(',') : undefined,
        name: 'fileListener',
        css: o.inputCss,
        multiple: o.multiple
      }).appendTo($el)
    }

    function onselect(e) {
      var files = FileAPI.getFiles(e)
      if( !files || !files.length ) return

      if( !o.multiple && files.length > 1 ) {
        fl.trigger('files:error:multiple', files)
        return
      }

      var maxFileSize = o.maxFileSize
      if( maxFileSize && !F.validates(files, 'maxSize', maxFileSize) ) {
        fl.trigger('files:error:maxFileSize', files, maxFileSize)
        return
      }

      var accept = o.accept
      if( accept && !F.validates(files, 'accept', accept) ) {
        fl.trigger('files:error:accept', files, accept)
        return
      }

      fl.trigger('files:selected', o.multiple ? files : files[0])
      fl.reset()
    }

    fl.reset = function(){
      fl.$input = $(FileAPI.reset(fl.$input))
    }

    fl.enable = function() {
      fl.$input
        .on('change', onselect)
        .show()
        .removeAttr('disabled')
      fl.trigger('enabled')
      return fl
    }

    fl.disable = function() {
      fl.$input
        .off('change', onselect)
        .hide()
        .attr('disabled', 'disabled')
      fl.trigger('disabled')
      return fl
    }

    fl.remove = function() {
      fl.disable()
      fl.off()
      fl.$input.remove()
    }

    init()

    return fl.enable()
  }
})()

/** File Validator
* Functions to validate file objects
*/
;(function(){
  var F = gp.Helpers.file

  F.validates = function(files, type, options) {
    function v(_file) {
      return F.validates[type](_file, options)
    }

    if( (window.FileList && files instanceof FileList) || FileAPI.isArray(files) ) {
      if( !files.length ) return false
      for( var i = 0; i < files.length; i++ ) {
        if( !v(files[i]) ) return false
      }
      return true
    }

    return v(files)
  }

  F.validates.maxSize = function(file, max) {
    if( !file || typeof max !== 'number' ) return false
    if( !file.size ) return true // IE :(
    if( file.size > max * FileAPI.MB ) {
      return false
    }
    return true
  }

  F.validates.accept = function(file, mimeTypes) {
    if( !_.isArray(mimeTypes) ) return false
    if( _.indexOf(mimeTypes, file.type) < 0 ) {
      return false
    }
    return true
  }
})()

/** File Uploader
*/
;(function(){
  var F = gp.Helpers.file

  function Upload(url, field, file, params){
    var that = this

    this.url = url
    this.field = field
    this.file = file
    this.params = params

    this.deferred = $.Deferred()
    this.promise = this.deferred.promise()
    this.promise.abort = function(){
      if( that.xhr ) {
        that.xhr.abort()
      } else {
        queue = _.without(queue, that)
      }
    }
  }

  Upload.prototype.start = function() {
    var that = this
    var headers = {}
    var data = _.extend({ '_method': 'create' }, this.params)
    var files = {}
    files[this.field] = this.file
    if( gp.CSRFtoken && gp.Helpers.url.isSameDomain(this.url) ) {
      headers['X-CSRF-Token'] = gp.CSRFtoken
    }
    that.xhr = FileAPI.upload({
      url: this.url,
      headers: headers,
      data: data,
      files: files,
      fileprogress: function(e){
        var percentage = Math.ceil(e.loaded / e.total * 100)
        that.deferred.notify(that.file, percentage)
      },
      filecomplete: function(err){
        if( err ) return that.deferred.reject(that.file, err)
        var data = $.parseJSON(that.xhr.responseText)
        that.deferred.resolve(that.file, data)
      }
    })

    return this
  }

  var maxUploading = 1
  var uploading = []
  var queue = []

  function tryUnqueue() {
    if( queue.length && uploading.length < maxUploading ) {
      uploading.push( queue.shift().start() )
    }
  }

  function addToQueue(up) {
    queue.push(up)

    up.promise.always(function(){
      uploading = _.without(uploading, up)
      tryUnqueue()
    })

    tryUnqueue()
  }

  F.upload = function(url, field, file, params){
    if( !F.inited() ) return null

    var up = new Upload(url, field, file, params)

    addToQueue(up)

    return up.promise
  }

  F.pending = function() {
    return uploading.length + queue.length
  }
})()

/** Creates a thumb from a file object
*/
;(function(){
  var F = gp.Helpers.file

  F.getThumb = function(imageFile, width, height){
    var deferred = new $.Deferred()

    FileAPI.Image(imageFile)
      .preview((width || 100), (height || 100))
      .get(function(err, img){
        if( err ) return deferred.reject(err)
        deferred.resolve(img)
      })

    return deferred.promise()
  }
})()

/**
* Flash messages Helper by @mjlescano
* Interface implemented to be homologous with rail's falsh messages
*
* Example:
* gp.Helpers.flash.success('Everything is ok!')
*/

//= require toastr
;(function(){
  toastr.options = {
    "debug": false,
    "positionClass": "toast-top-left",
    "onclick": null,
    "fadeIn": 300,
    "fadeOut": 1000,
    "timeOut": 10000,
    "extendedTimeOut": 1000
  }

  /**
  * @params for each flash: message, title, optionsOverride
  */
  gp.Helpers.flash = {
    success: function(){ toastr['success'].apply(this, arguments) },
    error: function(){ toastr['error'].apply(this, arguments) },
    notice: function(){ toastr['info'].apply(this, arguments) },
    alert: function(){ toastr['warning'].apply(this, arguments) }
  }
})()
//= require jquery.fieldSelection
;(function(){
  var P = gp.Helpers.parser = {}

  P.regex = {}
  P.regex.types = {
    youtube: /(?:http:\/\/)?(?:www\.)?(?:youtu)(?:be\.com\/(?:watch|embed|v)(?:\?v=|.*\&v=|\/)|\.be\/)([\w-]{11})(?:\S*)/gi,
    vimeo: /(?:http:\/\/)?(?:www\.)?(?:vimeo\.com\/)(\d+)/gi,
    instagram: /(?:http:\/\/)?(?:www\.)?(?:instagram\.com\/p\/)(\d+)\/?(?:\/embed\/)?/gi,
    url: /([-a-zA-Z0-9:%_\+.~#?&\/\/=]{2,256}\.[a-z]{2,4}\b(?:\/?[-a-zA-Z0-9@:%_\+.~#?&\/\/=]*)+)/gi
  }

  P.getItem = function(string) {
    if ( typeof string !== "string" ) return null

    var types = P.regex.types
    var type, regex, match, item = null
    for ( type in types ) {
      if ( !Object.prototype.hasOwnProperty.call(types, type) ) continue

      regex = types[type]
      match = regex.exec(string)

      if ( match && match.length === 2 ) {
        item = {
          type: type,
          val: match[1],
          url: string
        }
        break
      }
    }

    return item
  }

  P.getItems = function(string) {
    if ( typeof string !== 'string' ) return null
    var words = P.getWords(string)
    if( !words ) return null

    var items = []
    var i, item
    for ( i in words ) {
      if ( !Object.prototype.hasOwnProperty.call(words, i) ) continue

      item = P.getItem(words[i])
      if( item ) items.push(item)
    }

    return items.length ? items : null
  }

  P.getFirstItem = function(string) {
    if ( typeof string !== 'string' ) return null
    var words = P.getWords(string)
    if( !words ) return null

    var i, item = null
    for ( i in words ) {
      if ( !Object.prototype.hasOwnProperty.call(words, i) ) continue

      item = P.getItem(words[i])
      if( item ) break
    }

    return item
  }

  P.getWords = function(string) {
    if ( typeof string !== 'string' ) return null
    return string.match(/(\S+)+/g)
  }

  P.getLastWord = function(string) {
    if ( typeof string !== 'string' ) return null
    var match = string.match(/(\S+)\s*$/)
    return match ? match[1] : null
  }

  P.listenForItem = function($el) {
    var that = this

    function onItemFound(item) {
      $el.off('keyup', onKeyUp)
      $el.off('paste', onPaste)
      $el.trigger('parser:item:found', item)
    }

    var onKeyUp = _.debounce(function (e) {
      var keyCode = (e.keyCode ? e.keyCode : e.which)
      //SPACE or RETURN
      if( keyCode !== 32 && keyCode !== 13 ) return

      var word = $el.val()

      // try to get the caret position
      var selection = $el.getSelection()
      if ( selection ) word = word.substring(0, selection.start)

      word = P.getLastWord( word )

      var item = P.getItem(word)
      if( item ) onItemFound(item)
    }, 350)

    var onPaste = function(e) {
      _.defer(function(){
        var word = e.clipboardData || $el.val()
        var item = P.getFirstItem(word)
        if( item ) onItemFound(item)
      })
    }

    $el.on('keyup', onKeyUp)
    $el.on('paste', onPaste)
  }
})()
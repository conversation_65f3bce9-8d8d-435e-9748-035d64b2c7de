// ;(function(){
//   var srcCache = null
//   var showed = Modernizr.sessionstorage
//                && !!sessionStorage.getItem('signup_modal_showed')

//   function getSrc(){
//     if( srcCache ) {
//       var d = new $.Deferred()
//       d.resolve(srcCache)
//       return d.promise()
//     } else {
//       return $.ajax({
//         url: '/users/signup',
//         data: { xhr: true }
//       }).done(function(html){
//         srcCache = html
//       })
//     }
//   }

//   gp.Helpers.signupModal = function(){
//     if( gp.user ) return false
//     var d = new $.Deferred()

//     getSrc().done(function(src){
//       $.magnificPopup.close()
//       $.magnificPopup.open({
//         items: {
//           src: src
//         },
//         type: 'inline',
//         mainClass: 'mfp-no-gutter mfp-fade',
//         tLoading: I18n.t('js.v5.helpers.loadingModal.loading'),
//         showCloseBtn: false,
//         callbacks: {
//           open: function(){
//             var self = this
//             self.contentContainer.on('click', '.signup-modal .mfp-close', function(){
//               self.close()
//             })
//             self.contentContainer.on('click', '.signup-modal .login-message-button', function(){
//               self.close()
//             })
//             d.resolve()
//           }
//         }
//       }, 0)
//     })

//     d.done(gp.Helpers.signupModal.setShowed)

//     return d.promise()
//   }

//   gp.Helpers.signupModal.showIfNotShowed = function() {
//     // if( gp.debug ) return
//     if( gp.user ) return
//     if( !Modernizr.sessionstorage ) return
//     if( $window.width() < gp.Helpers.media.screens.medium ) return

//     // Only show when the user reachs a second navigated page.
//     if( !sessionStorage.getItem('signup_modal_first_view') ) {
//       sessionStorage.setItem('signup_modal_first_view', true)
//       return false
//     } else {
//       sessionStorage.setItem('signup_modal_first_view', false)
//       if( !gp.Helpers.signupModal.showed() ){
//         return _.delay(gp.Helpers.signupModal, 2500)
//       }
//       return true
//     }

//   }

//   gp.Helpers.signupModal.setShowed = function() {
//     if( showed ) return showed
//     showed = true
//     if( Modernizr.sessionstorage ) {
//       sessionStorage.setItem('signup_modal_showed', showed)
//     }
//     return showed
//   }

//   gp.Helpers.signupModal.showed = function() {
//     return showed
//   }
// })()

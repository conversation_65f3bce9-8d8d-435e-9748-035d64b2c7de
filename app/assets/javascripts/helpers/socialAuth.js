;
(function () {
  gp.Helpers.authorizeTwitter = function (callback) {
    var modal = window.open('/auth/twitter', 'gpTwitterAuth', 'modal=yes,alwaysRaised=yes,width=800px,height=400px')
    gp.Helpers.authorizeTwitter.twitterAuthCallback = function(authentication) {
      authentication['type'] = 'Authentication::Twitter'
      callback(authentication)
      modal.close()
    }
  }

  gp.Helpers.authorizeFacebook = function (callback, askWritePermission) {
    if (!window.FB) return

    var scope = 'email'
    if (askWritePermission) {
      scope += ',publish_actions'
    }

    FB.login(function (response) {
      var authResponse = response.authResponse

      if (authResponse) {
        FB.api('/me', function (user) {
          var authentication = {
            oauth_token: authResponse.accessToken,
            oauth_expires_in: authResponse.expiresIn,
            uid: user.id,
            type: 'Authentication::Facebook',
            can_write: askWritePermission
          }

          callback(authentication)
        })
      } else {
        callback(null)
      }
    }, {scope: scope})
  }
})()
// Enquire not required until needed
// require respond
// require enquire
// require enquire.registerHack
/**
GP Media Queries for JS
=======================

Usage Example:
  // Passing an arrays of predefined sizes
  gp.Helpers.media.register(['from-small', 'til-large'],{
    match: function(){
      'Stuff to do'
    },
    unmatch: function(){
      'Stuff to do'
    }
  })
  gp.Helpers.media.matches(['from-small', 'til-large'])

  // Using normal query
  gp.Helpers.media.register('only screen and (min-width: 300px)',{
    match: function(){
      'Stuff to do'
    },
    unmatch: function(){
      'Stuff to do'
    }
  })
*/

;(function(){
  var M = gp.Helpers.media = {}

  M.screens = {
    small: 320,
    medium: 768,
    large: 996,
    wide: 1280
  }

  M.parseQuery = function(queries){
    if( typeof queries === 'string' ) queries = [queries]

    var queryString = []
    queries.forEach(function(query){
      if( !M.isPrettyQuery(query) ){
        return queryString.push(query)
      }

      var q = query.split('-')

      var screenSize = M.screens[q[1]]
      if( q[0] === 'before' ) screenSize--
      if( q[0] === 'after' ) screenSize++

      var preWidth = (q[0] === 'til' || q[0] === 'before' ) ? 'max' : 'min'

      queryString.push('('+preWidth+'-width: '+screenSize+'px)')
    })

    return queryString.join(' and ')
  }

  M.isPrettyQuery = function(query){
    return (/^(?:til|from|before|after)\-[a-z]+$/).test(query)
  }

  /**
  * It matches againt the window size, matchMedia doesn't updates on resize, except for Firefox
  * http://www.paulrhayes.com/2011-11/use-css-transitions-to-link-media-queries-and-javascript/
  */
  M.matches = function(query){
    if( !window.matchMedia ) return false
    return matchMedia(M.parseQuery(query)).matches
  }

  // require Enquire at the top of this file to be able to use this method
  M.register = function(query, handlerObject){
    return enquire.register(M.parseQuery(query), handlerObject)
  }
})()
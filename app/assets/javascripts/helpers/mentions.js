;(function(){
  var M = gp.Helpers.mentions = {}

  var cachequeryMentions = []
  M.listen = function($field){
    return $field.atwho({
      at: '@',
      limit: 8,
      tpl: '<li class="mentionable" data-value="${login}">${avatar} <p>${full_name}</p> <p class="login">${login}</p> </li>',
      callbacks: {
        remote_filter: function(query, render_view){
          var thisVal = query, self = $(this)
          if( !self.data('active') && thisVal.length >= 2 ){
            self.data('active', true)
            var itemsMentions = cachequeryMentions[thisVal]
            if( typeof itemsMentions === 'object' ){
              render_view(itemsMentions)
            } else {
              if( self.xhr ) self.xhr.abort()
              self.xhr = $.ajax({
                url: '/mentions/mentionable',
                data: { query: query },
                dataType: 'json'
              }).done(function(data) {
                cachequeryMentions[thisVal] = data
                render_view(data)
              })
            }
            self.data('active', false)
          }
        }
      }
    })
  }
})()
/**
* Templates <PERSON><PERSON> by @mjlescano
*/
;(function(){
  var templates = {}

  var H = gp.Helpers.templates = function(template, $scope, options){
    options = _.extend({}, { variable: 'data' }, options)

    if( !templates[template] ){
      var $template = $('#'+template+'[type="text/template"]', $scope)
      if( $template.length ){
        var html = $.trim($template.html())
        html = _.template(gp.Helpers.string.unscapeHTML(html), options)
        templates[template] = function(obj){ return $( html(obj) ) }
      }
    }

    return templates[template]
  }
})()

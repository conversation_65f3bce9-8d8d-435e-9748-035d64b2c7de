/**
* Helper to preload images via javascript.
*/
;(function(){
  var P = gp.Helpers.preload = {}

  P.image = function(src){
    if( !src || typeof src !== 'string' ) return
    (new Image()).src = src
    return true
  }

  P.images = function(imgs){
    if( typeof imgs === 'string' ) imgs = [imgs]
    _.each(imgs, function(src){
      P.image(src)
    })
    return true
  }

  P.backgroundOf = function($el){
    var src = $el.css('background-image')

    if( !src || typeof src !== 'string' ) return
    src = src.match(/url\((.+)\)/)
    if( !src || src.length !== 2 ) return

    return P.image( src[1] )
  }
})()
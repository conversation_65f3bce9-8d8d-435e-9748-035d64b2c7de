;(function(){
  gp.Helpers.rulers = function(){
    if( window.RulersGuides ) return

    yepnope({
      load: [
        'https://raw.github.com/mark-rolich/Event.js/master/Event.js',
        'https://raw.github.com/mark-rolich/Dragdrop.js/master/Dragdrop.js',
        'https://raw.github.com/mark-rolich/RulersGuides.js/master/RulersGuides.js'
      ],
      complete: function(){
        var evt         = new Event(),
            dragdrop    = new Dragdrop(evt),
            rg          = new RulersGuides(evt, dragdrop)
      }
    })
  }
})()
/**
 * Rails Form Helper
**/
;(function(){
  var F = gp.Helpers.form = {}

  var t = I18n.withScope('javascripts.helpers.form')

  // Function to validate a form, using HTML5 attributes (works as fallback)
  // @returns undefined if there's no errors OR Array of string errors.

  F.validate = function($form){
    var errors = []
    var $inputs = $form.find('textarea, input')

    ;[
      F.validate.required,
      F.validate.maxlength,
      F.validate.pattern
    ].forEach(function(validate){
      var e = validate($inputs)
      if( e ) errors.push(e)
    })

    return errors.length ? errors : undefined
  }

  F.validate.required = function($inputs){
    var names = []

    $inputs.each(function(i, el){
      var $el = $(this)
      if( $el.prop('required') && !$el.val() ) {
        names.push($el.attr('title'))
      }
    })

    return names.length ? t('required', { titles: names.join(', ') }) : null
  }

  F.validate.pattern = function($inputs){
    var names = []

    $inputs.each(function(i, el){
      var $el = $(this)
      var pattern = $el.attr('pattern')
      if( pattern && !RegExp(pattern).test($el.val()) ) {
        names.push($el.attr('title'))
      }
    })

    return names.length ? t('pattern', { titles: names.join(', ') }) : null
  }

  F.validate.maxlength = function($inputs){
    var names = []

    $inputs.each(function(i, el){
      var $el = $(this)
      var maxlength = +$el.attr('maxlength')
      if( maxlength && maxlength < $el.val().length ) {
        names.push($el.attr('title'))
      }
    })

    return names.length ? t('maxlength', { titles: names.join(', ') }) : null
  }

  F.ajaxify = function($form, beforeSubmit){
    function _beforeSubmit(){
      var errors = F.validate($form)
      if( errors && errors.length ){
        alert( errors.join('\r\n') )
        return
      }
      if( typeof beforeSubmit === 'function' ){
        beforeSubmit.call($form, makeCall, $form)
      } else {
        makeCall()
      }
    }

    function makeCall(){
      $form.trigger('form:submit')

      $.ajax({
        url: $form.attr('action'),
        type: $form.attr('method'),
        data: $form.formParams(),
        dataType: 'html',
        cache: false
      }).done(function(data){
        $form.trigger('form:done', gp.Helpers.array.argsToArray(arguments))
      }).fail(function(xhr){
        $form.trigger('form:fail', gp.Helpers.array.argsToArray(arguments))
      }).always(function(){
        $form.trigger('form:always', gp.Helpers.array.argsToArray(arguments))
      })
    }

    $form.on('submit', function(e){
      e.preventDefault(), e.stopPropagation()
      _beforeSubmit()
      return false
    })

    return $form
  }
})()

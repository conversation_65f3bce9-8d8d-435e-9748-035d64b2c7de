/**
* Array Helper by @mjlescano
*/
;(function(){
  var U = gp.Helpers.array = {
    chunks: function(array, size){
      var results = []
      while (array.length) {
        results.push(array.splice(0, size));
      }
      return results
    },

    cartesianProductOf: function() {
      return Array.prototype.reduce.call(arguments, function(a, b) {
        var ret = [];
        a.forEach(function(a) {
          b.forEach(function(b) {
            ret.push(a.concat([b]));
          });
        });
        return ret;
      }, [[]]);
    },

    argsToArray: function(args){
      return [].slice.call(args)
    }
  }
})()

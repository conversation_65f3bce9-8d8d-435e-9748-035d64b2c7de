;(function(){
  var S = gp.Helpers.share = {}

  /**
  * Function (pseudo-hack) to get the Pinterest global object
  * Pinit.js code: https://github.com/pinterest/widgets/blob/master/pinit.js
  */
  var _Pinterest = undefined
  function Pinterest() {
    if( _Pinterest === undefined ) {
      for( var i in window ) {
        if( /^PIN_[0-9]{6,}/.test(i) ){
          _Pinterest = window[i].f
          break
        }
      }
      if( !_Pinterest ) return null
    }
    return _Pinterest
  }

  var pinterestInited = false
  S.initPinterest = function(callback){
    return yepnope.injectJs('//assets.pinterest.com/js/pinit.js', function(){
      pinterestInited = true
    }, callback)
  }

  /**
  * Opens a pinterest pinit functionality for this page.
  * No params needed, will parse the og:meta for this page.
  */
  S.onPinterest = function(options){
    // Implementation on a separate window
    if( options ) {
      var _url = 'http://pinterest.com/pin/create/button/'+gp.Helpers.url.toParams(options)
      var w = 750, h = 300
      var l = (screen.width/2)-(w/2), t = (screen.height/2)-(h/2)
      return window.open(_url, 'PinIt', 'width='+w+',height='+h+',left='+l+',top='+t)
    }

    // PinIt Current Page
    if( !pinterestInited ) {
      S.initPinterest(gp.Helpers.share.onPinterest)
      return null
    } else {
      return Pinterest() && Pinterest().fireBookmark()
    }
  }

  /**
  * If the page has og:meta tags facebook will parse the page and read them.
  * So the options are optionals.
  */
  S.onFacebook = function(options, callback){
    // New implementation
    // https://developers.facebook.com/docs/reference/dialogs/feed/
    if( callback ) {
      if ( !window.FB ) return
      var shareObj = {
        method: 'feed',
        link: window.location.href,
        caption: window.location.href
      }
      return FB.ui(_.extend(shareObj, options), callback)
    }

    // Old implementation, lets to choose where to post the item.
    // https://developers.facebook.com/docs/reference/plugins/share-links/
    var link = (options && options.link) || window.location.href
    var url = 'https://www.facebook.com/sharer/sharer.php?u='+
               encodeURIComponent(link)
    var w = 626, h = 436
    var l = (screen.width/2)-(w/2), t = (screen.height/2)-(h/2)
    window.open(url, 'facebook share', 'width='+w+',height='+h+',left='+l+',top='+t)
  }

  S.onTwitter = function(options){
    options = options || {}
    var shortenedUrl = encodeURIComponent(options.url)

    $.ajax({
      url: 'https://api-ssl.bitly.com/v3/shorten?access_token=****************************************&longUrl=' + shortenedUrl,
      dataType: 'jsonp',
      type: 'GET'
    }).done(function(bitly){
        shortenedUrl = encodeURIComponent(bitly.data.url)
      })
      .always(function(bitly){
        var url = 'https://twitter.com/intent/tweet'
        url += '?url=' + shortenedUrl
        url += '&text=' + encodeURIComponent(options.text || I18n.t('javascripts.helpers.share.twitter_default_text'))
        url += '&hashtags=' + encodeURIComponent(I18n.t('javascripts.helpers.share.twitter_hashtag'))
        url += '&via=' + encodeURIComponent(I18n.t('javascripts.helpers.share.twitter_handler'))
        window.open(url, 'shareOnTwitter')
      })

    var w = 550, h = 300
    var l = (screen.width/2)-(w/2), t = (screen.height/2)-(h/2)
    window.open('', 'shareOnTwitter', 'width='+w+',height='+h+',left='+l+',top='+t)
  }

  S.getTwitterCount = function(url){
    var ajax = $.ajax({
      url: 'http://cdn.api.twitter.com/1/urls/count.json?url='+url,
      dataType : 'jsonp',
      type: 'GET'
    })
    return ajax.pipe(function(data){
      return data.count || 0
    })
  }

  S.getFacebookCount = function(url){
    var ajax = $.ajax({
      url: 'http://graph.facebook.com/'+url,
      type: 'GET'
    })
    return ajax.pipe(function(data){
      return data.shares || 0
    })
  }

  S.getCount = function(url){
    url = url || window.location.href
    var when = $.when(S.getTwitterCount(url), S.getFacebookCount(url))
    return when.pipe(function(){
      return _.reduce(arguments, function(sum, n) { return sum += n }) || 0
    })
  }
})()

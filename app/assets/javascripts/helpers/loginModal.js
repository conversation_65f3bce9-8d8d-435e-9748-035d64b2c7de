;(function(){
  gp.Helpers.loginModal = function(msg, params, ajaxed){
    if( gp.user ) return false

    var url = location.origin.replace('http://', 'https://') + '/login'
    var returnTo = location.href.replace('http://', 'https://')

    if( gp.debug || gp.onStaging ) {
      url = url.replace('https://', 'http://')
      returnTo = returnTo.replace('https://', 'http://')
    }

    var data = {
      msg: msg,
      return_to: returnTo,
      network: gp.network,
      xhr: 1
    }

    if( params ){
      _.extend(data, params)
    }

    var deferred = ajaxed && jQuery.Deferred()
    var $form = null

    $.magnificPopup.close()
    $.magnificPopup.open({
      items: {
        src: url
      },
      type: 'ajax',
      tLoading: I18n.t('js.v5.helpers.loadingModal.loading'),
      showCloseBtn: false,
      ajax: {
        settings: { data: data }
      },
      callbacks: {
        ajaxContentAdded: function(){
          if( !ajaxed ) return
          $form = $.magnificPopup.instance.content.find('#guest-form')

          gp.Helpers.form.ajaxify($form)
          $form.on('form:submit', function(){
            $.magnificPopup.instance.content.addClass('loading')
          })
          $form.on('form:done', function(e, response){
            response = JSON.parse(response)
            deferred.resolve(response.guest)
            $.magnificPopup.close()
          })
          $form.on('form:fail', function(){
            $.magnificPopup.instance.content.removeClass('loading')
          })
        },

        beforeClose: function(){
          if( !ajaxed ) return
          $form.off()
          $form = null
          deferred.reject()
        }
      }
    }, 0)

    return deferred ? deferred.promise() : true
  }
})()

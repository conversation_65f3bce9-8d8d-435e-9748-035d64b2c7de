/**
* String Helper by @mjlescano
*/
;(function(){
  var U = gp.Helpers.string = {
    capitalize: function(str){
      return str.charAt(0).toUpperCase() + str.slice(1)
    },

    scapeHTML: function(str){
      return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&apos;')
    },

    unscapeHTML: function(str){
      return str.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"').replace(/&apos;/g, '\'')
    },

    repeat: function(str, num){
      return new Array(num + 1).join(this);
    }
  }
})()

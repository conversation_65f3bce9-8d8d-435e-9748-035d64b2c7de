;(function(){
  var $subheader = $('.sub-header-wrapper')

  // Submenu show functionality, both mobile and desktop
  ;(function(){
    var withSubmenuSelector = '.sub-header-menu .category-item.with-submenu'
    var $active = null
    function activate($el) {
      if( $el === $active ) return
      deactivate()
      $active = $el.addClass('active')
    }
    function deactivate() {
      if( $active ) $active.removeClass('active')
      $active = null
    }
    $subheader.on('mouseenter', withSubmenuSelector, function(e){
      activate($(e.currentTarget))
    })
    $subheader.on('mouseleave', withSubmenuSelector, deactivate)
    $subheader.on('tap', withSubmenuSelector+':not(.active)', function(e){
      e.stopPropagation()
      e.preventDefault()
      var $el = $(e.currentTarget)
      activate($el)
      $el.find('.category-submenu h2').one('tap', function(e){
        deactivate()
        e.stopPropagation(); e.preventDefault()
        return false
      })
      return false
    })

    var wWidth
    function fixSubmenuPosition($el) {
      if( !wWidth ) wWidth = $window.width()

      var $sub = $el.find('.category-submenu')
      var width = $sub.width()
      var left = $sub.offset().left, right = left + width

      if( right > wWidth ) {
        var newLeft = wWidth - width - 23
        $sub.offset({ left: newLeft })
        var $subTitle = $sub.find('> h2')
        $subTitle.css('left', (left - newLeft))
        $el.addClass('js-submenu-position-fixed')
      }
    }

    $subheader.find(withSubmenuSelector).each(function(i, el){
      var $el = $(el)
      $el.one('mouseover tap', function(e){
        _.defer(function(){
          fixSubmenuPosition($el)
        })
      })
      $el.one('mouseover', function(e){
        _.defer(function(){
          $el.find('h2 .full-link').show()
        })
      })
    })
  })()
})()

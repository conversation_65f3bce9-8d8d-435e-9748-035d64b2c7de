// Order reviews
;if( gp.controller.orders_reviews && gp.action['new'] ) (function(){

  var ReviewForm = gp.App.View.extend({

    events: {
      'focus .mkp_review_title': 'focusTitle'
    },

    focusTitle: function(e){
      $(e.currentTarget).first().closest('form').find('.fields').show()
    }
  })

  $('.rate-stars').each(function(){
      new gp.Views['partials/rate_stars']({
          el: this,
          $input: $('input[name*="[stars]"]')
      })
  })

  $('.new_mkp_review').each(function(){
    new ReviewForm({ el: this })
  })

  var $form = $('.new_mkp_review')
  $form.each(function(){
    var $el = $(this)

    var beforeSubmit = function(makeCall){
      description = $el.find('#mkp_review_description').val()
      title       = $el.find('#mkp_review_title').val()

      if(description == '' || title == ''){
        alert(I18n.t('javascripts.mkp.orders_reviews.validate'))
      } else {
        makeCall()
      }
    }

    gp.Helpers.form.ajaxify($el, beforeSubmit)

    $el.on('form:fail', function(){
      alert(I18n.t('javascripts.mkp.orders_reviews.error'))
    })

    $el.on('form:done', function(){
      $el.find('.form-fields').hide()

      var title = $el.find('#mkp_review_title').val()
      var description = $el.find('#mkp_review_description').val()

      $el.addClass('submited')

      $el.find('.star').removeClass('star-action')
      $el.find('.title').html(title)
      $el.find('.description').html(description)
      $el.find('.title, .description').show()
    })
  })
})()

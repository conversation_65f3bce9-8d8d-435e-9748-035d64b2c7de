gp.Mkp.Product = gp.App.Model.extend({
  urlRoot: '/api/shop/products',
  validation: {
    title: {
      validate: function(title) {
        if( !_.isString(title) ||  !title.length ){
          return "Title can't be blank."
        }
        /* TODO @@ ML: Validate the length on back-end first
        else if( title.length > 128 ) {
          return "Title can't be longer than 128 characters";
        }
        */
      }
    },
    description: true
  },

  price: function(){
    return this.get('sale_price') || this.get('regular_price') || 0
  },

  getPicture: function(id){
    id = +id
    return _.find(this.get('pictures'), function(p){
      return (+p.id) === id
    })
  },

  getPictureWhereColor: function(color){
    if( !color ) return null
    return _.find(this.get('pictures'), function(p){
      return p.color === color
    })
  },

  availablePropertyValuesWhere: function(property, properties) {
    var values = []
    var propertiesNames = _.keys(properties)

    _.each(this.get('variants'), function(variant) {
      if (typeof variant.properties[property] === 'undefined') return

      var p = _.pick(variant.properties, propertiesNames)

      if (_.isEqual(p, properties)) {
        value = variant.properties[property]
        values.push(value)
      }
    })

    return _.uniq(values)
  },

  variantsWhere: function(properties) {
    var self = this

    if (!properties) {
      return this.get('variants')
    }

    return _.filter(this.get('variants'), function(variant) {
      return _.every(properties, function(value, key) {
        return _.isEqual(variant.properties[key], value)
      })
    })
  },

  availableQuantityFor: function(properties) {
    return _.some(this.variantsWhere(properties), function(variant) {
      return !!variant.quantity
    })
  }
})

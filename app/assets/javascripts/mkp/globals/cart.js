// TODO:
// Crate a CartItem model, that has a variant and a product.
// And the cart will be a collection of cartItems
;(function(){
  var root = this, gp = root.gp

  if( gp && gp.cart ) {
    if( gp.debug ) console.error('gp.cart defined more than once.')
    return
  }

  /**
  * Cart Collection, lets edit the cart content.
  */
  var Parent = gp.App.Collection,
      parent = Parent.prototype

  var Cart = Parent.extend({
    model: gp.Mkp.Variant,
    items: [],

    initialize: function(){
      var that = this

      _.bindAll(
        this,
        'cartGet',
        'cartCount',
        'cartCountOf',
        'cartTotal',
        'cartTotalOf',
        'cartUpdate',
        'cartAdd',
        'cookiefy',
        'syncToBackend',
        'persist',
        'bootstrap'
      )

      this.bootstrapping = false
      this.persistDebounced = _.debounce(this.persist, 10)

      this.on('remove', function(model, collection) {
        that.items = _.reject(that.items, function(i) {
          return i.variant_id == model.id
        })
        that.persistDebounced()
        that.trigger('cart:change')
      })

      this.on('reset', function(){
        that.items = []
        that.persistDebounced()
        that.trigger('cart:change')
      })
    },

    cartGet: function(variant_id){
      variant_id = +variant_id
      return _.find(this.items, function(item){
        return item.variant_id === variant_id
      })
    },

    cartCount: function(){
      var count = 0
      _.each(this.items, function(i){
        count += i.quantity
      })
      return count
    },

    cartCountOf: function(model){
      var item = this.cartGet(model.id)
      return (item && item.quantity) || 0
    },

    cartTotal: function(){
      var that = this, t = 0
      _.each(this.items, function(item){
        var m = that.get(item.variant_id)
        var price = m.sale_price || m.regular_price
        t += m.price() * item.quantity
      })
      return t.toFixed(2)
    },

    cartTotalOf: function(model){
      var item = this.cartGet(model.id)
      return item && (model.price() * item.quantity).toFixed(2)
    },

    cartUpdate: function(item){
      var that = this

      item.variant_id = (+item.variant_id)
      item.quantity = (+item.quantity)

      var oldItem = this.cartGet(item.variant_id)

      if( oldItem ) {
        _.extend(oldItem, item)
        this.persistDebounced()
        this.trigger('cart:change')
      }
    },

    cartAdd: function(item, model){
      var that = this
      var items = this.items

      item.variant_id = (+item.variant_id)
      item.quantity = (+item.quantity)

      var oldItem = this.cartGet(item.variant_id)

      if( oldItem ){
        oldItem.quantity += item.quantity
      } else {
        items.push(item)
        if( !model ) {
          model = this.create({ id: item.variant_id })
          model.fetch()
        }
        this.add(model)
      }
      if ( !this.bootstrapping ){
        this.persistDebounced()
      }
      this.trigger('cart:change')

      return model
    },

    cookiefy: function(){
      if( !root.cookie || !cookie.enabled() || !root.JSON ) return
      var cart = {}
      var cookieCart = cookie.get('cart')
      if( cookieCart !== undefined ){
        cart = JSON.parse(cookieCart) || {}
      }
      if( this.items.length ) {
        cart[gp.network] = this.items
      } else {
        delete cart[gp.network]
      }
      cookie.set('cart', JSON.stringify(cart))
    },

    syncToBackend: function(){
      if( gp.user ){
        this.trigger('fetch:start')
        $.ajax({
            url:  '/shop/carts/sync',
            data: { cart: this.items },
            type: 'post',
            dataType: 'json'
        })
        this.trigger('fetch:success')
      } else {
        this.cleanupCartOwner()
      }
    },

    persist: function(){
      this.cookiefy()
      this.syncToBackend()
    },

    bootstrap: function(bootstrap_items){
      var that = this
      this.bootstrapping = true
      _.each(bootstrap_items, function(data){
        var item = {
          variant_id: data.id,
          quantity: data.quantity
        }
        delete data.quantity
        var model = new gp.Mkp.Variant(data)
        that.cartAdd(item, model)
      })
      this.bootstrapping = false
    },

    cleanupCartOwner: function(){
      var cookieCartOwner = cookie.get('cart_owner')
      if( cookieCartOwner !== undefined){
        cookie.remove('cart_owner')
      }
    }
  })

  gp.cart = new Cart()
})()

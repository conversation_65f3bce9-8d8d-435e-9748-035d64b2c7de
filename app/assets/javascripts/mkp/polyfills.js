//= require selectivizr
//= require console-polyfill
//= require_self
//
// A fix for window.location.origin in Internet Explorer
// http://tosbourn.com/2013/08/javascript/a-fix-for-window-location-origin-in-internet-explorer/
if( !window.location.origin ) {
  window.location.origin = window.location.protocol + "//" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');
}
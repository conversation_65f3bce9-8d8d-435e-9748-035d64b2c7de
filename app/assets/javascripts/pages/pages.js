// Mobile Menu
;if( gp.mobile && gp.controller.pages) (function(){
  var v = gp.Views['pages/mobile_menu'] = {}

  v.$el = $('.pages-mobile-menu-wrapper')

  v.open = function() { v.$el.addClass('active') }
  v.close = function() { v.$el.removeClass('active') }

  $document.on('click', '.pages-mobile-menu-header', v.open)
  $document.on('click', '.pages-mobile-menu-wrapper-overlay', v.close)

  if( gp.controller.pages ) {
    $document.on('click', '.pages-mobile-menu a[href*="/pages/"]', v.close)
  }
})()

if( gp.controller.pages ) (function(){
  // Pushstate Menu
  ;(function(){
    if( !Modernizr.history ) return

    var Router = Backbone.Router.extend({
      routes: {
        ':network/pages/:page': 'showPage'
      },

      initialize: function(){
        var that = this
        this.$body = $('body')
        this.$menu = $('.pages-menu, .pages-mobile-menu')
        this.$title = $('.pages-title h2')
        this.$content = $('.pages-content-wrapper')
        this.$elems = this.$content.add(this.$title)

        $(function(){
          that.$title.show().fitText(0.4, {
            maxFontSize: that.$title.css('font-size')
          })
        })
      },

      showPage: function(network, page){
        var that = this
        var $el = this.$menu.find('.'+page)
        this.$menu.find('.active').removeClass('active')
        this.$body.attr('id', 'pages-'+$el.attr('class'))
        $el.addClass('active')

        this.$elems.addClass('loading')
        window._gaq && _gaq.push(['_trackPageview', window.location.pathname])

        $.ajax({
          url: window.location.pathname,
          dataType: 'html',
          type: 'GET',
          data: { xhr: true }
        }).done(function(html){
            that.$content.html(html)
            that.$title.html($el.data('title'))
            that.$elems.removeClass('loading')
          })
      }
    })

    var app = new Router()

    var linkSelector = [
      '.pages-menu a[href*="/pages/"]:not(.active)',
      '.pages-mobile-menu a[href*="/pages/"]:not(.active)'
    ]

    $document.on('click', linkSelector.join(', '),function(e){
      if( e.altKey || e.ctrlKey || e.metaKey || e.shiftKey) return
      e.preventDefault()
      e.stopPropagation()

      var $el = $(e.currentTarget)
      app.navigate($el.attr('href'), { trigger: true })
      return false
    })

    Backbone.history.start({
      pushState: true,
      hashChange: false,
      silent: true
    })
  })()

  $document.on('click', '.apply-now-button', function(){
    var $modal = $('#apply-now-form-modal')
    $.magnificPopup.open({
      showCloseBtn: false,
      items: {
        src: $modal,
        type: 'inline'
      }
    })

    $modal.on('submit', 'form', function(e){
      e.preventDefault()
      e.stopPropagation()
      var $form = $(e.currentTarget)

      $modal.addClass('loading')
      $.ajax({
        url: $form.prop('action'),
        type: $form.prop('method'),
        data: $form.formParams()
      })
        .done(function(){
          $(".apply-now-form form")[0].reset()
          gp.Helpers.alert(I18n.t('javascripts.pages.brands.thanks_html'))
        })
        .fail(function(){
          gp.Helpers.alert(I18n.t('javascripts.pages.brands.error_html'))
        })
        .always(function(){
          $modal.removeClass('loading')
        })

      return false
    })
  })

  gp.Helpers.signupModal.showIfNotShowed()
})()
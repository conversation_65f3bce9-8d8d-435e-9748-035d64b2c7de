gp.Social.FavoriteButtonView = gp.App.View.extend({
  className: 'favorite-button',

  events: {
    'click': 'favoriteAction'
  },

  initialize: function(){
    _.bindAll(
      this,
      'setFavorited',
      'setUnfavorited',
      'set',
      'favoriteAction'
    )

    this.$hi5 = this.$('.high-fived')
    this.url = this.$el.data('favorite-url')
    this.type = this.$el.data('favoritable-type')

    var eventModel = gp.favorites.getEventModelForUrl(this.url)
    this.listenTo(gp.favorites, 'favorited:'+eventModel, this.setFavorited)
    this.listenTo(gp.favorites, 'unfavorited:'+eventModel, this.setUnfavorited)
  },

  close: function(){
    this.stopListening(gp.favorites)
  },

  setFavorited: function(){
    this.set(true)
  },

  setUnfavorited: function(){
    this.set(false)
  },

  set: function(setFavorite){
    var self = this
    var action = setFavorite ? 'favorited' : 'unfavorited'
    var unaction = setFavorite ? 'unfavorited' : 'favorited'

    self.$el
      .removeClass(unaction)
      .addClass(action)

    if( setFavorite ) {
      var $hi5 = self.$hi5
      $hi5.fadeIn()
      _.delay(function(){
        $hi5.fadeOut()
      }, 900)
    }

    self.trigger(action)
  },

  favoriteAction: function(e){
    if( gp.Helpers.loginModal(I18n.t('javascripts.partials.favorite_button.need_login')) ) {
      e.preventDefault()
      e.stopPropagation()
      return false
    }

    var self = this
    var favorited = self.$el.hasClass('favorited')
    gp.favorites[favorited ? 'unfavorite' : 'favorite'](this.url, this.type)
      .fail(function(){
        self.$hi5.hide()
      })
  }
})

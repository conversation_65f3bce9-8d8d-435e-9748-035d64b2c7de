gp.Social.FeedItemView = gp.App.View.extend({
  className: 'feed-item',
  events: {
    'click .feed-item-share .button.facebook': 'shareOnFacebook',
    'click .feed-item-share .button.twitter': 'shareOnTwitter',
    'click .feed-item-share .button.high-five': 'favoriteAction',
    'click #close-followers-module': 'closeFollowersModule',
    'click .show-item': 'openPost'
  },

  initialize: function(){
    _.bindAll(
      this,
      'render',
      'closeFollowersModule',
      'disableMoreFollowersModule',
      'triggerChangeSize',
      'openPost',
      'redrawCursor',
      'shareOnTwitter',
      'shareOnFacebook',
      'setFavorited',
      'setUnfavorited',
      'favoriteAction'
    )
  },

  render: function(){
    var that = this
    var $followAuthorBtn = this.$('.feed-item-author .main-follow')
    if( $followAuthorBtn.length ) {
      this.followAuthorBtnView = new gp.Social.FollowButtonView({
        el: $followAuthorBtn
      }).render()
    }

    this.userRecommendationsView = this.autoInstantiate(gp.Social.UsersRecommendationsView)[0]
    if( this.userRecommendationsView ) {
      this.listenTo(
        this.userRecommendationsView,
        'change:size',
        that.triggerChangeSize
      )
      // this.$el.prepend('<span id="close-followers-module">&times;</span>')
    }

    this.$h5Button = this.$('.feed-item-share .button.high-five')
    if( this.$h5Button.length ) {
      var eventModel = gp.favorites.getEventModelForUrl(this.$h5Button.data('favorite-url'))
      this.listenTo(gp.favorites, 'favorited:'+eventModel, this.setFavorited)
      this.listenTo(gp.favorites, 'unfavorited:'+eventModel, this.setUnfavorited)
    }

    this.$('img').one('load', this.triggerChangeSize)

    var fromNow = moment(this.$('.time').attr('title')).fromNow()
    this.$('.time').html(fromNow)

    return this
  },

  closeFollowersModule: function(){
    this.disableMoreFollowersModule()
    this.remove()
    this.trigger('close')
  },

  disableMoreFollowersModule: function(){
    $.ajax({
      url: '/api/disable_followers_module',
      type: 'put',
      dataType: 'json'
    });
  },

  triggerChangeSize: function(){
    this.trigger('change:size')
  },

  openPost: function(e){
    e.stop()

    var self = this
    var $link = $(e.currentTarget)
    var postUrl = $link.attr('href')
    var picIndex = +$link.data('pic-index') || null

    gp.Views['posts/modal'].open(postUrl, picIndex)

    return false
  },

  redrawCursor: function(){
    try {
      document.body.style.cursor = 'auto'
      window.scroll(window.scrollX, window.scrollY)
    } catch(e) {  }
  },

  shareOnTwitter: function(e){
    e.preventDefault()
    e.stopPropagation()

    var options = {
      url: window.location.origin + $(e.currentTarget).data('link'),
      text: $(e.currentTarget).data('title')
    }

    gp.Helpers.share.onTwitter(options)
  },

  shareOnFacebook: function(e){
    e.preventDefault()
    var link = window.location.origin + $(e.currentTarget).data('link')
    var options = {
      link: link,
      caption: link
    }

    gp.Helpers.share.onFacebook(options)
  },

  setFavorited: function(){
    this.$h5Button.addClass('high-fived')
  },

  setUnfavorited: function(){
    this.$h5Button.removeClass('high-fived')
  },

  favoriteAction: function(e){
    if( gp.Helpers.loginModal(I18n.t('javascripts.partials.favorite_button.need_login')) ) {
      e.stopPropagation()
      e.preventDefault()
      return false
    }
    var $el = $(e.currentTarget), favorited = $el.hasClass('high-fived')
    var url = $el.data('favorite-url'), type = $el.data('favorite-type')
    gp.favorites[favorited ? 'unfavorite' : 'favorite'](url, type)
  }
})

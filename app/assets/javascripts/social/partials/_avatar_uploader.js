;(function(){
  gp.Social.AvatarUploaderView = gp.App.View.extend({
    className: 'avatar-uploader',

    fileOptions: {
      accept: ['image/bmp','image/gif','image/jpeg','image/png'],
      maxFileSize: 24,
      multiple: false
    },

    intialize: function() {
      _.bindAll(this, 'render', 'listenForFiles', 'uploadAvatar')
    },

    render: function() {
      var that = this
      this.$thumb = this.$('.avatar-uploader-thumb')
      this.$button = this.$('.choose-photo')

      gp.Helpers.file.init()
        .done(function(){
          that.listenForFiles()
        })
        .fail(function(){
          alert('Your device is not compatible for uploading pictures.')
        })

      return this
    },

    listenForFiles: function() {
      if( this.fileListener ) return

      var that = this
      var $els = this.$thumb.add(this.$button)
      var fileListener = gp.Helpers.file.listen($els, this.fileOptions)
      this.fileListener = fileListener

      if( !fileListener ) {
        alert("There was an error, you won't be able to change the default avatar.")
      }

      fileListener
        .on('files:selected', function(file){
          that.uploadAvatar(file)
        })
        .on('files:error:maxFileSize', function(file, maxFileSize){
          alert( I18n.t('javascripts.partials.form.upload_file_too_big', { max: maxFileSize }) )
        })
        .on('files:error:accept', function(file, types){
          alert( I18n.t('javascripts.partials.form.file_type_not_allowed', { types: types.join(', ') }) )
        })
    },

    uploadAvatar: function(file) {
      var that = this

      this.trigger('upload:start')
      this.fileListener.disable()
      this.$el.addClass('loading')

      var url = this.$el.attr('data-url')
      var field = 'unpublished_picture[photo]'
      var params = {}

      var uploader = gp.Helpers.file.upload(url, field, file, params)

      uploader
        .done(function(file, data){
          if( data && data.picture ) {
            that.$thumb.add( that.$thumb.find('.hexagon-in2') )
              .css({ 'background-image': 'url("' + data.picture.url + '")' })
          }

          that.trigger('upload:complete', data)
        })
        .fail(function(file, e){
          that.$msg.html( I18n.t('javascripts.partials.form.upload_error') )
        })
        .always(function(){
          that.$el.removeClass('loading')
          that.fileListener.enable()
        })
    }
  })
})()

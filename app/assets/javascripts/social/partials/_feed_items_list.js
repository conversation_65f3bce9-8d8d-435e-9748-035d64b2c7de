gp.Social.FeedItemsListView = gp.App.View.extend({
  className: 'feed-items-list',
  items: null,

  events: {
    'click .load-more-items-button:not(.disabled)': 'loadNextPage'
  },

  initialize: function(){
    _.bindAll(
      this,
      'close',
      'render',
      'removeAll',
      'add',
      'onResize',
      'sort',
      'numOfCol',
      'showLoad',
      'loadNextPage'
    )

    this.sortDebounced = _.debounce(this.sort, 150)
  },

  close: function(){
    $window.off('resize', this.onResize)
    return gp.App.View.prototype.close.call(this)
  },

  render: function(){
    var that = this
    this.items = this.autoInstantiate(gp.Social.FeedItemView)
    _.each(this.items, function(view){
      that.listenTo(view, 'change:size', that.sortDebounced)
      that.listenTo(view, 'close',       that.sortDebounced)
    })

    this.onResize()

    this.$list = this.$('.feed-items-list-items')
    this.$joinedOn = this.$('.joined-on')
    this.$load = this.$('.load-more-items-button')
    this.currentPage = +this.$load.attr('data-current-page')
    this.totalPages = +this.$load.attr('data-total-pages')
    this.showLoad()

    return this
  },

  removeAll: function() {
    if( this.items && this.items.length ) {
      _.each(this.items, function(view){
        view.close().remove()
      })
    }
  },

  add: function(html){
    var that = this, items = this.items, View = gp.Social.FeedItemView
    var itemClass = '.'+View.prototype.className
    var $items = $(html).filter(itemClass)
    this.$list.append($items)
    $items.each(function(i, el){
      var view = new View({ el: $(el) }).render()
      items.push(view)
      that.listenTo(view, 'change:size', that.sortDebounced)
      that.listenTo(view, 'close',       that.sortDebounced)
    })
  },

  onResize: function(){
    var that = this
    this.sortDebounced()
    window.setTimeout(function(){
      $window.one('resize', that.onResize)
    }, 10)
  },

  sort: function(){
    var cols = this.numOfCol()
    this.$list.BlocksIt({
      numOfCol: cols,
      offsetX: 10,
      offsetY: 8,
      blockElement: '.'+gp.Social.FeedItemView.prototype.className
    })
  },

  numOfCol: function(){
    var w = this.$el.width()
    if( w >= 768 ) return 3
    if( w >= 450 ) return 2
    return 1
  },

  showLoad: function(){
    if( this.currentPage < this.totalPages ) {
      this.$load.css('display', 'inline-block')
    }else {
      this.$load.hide()
      this.$joinedOn.show()
    }
  },

  loadNextPage: function(e){
    var path = this.$load.attr('data-path')
    e.preventDefault()
    if( this.currentPage < this.totalPages ) {
      var page = this.currentPage + 1
      var that = this
      that.$load.addClass('disabled')
      that.$load.html(
        '<span class="load-more-items-button-icon">' +
        '<span class="load-more-items-button-animation"></span></span>' +
        I18n.t('javascripts.social.partials.feed_items_list.loading_more')
      )
      $.ajax({
        url: path,
        data: { page: page },
        dataType: 'html',
        success: function(html) {
          that.add(html)
          that.sortDebounced()
          that.$load.attr('data-current-page', page)
          that.currentPage = page
          that.showLoad()
        },
        complete: function(){
          that.$load.removeClass('disabled')
          that.$load.html(
            '<span class="load-more-items-button-icon">' +
            '<span class="load-more-items-button-animation"></span></span>' +
            I18n.t('javascripts.social.partials.feed_items_list.load_more')
          )
        }
      })

      // Track pushState view on google analytics.
      if( window._gaq ) {
        var urlHelper = gp.Helpers.url
        var analyticsUrl = urlHelper.getParamsObject()
        analyticsUrl.page = page
        for(var action in gp.action);
        for(var controller in gp.controller);
        // Specify controller and action when the user is on root
        if( window.location.pathname === '/' ) {
          _.extend(analyticsUrl, {
            controller: controller,
            action: action
          })
        }
        analyticsUrl = window.location.pathname + urlHelper.toParams(analyticsUrl)
        _gaq.push(['_trackPageview', analyticsUrl])
      }
    }
  }
})

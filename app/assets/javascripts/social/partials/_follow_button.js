gp.Social.FollowButtonView = gp.App.View.extend({
  className: 'main-follow',
  ownerId: null,

  events: {
    'click': 'onClick'
  },

  initialize: function(){
    _.bindAll(
      this,
      'render',
      'onClick',
      'setFollowing',
      'setNotfollowing',
      'follow',
      'unfollow'
    )
  },

  render: function(){
    var ownerId = this.ownerId = this.$el.attr('data-follow-id')

    gp.followings.on('following:'+ownerId, this.setFollowing)
    gp.followings.on('notfollowing:'+ownerId, this.setNotfollowing)

    return this
  },

  onClick: function(e){
    if( gp.Helpers.loginModal(I18n.t('javascripts.partials.follow_button.need_login')) ) {
      e.stopPropagation()
      e.preventDefault()
      return false
    }
    this[this.$el.attr('data-follow-action')]()
  },

  setFollowing: function(){
    this.$el
      .removeClass('follow')
      .addClass('unfollow')
      .attr('data-follow-action', 'unfollow')
  },

  setNotfollowing: function(){
    this.$el
      .removeClass('unfollow')
      .addClass('follow')
      .attr('data-follow-action', 'follow')
  },

  follow: function(){
    var basePath = this.$el.data('base-path')
    gp.followings.follow( this.ownerId, basePath )
  },

  unfollow: function(){
    var basePath = this.$el.data('base-path')
    gp.followings.unfollow( this.ownerId, basePath )
  }
})

;(function(){
  gp.Social.WhatsupsFormView = gp.App.View.extend({
    id: 'whatsups-form',
    className: 'whatsups-form',

    events: {
      // It will be rendered when foundation reveal triggers open
      'open': 'render',
      'opened': 'formToggle',
      'click [type="submit"]': 'submitForm',
      'click .facebook-share': 'toggleFacebookShare',
      'click .twitter-share': 'toggleTwitterShare',
      'click .metadata-container .metadata .close': 'metaRemove',
      'submit form': 'disableSubmitButton'
    },

    initialize: function(){
      _.bindAll(
        this,
        'render',
        'initCreateAlbumButton',
        'showAlbumsForm',
        'submitForm',
        'formToggle',
        'loadMediaUploader',
        'uploadVideo',
        'fetchVideoUploadUrl',
        'uploadPic',
        'toggleFacebookShare',
        'toggleTwitterShare',
        'listenForMeta',
        'onMetaFound',
        'metaRemove',
        'disableSubmitButton',
        'enableSubmitButton',
        'loadMentionableUsers',
        'setSportTags'
      )
    },

    render: function(){
      if(this.rendered) return
      var that = this
      this.$hiddens = this.$('.hiddens')
      this.$picturesForm = this.$('.pictures-form')
      this.$mediaBtns = this.$('.add-img, .create-album')
      this.$uploader = this.$('.file-uploader')
      this.$uploaderMsg = this.$uploader.find('.file-uploader-msg')

      this.$body = this.$('textarea[name="social_whatsup[body]"]')
      this.$sports = this.$('#social_whatsup_sport_ids')
      this.$metaItem = this.$('.metadata-container')
      gp.Helpers.mentions.listen(this.$body)
      this.listenForMeta()
      this.setSportTags()

      gp.Helpers.file.init()
        .done(function(){
          that.loadMediaUploader()
          that.initCreateAlbumButton()
        })
        .fail(function(){
          alert('Your device is not compatible for uploading media.')
        })

      this.rendered = true

      return this
    },

    initCreateAlbumButton: function(){
      var that = this

      var fileOptions = gp.Social.AlbumsFormView.fileOptions
      var $createAlbum = this.$('.create-album').removeClass('disabled')
      var fileListener = gp.Helpers.file.listen($createAlbum, fileOptions)

      if( !fileListener ) {
        alert('There was an error creating the album.')
      }

      fileListener
        .on('files:selected', function(files){
          that.showAlbumsForm(files)
        })
        .on('files:error:maxFileSize', function(file, maxFileSize){
          alert( I18n.t('javascripts.partials.form.upload_file_too_big', {max: maxFileSize}) )
        })
        .on('files:error:accept', function(file, types){
          alert( I18n.t('javascripts.partials.form.file_type_not_allowed', {types: types.join(', ')}) )
        })
    },

    showAlbumsForm: function(preloadedFiles){
      if( this.albumFormView ) {
        this.albumFormView.show()
      } else {
        var AlbumFormView = gp.Social.AlbumsFormView
        var $albumForm = $('#'+AlbumFormView.prototype.id)
        var albumFormView = new AlbumFormView({
          el: $albumForm,
          preloadedFiles: preloadedFiles
        }).render()

        this.albumFormView = albumFormView
      }
    },

    submitForm: function(e){
      if ( this.$el.hasClass('loading') ) {
        e.preventDefault()
        return false
      }
      var model = new gp.Social.Whatsup({
        sport_id: this.$('[name="social_whatsup[sport_id]"]').val(),
        picture_id: this.$('[name="social_whatsup[unpublished_picture_id]"]').val(),
        video_key: this.$('[name="social_whatsup[video_attributes][key]"]').val(),
        body: this.$('[name="social_whatsup[body]"]').val()
      })
      if ( !model.isValid() ) {
        var errors = _.values(model.validationError).join(' ')
        alert(errors)
        e.preventDefault()
        return false
      }
      if ( this.mediaUploader ) {
        this.mediaUploader.remove()
      }
    },

    formToggle: function(){
      this.$('textarea:first').focus()
    },

    mediaUploader: null,
    loadMediaUploader: function(){
      var that = this
      var fileOptions = {
        accept: that.picTypes.concat(that.videoTypes),
        multiple: false
      }
      var $uplaoderBtn = this.$('.add-img').removeClass('disabled')
      var fileListener = gp.Helpers.file.listen($uplaoderBtn, fileOptions)

      this.mediaUploader = fileListener

      fileListener
        .on('files:selected', function(file){
          if( _.indexOf(that.videoTypes, file.type) >= 0 ) {
            if ( gp.Helpers.file.validates(file, 'maxSize', 600) ) {
              that.uploadVideo(file)
              fileListener.disable()
            } else {
              alert( I18n.t('javascripts.partials.form.upload_file_too_big', { max: 600 }) )
            }
          } else {
            if ( gp.Helpers.file.validates(file, 'maxSize', 10) ) {
              that.uploadPic(file)
              fileListener.disable()
            } else {
              alert( I18n.t('javascripts.partials.form.upload_file_too_big', { max: 10 }) )
            }
          }
        })
        .on('files:error:accept', function(file, types){
          alert( I18n.t('javascripts.partials.form.file_type_not_allowed', {types: types.join(', ')}) )
        })
        .on('files:error:multiple', function(){
          alert( I18n.t('javascripts.partials.form.multiple_files_not_allowed') )
        })
        .on('disabled', function(){
          that.$mediaBtns.hide()
        })
        .on('enabled', function(){
          that.$mediaBtns.show()
        })
    },

    videoTypes: [
      'video/x-ms-asf',
      'video/x-msvideo',
      'video/x-flv',
      'video/quicktime',
      'video/mp4',
      'video/mpeg',
      'video/avi'
    ],
    videoUploader: null,
    uploadVideo: function(file){
    //   var self = this
    //   if( !self.videoUploader ) {
    //     self.videoUploader = new gp.Social.FileUploaderView({
    //       el: self.$('#whatsups-video-uploader')
    //     }).render()
    //     self.videoUploader.showing = false
    //   }

    //   if( self.videoUploader.showing ) return

    //   self.videoUploader.show()

    //   function cancel(){
    //     self.$el.removeClass('loading')
    //     self.enableSubmitButton()
    //     self.mediaUploader.enable()
    //     self.videoUploader.hide()
    //   }

    //   function onError(){
    //     cancel()
    //     alert( I18n.t('javascripts.partials.form.upload_error') )
    //   }

    //   function upload(){
    //     var $file = self.videoUploader.add(file)
    //       .on('file:done', function(e, data){
    //         self.$el.removeClass('loading')
    //         self.enableSubmitButton()
    //       })
    //       .on('file:fail', onError)
    //       .on('file:remove', cancel)
    //   }

    //   self.$el.addClass('loading')
    //   self.disableSubmitButton()
    //   self.fetchVideoUploadUrl()
    //     .done(function(data) {
    //       self.videoUploader.$el.data('url', data.url)
    //       upload()
    //     })
    //     .fail(onError)
    // },

    // uploadVideo2: function(file) {
      var that = this
      this.disableSubmitButton()
      if( this.videoUploader ) return

      this.$uploaderMsg
        .html( I18n.t('javascripts.partials.form.uploading_msg') )
        .fadeIn()

      // Load the template for the uplaoder file item list
      var $item = this.template('uploader-item')({
        progress: 0,
        thumb: '',
        name: file.name
      })
      $item.addClass('no-thumb')
      var $progressPercentage = $item.find('.file-uploader-item-progress-percentage')
      var $progressBar = $item.find('.file-uploader-item-progress-bar')

      // Append the file item to the list
      this.$uploader.show().append($item)

      // Get the hidden input for the form
      var $videoHidden = that.$hiddens.find('[name="social_whatsup[video_attributes][key]"]')
      if ( !$videoHidden.length ) {
        $videoHidden = $('<input type="hidden" name="social_whatsup[video_attributes][key]">')
        $videoHidden.appendTo(that.$hiddens)
      }

      // Load de uploader on a dummy non DOM element
      var $dummy = $('<span></span>')
      var uploader = this.videoUploader = $dummy.Upload({
        params: { title: file.name },
        onUpdate: function(){
          var prog = this.getPercentage()
          if( +prog === 100 ) prog = 99
          $progressBar.css('width', prog+'%')
          $progressPercentage.html(prog+'%')
        },
        onComplete: function(data){
          video_key = data.media.key
          uploader.disable()
          $dummy.off().remove()
          $videoHidden.val(video_key)

          $progressBar.css('width', '100%')
          $progressPercentage.html('100%')
          that.$el.removeClass('loading')
          that.enableSubmitButton()
          that.$uploaderMsg
            .html( I18n.t('javascripts.partials.form.upload_complete') )
        },
        onError: onVideoUploadError
      })

      // Set the file on the uploader
      uploader.file = file

      // function to be called when theres an error (shouldnt be any)
      function onVideoUploadError(){
        cancelUpload()
        that.$uploaderMsg
          .html( I18n.t('javascripts.partials.form.upload_error') )
      }

      // Cancel Upload Action
      function cancelUpload(){
        if( uploader.inProgress() ) {
          uploader.cancel()
        }
        uploader.disable()
        $dummy.off()
        $item.remove()
        that.$el.removeClass('loading')
        that.enableSubmitButton()
        that.videoUploader = null
        that.mediaUploader.enable()
        $videoHidden.remove()
        that.$uploaderMsg.html('')
      }

      $item.on('click', '.file-uploader-item-cancel', cancelUpload)

      this.fetchVideoUploadUrl()
        .done(function(data) {
          uploader.options.url = data.url
          if ( uploader.sendFile() ) {
            that.$el.addClass('loading')
          } else {
            onVideoUploadError()
          }
        })
        .fail(onVideoUploadError)
    },

    fetchVideoUploadUrl: function() {
      return $.ajax({
        url: '/videos/upload_url',
        type: 'POST'
      })
    },

    picTypes: ['image/bmp','image/gif','image/jpeg','image/png'],
    picUploader: null,
    uploadPic: function(file){
      var that = this
      if( !this.picUploader ) {
        this.picUploader = new gp.Social.FileUploaderView({
          el: this.$('#whatsups-pic-uploader')
        }).render()
        this.picUploader.showing = false
      }

      if( this.picUploader.showing ) return

      this.picUploader.show()

      function cancel(){
        that.$el.removeClass('loading')
        that.enableSubmitButton()
        that.mediaUploader.enable()
        that.picUploader.hide()
      }

      var $picHidden = that.$hiddens.find('[name="social_whatsup[unpublished_picture_id]"]')
      if ( !$picHidden.length ) {
        $picHidden = $('<input type="hidden" name="social_whatsup[unpublished_picture_id]">')
        $picHidden.appendTo(that.$hiddens)
      }

      var $file = this.picUploader.add(file)
        .on('file:done', function(e, data){
          $picHidden.val(data.picture.id)
          that.$el.removeClass('loading')
          that.enableSubmitButton()
        })
        .on('file:fail', function(e, data){
          cancel()
          $picHidden.remove()
          alert( I18n.t('javascripts.partials.form.upload_error') )
        })
        .on('file:remove', function(e, data){
          cancel()
          $picHidden.remove()
        })

      that.$el.addClass('loading')
      that.disableSubmitButton()
    },

    toggleFacebookShare: function() {
      var that = this
      var facebookShare = this.$('.facebook-share')

      if (facebookShare.hasClass('active')) {
        facebookShare.removeClass('active')
        $('input[name="facebook_share"]').remove()
      }else {
        if (facebookShare.data('can_write') !== true) {
          gp.Helpers.authorizeFacebook(function(authentication) {
            if (authentication) {
              facebookShare.addClass('active')

              $.post('/authentications', {authentication: authentication}, function(response) {
                $('<input type="hidden" name="facebook_share" value="true">').appendTo(that.$hiddens)
              }, 'json')
            }
          }, true)
        } else {
          facebookShare.addClass('active')
          $('<input type="hidden" name="facebook_share" value="true">').appendTo(that.$hiddens)
        }
      }
    },

    toggleTwitterShare: function() {
      var that = this
      var twitterShare = this.$('.twitter-share')
      if (twitterShare.hasClass('active')) {
        twitterShare.removeClass('active')
        $('input[name="twitter_share"]').remove()
      }else {
        if (twitterShare.data('can_write') !== true) {
          gp.Helpers.authorizeTwitter(function(authentication) {
            if (authentication) {
              twitterShare.addClass('active')

              $.post('/authentications', {authentication: authentication}, function(response) {
                $('<input type="hidden" name="twitter_share" value="true">').appendTo(that.$hiddens)
              }, 'json')
            }
          }, true)
        } else {
          twitterShare.addClass('active')
          $('<input type="hidden" name="twitter_share" value="true">').appendTo(that.$hiddens)
        }
      }
    },

    listenForMeta: function(){
      gp.Helpers.parser.listenForItem(this.$body)
      this.$body.one('parser:item:found', this.onMetaFound)
    },

    onMetaFound: function(e, item){
      var that = this, url = item.url
      this.$metaItem.addClass('loading')
      gp.Helpers.scrap(url)
        .done(function(data){
          var $meta = $(data)
          $meta.prepend('<span class="close">X</span>')
          that.$metaItem.append($meta)

          var $metaValue = that.$hiddens.find('[name="social_whatsup[metadata][url]"]')
          if ( !$metaValue.length ) {
            $metaValue = $('<input type="hidden" name="social_whatsup[metadata][url]">')
            $metaValue.appendTo(that.$hiddens)
          }
          $metaValue.val(url)
        })
        .always(function(){
          that.$metaItem.removeClass('loading')
        })
    },

    metaRemove: function(e){
      this.$hiddens.find('[name="social_whatsup[metadata][url]"]').remove()
      this.$metaItem.find('.metadata').remove()
      this.listenForMeta()
    },

    disableSubmitButton: function() {
      $('.post-item').prop('disabled', true)
    },

    enableSubmitButton: function() {
      $('.post-item').prop('disabled', false)
    },

    loadMentionableUsers: function() {
      var that = this
      return $.ajax({
        url: '/mentions/mentionable',
        dataType: 'json'
      }).done(function(data){

      })
    },

    setSportTags: function() {
      var w = $window.width()
      if( w < gp.Helpers.media.screens.medium ) return

      this.$('.sport-selector').removeClass('without-select2')
      function format(sport){
        return "#" + sport.text
      }
      this.$sports.select2({
        width: '100%',
        placeholder: I18n.t('javascripts.partials.form.select_a_sport'),
        allowClear: true,
        formatResult: format,
        containerCssClass: 'whatsups-form-select2-container',
        dropdownCssClass: 'whatsups-form-select2-drop'
      })
      $('.select2-drop').prepend("<div class='popular-tags'>" + I18n.t('javascripts.partials.form.popular_tags') + '<div>')
    }
  })
})()

gp.Social.FeedsRecommendationsView = gp.App.View.extend({
  className: 'feeds-recommendations',

  initialize: function(){
    _.bindAll(this, 'fetch', 'setEmpty')

    this.fetch()
  },

  fetch: function(){
    var self = this
    return $.ajax({
      type: 'get',
      url: this.$el.attr('data-url'),
      data: {
        type: this.$el.attr('data-type'),
        id: this.$el.attr('data-id'),
        limit: this.$el.attr('data-limit'),
        network: this.$el.attr('data-network')
      }
    }).done(function(html){
      html = $.trim(html)

      if( !html ) {
        self.setEmpty()
        return
      }

      self.show()
      self.$('.feed-items-list-items').html(html)
      self.autoInstantiate(gp.Social.FeedItemsListView)
    }).fail(function(){
      self.setEmpty()
    })
  },

  setEmpty: function(){
    this.hide().close().remove()
  }
})
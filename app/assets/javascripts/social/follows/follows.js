;(function(){
  if( !gp.controller.follows || !(gp.action.followers || gp.action.following) ) {
    return
  }

  var $followBox = $('.follow-box')
  var $miniUsers = $('.mini-users')
  var $showMore = $followBox.find('.show-more')

  new gp.Social.FollowButtonView({
    el: $('.profile-stats .main-follow')
  }).render()

  gp.App.View.autoInstantiate( gp.Social.FollowButtonView, $('.follow-box') )

  $showMore.on('click', function(e){
    var currentPage = parseInt($showMore.attr('data-current-page'))
    var totalPages = parseInt($showMore.attr('data-total-pages'))
    var path = $showMore.attr('data-path')

    e.preventDefault()
    $showMore.attr('disabled', true)

    if( currentPage < totalPages ) {
      var page = (+currentPage) + 1
      $.ajax({
        url: path,
        data: { page: page },
        dataType: 'html'
      }).done(function(html){
        var $html = $(html)
        $showMore.before($html)
        $showMore.attr('data-current-page', page)
        $showMore.attr('disabled', false)
        $html.find('.main-follow').each(function(i, el){
          new gp.Social.FollowButtonView({
            el: el
          }).render()
        })
      }).always(function(){
        if( page == totalPages ) {
          $showMore.hide()
        }
      })
    }
  })

  $('.brand-avatar a').magnificPopup({ type:'image' });
})()

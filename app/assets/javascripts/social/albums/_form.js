;(function(){
  gp.Social.AlbumsFormView = gp.App.View.extend({
    id: 'albums-form',
    className: 'albums-form',

    events: {
      'opened': 'onShow',
      'submit form#new_social_album': 'submit'
    },

    initialize: function(){
      _.bindAll(
        this,
        'render',
        'show',
        'onShow',
        'submit'
      )
    },

    render: function(){
      var that = this
      this.show()

      this.$hiddens = this.$('.hiddens')

      this.$sports = this.$('#social_album_sport_ids')
      this.setSportTags()

      gp.Helpers.file.init()
        .done(function(){
          that.uploader = that.autoInstantiate(gp.Social.PicturesUploaderView)[0]
          that.uploader.show()
          that.addImageFiles(that.options.preloadedFiles)
          that.initAddMoreImages()
        })
        .fail(function(){
          alert('Your device is not compatible for uploading media.')
        })

      return this
    },

    show: function(){
      this.$el.foundation('reveal', 'open')
      return gp.App.View.prototype.show.apply(this, arguments)
    },

    onShow: function(){
      var that = this
      _.defer(function(){
        that.$('form input[type="text"], form textarea').first().focus()
      })
    },

    submit: function(e){
      if ( this.$el.hasClass('loading') ) {
        e.preventDefault()
        return false
      }
      var params = this.$('form').formParams().social_album
      if(!params.unpublished_picture_attributes) {
        alert( I18n.t('javascripts.social.whatsups.no_pictures') )
        e.preventDefault()
        return false
      }
      var model = new gp.Social.Album({
        title: params.title
      })
      if ( !model.isValid() ) {
        var errors = _.values(model.validationError).join(' ')
        alert(errors)
        e.preventDefault()
        return false
      }
    },

    disableSubmitButton: function() {
      this.$('.post-item').prop('disabled', true)
    },

    enableSubmitButton: function() {
      this.$('.post-item').prop('disabled', false)
    },

    initAddMoreImages: function(){
      var that = this
      var fileOptions = gp.Social.AlbumsFormView.fileOptions
      var $addImages = this.$('.add-photos')
      var fileListener = gp.Helpers.file.listen($addImages, fileOptions)

      if( !fileListener ) {
        alert('There was an error creating the album.')
      }

      fileListener
        .on('files:selected', function(files){
          that.addImageFiles(files)
        })
        .on('files:error:maxFileSize', function(file, maxFileSize){
          var msg = I18n.t(
            'javascripts.partials.form.upload_file_too_big',
            { max: maxFileSize }
          )
          alert(msg)
        })
        .on('files:error:accept', function(file, types){
          var msg = I18n.t(
            'javascripts.partials.form.file_type_not_allowed',
            { types: types.join(', ') }
          )
          alert(msg)
        })
    },

    addImageFiles: function(files){
      if( !files ) return
      if( files.length ) {
        for( var i = 0; i < files.length; i++ ) {
          this.addImageFile(files[i])
        }
      } else if( files.type ) {
        this.addImageFile(files)
      }
    },

    _imageFilesAdded: 0,
    _imageFilesDone: 0,
    addImageFile: function(file){
      var that = this
      this._imageFilesAdded++
      this.disableSubmitButton()

      var $id = $('<input/>', {
        type: 'hidden',
        name: 'social_album[unpublished_picture_attributes][][id]'
      })
      var $description = $('<input/>', {
        type: 'hidden',
        name: 'social_album[unpublished_picture_attributes][][description]'
      })

      var $picHiddens = $id.add($description)

      $picHiddens.appendTo(this.$hiddens)

      var $file = this.uploader.add(file)
      var done = false
      $file
        .on('file:done', function(e, data){
          that._imageFilesDone++
          if ( that._imageFilesAdded - that._imageFilesDone == 0 ) {
            that.enableSubmitButton()
          }

          $id.val(data.picture.id)
          done = true
        })
        .on('file:fail', function(e, data){
          that._imageFilesDone++
          if ( that._imageFilesAdded - that._imageFilesDone == 0 ) {
            that.enableSubmitButton()
          }

          $picHiddens.remove()
          alert( I18n.t('javascripts.partials.form.named_upload_error', { file: file.name }) )
        })
        .on('file:remove', function(e, data){
          var pendingFiles = that._imageFilesAdded - that._imageFilesDone
          if ( done && pendingFiles > 0 ) {
            that._imageFilesDone++
          }

          if ( that._imageFilesAdded - that._imageFilesDone == 0 ) {
            that.enableSubmitButton()
          }

          $picHiddens.remove()
        })
        .on('input', '[name="unpublished_picture[description]"]', function(e){
          $description.val( $(e.currentTarget).val() )
        })
    },

    setSportTags: function(){
      var w = $window.width()
      if( w < gp.Helpers.media.screens.medium ) return

      function format(sport){
        return "#" + sport.text
      }
      this.$sports.select2({
        width: '100%',
        placeholder: I18n.t('javascripts.partials.form.select_a_sport'),
        allowClear: true,
        formatResult: format,
        containerCssClass: 'whatsups-form-select2-container',
        dropdownCssClass: 'whatsups-form-select2-drop'
      })
      $('.select2-drop').prepend("<div class='popular-tags'>" + I18n.t('javascripts.partials.form.popular_tags') + '<div>')
    }
  },{
    fileOptions: {
      accept: ['image/bmp','image/gif','image/jpeg','image/png'],
      multiple: true,
      maxFileSize: 24
    }
  })
})()

gp.Social.PicturesUploaderView = gp.App.View.extend({
  className: 'pictures-uploader',

  intialize: function() {
    _.bindAll(
      this,
      'render',
      'add'
    )
  },

  render: function() {
    this.$itemTemplate = this.template('uploader-item')
    this.$items = this.$('.pictures-uploader-items')
    this.$addItem = this.$('.pictures-uploader-item.add-photos')

    return this
  },

  add: function(file) {
    var that = this

    // Load the template for the uplaoder file item list
    var $item = this.$itemTemplate({
      progress: 0,
      thumb: '',
      name: file.name
    })
    var $thumb = $item.find('.pictures-uploader-item-thumb')
    var $progressBar = $item.find('.pictures-uploader-item-progress-bar')

    // Has to be at least one space between the block-grid items.
    // Append the file item to the list
    this.$addItem.before('\r\n', $item)

    var url = this.$el.data('url')
    var field = 'unpublished_picture[photo]'
    var type = this.$el.data('type')
    var params = { type: type }

    var uploader = gp.Helpers.file.upload(url, field, file, params)
      .progress(function(file, percentage){
        if( +percentage === 100 ) percentage = 99
        $progressBar.css('width', percentage+'%')

        $item.trigger('file:progress', [percentage])
      })
      .done(function(file, data){
        $progressBar.css('width', '100%')

        $thumb.css({
          'background-image': 'url("' + data.picture.url + '")'
        })

        $item
          .removeClass('loading')
          .trigger('file:done', [data])
      })
      .fail(function(file, e){
        if( e !== 'abort' ) {
          $item.trigger('file:fail', [e])
          _.defer(function(){
            $item.remove().off()
          })
        }
      })

    $item.on('click', '.pictures-uploader-item-cancel', function(){
      var msg = I18n.t('javascripts.partials.form.confirm_delete', { file: file.name })
      var abort = confirm(msg)
      if( !abort ) return

      if( uploader.state() !== 'resolved' ) {
        uploader.abort()
      }
      $item.trigger('file:remove')
      $item.remove().off()
    })

    return $item
  }
})
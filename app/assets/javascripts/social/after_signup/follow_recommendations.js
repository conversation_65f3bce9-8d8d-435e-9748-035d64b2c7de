;(function(){
  if( !gp.controller.after_signup || !gp.action.follow_recommendations ) {
    return
  }

  gp.App.View.autoInstantiate( gp.Social.FollowButtonView )

  // SignUp proccess logic
  ;(function(){
    var follows_ids = []
    var recommendationsView = $('.recommendations')

    var stepShowing = 0, signupSteps = [
      recommendationsView
    ]

    signupSteps[stepShowing].show()

    $document.on('click', '.signup-step .next-signup-step', function(){
      signupSteps[stepShowing].hide()
      signupSteps[++stepShowing].show()
    })
    $document.on('click', '.signup-step .prev-signup-step', function(){
      signupSteps[stepShowing].hide()
      signupSteps[--stepShowing].show()
    })
  })()
})()
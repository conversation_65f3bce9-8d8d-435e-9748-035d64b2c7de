$(function(){
  if( !gp.controller.profiles || !gp.action.edit ) return

  var ProfileFormView = gp.App.View.extend({
    className: 'user-info',
    formFor: 'profile',

    picUploader: null,

    events: {
      'submit #edit_profile': 'submit',
      'focusout .error': 'validate'
    },

    initialize: function(){
      _.bindAll(
        this,
        'render',
        'show',
        'submit',
        'enable',
        'disable',
        'validate',
        'loadAvatarUploader',
        'loadCoverUploader'
      )
    },

    render: function(){
      this.$form = this.$('#edit_profile')
      this.$submitButton = this.$('[type=submit]')
      this.loadAvatarUploader()
      this.loadCoverUploader()

      return this
    },

    show: function(){
      gp.App.View.prototype.show.call(this);
      this.$form.find('input[type="text"]:first').focus()
      return this
    },

    submit: function(e){
      if( !this.validate() ) {
        e.preventDefault()
        return false
      }
    },

    enable: function(){
      this.$submitButton.removeAttr('disabled')
    },

    disable: function(){
      this.$submitButton.attr('disabled', 'disabled')
    },

    validate: function(){
      var $form = this.$form;

      // Reset Form Errors
      $form.find('input.error, textarea.error, select.error')
        .removeClass('error')
          .parent().find('small.error').remove()

      // TODO @@ ML Allow the form to validate only one field when it changes

      var profileType = $form.data('profile_type')
      if (profileType == 'Social::Profile::User') {
        var model = new gp.App.UserProfile( $form.formParams()[this.formFor] )
      } else if (profileType == 'Social::Profile::Brand') {
        var model = new gp.App.BrandProfile( $form.formParams()[this.formFor] )
      }

      if( !model.isValid() ) {
        // Recursive function to apply errors on elements
        var addError = function(error, attr, name) {
          name = name+(attr ? '['+attr+']':'')
          if ( _.isString(error) ) {
            var $el = $form.find('[name="'+name+'"]')
            $el.addClass('error')
               .after('<small class="error">'+error+'<small>')
          } else if ( _.isObject(error) ) {
            _.each(error, function(e, a){
              addError(e, a, name)
            })
          }
        }

        var errors = model.validationError
        addError(errors, undefined, this.formFor)

        return false
      } else {
        return true
      }
    },

    loadAvatarUploader: function(){
      var that = this
      this.avatarUploader = new gp.Social.AvatarUploaderView({
        el: this.$('#avatar-uploader'),
        type: 'Social::Attachment::AvatarPicture'
      }).render()

      this.avatarUploader.on('upload:start', function(){
        that.disable()
      })

      this.avatarUploader.on('upload:complete', function(data){
        var attrName = 'profile[unpublished_avatar_id]'
        var $profileId = that.$form.find('[name="'+attrName+'"]')
        if ( !$profileId.length ) {
          $profileId = $('<input type="hidden" name="'+attrName+'">')
          $profileId.appendTo(that.$form)
        }
        $profileId.val(data.picture.id)
        that.enable()
      })
    },

    loadCoverUploader: function(){
      var that = this
      this.coverUploader = new gp.Social.AvatarUploaderView({
        el: this.$('#cover-uploader'),
        type: 'Social::Attachment::CoverPicture'
      }).render()

      this.coverUploader.on('upload:start', function(){
        that.disable()
      })

      this.coverUploader.on('upload:complete', function(data){
        var attrName = 'profile[unpublished_cover_id]'
        var $profileId = that.$form.find('[name="'+attrName+'"]')
        if ( !$profileId.length ) {
          $profileId = $('<input type="hidden" name="'+attrName+'">')
          $profileId.appendTo(that.$form)
        }
        $profileId.val(data.picture.id)
        that.enable()
      })
    }
  })

  var profileForm = gp.App.View.autoInstantiate( ProfileFormView )[0]

  $('.js-validate-aggregated-profile').each(function(i, el){
    var $el = $(el)
    var $input = $el.find('> input')
    var input = $input[0]
    var type = $el.attr('data-aggregated-type')
    var $msg = $el.find('.input-with-status-status')

    var validating = false

    function reset() {
      return $el.removeClass('error').removeClass('success')
    }

    var validate = _.debounce(function(){
      if( validating ) return
      if( input.checkValidity && !input.checkValidity() ) {
        reset().addClass('error')
        return
      }

      var val = $input.val()
      if( !val ) return reset()

      validating = true
      reset().addClass('loading')
      $msg.html(I18n.t('javascripts.social.profiles.edit.aggregated_profiles_statuses.loading'))

      gp.Helpers.externalAPIS[type].userExists(val)
        .done(function(){
          $el.addClass('success')
          $msg.html(I18n.t('javascripts.social.profiles.edit.aggregated_profiles_statuses.success'))
        })
        .fail(function(){
          $el.addClass('error')
          $msg.html(I18n.t('javascripts.social.profiles.edit.aggregated_profiles_statuses.error'))
        })
        .always(function(){
          validating = false
          $el.removeClass('loading')
          if( !$input.val() ) return reset()
        })
    }, 500)

    $input.on('input', validate)
  })
})
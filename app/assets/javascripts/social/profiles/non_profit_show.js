;if( gp.controller.profiles
 &&  gp.action.non_profit_show ) (function(){

  var userFollowButton = new gp.Social.FollowButtonView({
    el: $('.brand-info .main-follow')
  }).render()

  gp.App.View.autoInstantiate(gp.Social.FeedItemsListView)

  var $mobileBrandCover = $('.mobile-brand-cover')
  $mobileBrandCover.royalSlider({
    arrowsNav: false,
    slidesSpacing: 0,
    loopRewind: true
  })

  $('.brand-avatar-wrapper .square a').magnificPopup({ type:'image' });

  ;(function() {
    var $coverMask = $('.brand-bio .cover-mask')
    var $description = $('.description')

    if( !$coverMask.length || !$description.length ) return

    var coverMaskHeight = $coverMask.outerHeight()
    var descriptionHeight = $description.height()

    while(coverMaskHeight < descriptionHeight) {
      $coverMask.width($coverMask.outerWidth() + 10)
      coverMaskHeight = $coverMask.outerHeight()
      descriptionHeight = $description.height()
    }
  })()

})()

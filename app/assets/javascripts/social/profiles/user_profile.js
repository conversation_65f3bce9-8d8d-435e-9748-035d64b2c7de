gp.App.UserProfile = gp.App.Model.extend({
  validation: {
    first_name: function(attr) {
      if ( !attr ) return I18n.t('javascripts.partials.form.first_name_required')
      if ( (''+attr).length > 127 ) {
        return I18n.t('javascripts.partials.form.first_name_too_long')
      }
    },
    last_name: function(attr) {
      if ( !attr ) return I18n.t('javascripts.partials.form.last_name_required')
      if ( (''+attr).length > 127 ) {
        return I18n.t('javascripts.partials.form.last_name_too_long')
      }
    }
  }
})
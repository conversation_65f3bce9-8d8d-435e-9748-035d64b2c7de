;(function(){
  if( !gp.controller.profiles || !gp.action.user_show ) return

  $('a.create-whatsup').click(function() {
    gp.Views['partials/whatsups_form'].open()
  })

  $('.profile-header').hover(function() {
    $('.edit-button').show()
  }, function() {
    $('.edit-button').hide()
  })

  var userFollowButton = new gp.Social.FollowButtonView({
    el: $('.profile-stats .main-follow')
  }).render()

  gp.App.View.autoInstantiate(gp.Social.UsersRecommendationsView)
  gp.App.View.autoInstantiate(gp.Social.FeedItemsListView)

})()

;if( gp.controller.settings && gp.action.widgets ) (function(){
  // Select all on textarea on click
  $document.on('click', '.js-select-all-on-click', function(e){
    $(e.currentTarget).focus().select()
  })

  function widgetLogoUrl(size, color) {
    return '//s3.amazonaws.com/a01.gpstatic.com/logos-widgets/goodpeople_'+size+'px_'+color+'.png'
  }

  $('.widgets-widget').each(function(i, el){
    var $widget = $(el)
    var $size = $widget.find('.widgets-widget-code-selector select[name="size"]')
    var $color = $widget.find('.widgets-widget-code-selector select[name="color"]')

    var $preview = $widget.find('.widgets-widget-preview')
    var $img = $preview.find('img')
    var $code = $widget.find('.widgets-widget-code')

    $widget.on('change', 'select', function(){
      $img
        .attr('src', widgetLogoUrl($size.val(), $color.val()))
        .attr('width', $size.val())
      $code.val($.trim($preview.html()))
    })
  })
})();

// Products Widget
;if( gp.controller.settings && gp.action.widgets ) (function(){
  var $widget = $('.widgets-products-widget')

  var $preview = $widget.find('.widgets-widget-preview')
  var $code = $widget.find('.widgets-widget-code')

  var $iframe = $($.trim($code.val()))
  var $container = $('<div/>').append($iframe)

  var origSrc = $iframe.attr('src').split('?')
  var path = origSrc[0]
  var paramsObj = gp.Helpers.url.toObject(origSrc[1])

  function set(obj) {
    if( obj.width ) {
      $iframe.attr('width', obj.width)
      obj.width = undefined
    }
    if( obj.height ) {
      $iframe.attr('height', obj.height)
      obj.height = undefined
    }
    _.extend(paramsObj, obj)
    $iframe.attr('src', path + gp.Helpers.url.toParams(paramsObj))
    $preview.add($code).html($container.html())
  }

  $widget.on('change', '#products-widget-orientation, #products-widget-show', function(e){
    var $select = $(e.currentTarget)
    var newParams = {}
    newParams[$select.attr('name')] = $select.val()
    set(newParams)
  })

  var sizesParams = {
    vertical: {
      small: {
        limit: 4, width: 300, height: 340
      },
      medium: {
        limit: 4, width: 768, height: 200
      },
      large: {
        limit: 8, width: 768, height: 420
      }
    },
    horizontal: {
      small: {
        limit: 2, width: 300, height: 150
      },
      medium: {
        limit: 2, width: 400, height: 200
      },
      large: {
        limit: 6, width: 400, height: 580
      }
    }
  }
  $widget.on('change', '#products-widget-size', function(e){
    var size = $(e.currentTarget).val()
    var orientation = paramsObj.orientation || 'vertical'
    var newParams = _.extend({}, sizesParams[orientation][size])
    set(newParams)
  })
  $widget.on('change', '#products-widget-orientation', function(){
    $widget.find('#products-widget-size').trigger('change')
  })
})();
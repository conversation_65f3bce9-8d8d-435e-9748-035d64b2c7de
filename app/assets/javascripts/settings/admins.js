;(function(){
  if( !gp.controller.settings || !gp.action.admins ) return

  function initUsersSelector() {
    var $usersSelector = $('#users')
    var restrictionUsersUrl = $usersSelector.data('users-url')

    $usersSelector.select2({
      placeholder: 'Search by username...',
      multiple: true,
      minimumInputLength: 2,
      maximumSelectionSize: 1,
      ajax: {
        url: restrictionUsersUrl,
        data: function(term) { return {login: term} },
        results: function (data, page) {
          return { results: data }
        }
      }
    })
    return $usersSelector
  }

  function initRoleSelectors() {
    $('.role-selector').select2({minimumResultsForSearch: -1, width: '10em'})
                       .on('change', function(e) { $(this).parent().submit() } )
  }

  $('.remove').click(function(){
    $(this).closest('form').submit()
  })

  $('#add-administrator').submit(function(e){
    if(usersSelector.select2('val').length < 1){
      e.preventDefault()
      return false
    }
  })

  initUsersSelector()
  initRoleSelectors()

  $('#add-administrator button').prop('disabled', false)
})()

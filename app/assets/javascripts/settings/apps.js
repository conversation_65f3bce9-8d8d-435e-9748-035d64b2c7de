;(function(){
  if( !gp.controller.settings || !gp.action.apps ) return

  function submitAuthForm(authentication, form) {
    for (var attr_name in authentication) {
      var name = 'authentication[' + attr_name + ']'
      var value = authentication[attr_name]
      var attrField = '<input type="hidden" name="' + name + '" value="' + value + '" >'
      form.append(attrField)
    }

    form.submit()
  }

  function authorizeFacebook(callbacks) {
    gp.Helpers.authorizeFacebook(function(authentication) {
      if (authentication) {
        callbacks.accepted(authentication)
      } else {
        callbacks.canceled()
      }
    })
  }

  function authorizeTwitter(callbacks) {
    gp.Helpers.authorizeTwitter(function(authentication) {
      if (authentication) {
        callbacks.accepted(authentication)
      } else {
        callbacks.canceled()
      }
    })
  }

  $('#new_facebook_auth').find('button').on('click', function(e) {
    var btn = $(this)
    btn.prop('disabled', 'true')

    authorizeFacebook({
      accepted: function(auth) { submitAuthForm(auth, $('#new_facebook_auth')) },
      canceled: function() { btn.removeProp('disabled') }
    })

    e.preventDefault()
    return false
  })

  $('#destroy_facebook_auth').find('button').on('click', function(e) {
    $(this).attr('disabled', 'true')
    $('#destroy_facebook_auth').submit()
  })

  $('#new_twitter_auth').find('button').on('click', function(e) {
    var btn = $(this)
    btn.prop('disabled', 'true')

    authorizeTwitter({
      accepted: function(auth) { submitAuthForm(auth, $('#new_twitter_auth')) },
      canceled: function() { btn.removeProp('disabled') }
    })

    e.preventDefault()
    return false
  })

  $('#destroy_twitter_auth').find('button').on('click', function(e) {
    $(this).attr('disabled', 'true')
    $('#destroy_twitter_auth').submit()
  })
})()


;(function(){
  // This JS will be loaded on users NEW and CREATE
  // beause when theres an error on the form
  // it redirects to CREATE instead of NEW
  if( !gp.controller.users || (!gp.action['new'] && !gp.action['create']) ) {
    return
  }

  var UserFormView = gp.App.View.extend({
    className: 'user-info',
    formFor: 'social_user',

    picUploader: null,

    events: {
      'submit #new_social_user': 'submit'
    },

    initialize: function(){
      _.bindAll(
        this,
        'render',
        'show',
        'submit',
        'trimInputsValues',
        'enable',
        'disable',
        'validate',
        'addError',
        'validateRemote',
        'checkUniquenessOf',
        'loadAvatarUploader'
      )
    },

    render: function(){
      this.$form = this.$('#new_social_user')
      this.$submitButton = this.$('[type=submit]')

      this.$submitButton.removeAttr('disabled')
      this.$submitButton.text(I18n.t('javascripts.users.new.next'))

      this.loadAvatarUploader()

      return this
    },

    show: function(){
      gp.App.View.prototype.show.call(this);
      this.$form.find('input[type="text"]:first').focus()
      return this
    },

    submit: function(e){
      var that = this
      e.preventDefault()
      this.trimInputsValues()
      if( this.validate() ) {
        this.validateRemote(function() {
          that.$submitButton.text(I18n.t('javascripts.users.messages.saving'))
          that.$form[0].submit()
        })
      } else {
        this.$submitButton.removeAttr('disabled')
        this.$submitButton.text(I18n.t('javascripts.users.new.next'))
      }

      e.preventDefault()
      return false
    },

    _$inputs: null,
    trimInputsValues: function(){
      if( !this._$inputs ) {
        this._$inputs = this.$form.find('input[type="text"]')
      }
      this._$inputs.each(function(i, el){
        var $el = $(el)
        $el.val($.trim($el.val()))
      })
    },

    enable: function(){
      this.$submitButton.removeAttr('disabled')
    },

    disable: function(){
      this.$submitButton.attr('disabled', 'disabled')
    },

    validate: function(){
      var $form = this.$form;

      // Reset Form Errors
      $form.find('input.error, textarea.error, select.error')
        .removeClass('error')
          .parent().find('small.error').remove()

      // Submit Btn disable and let the user know the activity
      this.disable()
      this.$submitButton.text(I18n.t('javascripts.users.messages.validating'))

      // TODO @@ ML Allow the form to validate only one field when it changes
      var model = new gp.App.User( $form.formParams()[this.formFor] )
      if( !model.isValid() ) {
        var errors = model.validationError
        this.addError(errors, undefined, this.formFor)

        return false
      } else {
        return true
      }
    },

    // Recursive function to apply errors on elements
    addError: function(error, attr, name) {
      var $form = this.$form
      var that = this

      name = name+(attr ? '['+attr+']':'')
      if ( _.isString(error) ) {
        var $el = $form.find('[name="'+name+'"]')
        $el.addClass('error')
          .after('<small class="error">'+error+'<small>')
      } else if ( _.isObject(error) ) {
        _.each(error, function(e, a){
          that.addError(e, a, name)
        })
      }
    },

    validateRemote: function(onSuccess) {
      var that = this
      $.when(this.checkUniquenessOf('email'), this.checkUniquenessOf('login'))
        .done(function(emailCheck, loginCheck){

        var hasErrors = false

        if (!emailCheck[0].valid) {
          hasErrors = true
          that.addError(I18n.t('javascripts.users.messages.taken'), 'email', that.formFor)
        }
        if (!loginCheck[0].valid) {
          hasErrors = true
          that.addError(I18n.t('javascripts.users.messages.taken'), 'login', that.formFor)
        }

        if (!hasErrors) {
          onSuccess()
          // that.$submitButton.text(I18n.t('javascripts.users.messages.saving'))
          // that.$form[0].submit()
        } else {
          that.$submitButton.removeAttr('disabled')
          that.$submitButton.text(I18n.t('javascripts.users.new.next'))
        }
      })
    },

    checkUniquenessOf: function(attr) {
      var $form = this.$form;
      var value = $form.formParams()[this.formFor][attr]

      var data = {}
      data[attr] = value

      var url = $form.data('availability-path').replace('<attr>', attr)

      return $.ajax({
        url: url,
        type: 'GET',
        data: data
      })
    },

    loadAvatarUploader: function(){
      var that = this
      this.avatarUploader = new gp.Social.AvatarUploaderView({
        el: this.$('#avatar-uploader'),
        type: 'Social::Attachment::AvatarPicture'
      }).render()

      this.avatarUploader.on('upload:start', function(){
        that.disable()
      })

      this.avatarUploader.on('upload:complete', function(data){
        var $profileId = that.$form.find('[name="social_user[profile_attributes][unpublished_avatar_id]"]')
        if ( !$profileId.length ) {
          $profileId = $('<input type="hidden" name="social_user[profile_attributes][unpublished_avatar_id]">')
          $profileId.appendTo(that.$form)
        }
        $profileId.val(data.picture.id)
        that.enable()
      })
    }
  })

  var userForm = gp.App.View.autoInstantiate( UserFormView )[0]

  $(document).ready(function() {
    $('.button.red.right-arrow').prop('disabled',  false)
  })
})()
//  recommendations Popup
;(function(){ // So<PERSON>, lo tenia que hacer rapido y tire codigo y quedo ésto:
  var $element = null
  var $listWrapper = null
  var $list = null

  function getEl(userType){
    var d = new $.Deferred()
    if( $element ) {
      initEl(userType)
      d.resolve($element)
    } else {
      $.ajax({
        url: '/users/recommendations',
        cache: false,
        data: {
          xhr: true,
          type: userType
        }
      }).done(function(html){
        $element = $($.trim(html))
        $listWrapper = $element.find('.users-list-wrapper')
        $list = $listWrapper.find('.users-list')
        initEl(userType)
        d.resolve($element)
      })
    }
    return d.promise()
  }

  function initEl(userType){
    gp.App.View.autoInstantiate(gp.Social.FollowButtonView, $element)
    initScroll()
    initTypes()
    var selectedType = $element.find('.user-type.active').attr('data-type')
    if( selectedType && userType != selectedType ){
      $element.find('.user-type[data-type="'+userType+'"]').click()
    }
  }

  function initScroll(){
    var onScroll = _.debounce(function(e){
      var currY = $listWrapper.scrollTop()
      var postHeight = $listWrapper.height()
      var scrollHeight = $list.height()
      var scrollPercent = (currY / (scrollHeight - postHeight)) * 100

      if( scrollPercent >= 60 ) {
        loadNextPage()
      }
    }, 25)
    $listWrapper.on('scroll', onScroll)
  }

  function initTypes(){
    var $types = $element.find('.users-recommendations-types')
    $types.on('click', '.user-type:not(.active)', function(e){
      var $type = $(e.currentTarget)
      $type.add($types.find('.user-type.active')).toggleClass('active')
      $list.removeClass('last-page')
      $element
        .attr('data-type', $type.attr('data-type'))
        .attr('data-page', 0)
        .data('scrollEnded', false)

      loadNextPage()
    })
  }

  var loading = false, lastType = null
  function loadNextPage(){
    if( loading || $element.data('scrollEnded') ) return

    loading = true
    var page = +$element.attr('data-page') + 1
    var type = $element.attr('data-type')

    if( page <= 1 ) {
      $listWrapper.addClass('loading').removeClass('empty')
      $list.find('.'+gp.Social.FollowButtonView.prototype.className)
        .each(function(i, el){
          var view = $(el).data('view')
          if( view ) view.close()
        })
      $list.html('')
    }

    $.ajax({
      url: $element.attr('data-url'),
      type: 'get',
      data: {
        type: type,
        page: page,
        seed: $element.attr('data-seed'),
        xhr: true
      }
    }).done(function(html){
      html = $.trim(html)
      if( !html ) {
        $list.addClass('last-page')
        if( page === 1 ) {
          $listWrapper.addClass('empty')
        }
        $element.data('scrollEnded', true)
        return
      }
      var $items = $(html)
      $list.append($items)
      $items.find('.'+gp.Social.FollowButtonView.prototype.className)
        .each(function(i, el){
          var $el = $(el)
          var view = new gp.Social.FollowButtonView({ el: $el }).render()
          $el.data('view', view)
        })
      $element.attr('data-page', page)
    }).always(function(){
      loading = false
      $listWrapper.removeClass('loading')
    })
  }

  gp.Social.usersRecommendationsPopup = function(userType){
    if( !gp.user ) return

    gp.Helpers.loadingModal()

    getEl(userType).done(function($el){
      $.magnificPopup.close()
      $.magnificPopup.open({
        items: {
          src: $el
        },
        type: 'inline',
        mainClass: 'mfp-no-gutter mfp-fade',
        tLoading: I18n.t('js.v5.helpers.loadingModal.loading'),
        showCloseBtn: false
      }, 0)
    })
  }
})()

gp.Social.UsersRecommendationsView = gp.App.View.extend({
  className: 'users-recommendations-item',

  events: {
    'click .user-type:not(.active)': 'showUsers',
    'click .show-type': 'openFollowMore'
  },

  initialize: function(){
    _.bindAll(
      this,
      'render',
      'openFollowMore',
      'initSelectedType',
      'showUsers',
      'fetchType'
    )
  },

  render: function(){
    this.autoInstantiate(gp.Social.FollowButtonView)
    this.initSelectedType()

    return this
  },

  openFollowMore: function(e){
    var $el = $(e.currentTarget)
    gp.Social.usersRecommendationsPopup($el.attr('data-type'))
    e.preventDefault()
    return false
  },

  selectedType: null,
  $selectedType: null,
  $selectedUsersList: null,
  $selectedShowType: null,
  initSelectedType: function(){
    this.$selectedType = this.$('.user-type.active')
    this.selectedType = this.$selectedType.data('type')
    this.$selectedUsersList = this.$('.mini-users-wrapper.active')
    this.$selectedShowType = this.$('.show-type.active')
  },

  showUsers: function(e){
    if( this.$el.hasClass('loading') ) return
    var self = this
    var $type = $(e.currentTarget)
    var type = $type.data('type')

    var $showType = this.$('.show-type[data-type="'+type+'"]')
    var $usersList = this.$('.mini-users-wrapper[data-type="'+type+'"]')
    var $users = $usersList.find('.mini-user')

    if( !$users.length && !$usersList.hasClass('empty') ) {
      this.$el.addClass('loading')
      this.fetchType(type, $usersList).done(function(){
        self.$el.removeClass('loading')
        self.showUsers(e)
      })
    } else {
      this.$selectedType
        .add(this.$selectedUsersList)
        .add(this.$selectedShowType)
        .removeClass('active')

      this.selectedType = type
      this.$selectedType = $type.addClass('active')
      this.$selectedUsersList = $usersList.addClass('active')
      this.$selectedShowType = $showType.addClass('active')

      self.trigger('change:size')
    }
  },

  fetchType: function(type, $usersList){
    return $.ajax({
      url: '/users/recommendations',
      cache: false,
      data: {
        xhr: true,
        type: type,
        limit: 5,
        page: 1
      }
    }).done(function(html){
      html = $.trim(html)
      if( html ) {
        var $users = $(html)
        gp.App.View.autoInstantiate(gp.Social.FollowButtonView, $users)
        $usersList.append($users)
      } else {
        $usersList.addClass('empty')
      }
    }).fail(function(){
      $usersList.addClass('empty')
    })
  }
})

gp.App.User = gp.App.Model.extend({
  validation: {
    profile_attributes: {
      first_name: function(attr) {
        if ( !attr ) return I18n.t('javascripts.partials.form.first_name_required')
        if ( (''+attr).length > 127 ) {
          return I18n.t('javascripts.partials.form.first_name_too_long')
        }
      },
      last_name: function(attr) {
        if ( !attr ) return I18n.t('javascripts.partials.form.last_name_required')
        if ( (''+attr).length > 127 ) {
          return I18n.t('javascripts.partials.form.last_name_too_long')
        }
      }
    },
    email: function(attr) {
      if ( !attr ) return I18n.t('javascripts.partials.form.email_required')
      if ( !/^\S+@\S+$/i.test(attr) ) {
        return I18n.t('javascripts.partials.form.invalid_email')
      }
    },
    login: function(attr) {
      if ( !attr ) return I18n.t('javascripts.partials.form.username_required')
      if ( (''+attr).length < 3 ) {
        return I18n.t('javascripts.partials.form.username_too_short')
      }
      if ( (''+attr).length > 25 ) {
        return I18n.t('javascripts.partials.form.username_too_long')
      }
      if ( !/^[a-z][a-z0-9\-\.\_]{3,25}$/i.test(attr) ) {
        return I18n.t('javascripts.partials.form.invalid_username')
      }
    },
    password: function(attr, attrs) {
      if ( !attr ) return I18n.t('javascripts.partials.form.password_required')
      if ( (''+attr).length < 4 ) {
        return I18n.t('javascripts.partials.form.password_too_short')
      }
      if ( /\s/.test(attr) ) {
        return I18n.t('javascripts.partials.form.password_whitespaces')
      }
    },
    password_confirmation: function(attr, attrs) {
      if ( !attr || attr !== attrs.password ) {
        return I18n.t('javascripts.partials.form.password_not_match')
      }
    }
  }
})

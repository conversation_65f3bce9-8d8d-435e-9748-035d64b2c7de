/**
* gp.followings global object
* Meant to be used as a HUB for follow related events.
*/
;(function(){
  var root = this, gp = root.gp

  gp.followings = {
    follow: function(entity_id, basePath){
      var that = this
      var r = $.post('/'+basePath+'/'+entity_id+'/follow')
        .fail(function(){
          that.trigger('notfollowing:'+entity_id, entity_id, r)
          that.trigger('notfollowing', entity_id, r)
        })
      that.trigger('following:'+entity_id, entity_id, r)
      that.trigger('following', entity_id, r)
      return r
    },

    unfollow: function(entity_id, basePath){
      var that = this
      var r = $.post('/'+basePath+'/'+entity_id+'/unfollow')
        .fail(function(){
          that.trigger('following:'+entity_id, entity_id, r)
          that.trigger('following', entity_id, r)
        })
      that.trigger('notfollowing:'+entity_id, entity_id, r)
      that.trigger('notfollowing', entity_id, r)
      return r
    }
  }

  _.extend(gp.followings, Backbone.Events)
}).call(this)
/**
* gp.notifications Collection
*/
;(function(){
  if( !window.gp || !gp.user ) return

  var Notification = Backbone.Model.extend({
  })

  var Notifications = Backbone.Collection.extend({
    url: '/notifications',
    model: Notification,

    _unreadCount: 0,
    parse: function(r){
      if( this._unreadCount !== +r.unread_count ) {
        this._unreadCount = +r.unread_count
        this.trigger('change:unreadCount')
      }
      return r.notifications
    },

    unreadCount: function(){
      return this._unreadCount
    },

    readAll: function(){
      if( !this.unreadCount() ) {
        return true
      }

      return $.ajax({
        url: '/read_all_notifications',
        type: 'PUT'
      }).always(function(){
        gp.notifications.fetch({ reset: true })
      })
    }
  })

  gp.notifications = new Notifications()
  gp.notifications.enabled = true

  $(function(){
    var useNotifications = gp.notifications.enabled
    if (useNotifications) {
      gp.notifications.fetch({ reset: true })
    }
  })
})()
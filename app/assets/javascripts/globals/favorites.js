/**
* gp.favorites global object
* Meant to be used as a HUB for favorites/high-five related events.
*/
;(function(){
  var root = this, gp = root.gp

  function favoriteAction(action, url, type) {
    if( action !== 'favorite' && action !== 'unfavorite' ) return false

    var favoriting = (action === 'favorite' ? true : false)
    var unaction = favoriting ? 'unfavorite' : 'favorite'
    var eventModel = F.getEventModelForUrl(url)
    if( !eventModel ) return false

    F.trigger(action+'d:'+eventModel)
    var r = $.ajax({
      url: url,
      type: 'POST',
      data: {
        _method: favoriting ? 'create' : 'delete',
        favoritable_type: type
      }
    }).fail(function(){
      F.trigger(unaction+'d:'+eventModel)
    })
    return r
  }


  /**
  * Favorites object, both actions takes an url generated by the rails helper,
  * the end of the url has to match with :plural_model/:model_id/favorites
  * @param: url
  * @param: type
  */
  var F = gp.favorites = _.extend({
    favorite: function(){
      var args = ['favorite'].concat(Array.prototype.slice.call(arguments))
      return favoriteAction.apply(this, args)
    },

    unfavorite: function(){
      var args = ['unfavorite'].concat(Array.prototype.slice.call(arguments))
      return favoriteAction.apply(this, args)
    },

    /**
    * Reuturns an array of favorite, unfavorite event so it can be used to
    * listen the event for that model, i.e:
    *   this.listenTo(gp.favorites, 'favorited:'+ gp.favorites.getEventModelForUrl(url))
    */
    getEventModelForUrl: function(url){
      var match = url.match(/([a-z_-]+)\/([0-9a-z_-]+)\/favorites$/)
      if( !match ) return false
      return match[1]+':'+match[2]
    }
  }, Backbone.Events)

})()

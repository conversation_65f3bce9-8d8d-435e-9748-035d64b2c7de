;(function(){
  var $header = $('.mobile-header')

  // Mobile Menu
  ;(function(){
    var $mainMenu = $('.mobile-main-menu-wrapper')
    var mainMenu = $mainMenu.offcanvy().data('offcanvy')

    $header.on('click', '.h-btn-menu', $.proxy(mainMenu.show, mainMenu))

    var active = 'active'
    var $shopCats = $mainMenu.find('.shop-by-category')
    var $cats = $mainMenu.find('.mobile-categories-menu')

    $mainMenu.on('click', '.shop-by-category', function(e){
      $shopCats.add($cats).toggleClass(active)
      e.stopPropagation(); e.preventDefault()
      return false
    })

    var $catActive
    $mainMenu.on('click', '.category-item.with-submenu .category-item-title', function(e){
      var $el = $(e.currentTarget)
      $el = $el.add($el.parent())
      if( $catActive && $catActive.is($el) ) {
        $catActive.removeClass(active)
        $catActive = null
      } else {
        if( $catActive ) {
          $catActive.removeClass(active)
          $catActive = null
        }
        $el.addClass(active)
        $catActive = $el
      }
      e.stopPropagation(); e.preventDefault()
      return false
    })
  })()

  var overlayShowing = null
  var MobileOverlayMixin = {
    onResize: _.debounce(function(){
      if( $window.width() > 960 ) this.hide()
    }, 50),

    show: function(){
      if( overlayShowing ) overlayShowing.hide()
      overlayShowing = this
      $window.on('resize', this.onResize)
      $('html, body').add(this.$el.parent())
        .addClass('noscroll')
        .addClass('fullsize')
      return gp.App.View.prototype.show.apply(this, arguments)
    },

    hide: function(){
      if( overlayShowing == this ) overlayShowing = null
      $window.off('resize', this.onResize)
      $('html, body').add(this.$el.parent())
        .removeClass('noscroll')
        .removeClass('fullsize')
      return gp.App.View.prototype.hide.apply(this, arguments)
    }
  }

  // Login Button
  ;(function(){
    $header.on('click', '.h-btn-login', function(e){
      if( gp.Helpers.loginModal() ) {
        e.preventDefault()
        e.stopPropagation()
        return false
      }
    })
  })()

  // Notifications Button
  ;(function(){
    if( !gp.notifications ) return

    var $btn = $header.find('.h-btn-notifications')
    var $el = $($btn.attr('data-template'))
    $el = $($.trim($el.html()))

    var View = gp.headerNotificationsView.extend(MobileOverlayMixin).extend({
      show: function(){
        this.$el.on('click', this.hideOnClick)
        return MobileOverlayMixin.show.apply(this, arguments)
      },

      hideOnClick: function(e){
        var $el = $(e.currentTarget)
        if( $el[0] === e.target
        ||( $el.hasClass('empty') && $el.find(e.target) ) ) {
          this.hide()
          this.$el.off('click', this.hideOnClick)
        }
      }
    })

    var view = new View({
      collection: gp.notifications,
      el: $el,
      $count: $btn.find('.h-btn-count')
    }).hide()
    $el.insertAfter($('header'))
    $btn.on('click', view.toggle)
  })()

  // Cart Button
  ;(function(){
    if( !gp.cart ) return

    var $btn = $header.find('.h-btn-cart')
    var $el = $($btn.attr('data-template'))
    $el = $($.trim($el.html()))
    var View = gp.mobileCartInfoView.extend(MobileOverlayMixin)
    var view = gp.mobileCartInfo = new View({
     collection: gp.cart,
     el: $el
    }).hide()
    $el.insertAfter($('header'))

    var $count = $btn.find('.h-btn-count')
    var onCountChange = _.debounce(function(){
      var count = gp.cart.cartCount()
      $count.attr('data-count', count).html(count)
    }, 100)

    gp.cart.on('cart:change', onCountChange)

    $btn.on('click', view.toggle)
  })()
})()
;(function(){
  var $header = $('header')

  // Search Bar
  ;(function(){
    var $search = $header.find('.default-header .h-search')
    $search.on('focusin', 'input', function(){ $search.addClass('active') })
    $search.on('focusout', 'input', function(){ $search.removeClass('active') })
    $search.on('click', '.h-search-icon', function(){ $search.find('input').focus() })
  })()

  // Search Bar Filter
  ;(function(){
    var $el = $header.find('.default-header .h-search .h-search-filter')
    var $filterValue = $el.find('#search-filter')
    var $dropdown = $el.find('.h-search-filter-dropdown')
    var $dropdownSelected = $el.find('.h-search-filter-selected')
    function show(){
      $el.addClass('active')
      _.defer(function(){
        $document.on('click', outsideClick)
      })
    }
    function hide(){
      $document.off('click', outsideClick)
      $el.removeClass('active')
    }
    function outsideClick(e){
      var $target = $(e.target)
      if( !$dropdown.is($target) && !$dropdown.has($target).length ) hide()
    }
    var $searchInput = $header.find('.default-header .h-search .h-search-input')
    function fixWidth(){
      $searchInput.css({ marginLeft: $el.outerWidth() })
    }
    $header.on('click', '.default-header .h-search .h-search-filter:not(.active)', show)
    $header.on('click', '.default-header .h-search .h-search-filter.active', hide)
    $window.on('resize', _.debounce(fixWidth, 100))

    $dropdown.on('click', '.drop-item', function(e){
      var $item = $(e.currentTarget)
      var val = $item.attr('data-value')
      if( val === 'all' ) {
        $filterValue.val(null).prop('disabled', true)
      } else {
        $filterValue.val(val).prop('disabled', false)
      }
      $dropdownSelected.html($item.html())
      fixWidth()
      $searchInput.find('input').focus()
    })
  })()

  // Login Dropdown
  ;(function(){
    var $dropdown = $header.find('.default-header .login-dropdown')
    var $overlay
    function hide() {
      $overlay.hide()
      $dropdown.hide()
    }
    $header.on('click', '.h-btn-login', function(e){
      e.stopPropagation()
      e.preventDefault()
      gp.Helpers.signupModal.setShowed()
      if( !$overlay ){
        $overlay = $('<div class="login-dropdown-overlay" />')
        $overlay.insertBefore($header)
      }
      $overlay.show()
      $dropdown.show()
      $dropdown.find('input[type="text"]').first().focus()
      $overlay.one('click', hide)
      return false
    })
  })()

  // Whatsup Form
  var WhatsupsForm = gp.Social.WhatsupsFormView
  var whatsUpForm = new WhatsupsForm({
    el: '#'+WhatsupsForm.prototype.id
  })

  // Cart Button
  ;(function(){
    var CartInfo = gp.CartInfoView
    gp.cartInfo = new CartInfo({
      el: '#'+CartInfo.prototype.id
    })

    var $cartItemsCount = $header.find('.h-btn-cart .h-btn-count')

    var updateCartItemCount = function(){
      $cartItemsCount.html( gp.cart.cartCount() )
    }
    updateCartItemCount()
    gp.cart.on('cart:change', updateCartItemCount)
  })()

  // Header Dropdowns
  ;(function(){
    $header.find('.h-dropdown-wrapper').each(function(i, el){
      var $wrapper = $(el), $dropdown = $wrapper.find('.h-dropdown')
      $wrapper.on('click', '.h-dropdown-btn', function(e){
        if( !$wrapper.hasClass('active') &&
            !$wrapper.hasClass('loading') &&
            !$wrapper.hasClass('empty') ) {
          $wrapper.add($wrapper.find('.h-dropdown-btn'))
            .addClass('active')
          _.defer(function(){
            $document.one('click', outsideDropdownClick)
          })
        }
      })
      function outsideDropdownClick(e){
        var $target = $(e.target)
        if( !$dropdown.is($target) && !$dropdown.has($target).length ){
          $wrapper.add($wrapper.find('.h-dropdown-btn'))
            .removeClass('active')
        } else {
          $document.one('click', outsideDropdownClick)
        }
      }
    })
  })()

  // Notifications View
  ;(function(){
    if( !gp.notifications ) return
    var $el = $header.find('.h-btn-notifications-wrapper')
    new gp.headerNotificationsView({
      collection: gp.notifications,
      el: $el,
      $count: $el.find('.h-btn-count')
    })
  })()

  // Network Selector
  ;(function(){
    var $selector = $header.find('.network-selector')
    var $dropdown = $header.find('.network-selector-dropdown')

    $selector.on('click', '.network-selector-selected', function(){
      if(!$selector.hasClass('active')){
        $selector.addClass('active')
        _.defer(function(){
          $document.one('click', outsideNetworkClick)
        })
      }
    })

    function outsideNetworkClick(e){
      var $target = $(e.target)
      if(!$dropdown.is($target) && !$dropdown.has($target).length){
        $selector.removeClass('active')
      }else{
        $document.one('click', outsideNetworkClick)
      }
    }

    $header.on('click', '.network-selector [data-network]', function(e){
      var network = $(e.currentTarget).attr('data-network')
      cookie.set('network', network)
    })
  })()

  // Marketing Communications
  ;(function(){
    var $freeShipping = $header.find('.free-shipping')
    var $monthlyPayments = $header.find('.monthly-payments')

    function toggleCommunications() {
      $freeShipping.show().delay(5000).fadeOut(600).delay(5000).fadeIn(600)
      $monthlyPayments.hide().delay(5000).fadeIn(600).delay(5000).fadeOut(600, toggleCommunications)
    }

    $(document).ready(function() {
      toggleCommunications()
    })
  })()
})()

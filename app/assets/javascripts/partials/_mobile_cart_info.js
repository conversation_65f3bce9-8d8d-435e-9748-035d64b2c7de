gp.mobileCartInfoView = gp.App.View.extend({
  itemTemplate: null,
  $list: null,

  events: {
    'click .mobile-cart-info-list-item .remove': 'removeItem',
    'click .mobile-cart-info-actions .continue': 'hide',
    'click .mobile-cart-info-actions .checkout': 'goToCheckout',
    'click': 'onClick'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'onC',
      'reset',
      'add',
      'remove',
      'update',
      'removeItem',
      'updateTotal',
      'updateQuantities',
      'onClick',
      'activateDropQCartFlag',
      'goToCheckout'
    )

    this.$list = this.$('.mobile-cart-info-list')
    this.itemTemplate = this.template('mobile-cart-info-item')

    this.updateDebounced = _.debounce(this.update, 10)

    this.onC('reset', this.reset)
    this.onC('add', this.add)
    this.onC('remove', this.remove)
    this.onC('cart:change', this.updateDebounced)

    this.reset()
    this.$el.removeClass('loading')
  },

  onC: function(event, callback){
    return this.listenTo(this.collection, event, callback)
  },

  reset: function(){
    var that = this
    this.$list.html('')
    this.collection.each(function(n){ that.add(n) })
    this.updateTotal()
  },

  add: function(m){
    var obj = m.toJSON()

    var attrs = []
    if( obj.properties.color && obj.properties.color.name ){
      attrs.push(I18n.t('color.'+obj.properties.color.name))
    }
    _.each([
      'size',
      'dimensions',
      'hardness',
      'length',
      'material'
    ], function(v){
      if( obj.properties[v] ){
        attrs.push(I18n.t('javascripts.partials.cart_info.'+v)+': '+obj.properties[v])
      }
    })
    obj.attrs = attrs.join(' / ')
    obj.quantity = this.collection.cartCountOf(m)
    obj.price = gp.cart.cartTotalOf(m)
    var pic = m.picture()
    obj.thumb = pic && pic.thumb

    var $item = this.itemTemplate(obj)
    // $item.html(gp.Helpers.string.unscapeHTML($item.html()))
    this.$list.append($item)
    this.$el.removeClass('empty')
  },

  remove: function(m){
    this.$list.find('[data-item-id="'+m.id+'"]').remove()
  },

  update: function(){
    this.updateTotal()
    this.updateQuantities()
  },

  removeItem: function(e){
    var $el = $(e.currentTarget).parent()
    var model = this.collection.get($el.attr('data-item-id'))
    this.collection.remove(model)
  },

  $total: null,
  updateTotal: function(){
    if( !this.$total ){
      this.$total = this.$('.mobile-cart-info-total .value')
    }
    this.$total.html(gp.cart.cartTotal())
  },

  updateQuantities: function(){
    var that = this
    _.each(this.collection.items, function(item){
      var $item = that.$list.find('[data-item-id="'+item.variant_id+'"]')
      $item.find('.qty .val').html(item.quantity)
    })
  },

  onClick: function(e){
    if( e.currentTarget === e.target ) {
      this.hide()
    }
  },

  activateDropQCartFlag: function(){
    if( cookie.enabled() ) {
      cookie.set('remove_qcart', true)
    }
  },

  goToCheckout: function(e){
    e.preventDefault()
    e.stopPropagation()
    if( gp.cart.items.length > 0 ){
      this.activateDropQCartFlag()
      $(e.currentTarget)
        .addClass('disabled')
        .html(I18n.t('javascripts.partials.cart_info.going_to_checkout'))
      this.showLoader()
      $('#go-to-checkout-form').submit()
    } else {
      return false
    }
  }
})

gp.CartInfoView = gp.App.View.extend({
  id: 'cart-info-modal',

  events: {
    'open':                               'render',
    'change .cart-item .quantity .val':     'changeItemQuantity',
    'click .cart-item .remove-item':      'removeItemCart',
    'click .close, .continue-shopping':   'close',
    'submit #go-to-checkout-form':        'goToCheckout'
  },

  resetDebounced: null,
  initialize: function(){
    _.bindAll(
      this,
      'render',
      'onResize',
      'changeItemQuantity',
      'removeItemCart',
      'close',
      'goToCheckout'
    )

    this.resetDebounced = _.debounce(this.reset, 10)
  },

  render: function(){
    if( this.rendered ) return
    var that = this

    this.$modal = $('#cart-info-modal')
    this.$itemTemplate = this.template('cart-item')
    this.$title = this.$el.find('.products')
    this.$count = this.$el.find('.count-modal')
    this.$list = this.$el.find('.cart-items')
    this.$checkoutBtn = this.$el.find('.go-to-checkout')

    this.reset()

    this.updateTotalItems()
    var itemsCount = gp.cart.cartCount()
    this.$title.html(I18n.t('javascripts.partials.cart_info.items', { count: +(itemsCount) })+')')

    $window.on('resize', this.onResize)
    this.listenTo(gp.cart, 'cart:change', this.resetDebounced)

    this.rendered = true
    return this
  },

  onResize: _.debounce(function(){
    if( !this.close ) return
    if( $window.width() < 960 ) this.close()
  }, 50),

  setEmpty: function(){
    this.$el.addClass('empty')
    this.$checkoutBtn.prop('disabled', true)
  },

  unsetEmpty: function(){
    this.$el.removeClass('empty')
    this.$checkoutBtn.prop('disabled', false)
  },

  close: function(){
    $window.off('resize', this.onResize)
    this.$modal.foundation('reveal', 'close')
  },

  reset: function(){
    var that = this
    if( gp.cart.length < 1 ) {
      this.setEmpty()
    } else {
      this.$list.find('.cart-item').remove()
      this.unsetEmpty()
      var $items = []
      gp.cart.each(function(model, index){
        var v = model.toJSON()
        var p = v.product
        var pic = model.picture()
        var $item = that.$itemTemplate({
          id: v.id,
          thumb: (pic && pic.thumb),
          regular_price: p.regular_price,
          sale_price: p.sale_price,
          on_sale: model.onSale(),
          color: (v.properties.color && v.properties.color.name),
          quantity: gp.cart.cartCountOf(model),
          size:  v.properties.size,
          title: p.title,
          total: gp.cart.cartTotalOf(model),
          currency_symbol: gp.currencyFormat,
          link: v.url,
          manufacturer: p.manufacturer,
          properties: v.properties
        })
        $item.find('.quantity .val').inputIncrementor({
          min: (+v.quantity) < 1 ? 0 : 1,
          max: (+v.quantity)
        })
        $item.attr('data-item-id', v.id)

        $items.push($item)
      })
      this.$list.append($items)
      this.hardcodearMsgEnvioGratis()
      this.updateTotalItems()
    }
  },

  removeItemCart: function(e){
    var $el = $(e.currentTarget).parents('.cart-item')
    var variant_id = $el.attr('data-item-id')
    gp.cart.remove(gp.cart.get(variant_id))
    $el.remove()
    if( gp.cart.length < 1 ){
      this.setEmpty()
    }
    this.updateTotalItems()
  },

  endCheckoutButtonLoading: function(){
    this.$checkoutBtn.attr('disabled', false)
  },

  startCheckoutButtonLoading: function(){
    this.$checkoutBtn.attr('disabled', true)
  },

  goToCheckout: function(e){
    if( gp.cart.items.length > 0 ){
      this.showLoader()
      var $form = $(e.currentTarget)
      var $btn = $form.find('[type="submit"]')
      this.activateDropQCartFlag()
      $btn.attr('disabled', 'disabled')
        .html(I18n.t('javascripts.partials.cart_info.going_to_checkout'))
    } else {
      e.preventDefault()
      e.stopPropagation()
      return false
    }
  },

  activateDropQCartFlag: function(){
    if( cookie.enabled() ) {
      cookie.set('remove_qcart', true)
    }
  },

  changeItemQuantity: function(e){
    var $input = $(e.currentTarget)
    var $item = $input.parents('.cart-item')

    gp.cart.cartUpdate({
      quantity: +$input.val(),
      variant_id: +$item.attr('data-item-id')
    })
  },

  hardcodearMsgEnvioGratis: function(){
    var that = this
    if( !gp.cart.length ) return
    var variants = gp.cart.filter(function(v){
      var attrs = v.attributes.product
      return (!v.onSale() && attrs.shop && attrs.shop.id == 1)
    })
    if( !variants.length ) return
    var total = _.reduce(variants, function(memo, v){
      return memo + gp.cart.cartTotalOf(v)
    }, 0)

    if( total < 999 ) return
    _.each(variants, function(v){
      var $item = that.$('#variant-'+v.id)
      if( !$item.length ) return
      $item.find('.msg')
        .html('Producto con envío gratis.').show()
    })
  },

  updateTotalItems: function(){
    var count = gp.cart.cartCount()
    this.$count.html(count)
  }
});

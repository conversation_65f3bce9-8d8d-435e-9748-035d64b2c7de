gp.headerNotificationsView = gp.App.View.extend({
  itemTemplate: null,
  $list: null,
  $count: null,

  events: {
    'click .read-all': 'readAll'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'onC',
      'updateCount',
      'reset',
      'add',
      'readAll'
    )

    this.$count = options.$count
    this.$list = this.$('.notifications-items-list')
    this.itemTemplate = this.template('notifications-item')

    this.onC('change:unreadCount', this.updateCount)
    this.onC('reset', this.reset)
    this.onC('add', this.add)

    this.reset()
    this.$el.removeClass('loading')
  },

  onC: function(event, callback){
    return this.listenTo(this.collection, event, callback)
  },

  updateCount: function(){
    var count = this.collection.unreadCount()
    if( count ) {
      this.$count.attr('data-count', count).html(count).show()
    } else {
      this.$count.hide()
    }
  },

  reset: function(){
    var that = this
    this.$list.find('.notifications-item').remove()
    this.collection.each(function(n){ that.add(n) })
    this.updateCount()
  },

  add: function(n){
    var obj = n.toJSON()
    obj.extraClass = obj.read ? '' : 'unread'
    var $notif = this.itemTemplate(obj)
    var $text = $notif.find('.text')
    $notif.html(gp.Helpers.string.unscapeHTML($notif.html()))
    this.$list.append($notif)
    this.$el.removeClass('empty')
  },

  readAll: function(){
    this.$count.hide()
    this.collection.readAll()
  }
})
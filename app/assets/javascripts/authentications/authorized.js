;(function(){
  $(document).ready(function() {
    if ($(document.body)[0].id != 'authentications-authorized') return

    function toUnderscores(string) {
      return string.replace(/([A-Z])/g, function($1) { return "_" + $1.toLowerCase(); })
    }

    function objectToUnderscores(object) {
      var result = {}

      $.each(object, function(a) {
        result[toUnderscores(a)] = object[a]
      })

      return result
    }

    if (window.opener.gp.Helpers.authorizeTwitter.twitterAuthCallback) {
      var auth = objectToUnderscores($('#authentication').data())
      window.opener.gp.Helpers.authorizeTwitter.twitterAuthCallback(auth)
    }
  })
})()

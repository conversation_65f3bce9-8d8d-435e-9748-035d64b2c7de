.mini-user {
  margin-bottom: 14px;

  .hexagon,
  .square {
    margin-right: 9px;
    float: left;
  }

  .name {
    margin: 0;
    padding-bottom: 3px;
    font-size: 15px;
    font-weight: 500;
    line-height: 16px;
  }

  .bio {
    @include multiline-ellipsis(
      $font-size: 10px,
      $line-height: 1.2,
      $lines-to-show: 2
    );
    position: relative;
    padding: 0 5px;
    color: #CD4534;
    margin: 0;
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 5%;
      width: 1px;
      height: 19px;
      background: #CD4534;
    }
  }

  &.sport {
    p {
      line-height: 3;
    }
  }
}
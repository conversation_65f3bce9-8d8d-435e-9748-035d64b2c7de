.users-recommendations-item {
  position: relative;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  hr {
    margin-top: 1.25em;
    margin-bottom: 0.35em;
  }
  &.loading {
    .loader { display: block; }
  }
  .loader {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: image-url('loaders/spinner-small.gif') no-repeat center center;
    background-color: white;
    background-color: rgba(white, 0.7);
  }
  .mini-users-wrapper {
    overflow: hidden;
    display: none;
    padding: 14px 14px 0;
    height: 324px;
    &.active {
      display: block;
    }
    &.empty {
      .no-recommendations { display: block; }
    }
  }
  .no-recommendations {
    display: none;
    padding: 10.8em 0;
    text-align: center;
    font-weight: bold;
  }
  .users-recommendations-types-show {
    position: relative;
    border-top: 1px solid #D9D9D9;
    .show-type {
      display: none;
      padding: 0 30px 0 1em;
      line-height: 3.8em;
      font-size: 14px;
      text-transform: uppercase;
      cursor: pointer;
      @include transition(background-color .15s);
      &:hover {
        background-color: #F7F7F7;
      }
      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 40px;
        font-weight: bold;
        background-repeat: no-repeat;
        background-position: center center;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMzJweCIgaGVpZ2h0PSIzMnB4IiB2aWV3Qm94PSIwIDAgMzIgMzIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDMyIDMyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48c3R5bGU+LnN0eWxlMHtmaWxsOgkjNEM0QzREO308L3N0eWxlPjxwb2x5Z29uIHBvaW50cz0iMjAuNCwxNCAxOS40LDE0IDE5LjQsMTMgMTguNCwxMyAxOC40LDEyIDE3LjQsMTIgMTcuNCwxMSAxNi40LDExIDE2LjQsMTAgMTUuNCwxMCAxNS40LDkgMTQuNCw5IDE0LjQsOCAxMi40LDggMTIuNCwxMCAxMy40LDEwIDEzLjQsMTEgMTQuNCwxMSAxNC40LDEyIDE1LjQsMTIgMTUuNCwxMyAxNi40LDEzIDE2LjQsMTQgMTcuNCwxNCAxNy40LDE1IDE3LjQsMTYgMTYuNCwxNiAxNi40LDE3IDE1LjQsMTcgMTUuNCwxOCAxNC40LDE4IDE0LjQsMTkgMTMuNCwxOSAxMy40LDIwIDEyLjQsMjAgMTIuNCwyMiAxNC40LDIyIDE0LjQsMjEgMTUuNCwyMSAxNS40LDIwIDE2LjQsMjAgMTYuNCwxOSAxNy40LDE5IDE3LjQsMTggMTguNCwxOCAxOC40LDE3IDE5LjQsMTcgMTkuNCwxNiAyMC40LDE2IiBjbGFzcz0ic3R5bGUwIi8+PC9zdmc+);
      }
      &.active {
        display: block;
      }
    }
  }
}

.users-recommendations {
  position: relative;
  margin: 0 auto;
  background-color: white;
  width: auto;
  max-width: 810px;
  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15%;
    pointer-events: none;
    @include linear-gradient(rgba(255,255,255, 0), white);
  }
  .users-list-wrapper {
    position: relative;
    overflow: auto;
    padding-top: 0.8em;
    height: 342px;
    &.loading {
      background: image-url('loaders/spinner-small.gif') no-repeat center center;
      .users-list { display: none; }
    }
    &.empty {
      .empty-msg { display: block; }
    }
    .empty-msg {
      display: none;
      margin: 0;
      line-height: 12.7em;
      text-align: center;
    }
  }
  .users-list {
    padding-bottom: 20px;
    margin-bottom: 20px;
    background: image-url('loaders/spinner-small.gif') no-repeat center bottom;
    &.last-page {
      padding-bottom: 0;
      margin-bottom: 0;
      background: none;
    }
  }
  .mini-user {
    float: left;
    width: 100%;
    padding-right: 0.8em;
    padding-left: 0.8em;
    @media #{$from-medium} {
      width: 50%;
      padding-right: 1.5%;
      padding-left: 1.5%;
    }
  }
}

.users-recommendations-types {
  border-bottom: 1px solid #D9D9D9;
  &.with-1 {
    .user-type { width: 100%; }
  }
  &.with-2 {
    .user-type { width: 50%; }
  }
  &.with-3 {
    .user-type { width: 33%; }
    .user-type:last-child { width: 34%; }
  }
  .user-type {
    display: block;
    float: left;
    color: #7c7c7c;
    text-transform: uppercase;
    cursor: pointer;
    line-height: 3.3em;
    text-align: center;
    border-bottom: 2px solid transparent;
    @include transition(border .3s);
    span {
      font-size: 14px;
      position: relative;
      top: 2px;
    }
    &:hover, &.active {
      color: $gris;
    }
    &.active {
      font-weight: bold;
      border-bottom: 2px solid #D9D9D9;
    }
    &:hover {
      border-bottom: 2px solid #D9D9D9;
    }
  }
}
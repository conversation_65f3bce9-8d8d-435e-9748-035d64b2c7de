.signup-modal {
  $main-width: 430px;

  position: relative;
  max-width: $main-width;
  margin: 0 auto;
  font-size: $em-base;
  border-radius: 4px;
  box-shadow: 0 0 20px rgba(50,50,50,0.2);
  .mfp-close {
    top: 6px;
    color: #565656;
  }
  .logo {
    @include unselectable;
    margin: 0 auto 1.2em;
    max-width: 258px;
    padding-top: 14%;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: image-url('logos/isologo-dark-258.png');
    @media #{$retina} {
      background-size: 256px;
      background-image: image-url('logos/isologo-dark-516.png');
    }
  }
  .sign-up-header {
    height: emCalc(8px);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: lighten($gris-claro, 5%);
    box-shadow: inset 0 -2px 5px 1px darken($gris-claro, 5%);
  }
  .sign-up {
    padding: 1.5em 3em;
    background-color: $blanco-oscuro;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    @media (max-width: $main-width) {
      padding: 1em 1.3em;
    }
    .sign-up-message {
      margin-bottom: 1.3em;
      font-size: inherit;
      color: $gris;
      font-weight: bold;
      text-shadow: 1px 1px 0px white;
      line-height: emCalc(24px);
      text-align: center;
      @include unselectable;
    }
    .button {
      width: 100%;
      text-align: center;
      &.facebook { margin-bottom: emCalc(12px); }
      @media #{$after-small} {
        width: 47%;
        &.twitter { float: right; }
        &.facebook { margin-bottom: 0; }
      }
    }
    .sign-up-divider {
      margin: 2.6em 0;
      height: 1px;
      text-align: center;
      box-shadow: 0 1px 1px 0 white;
      color: #999;
      background-color: #b6b6b6;
      .sign-up-email {
        position: relative;
        top: -11px;
        padding: 0 1em;
        font-size: 1.2em;
        background-color: $blanco-oscuro;
      }
    }
    #partial_signup_email {
      margin-bottom: 1em;
    }
    .terms-and-policy-check {
      font-size: 0.70em;
      color: $gris;
      margin: 1.2em 0 0 0;
      a {
        font-weight: 700;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    form {
      .button {
        width: 100%;
      }
    }
  }
  .login-message {
    text-align: center;
    margin: 1em 0;
    font-weight: bold;
    cursor: pointer;
  }
}
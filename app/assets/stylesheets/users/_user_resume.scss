.user-resume {
  position: relative;
  margin-bottom: 2.5em;
  padding: 1em;
  border-radius: 4px;
  background-color: #f6f6f6;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);
  text-align: center;
  .user-resume-avatar {
    position: relative;
    width: 130px;
    margin: 0 auto 7px;
  }
  .feed-avatar {
    width: 100%;
    height: auto;
    padding-top: 100%;
  }
  .user-resume-name {
    margin: 0;
    text-shadow: rgba(0, 0, 0, 0.3) 1px 1px 0;
    a {
      color: $gris;
    }
  }
  .user-resume-login {
    margin: 0;
    font-weight: bold;
    font-size: 12px;
  }
  .user-resume-bio {
    @include multiline-ellipsis($font-size: 12px, $line-height: 1.6, $lines-to-show: 5);
    margin: 0 0 10px;
    text-shadow: rgba(black, 0.1) 1px 1px 0;
    color: #888;
  }
}
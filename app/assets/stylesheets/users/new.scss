.signup-page {
  background-image: none;

  footer {
    @media #{$before-medium} {
      margin-top: 0;
    }
  }

  #subheader {
    display: none;
    margin: 0;
    height: 200px;
    padding-top: 30px;
    border-bottom: 1px solid $blanco;
    background-color: #C6C6C6;
  }

  .signup-step {
    margin-top: 2em;
    margin-bottom: 2em;
    max-width: 768px;
    border: 1px solid darken($blanco, 10%);
    border-top: none;
    .signup-step-top {
      position: relative;
      background-color: darken($blanco, 5%);
      .state {
        width: 100%;
        @include unselectable;
      }
    }
    .signup-step-content, .signup-step-bottom {
      padding-top: $column-gutter / 2;
      padding-bottom: $column-gutter / 2;
    }
    .signup-step-content {
      border-bottom: 1px solid darken($blanco, 10%);
      background-color: $blanco;
      @include linear-gradient($blanco, darken($blanco, 2%));
    }
    .signup-step-bottom {
      border-top: 1px solid darken($blanco, 2%);
      background-color: white;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius:  4px;
      @media #{$before-medium} {
        border-bottom-right-radius: 0;
        border-bottom-left-radius:  0;
      }
      .button.right-arrow {
        float: right;
      }
    }
    .next-signup-step {
      float: right;
    }
    &.user-info {
      @extend %user-info-step;
    }
    &.recommendations {
      @extend %recommendations-step;
    }
    &.invite-friends {
      @extend %invite-friends-step;
    }
  }
}

%user-info-step {
  position: relative;
  #avatar {
    cursor: pointer;
    position: relative;
    width: 100%;
    height: 150px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: darken($blanco, 5%);
    input[type="file"] {
      z-index: 5;
      cursor: pointer;
    }
    .avatar-loader {
      display: none;
      z-index: 10;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.7);
    }
    .avatar-thumb {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: darken($blanco, 5%);
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }
    #choose-photo {
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      font-size: 12px;
      position: absolute;
      left: 5px;
      right: 5px;
      bottom: 5px;
      text-align: center;
      padding: 5px;
      display: none;
    }
  }
  .profile-form {
    border-left: 1px solid darken($blanco, 10%);
  }
  textarea {
    resize: none;
  }
  label {
    display: none;
  }
}

%recommendations-step {
  .block {
    margin-bottom: 1em;
    border-radius: 3px;
    border: 1px solid darken($blanco, 10%);
    .block-title {
      padding: 20px 15px;
      @include linear-gradient($blanco, #E7E8E9);
      h6 {
        margin: 0;
        text-transform: uppercase;
      }
    }
    .block-list {
      padding: 15px;
      overflow: auto;
      max-height: 225px;
      box-shadow: inset 0px 1px 3px #999;
      background-color: $blanco;
      .sport {
        clear: both;
      }
    }
  }
}

%invite-friends-step {
  .title {
    padding-bottom: 1em;
    h3 {
      text-align: center;
    }
    p {
      margin-bottom: 0.5em;
    }
  }
  .block {
    border-top: 1px solid darken($blanco, 10%);
    padding-top: 1.5em;
    .invite-facebook {
      margin-bottom: 2em;
    }
    .invite-email {
    }
  }
}
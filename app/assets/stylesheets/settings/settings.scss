.settings-page {
  %menu-links {
    color: lighten($rojo, 10%);
    text-decoration: none;
    span {
      background-position: -7px 0px;
    }
  }

  %dark-profile-box {
    background-color: #efefef;
    border: 1px solid lighten(#b3b3b3, 8%);
    border-radius: 4px;
    box-shadow: inset 0px 0px 3px 1px #ffffff;
  }

  %dark-profile-divider {
    height: 1px;
    background-color: #cdcdcd;
    box-shadow: 0 1px 2px 0.5px #ffffff;
  }

  .user-box {
    margin-bottom: 22px;
    padding: 0.8em;
    @include profile-box;
    .user-avatar {
      float: left;
      margin: 0 auto;
      .settings-avatar {
        &.hexagon {
          @include hexagon(2.9em);
          position: relative;
          top: 2px;
        }
        &.square {
          @include square(3.5em);
          margin-bottom: 1px;
        }
      }
    }
    .user-name {
      float: left;
      padding-left: 1em;
      font-size: 0.9em;
      h5 {
        margin: 0.7em 0 0 0;
        font-size: 1em;
        line-height: 1em;
      }
      a { font-size: 0.875em; }
    }
  }

  .menu-box {
    @include profile-box;
    a {
      padding-right: 20px;
      padding-left: 15px;
      line-height: 50px;
      display: block;
      &.first_link {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }
      &.last_link {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    a.hover, a:not(.button):hover {
      @extend %menu-links;
      background-color: lighten(#ededed, 3.5%);
    }
    a.active {
      @extend %menu-links;
      font-weight: 700;
      background-color: #ededed;
    }
    .menu-divider {
      height: 1px;
      background-color: $gris-claro;
    }
  }

  .good-people {
    p {
      margin: 1em 1.1em;
      font-size: 0.75em;
    }
  }

  .settings-box {
    @extend %dark-profile-box;
    .settings-header {
      padding: 1.3em 1.8em;
      h5 {
        margin-top: 0;
        margin-bottom: 0.1em;
        color: $rojo;
      }
      p {
        margin-bottom: 0;
      }
    }
    .settings-divider {
      @extend %dark-profile-divider;
    }
    .settings-section {
      padding: 28px emCalc(28px);
      .setting {
        .label {
          p {
            margin-bottom: emCalc(16px);
          }
          label {
            display: inline;
            line-height: 3.5em;
            text-transform: capitalize;
            cursor: default;
          }
        }
        .field {
          .image {
            width: 90px;
            .avatar {
              width: 90px;
              height: 100px;
              position:absolute;
            }
          }
          .image-upload, .mobile-upload {
            .button {
              padding: 11px 18px;
              font-size: 0.95em;
            }
          }
          input, select {
            margin-bottom: 0;
          }
          select {
            height: 3em;
            font-size: 0.9em;
            background: #ffffff;
            border-radius: 6px;
          }
          .help {
            margin: 8px 0 0 emCalc(2px);
            font-size: 0.75em;
            &.big {
              margin: 12px 0 0 emCalc(2px);
              font-size: 0.85em;
            }
          }
          small.error {
            margin: 0;
          }
        }
        @media #{$before-large} {
          .field {
            .image {
              margin: auto;
              width: 175px;
              // width: 100%;
              #avatar {
                width: 175px;
                height: 175px;
                background-size: cover;
                position: relative;
              }
            }
            .image-upload, .mobile-upload {
              margin-top: 8px;
              .button {
                width: 100%;
                text-align: center;
              }
            }
            select {
              width: 100%;
            }
          }
        }
        @media #{$from-large} {
          @include clearfix;
          margin-bottom: 18px;
          .label {
            float: left;
            padding-right: emCalc(31px);
            width: 28%;
            text-align: right;
          }
          .field {
            float: right;
            width: 72%;
            .image {
              float: left;
              #avatar {
                position: relative;
                height: 100px;
                background-size: cover;
              }
            }
            .image-upload, .mobile-upload {
              float: left;
              margin-left: emCalc(29px);
              width: 65%;
            }
            input[type='text'], input[type='email'], input[type='password'] {
              width: 70%;
            }
            select {
              width: 50%;
            }
            small.error {
              width: 70%;
            }
          }
        }
      }
      &.last-section {
        padding-top: 18px;
        padding-bottom: 18px;
        @media #{$before-large} {
          input[type='submit'] {
            width: 100%;
          }
        }
        @media #{$from-large} {
          input[type='submit'] {
            margin-left: 26%;
          }
        }
      }
    }
  }

  .avatar-uploader {
    text-align: left;
    .avatar-uploader-thumb {
      float: left;
      margin-right: 25px;
      width: 87px;
      height: 100px;
    }
    .choose-photo {
      margin-bottom: 0;
    }
  }
}

.settings-page > .row {
  margin-top: 20px;
  margin-bottom: 20px;
}

#settings-orders {
  .settings-box {
    .settings-section.orders {
      padding: 0em;
    }
  }
  .gp-simple-panel {
    @include clearfix;
    padding: 0.625em;
    border-radius: 0em;
    &:last-child {
      border-bottom-right-radius: 3px;
      border-bottom-left-radius: 3px;
    }
  }
  .date{
    padding: 0.3em 0 0 0;
    font-size: 0.9em;
  }
  .seller_data{
    margin-bottom: 0em;
    font-size: 0.875em;
    font-weight: 700;
    color: $gris;
    span {
      font-weight: 400;
      color: lighten($gris, 15%);
    }
  }
  .order-description{
    font-size: 1.2em;

    h3{
      margin: 0em;
      font-size: 0.9em;
    }
    .payment {
      padding-left: 0.75em;
      .payment-img {
        margin-right: .5em;
      }
    }
    .title {
      padding: 0px;
      .total-price{
        position: absolute;
        right: 0em;
        bottom: 0em;
        font-weight: 700;
        text-align: right;
        color: $gris;
      }
    }
    .shipments {
      margin-top: 0.5em;
      h3 {
        font-size: 0.75em;
      }
      hr {
        margin: 0.5em 0 0.5em;
        border: 1px dashed #ddd;
      }
      .shipment {
        .status {
          position: absolute;
          right: 0px;
        }
      }
      .order-items {
        .order-item {
          margin: 0.5em 0 0.5em 0;
          .color-box {
            width: 20px;
            height: 20px;
            display: inline-block;
            border: 1px solid black;
          }
          .quantity {
            font-size: 1em;
          }
        }
      }
      .gateway {
        margin: 0.5em 0 0.5em 0;
        a {
          color: #4fe3fe;
        }
      }
    }
  }

  .status{
    font-size: 1em;
    font-weight: 900;
    text-decoration: none;
    font-weight: bold;
    text-transform: capitalize;
  }
  // STATUS COLORS
  .unfulfilled, .pending, .in_process {
    color: $yellow;
  }
  .rejected, .cancelled, .not_delivered {
    color: $light-red;
  }
  // SHIPMENT SPECIFIC
  .shipped {
    color: $blue;
  }
  .delivered {
    color: $lighter-green;
  }
  // PAYMENT SPECIFIC
  .collected {
    color: $lighter-green;
  }
  .refunded {
    color: #b8b9b9;
  }
  .no-orders {
    padding: 28px 1.75em;
  }
}

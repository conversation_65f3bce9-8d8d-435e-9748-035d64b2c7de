#settings-admins {
  .settings-box {
    .settings-section.orders {
      padding: 0em;
    }
    .settings-section.admin {
      padding: 12px 0 6px 0;
    }
  }
  .remove-admin-button, {
    padding-top: 0.68em;
    padding-bottom: 0.68em;
    margin-top: 1em;
    font-size: 0.7em;
  }
  .role-selector {
    margin-top: 1em !important;
  }
  .login-and-email {
    margin-left: 5%;
  }
  .gp-simple-panel {
    @include clearfix;
    padding: 0.625em;
    border-radius: 0em;
    &:last-child {
      border-bottom-right-radius: 3px;
      border-bottom-left-radius: 3px;
    }
  }
  .select2-choices {
    height: emCalc(40px);
    padding: 0.55em 0.75em;
    &:focus {
      border-color: $gris;
      box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
    }
  }
  .select2-container, .select2-container-multi {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    margin-bottom: 1.25em;
    .select2-choices {
      border-radius: 3px 3px 3px 3px;
      outline: medium none;
      background-color: white;
      border: 1px solid #CCCCCC;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      background-image: none;
      color: rgba(0, 0, 0, 0.75);
      cursor: text;
      padding: 0.45em 0.75em 0.25em;
      &:focus{
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
  }

}

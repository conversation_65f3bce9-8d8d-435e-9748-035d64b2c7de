.settings-box {
  .settings-section.apps {
    padding: 24px emCalc(24px);
    .app {
      .app-image {
        text-align: center;
      }
      .app-info {
        p {
          span {
            font-weight: 700;
          }
          &.date-approved {
            color: lighten($gris, 30%);
          }
          &.date-expired {
            color: $rojo;
          }
        }
      }
      .app-access {
        .button {
          text-align: center;
        }
      }
      @media #{$before-large} {
        .app-image {
          img {
            width: 100px;
            height: 100px;
          }
        }
        .app-info {
          p {
            margin-bottom: 4px;
          }
        }
        .app-access {
          .button {
            margin-top: 6px;
            width: 100%;
          }
        }
      }
      @media #{$from-large} {
        @include clearfix;
        .app-image {
          float: left;
          width: 12%;
          img {
            width: 75px;
            height: 75px;
          }
        }
        .app-info {
          float: left;
          padding-right: 2%;
          padding-left: 2%;
          width: 63%;
          p {
            margin-bottom: 2px;
          }
        }
        .app-access {
          float: left;
          width: 25%;
          text-align: right;
          .button {
            width: 151px;
          }
        }
      }
    }
  }
}
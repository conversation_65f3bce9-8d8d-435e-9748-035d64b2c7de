$mobile-header-height: 48px;
$mobile-header-bg-color: #2a2a2a;

.mobile-main-menu-wrapper {
  background: $mobile-header-bg-color;
}

.mobile-header-phantom {
  display: none;
  height: $mobile-header-height;
  background-color: $mobile-header-bg-color;
  @media (max-width: #{$topbar-breakpoint}) {
    display: block;
  }
}

.mobile-header {
  @include clearfix;
  color: #FFF;
  background-color: #FFFFFF;
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  display: none;
  @media (max-width: #{$topbar-breakpoint}) {
    display: block;
  }
  .h-btn-menu {
    @include unselectable;
    position: relative;
    float: left;
    display: block;
    margin-right: 12px;
    padding: 0 12px;
    border-right: 1px solid #D9D9D9;
    height: $mobile-header-height;
    cursor: pointer;
    color: #bdbdbd;
    &:active {
      background-color: #999;
      box-shadow: inset 0px 0px 3px #333;
      color: $mobile-header-bg-color;
    }
    .i-menu {
      position: relative;
      top: -4px;
      font-size: 23px;
    }
  }
  .h-logo {
    @include unselectable;
    float: left;
    display: block;
    cursor: pointer;
    height: $mobile-header-height;
    img {
      margin-top: 15px;
    }
  }
  .h-btn {
    @include reset-button;
    @include unselectable;
    $h-btn-h: $mobile-header-height;
    position: relative;
    float: right;
    display: block;
    padding: 0 10px;
    height: $h-btn-h;
    cursor: pointer;
    &:active {
      .h-btn-icon { color: #666; }
    }
    .h-btn-icon {
      color: #bdbdbd;
      font-size: 23px;
    }
    .h-btn-count {
      position: absolute;
      right: 4px; top: 3px;
      border: 2px solid $mobile-header-bg-color;
      border-radius: 50%;
      padding: 0.3em 0.6em 0.3em 0.5em;
      font-size: 0.7em;
      line-height: 1em;
      text-align: center;
      font-weight: bold;
      font-family: monospace;
      color: white;
      background-color: $header-clr-3;
      &[data-count="0"] {
        display: none;
      }
    }
  }
  .h-btn-login {
    @include unselectable;
    float: right;
    height: $mobile-header-height;
    padding: 0 8px;
    border: 14px solid $mobile-header-bg-color;
    border-bottom: 15px solid $mobile-header-bg-color;
    font-size: 11px;
    font-weight: 600;
    line-height: 16px;
    color: #FFF;
    text-shadow: 1px 1px #333;
    cursor: pointer;
    background-color: $dark-gray;
    &:active {
      background-color: #999;
      box-shadow: inset 0px 0px 3px #333;
    }
  }
}

.mobile-header-overlay {
  z-index: 50;
  position: absolute;
  left: 0; top: 0;
  width: 100%; height: 100%;
  border-top: $mobile-header-height solid transparent;
  background-color: #E8E8E8;
  box-shadow: 0 -1px 14px black;
}

.mobile-header-notifications {
  &.empty {
    .empty-msg {
      display: block;
    }
  }
  .empty-msg {
    display: none;
    margin: 0;
    padding: 3em 1em;
    line-height: 1.4em;
    font-size: 1.7em;
    text-align: center;
  }
  .notifications-items-list {
    padding-top: 1.3em;
    overflow: auto;
    max-height: 100%;
    .notifications-item {
      @include clearfix;
      display: block;
      position: relative;
      padding: 0 0.4em 0.9em 0.5em;
      margin: 0 0.9em 1.1em 1.3em;
      border-bottom: 1px solid darken($gris-claro, 10%);
      box-shadow: 0px 1px 0px 0px $blanco-oscuro;
      font-size: 1.3em;
      &:last-child {
        border-bottom: none;
        box-shadow: none;
        margin-bottom: 0;
      }
      &.unread {
        .read-btn {
          display: block;
        }
      }
      .read-btn {
        display: none;
        position: absolute;
        left: -0.6em; top: 1.1em;
        width: 0.55em; height: 0.5em;
        border-radius: 50%;
        background-color: darken($rojo, 10%);
      }
      .pic {
        float: left;
        margin-right: 0.5em;
        width: 2.6em;
      }
      .text {
        margin: 0;
        color: $gris;
        a {
          font-weight: bold;
        }
      }
      .created-ago {
        display: block;
        font-size: 0.7em;
        line-height: 0.8em;
        margin-top: 0.4em;
        margin-left: 4.4em;
        color: lighten($gris, 30%);
      }
    }
  }
}
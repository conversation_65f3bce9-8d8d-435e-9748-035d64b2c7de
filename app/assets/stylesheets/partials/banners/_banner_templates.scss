.gp-vnr {
  .description {
    font-family: 'Source Sans Pro', sans-serif;
    letter-spacing: 0.03em;
    width: 100%;
  }
  .headline{
    width: 100%;
  }
  &.wave {
    %wave-common-appearance {
      bottom: 20%;
      width: 100%;
      font-family: 'Leckerli One', $body-font-family;
      text-align: center;
      color: #ffffff;
    }

    .gp-vnr-content-text {
      @extend %wave-common-appearance;
      .description {
        font-size: 40%;
      }
    }

    &.with-1-columns {
      .desktop-content .gp-vnr-content-text {
        bottom: 23%;

        @media (min-width: 768px) and (max-width: 959px) {
          font-size: 1.3em;
        }
        @media (min-width: 960px) and (max-width: 1280px) {
          font-size: 2em;
        }
        @media #{$after-wide} {
          font-size: 2.7em;
        }
      }
    }
    &.with-2-columns {
      .desktop-content .gp-vnr-content-text {
        font-size: 3.0em;

        @media (min-width: 768px) and (max-width: 959px) {
          font-size: 2.2em;
        }
        @media (min-width: 960px) and (max-width: 1280px) {
          font-size: 2.6em;
        }
        @media #{$after-wide} {
          font-size: 3.5em;
        }
      }
    }
    &.with-4-columns {
      .desktop-content .gp-vnr-content-text {
        @media (min-width: 768px) and (max-width: 959px) {
          font-size: 2.6em;
        }
        @media (min-width: 960px) and (max-width: 1280px) {
          font-size: 3.5em;
        }
        @media #{$after-wide} {
          font-size: 4.5em;
        }
      }
    }

    .mobile-content .gp-vnr-content-text {
      bottom: 3%;
      font-size: 2em;

      @media (min-width: 320px) and (max-width: 524px) {
        font-size: 2.0em;
      }
      @media (min-width: 525px) and (max-width: 767px) {
        font-size: 2.6em;
      }
    }
  }

  &.paint {
    %paint-common-appearance {
      bottom: 0;
      width: 100%;
      font-family: 'Finger Paint', $body-font-family;
      text-align: center;
      color: #ffffff;
      background-color: black;
      background-color: rgba(black, 0.5);
    }

    .gp-vnr-content-text {
      @extend %paint-common-appearance;

      .description {
        font-size: 60%;
      }
    }
    .desktop-content .gp-vnr-content-text{
      display: table;
      .headline{
        padding-top: 0.4em;
      }
      .description{
        padding-bottom: 0.8em;
      }
    }

    &.with-1-columns,
    &.with-2-columns {
      .desktop-content .gp-vnr-content-text {
        @media (min-width: 768px) and (max-width: 959px) {
          font-size: 1.5em;
        }
        @media (min-width: 960px) and (max-width: 1280px) {
          font-size: 1.7em;
        }
        @media #{$after-wide} {
          font-size: 2.1em;
        }
      }
    }
    &.with-4-columns {
      .desktop-content .gp-vnr-content-text {

        @media (min-width: 768px) and (max-width: 959px) {
          font-size: 2.2em;
        }
        @media (min-width: 960px) and (max-width: 1280px) {
          font-size: 2.5em;
        }
        @media #{$after-wide} {
          font-size: 3.5em;
        }
      }
    }

    .mobile-content .gp-vnr-content-text {
      font-size: 1.6em;

      @media (min-width: 320px) and (max-width: 524px) {
        font-size: 1.6em;
      }
      @media (min-width: 525px) and (max-width: 767px) {
        font-size: 2em;
      }
    }
  }
}

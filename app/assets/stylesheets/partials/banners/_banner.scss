.gp-vnr {
  @include block-grid-item;
  position: relative;
  margin-bottom: 1em;

  &.with-1-columns {
    width: 23.9%;
    .desktop-content { padding: 99.9% 0 0; }
  }
  &.with-2-columns {
    width: 49.4%;
    .desktop-content { padding: 48.3% 0 0; }
  }
  &.with-4-columns {
    width: 100%;
    .desktop-content { padding: 31.2% 0 0; }
  }

  .gp-vnr-content {
    @include gp-panel;
    position: relative;
    overflow: hidden;
    background-position: center;
    background-repeat: repeat-x;
    background-size: auto 100%;
    background-size: cover;
    border-radius: 0;

    .gp-vnr-content-text {
      position: absolute;
    }

    &.mobile-content {
      padding: 49% 0 0;
      .gp-vnr-content-text {
        padding: 7px;
      }
    }
  }

  @media #{$before-medium} {
    &.with-1-columns, &.with-2-columns { width: 100%; }
  }

  @media #{$before-medium} {
    .desktop-content { display: none; }
  }

  @media #{$from-medium} {
    .mobile-content { display: none; }
  }
}

%default-property {
  display: inline-block;
  border-radius: 3px;
  min-width: 36px;
  height: 36px;
  border: solid 1px #ddd;
  text-align: center;
  padding: 0 4px;
  line-height: 33px;
  span{
    font-size: 13px;
    position: relative;
    top: 1px;
  }
}

.cart-info{
  a{
    color: white;
    display: block;
  }
}

.cart-info-modal{
  @include reveal-bg;
  @include reveal-modal-style;
  @include reveal-modal-base($base-style: true, $width: 100%);
  @include gp-panel;
  top: 65px;
  padding: 0;
  border: 0;
  @media #{$after-medium} {
    top: 70px;
    max-width: 650px;
    @include reveal-modal-base(false, 650px);
    border-radius: 6px;
  }
  &.loading {
    .loader { display: block }
  }
  .loader {
    display: none;
    z-index: 10;
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(white, .8);
  }
  .count-modal{
    display: inline;
  }
  h5 {
    text-transform: uppercase;
    line-height: 29px;
    margin: 0;
  }
  .close {
    background-image: image-url('social/whatsups_form/uploader-sprite-wide.png');
    background-repeat: no-repeat;
    cursor: pointer;
    background-position: -163px 20px;
    float: right;
    height: 69px;
    width: 69px;
    border: 1px solid #ddd;
    text-align: center;
    line-height: 65px;
    font-size: 28px;
    font-weight: bold;
    overflow: auto;
    @media #{$after-medium} {
      border-top-right-radius: 6px;
    }
    &:hover{
      background-position: -163px -40px;
    }
  }
  .headline{
    @include linear-gradient($blanco, #eee);
    padding: 20px;
    border: 1px solid #ddd;
    height: 69px;
    font-weight: 600;
    @media #{$after-medium} {
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
    h1{
      margin: 0;
    }
  }
  .actions{
    border: 1px solid #ddd;
    text-align: right;
    padding: 18px;
    @media #{$after-medium} {
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
    }
    .continue-shopping {
      float: left;
    }
    .continue-shopping, .go-to-checkout{
      text-transform: uppercase;
    }
    #go-to-checkout-form {
      display: inline;
    }
  }
  &.empty {
    .empty-cart-msg {
      display: block;
    }
  }
  .empty-cart-msg {
    display: none;
    margin: 0;
    border-left: solid 1px #ddd;
    border-right: solid 1px #ddd;
    padding: 3em 0;
    line-height: 1.4em;
    font-size: 1.7em;
    text-align: center;
  }
  .cart-items{
    position: relative;
    display: block;
    max-height: 331px;
    overflow: scroll;
    overflow-x: hidden;
    overflow: auto;
    margin-bottom: 0px;
    li:last-child{
      border-bottom: 0px;
    }
    .cart-item{
      position: relative;
      background-color: white;
      float: none;
      margin: 0;
      padding: 1em;
      border-bottom: solid 1px #ddd;
      border-left: solid 1px #ddd;
      border-right: solid 1px #ddd;
      img{
        position: relative;
        margin: 8px 0;
        height: 69px;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: contain;
        border: 1px solid #ddd;
        padding: 33px;
        border-radius: 6px;
      }
      .info{
        margin-top: 7px;
        .title{
          display: inline;
          font-weight: 600;
        }
        .property-wrapper {
          display: inline-block;
          margin-top: 6px;
          height: 36px;
        }
        .manufacturer {
          line-height: 0.6em;
        }
        label {
          display: inline-block;
          margin-right: 0.4em;
          font-weight: bold;
        }
        .color {
          background-color: #da251d;
          border-radius: 3px;
          width: 36px;
          height: 36px;
          border: solid 1px #ddd;
          display: inline-block;
          margin-bottom: -14px;
        }
        .size, .material, .dimensions, .hardness, .length, .material, .custom-property {
          @extend %default-property;
        }
        .color, .size{
          margin-right: 10px;
        }
        .quantity {
          display: inline-block;
          position: relative;
          margin-right: 15px;
          input {
            box-shadow: -1px 1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 0 black;
            border: 1px solid #CACACA;
            border-radius: 3px 0px 0px 3px;
            display: inline-block;
            font-size: emCalc(13px);
            line-height: 18px;
            text-align: center;
            padding: 5px 7px 4px;
            background-color: white;
            width: 50px;
            height: 36px;
            margin-bottom: 0px;
          }
          .increment, .decrement {
            @include unselectable();
            @include linear-gradient(#F7F7F7, #E6E6E6);
            font-size: 8px;
            position: absolute;
            right: -12px;
            width: 21px;
            height: 15px;
            line-height: 18px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #CACACA;
            color:#515151;
            box-shadow: -1px -1px 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 white;
            cursor: pointer;
            height: 19px;
            &:hover {
              color: #666;
              background: #dedede;
            }
            &:active {
              box-shadow: -1px 1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 0 black;
            }
          }
          .increment {
            top: 0;
            border-radius: 0px 3px 0px 0px;
            border-bottom:0px none;
          }
          .decrement {
            bottom: 0px;
            border-radius: 0px 0px 3px 0px;
          }
        }
      }
      .price{
        font-size: 12px;
        text-align: right;
        .per-unit {
          text-transform: uppercase;
        }
        .sale-price{
          color: $rojo;
        }
        .original-price {
          position: relative;
          font-size: 0.8em;
          font-weight: normal;
          color: $gris;
          &:after {
            content: '';
            position: absolute;
            width: 110%;
            height: 1px;
            top: 50%;
            left: -5%;
            background-color: $gris;
          }
        }
      }
      .subtotal {
        position: relative;
        clear: both;
        font-size: 12px;
        text-align: right;
        text-transform: uppercase;
        span {
          font-weight: bold;
        }
      }
      .remove-item-container {
        text-align: right;
      }
      .remove-item {
        @include button(simple, #ECECEC);
        position: relative;
        background-image: linear-gradient($blanco, #DBDBDB);
        height: 32px;
        line-height: 13px;
        text-transform: uppercase;
        font-weight: 600;
        text-align: center;
        margin-top: 0.5em;
      }
      .subtotal-wrapper {
        margin-top: 0.8em;
      }
      .msg {
        clear: both;
        width: 100%;
        color: $rojo;
        text-align: right;
        font-size: 13px;
      }
    }
  }
  .total {
    display: inline-block;
    position: relative;
    font-size: 12px;
    text-align: right;
    text-transform: uppercase;
    margin-right: 0.8em;
    span {
      font-weight: bold;
    }
  }
}

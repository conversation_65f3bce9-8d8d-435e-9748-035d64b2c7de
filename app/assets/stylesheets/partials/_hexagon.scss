$em-base: 16px;

@function emCalc($px-width) {
  @return $px-width / $em-base * 1em;
}

.hexagon {
  position: relative;
  display: inline-block;
  z-index: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .hexagon-container {
    display: none;
  }
  > .full-link {
    z-index: 10;
  }
}

html.csstransforms .hexagon {
  background-image: none !important;
  background-color: transparent !important;
  .hexagon-container {
    display: block;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    visibility: hidden;
    cursor: pointer;
    @include transform(rotate(120deg));
    .hexagon-in1 {
      overflow: hidden;
      width: 100%;
      height: 100%;
      @include transform(rotate(-60deg));
    }
    .hexagon-in2 {
      position: relative;
      width: 100%;
      height: 100%;
      visibility: visible;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      @include transform(rotate(-60deg));
    }
  }
  .border {
    display: none;
    position: absolute;
    &.b-1, &.b-2 {
      left: 21%; top: 6%;
      width: 58%; height: 87%;
    }
    &.b-1 {
      @include transform(rotate(-30deg));
    }
    &.b-2 {
      @include transform(rotate(30deg));
    }
    &.b-3 {
      left: 0; top: 25%;
      width: 100%; height: 50%;
    }
  }
}

@mixin hexagon($width) {
  width: $width;
  height: ($width * 1.15);
  .hexagon-fallback-text {
    font-size: $width / 2;
    z-index: 5;
  }
}

html.csstransforms .hexagon {
  &.feed-avatar {
    @include hexagon(emCalc(42px));
    display: block;
  }
  &.user-avatar, &.brand-avatar {
    @include hexagon(emCalc(68px));
    display: block;
  }
  &.user-avatar {
    .hexagon-fallback-text {
      font-size: 16px;
      z-index: 5;
    }
  }
  &.mentionable-avatar {
    @include hexagon(emCalc(37px));
    display: block;
  }
  &.small-avatar {
    @include hexagon(emCalc(37px));
    display: block;
  }
}

@mixin square($width) {
  width: $width;
  height: $width;
  .hexagon-fallback-text {
    font-size: $width / 2;
  }
}

.square {
  background-size: cover;
  background-position: center;
  border-radius: 3px;
  &.feed-avatar {
    @include square(emCalc(39px));
  }
  &.brand-avatar {
    @include square(emCalc(39px));
  }
  &.mentionable-avatar {
    @include square(emCalc(39px));
  }
  &.small-avatar {
    @include square(emCalc(39px));
  }
}
.square > a {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.hexagon, .square {
  position: relative;
  overflow: hidden;
  &.hide-fallback {
    background-color: transparent !important;
  }
  &.show-fallback {
    .hexagon-fallback-text {
      display: block;
    }
  }
  .fallback-img {
    position: absolute;
    right: 100%;
    bottom: 100%;
  }
  .hexagon-fallback-text {
    display: none;
    position: absolute;
    top: 50%;
    width: 100%;
    text-align: center;
    color: white;
    font-weight: bold;
    margin-top: -0.48em;
    line-height: 1em;
  }
}


.input-with-status {
  position: relative;
  input {
    width: 100% !important;
  }
  .input-with-status-status {
    display: none;
    position: absolute;
    left: 102%;
    top: 0;
    padding-left: 19px;
    min-height: 100%;
    font-size: 0.8em;
    line-height: 3.3em;
  }
  &.loading {
    .input-with-status-status {
      display: block;
      background: image-url('loaders/spinner-small.gif') left 14px no-repeat;
    }
  }
  &.success {
    input {
      border-color: #66CD00;
      background-color: rgba(#66CD00, 0.1);
    }
    .input-with-status-status {
      display: block;
      color: #66CD00;
      background: image-url('loaders/accept.png') left 15px no-repeat;
    }
  }
  &.error {
    input {
      border-color: $rojo;
      background-color: rgba($rojo, 0.1);
    }
    .input-with-status-status {
      display: block;
      color: $rojo;
      background: image-url('loaders/error.png') left 15px no-repeat;
    }
  }
}
.mobile-cart-info {
  $actions-height: 3.5em;
  position: relative;
  padding-bottom: $actions-height * 2;
  &.empty {
    .empty-cart-msg {
      display: block;
    }
  }
  .empty-cart-msg {
    display: none;
    margin: 0;
    padding: 3em 1em;
    line-height: 1.4em;
    font-size: 1.7em;
    text-align: center;
  }
  .mobile-cart-info-list {
    overflow: auto;
    max-height: 100%;
  }
  .mobile-cart-info-list-item {
    position: relative;
    border-bottom: 1px solid $gris;
    padding-right: 0.6em;
    .thumb {
      float: left;
      height: 8.25em;
      margin-right: 0.8em;
    }
    .remove {
      @include reset-button;
      position: relative;
      float: right;
      width: 36px;
      height: 36px;
      cursor: pointer;
      .icon {
        position: absolute;
        right: 0px; top: 8px;
        width: 15px; height: 15px;
        background-position: -124px top;
        background-repeat: no-repeat;
        background-image: image-url('header/mobile-header-sprite.png');
      }
      &:hover {
        background-position: -124px -15px;
      }
      &:active {
        background-position: -124px -30px;
      }
    }
    .manufacturer {
      margin-top: 0.3em;
      .t {
        font-size: 0.8em;
      }
    }
    .title {
      color: $rojo;
      font-weight: bold;
      .t {
        color: $rojo;
        font-size: 1.2em;
      }
    }
    .attrs {
      .t {
        color: lighten($gris, 25%);
        font-size: 0.8em;
      }
    }
    .qty {
      float: left;
      .t {
        color: $gris;
        font-size: 1em;
        line-height: 1.6em;
        font-weight: bold;
      }
    }
    .price {
      float: right;
      .t {
        color: $rojo;
        font-size: 1.2em;
        font-weight: bold;
      }
    }
    .buy-info {
      margin-top: 1.6em;
    }
  }
  .mobile-cart-info-total {
    position: absolute;
    left: 0;
    bottom: $actions-height;
    width: 100%;
    height: $actions-height;
    border: 1px solid $gris;
    border-left: none;
    border-right: none;
    line-height: $actions-height;
    text-align: center;
    text-transform: uppercase;
  }
  .mobile-cart-info-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    button {
      @include reset-button;
      text-transform: uppercase;
      color: white;
      height: $actions-height;
      line-height: $actions-height;
      font-size: 1em;
      cursor: pointer;
      font-weight: bold;
    }
    .continue {
      width: 60%;
      background-color: #9D9D9D;
      float: left;
      &:hover {
        background-color: darken(#9D9D9D, 5%);
      }
    }
    .checkout {
      width: 40%;
      background-color: #E53C39;
      float: right;
      &:hover {
        background-color: darken(#E53C39, 5%);
      }
    }
  }
  &.empty .mobile-cart-info-actions .checkout,
  .mobile-cart-info-actions .checkout.disabled {
    cursor: not-allowed;
    color: lightcoral;
    &:hover {
      background-color: #E53C39;
    }
  }
}
.login-form {
  position: relative;
  max-width: 760px;
  margin: 1.8em auto;
  padding: 1.8em 0;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #FAFAFA;
  &.with-msg {
    padding-top: 0;
  }
  &.loading {
    .loader { display: block; }
  }
  .loader {
    display: none;
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background-color: white;
    background-color: rgba(white, 0.6);
    z-index: 10;
  }
  .remember_me{
    float: right;
    top: -4px;
    position: relative;
    label{
      display: inline;
      font-size: 13px;
      position: relative;
      top: 0.095em;
    }
    input{
      margin: 0;
    }
  }
  h5 {
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 1em;
  }
  p {
    margin-bottom: 0.7em;
  }
  .social-media, .login-form-data{
    padding: 0 1.8em;
  }
  .login-form-data{
    margin-left: 20%;

    @media #{$from-medium} {
      &:first-child {
        //border-right: 1px solid #ddd;
      }
    }
    .actions{
      text-align: right;
      margin-top: 1.3em;
    }
    .forgot-password{
      font-size: 12px;
      color: #ED1C24;
      text-transform: uppercase;
      font-weight: 400;
      margin-bottom: 17px;
    }
  }
  .social-media{
    .button {
      text-align: left;
      margin-bottom: 1.5em;
      font-weight: 400;
    }
  }
  .register {
    position: relative;
    top: 0.4em;
    clear: both;
    margin: 0 0.9em;
    font-weight: bold;
  }
  .msg {
    padding: 1.2em;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #F2F2F2;
    text-align: center;
    font-weight: bold;
    font-size: 1em;
  }
}

.mobile-main-menu {
  $color-base: #2a2a2a;
  $color-1: #ddd;
  $color-2: #bdbdbd;
  $color-3: #353535;
  $color-4: #000;

  margin-top: 15px;

  .link {
    position: relative;
    cursor: pointer;
    position: relative;
    display: block;
    margin: 0 11px;
    padding: 0.4em 0.7em 0.4em 32px;
    font-size: 13px;
    line-height: 37px;
    color: $color-1;
    background-color: $color-3;

    .link-icon {
      position: absolute;
      top: 5px;
      left: 11px;
      font-size: 11px;
    }
    &.with-flecha {
      padding-right: 1.8em;
    }
    .i-flecha-down {
      position: absolute;
      right: 0;
      top: 6px;
      font-size: 32px;
      @include transition(all .1s ease-in-out);
    }
    &.active {
      .i-flecha-down {
        @include transform(rotateX(180deg));
      }
    }
  }

  .mobile-main-menu-subtitle {
    @include unselectable;
    margin: 13px 11px 0;
    padding: 0.4em 0.7em;
    font-size: 13px;
    background-color: $color-3;
    .text {
      margin: 0;
      padding-bottom: 5px;
      border-bottom: 1px solid #4b4b4b;
      color: $color-2;
      font-weight: bold;
      text-transform: uppercase;
      font-size: inherit;
    }
  }

  .profile-link {
    display: block;
    margin: 0 11px;
    padding: 0em 0.7em 13px;
    background-color: $color-3;
    &:active {
      border-radius: 2px;
      background-color: $color-4;
    }
    .profile-link-avatar {
      float: left;
      margin-right: 0.5em;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .name {
      margin: 0;
      line-height: 2em;
      color: $color-1;
    }
    .login {
      margin: 0;
      line-height: 0.5em;
      color: #666;
    }
    &.current {
      margin: 0;
      padding: 0.9em 0.6em 0.65em;
      background-color: $color-base;
    }
  }

  .mobile-main-menu-subtitle + .profile-link {
    padding-top: 6px;
  }
}

.mobile-search-bar {
  position: relative;
  margin: 0 11px 0.65em;
  padding: 0;
  &:first-child {
    margin: 0.65em 11px;
  }
  .i-lupa {
    position: absolute;
    top: 0;
    left: 7px;
    color: #3A99D8;
    line-height: 28px;
    height: 28px;
  }
  form {
    input {
      width: 100%;
      padding: 6px 0.3em 5px 28px;
      margin: 0;
      height: auto;
      border: none !important;
      line-height: 16px;
      font-size: 12px;
      box-shadow: none !important;
      border-radius: 0;
      background-color: #eaeaea;
    }
  }
}

.mobile-categories-menu {
  display: none;
  &.active { display: block; }
  .category-item {
    position: relative;
    text-transform: uppercase;
    &.active {
      .submenu { display: block; }
    }
  }
  .link {
    background-color: #353535;
  }
  .category-item-title {
    color: #ddd;
    padding: 0.4em 0.7em;
  }
  .submenu {
    display: none;
  }
  .category-item-subitem {
    font-weight: bold;
    padding-left: 1.6em;
    background-color: #E2E2E2;
    text-transform: capitalize;
    color: #2a2a2a;
  }
  .category-item-subsubitem {
    padding-left: 3em;
  }
}

.searcher-mobile-fixed {
  $searcher-base-color: #bdbdbd;

  position: fixed;
  width: 100%;
  box-shadow: 0 2px 4px 0 rgba(161,161,161,0.50);
  padding-top: 9px;
  padding-bottom: 9px;
  border-top: 1px solid #D9D9D9;
  z-index: 50;
  background: white;

  .mobile-search-bar {
    margin: 0 6px;
    font-size: 25px;
    color: $searcher-base-color;

    .i-lupa {
      color: $searcher-base-color;
    }

    form {
      input {
        margin-left: 25px;
        background:  white;
        font-size: 14px;

        &:focus {
          border: none !important;
          outline-color: white !important;
        }
      }

      ::-webkit-input-placeholder {
        color: $searcher-base-color;
      }
      :-moz-placeholder {
        color: $searcher-base-color;
        opacity: 1;
      }
      ::-moz-placeholder {
        color: $searcher-base-color;
        opacity: 1;
      }
      :-ms-input-placeholder {
        color: $searcher-base-color;
      }
    }
  }
}

.searcher-mobile-fixed-phantom {
  height: 49px;
}
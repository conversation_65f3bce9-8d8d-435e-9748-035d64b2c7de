@mixin create-visibility-classes($name, $media-query) {
  @media #{$media-query} {
    .show-for-#{$name} { display: inherit !important; }
    .hide-for-#{$name} { display: none !important; }
  }
}

@mixin create-visibility-before-classes($name, $media-query) {
  @media #{$media-query} {
    .show-before-#{$name} { display: inherit !important; }
    .hide-before-#{$name} { display: none !important; }
  }
}

@mixin create-visibility-after-classes($name, $media-query) {
  @media #{$media-query} {
    .show-after-#{$name} { display: inherit !important; }
    .hide-after-#{$name} { display: none !important; }
  }
}

@include create-visibility-classes('small', $screen);
@include create-visibility-classes('medium', $from-medium);
@include create-visibility-classes('large', $from-large);
@include create-visibility-classes('after-wide', $after-wide);

@include create-visibility-after-classes('small', $after-small);
@include create-visibility-before-classes('medium', $before-medium);
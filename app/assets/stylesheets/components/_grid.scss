.row {
  @include grid-row;

  &.collapse {
    .column,
    .columns { @include grid-column($collapse:true); }
  }

  .row { @include grid-row($behavior:nest);
    &.collapse { @include grid-row($behavior:nest-collapse); }
  }
  @media #{$after-wide} {
    max-width: 1280px;
  }
}

.column,
.columns { @include grid-column($columns:$total-columns); }

@media only screen {
  .column,
  .columns { @include grid-column($columns:false); }

  @for $i from 1 through $total-columns {
    .small#{-$i} { @include grid-column($columns:$i,$collapse:null,$float:false); }
  }

  @for $i from 0 through $total-columns - 2 {
    .small-offset-#{$i} { @include grid-column($offset:$i, $collapse:null,$float:false); }
  }

  @for $i from 1 through $total-columns - 1 {
    .push#{-$i} { @include grid-column($push:$i, $collapse:null, $float:false); }
    .pull#{-$i} { @include grid-column($pull:$i, $collapse:null, $float:false); }
  }

  [class*="column"] + [class*="column"]:last-child { float: $opposite-direction; }
  [class*="column"] + [class*="column"].end { float: $default-float; }

  .column.small-centered,
  .columns.small-centered { @include grid-column($center:true, $collapse:null, $float:false); }
}

@mixin create-grid-classes($name, $media-query) {
  @media #{$media-query} {
    @for $i from 1 through $total-columns {
      .#{$name}#{-$i} { @include grid-column($columns:$i,$collapse:null,$float:false); }
    }

    @for $i from 0 through $total-columns - 1 {
      .row .#{$name}-offset-#{$i} { @include grid-column($offset:$i, $collapse:null,$float:false); }
    }

    @for $i from 1 through $total-columns - 1 {
      .#{$name}-push#{-$i} { @include grid-column($push:$i, $collapse:null, $float:false); }
      .#{$name}-pull#{-$i} { @include grid-column($pull:$i, $collapse:null, $float:false); }
    }

    .column.#{$name}-centered,
    .columns.#{$name}-centered { @include grid-column($center:true, $collapse:null, $float:false); }

    .column.#{$name}-uncentered,
    .columns.#{$name}-uncentered {
      margin-#{$default-float}: 0;
      margin-#{$opposite-direction}: 0;
      float: $default-float !important;
    }
  }
}

@include create-grid-classes('after-small', $after-small);
//@include create-grid-classes('before-medium', $from-medium);
@include create-grid-classes('medium', $from-medium);
// @include create-grid-classes('after-medium', $after-medium);
@include create-grid-classes('large', $from-large);
// @include create-grid-classes('after-large', $after-large);
// @include create-grid-classes('wide', $from-wide);
@include create-grid-classes('after-wide', $after-wide);

html, body {
  -webkit-tap-highlight-color: rgba(0,0,0,0); /* disable webkit tap highlight */
}

.offcanvy-html-active, .offcanvy-html-active body {
  overflow: hidden !important;
}

.offcanvy {
  position: fixed;
  width: 256px;
  height: 100%;
  top: 0;
  left: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  @include transform(translate3d(-256px,0,0));
  &.offcanvy-right {
    left: auto;
    right: 0;
    width: 180px;
  }
}

.offcanvy.offcanvy-active {
  z-index: 5;
  @include transform(translate3d(0,0,0));
}


.offcanvy, .offcanvy-content, .offcanvy-move {
  -webkit-transition-property: -webkit-transform;
     -moz-transition-property: -moz-transform;
       -o-transition-property: -o-transform;
          transition-property: transform;
  @include transition-duration(.2s);
  @include transition-timing-function(cubic-bezier(.16, .68, .43, .99));
  @include backface-visibility(hidden);
  @include perspective(1000);
}

.offcanvy-move {
  z-index: 15;
}

.offcanvy-html-active {
  .offcanvy-move {
    @include transform(translate3d(256px,0,0));
  }
  &.offcanvy-right {
    .offcanvy-move {
      @include transform(translate3d(-180px,0,0));
    }
  }
}

.offcanvy-content {
  position: relative;
  overflow-y: auto;
  background-color: inherit;
  -webkit-overflow-scrolling: touch;
  z-index: 10;
}

.offcanvy-content-active {
  @include transform(translate3d(256px,0,0));
  &.offcanvy-right {
    @include transform(translate3d(-180px,0,0));
  }
  &:after {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    z-index: 150;
  }
}
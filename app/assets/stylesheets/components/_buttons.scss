/**
* It's fake because is not a button because they don't have the hover, active and those states
*/
@mixin gp-fake-button($base-color) {
  $grayscale: false;
  @if $base-color == grayscale($base-color) {
    $grayscale: true;
  }
  $color:         hsl(0, 0, 100%);
  $text-shadow:   adjust-color($base-color, $saturation:  15%, $lightness: -18%);
  @if lightness($base-color) > 70% {
    $color:       hsl(0, 0, 20%);
    $text-shadow: adjust-color($base-color, $saturation: 10%, $lightness: 4%);
  }
  @if $grayscale == true {
    $text-shadow:   grayscale($text-shadow);
  }
  cursor: pointer;
  border-radius: 3px;
  box-shadow: 1px 1px 1px rgba($black, 0.3);
  color: $color;
  background-color: $base-color;
}

@mixin gp-plain-button {
  display: inline-block;
  position: relative;
  padding: 1em 1.85em 0.9em;
  font-size: emCalc(12px);
  cursor: pointer;
  text-transform: uppercase;
  border-radius: 3px;
  outline: none;
}

.plain-button {
  @include gp-plain-button;
  border: 1px solid darken($blanco, 15%);
  color: lighten($gris, 5%);
  background-color: $blanco;
  &:hover {
    border: 1px solid lighten($gris, 15%);
    color: $gris;
    background-color: darken($blanco, 15%);
  }
  &.small {
    padding: 0.7em 1.2em;
  }
  &.red {
    border: 1px solid $rojo;
    background-color: $rojo;
    color: $blanco;
    &:hover {
      background-color: darken($rojo, 15%);
    }
    &.left-arrow {

    }
  }
  &.black {
    text-transform: none;
    border: 1px solid lighten($negro, 30%);
    background-color: lighten($negro, 5%);
    color: $blanco;
    &:hover {
      background-color: lighten($negro, 15%);
    }
  }
}


@mixin gp-button-base {
  @include unselectable;
  position: relative;
  display: inline-block;
  margin: 0;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  text-decoration: none;
  outline: none;
}

@mixin gp-button-box-shadow($shadows...) {
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  $shadows: false !default;
  @if $shadows {
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1), $shadows;
  }
}

@mixin gp-button-size-default {
  font-size: 0.9em;
  line-height: 1em;
  padding: 0.9em 2em;
}
@mixin gp-button-size-small {
  font-size: 0.8em;
  line-height: 1.5em;
  padding: 0.6em 1.5em;
}
@mixin gp-button-size-tiny {
  font-size: 0.7em;
  line-height: 0.8em;
  padding: 0.7em 1.5em;
}
@mixin gp-button-size-large {
  font-size: 1.1em;
  line-height: 1.15em;
  padding: 0.85em 2.3em;
}
@mixin gp-button-size-wide {
  width: 100%;
  text-align: center;
}

@mixin gp-button-color-default {
  border-top-color: #ddd;
  border-right-color: #bebebe;
  border-left-color: #bcbcbc;
  border-bottom-color: #acacac;
  color: #686868;
  text-shadow: rgba(200,200,200,0.3) 1px 1px;
  @include linear-gradient(#fff, #eee 98%);
  @include gp-button-box-shadow;
}
@mixin gp-button-color-default-hover {
  @include linear-gradient(#fbfbfb, #e6e6e6 98%);
  @include gp-button-box-shadow(inset -1px 1px 1px white, inset 1px 1px 1px white);
}
@mixin gp-button-color-default-active {
  @include linear-gradient(#e6e6e6, #fbfbfb 98%);
  @include gp-button-box-shadow(inset 0 1px 3px rgba(0, 0, 0, 0.7));
}
@mixin gp-button-color-default-disabled {
  cursor: -webkit-not-allowed;
  cursor: not-allowed;
  color: #bbb;
  text-shadow: none;
  @include linear-gradient(#fff, #eee 98%);
  @include gp-button-box-shadow;
}

@mixin gp-button-color-red {
  color: $blanco;
  border-color: lighten(#9e0710, 25%);
  text-shadow: rgba(0,0,0,0.3) 1px 1px;
  @include linear-gradient(saturate($rojo, 5%), darken($rojo, 10%));
  @include gp-button-box-shadow(inset 0 1px 0px 0px rgba(255, 150, 150, 0.7));
}
@mixin gp-button-color-red-hover {
  border-color: #9e0710;
  @include linear-gradient($rojo, darken($rojo, 15%));
}
@mixin gp-button-color-red-active {
  @include linear-gradient(darken($rojo, 5%), darken($rojo, 15%));
  @include gp-button-box-shadow(inset 0 2px 9px rgba(0, 0, 0, 0.5));
}
@mixin gp-button-color-red-disabled {
  color: rgba(white, 0.6);
  text-shadow: none;
  border-color: #9e0710;
  background: darken($rojo, 15%);
  @include gp-button-box-shadow(1px 1px 2px rgba(0, 0, 0, 0.1));
}

@mixin gp-button-color-green {
  color: $blanco;
  border-color: lighten($verde, 5%);
  text-shadow: rgba(0,0,0,0.3) 1px 1px;
  @include linear-gradient(saturate($verde, 5%), darken($verde, 10%));
  @include gp-button-box-shadow(inset 0 1px 0px 0px rgba(150, 200, 150, 0.2));
}
@mixin gp-button-color-green-hover {
  border-color: $verde;
  @include linear-gradient($verde, darken($verde, 15%));
}
@mixin gp-button-color-green-active {
  @include linear-gradient(darken($verde, 5%), darken($verde, 15%));
  @include gp-button-box-shadow(inset 0 2px 9px rgba(0, 0, 0, 0.5));
}
@mixin gp-button-color-green-disabled {
  color: rgba(white, 0.6);
  text-shadow: none;
  border-color: $verde;
  background: darken($verde, 15%);
  @include gp-button-box-shadow(1px 1px 2px rgba(0, 0, 0, 0.1));
}

@mixin gp-button-color-grey {
  border: 1px solid #1b1b1b;
  color: white;
  text-shadow: rgba(200,200,200,0.3) 1px 1px;
  @include linear-gradient(#7C7C7C, #464646);
  @include gp-button-box-shadow(inset 0 1px 1px rgba(white, 0.8));
}
@mixin gp-button-color-grey-hover {
  @include linear-gradient(lighten($gris, 25%), lighten($gris, 15%));
}
@mixin gp-button-color-grey-active {
  @include linear-gradient(lighten($gris, 10%), lighten($gris, 15%));
  @include gp-button-box-shadow(inset 0 2px 9px rgba(0, 0, 0, 0.5));
}
@mixin gp-button-color-grey-disabled {
  color: rgba(white, 0.6);
  text-shadow: none;
  background: darken($gris, 10%);
  @include gp-button-box-shadow;
}

@mixin gp-button-color-facebook {
  position: relative;
  border: 1px solid #2e498f;
  color: white;
  text-shadow: black 0 1px 0;
  background: $facebook-color;
  padding-left: 3.5em;
  padding-right: 1.5em;
  @include gp-button-box-shadow(inset 0 1px 1px rgba(white, 0.8));
  @include linear-gradient(#6883c3, #244087);
  &:before {
    content: '';
    position: absolute;
    left: 1em;
    top: 50%;
    margin-top: -11px;
    width: 22px;
    height: 22px;
    background-image: image-url('social/buttons/social-sprite.png');
    /*
    @media #{$after-wide} {
      margin-top: -14px;
      width: 27px;
      height: 28px;
      background-image: image-url('social/buttons/social-sprite-large.png');
    }
    */
  }
  &.tiny {
    padding-left: 30px;
    padding-right: 13px;
    &:before {
      left: 6px;
      top: 50%;
      margin-top: -9px;
      width: 18px;
      height: 18px;
      background-image: image-url('social/buttons/social-sprite-tiny.png');
    }
  }
}
@mixin gp-button-color-facebook-hover {
  @include linear-gradient(darken(#6883c3, 5%), darken(#244087, 5%));
}
@mixin gp-button-color-facebook-active {
  @include linear-gradient(darken(#6883c3, 15%), darken(#244087, 5%));
  @include gp-button-box-shadow(inset 0 2px 9px rgba(0, 0, 0, 0.5));
}

@mixin gp-button-color-twitter {
  position: relative;
  border: 1px solid #2ab2db;
  color: white;
  text-shadow: darken(#63acc2, 5%) 0 1px 0;
  background: $twitter-color;
  padding-left: 3.5em;
  padding-right: 1.5em;
  @include gp-button-box-shadow(inset 0 1px 1px rgba(white, 0.8));
  @include linear-gradient(#62d4f6, #26b8e5);
  &:before {
    content: '';
    position: absolute;
    left: 1.1em;
    top: 50%;
    margin-top: -10px;
    width: 24px;
    height: 20px;
    background-position: -22px top;
    background-image: image-url('social/buttons/social-sprite.png');
    /*
    @media #{$after-wide} {
      margin-top: -12px;
      width: 29px;
      height: 24px;
      background-position: 29px top;
      background-image: image-url('social/buttons/social-sprite-large.png');
    }
    */
  }
  &.tiny {
    padding-left: 30px;
    padding-right: 13px;
    &:before {
      left: 6px;
      top: 50%;
      margin-top: -8px;
      width: 20px;
      height: 17px;
      background-position: -18px top;
      background-image: image-url('social/buttons/social-sprite-tiny.png');
    }
  }
}
@mixin gp-button-color-twitter-hover {
  @include linear-gradient(darken(#62d4f6, 5%), darken(#26b8e5, 5%));
}
@mixin gp-button-color-twitter-active {
  @include linear-gradient(darken(#62d4f6, 15%), darken(#26b8e5, 5%));
  @include gp-button-box-shadow(inset 0 2px 9px rgba(0, 0, 0, 0.5));
}

@mixin gp-button-color-pinterest {
  position: relative;
  color: #cb2027;
  padding-left: 3.2em;
  &:before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    margin-top: -8px;
    width: 13px;
    height: 16px;
    background-image: image-url('mkp/variants/icon-pin-sm.png');
  }
  &.tiny {
    padding-left: 30px;
    padding-right: 13px;
    &:before {
      left: 6px;
      top: 50%;
      margin-top: -9px;
      width: 21px;
      height: 17px;
      background-position: 21px top;
      background-size: 40px;
    }
  }
}

@mixin gp-button-color-high-five {
  position: relative;
  padding-left: 2.7em;
  padding-right: 1.3em;
  .high-five-text { display: inline; }
  .high-fived-text { display: none; }
  &:before {
    content: '';
    position: absolute;
    left: 0.9em;
    top: 50%;
    margin-top: -10px;
    width: 18px;
    height: 20px;
    background-position: -46px top;
    background-image: image-url('social/buttons/social-sprite.png');
  }
  &.tiny {
    padding-left: 28px;
    padding-right: 11px;
    &:before {
      left: 8px;
      top: 50%;
      margin-top: -9px;
      width: 14px;
      height: 17px;
      background-position: -38px top;
      background-image: image-url('social/buttons/social-sprite-tiny.png');
    }
  }
  &.high-fived {
    @include gp-button-color-red-active;
    color: white;
    .high-five-text { display: none; }
    .high-fived-text { display: inline; }
    &.tiny {
      &:before {
        background-position: -53px top;
      }
    }
  }
}

.button {
  @include gp-button-base;

  @include gp-button-size-default;
  &.tiny {
    @include gp-button-size-tiny;
  }
  &.small {
    @include gp-button-size-small;
  }
  &.large {
    @include gp-button-size-large;
  }
  &.wide {
    @include gp-button-size-wide;
  }

  @include gp-button-color-default;
  &:hover { @include gp-button-color-default-hover; }
  &:active, &.active { @include gp-button-color-default-active; }
  &.disabled, &[disabled] { @include gp-button-color-default-disabled; }
  &.red {
    @include gp-button-color-red;
    &:hover { @include gp-button-color-red-hover; }
    &:active, &.active { @include gp-button-color-red-active; }
    &.disabled, &[disabled] { @include gp-button-color-red-disabled; }
  }
  &.green {
    @include gp-button-color-green;
    &:hover { @include gp-button-color-green-hover; }
    &:active, &.active { @include gp-button-color-green-active; }
    &.disabled, &[disabled] { @include gp-button-color-green-disabled; }
  }
  &.grey {
    @include gp-button-color-grey;
    &:hover { @include gp-button-color-grey-hover; }
    &:active, &.active { @include gp-button-color-grey-active; }
    &.disabled, &[disabled] { @include gp-button-color-grey-disabled; }
  }
  &.facebook {
    @include gp-button-color-facebook;
    &:hover { @include gp-button-color-facebook-hover; }
    &:active, &.active { @include gp-button-color-facebook-active; }
  }
  &.twitter {
    @include gp-button-color-twitter;
    &:hover { @include gp-button-color-twitter-hover; }
    &:active, &.active { @include gp-button-color-twitter-active; }
  }
  &.pinterest {
    @include gp-button-color-pinterest;
  }
  &.high-five {
    @include gp-button-color-high-five;
  }

  &.left-arrow {
    text-transform: uppercase;
  }
  &.right-arrow {
    text-transform: uppercase;
  }
}

.fake-button {
  @include gp-button-base;
  @include gp-button-size-default;
  &.red {
    @include gp-button-color-red;
  }
}

@mixin gp-text-button {
  @include reset-button;
  cursor: pointer;

  color: #888;
  &:hover {
    color: $gris;
  }

  @include gp-button-size-default;
  &.tiny {
    @include gp-button-size-tiny;
  }
  &.small {
    @include gp-button-size-small;
  }
  &.large {
    @include gp-button-size-large;
  }
  &.wide {
    @include gp-button-size-wide;
  }

  &.on-left {
    padding-left: 0;
  }
}

.text-button {
  @include gp-text-button;
}

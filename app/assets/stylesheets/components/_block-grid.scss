/**
* Block Grid Component by @mjlescano
* Wanna know how it works?
*   http://www.barrelny.com/blog/text-align-justify-and-rwd/
* It is intended to work with the same classes conventions than foundation's:
*   http://foundation.zurb.com/docs/components/block-grid.html
*
* EXAMPLE:
    .container {
      position: relative;
      @include block-grid-container;
      .pictures-uploader-item {
        @include block-grid-item;
        width: 20%;
      }
    }
*
* WARNING:
  * When adding block grid elements with JS, also add carriage return. Has to be
  * a space between them, because the items are inline-block.
    $el.append($blockGridItem)
    $el.append('\r\n')
    $el.append($blockGridItem)

  * Also, dont forget to trigger a redraw to make sure it calculates well the sizes:
  $('.block-grid').fadeOut(0).fadeIn(0)
*/

@mixin block-grid-container {
  text-align: justify;
  -ms-text-justify: distribute-all-lines;
  text-justify: distribute-all-lines;
  line-height: 0;
  &:after {
    content: '';
    display: inline-block;
    width: 100%;
    font-size: 0;
    visibility: hidden;
  }
}

@mixin block-grid-item {
  display: inline-block;
  line-height: normal;
  vertical-align: top;
}

@mixin block-grid-item-last-odd-on-3-columns {
  &:nth-child(3n-1):last-child {
    @content;
  }
}

%block-grid {
  @include block-grid-container;
  li {
    @include block-grid-item;
  }
}

[class*="block-grid-"], .block-grid {
  @extend %block-grid;
}

@mixin create-block-grid-classes($name, $media-query) {
  @media #{$media-query} {
    @for $i from 1 through $total-columns {
      .#{$name}-block-grid-#{($i)} {
        li {
          width: floor(100% / $i) - 0.1;
        }
      }
    }
  }
}

@include create-block-grid-classes('small', $screen);
@include create-block-grid-classes('medium', $from-medium);
@include create-block-grid-classes('large', $from-large);
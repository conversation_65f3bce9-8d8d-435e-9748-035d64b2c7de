.reveal-modal-bg { @include reveal-bg; }
.#{$close-reveal-modal-class} {
  float: $opposite-direction;
}

%gray-modal-sprite {
  background-repeat: no-repeat;
  background-image: image-url('social/whatsups_form/uploader-sprite.png');
}

.gray-modal {
  // Foundation Reveal
  @include reveal-modal-base($base-style: true, $width: 98%);
  @include reveal-modal-style(
    $bg: $blanco-oscuro,
    $padding: 0,
    $border: true,
    $border-color: $gris-claro,
    $box-shadow: false
  );
  @media (min-width: 550px) {
    @include reveal-modal-style(
      false,
      0,
      false,
      $box-shadow: false,
      $top-offset: emCalc(75px)
    );
    @include reveal-modal-base(false, 550px);
  }
  border-radius: 4px;

  $local-gutter: 1em;
  .gray-modal-top {
    position: relative;
    border-bottom: 1px solid $gris-claro;
    .title {
      font-size: 1.2em;
      margin: 0;
      padding: 0.55em 0.9em;
      text-transform: uppercase;
    }
    .close-reveal-modal {
      cursor: pointer;
      margin: 0;
      padding: 0.8em 1.2em;
      max-height: 42px;
      max-width: 40px;
      border-#{$default-float}: 1px solid $gris-claro;
      background-position: -127px 12px;
      @extend %gray-modal-sprite;
      &:hover {
        background-position: -127px -36px;
      }
      @media #{$til-small} {
        padding: 1.3em 1.5em;
        background-position: -135px 5px;
        &:hover {
          background-position: -135px -43px;
        }
      }
      /*
      @media #{$after-wide} {
        background-position: -171px 10px;
        &:hover {
          background-position: -171px -50px;
        }
      }
      */
    }
  }
  .gray-modal-content {
    @extend .cf;
    padding: ($local-gutter * 1.1) $local-gutter;
  }
  .gray-modal-bottom {
    @extend .cf;
    padding: ($local-gutter * 1.1) $local-gutter;
    background-color: $blanco;
    border-top: 1px solid $gris-claro;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    .share {
      padding: 0;
      width: 46px;
      height: 45px;
      text-align: center;
      line-height: normal;
      .icon {
        width: 28px;
        height: 30px;
        display: inline-block;
        @extend %gray-modal-sprite;
      }
      &.facebook-share {
        margin-right: emCalc(18px);
        .icon {
          width: 15px;
          background-position: -94px 5px;
        }
        &:hover {
          .icon {
            background-position: -94px -43px;
          }
        }
        &.active {
          background-color: #4c70ba;
          @include linear-gradient (#4c70ba, #294786);
          border: 1px solid #2d4d92;
          box-shadow: inset 0 1px 0 0 #8a9ec8;
          .icon {
            background-position: -94px -91px;
          }
        }
      }
      &.twitter-share {
        .icon {
          background-position: -39px 5px;
        }
        &:hover {
          .icon {
            background-position: -40px -43px;
          }
        }
        &.active {
          background-color: #4fcaf1;
          @include linear-gradient (#4fcaf1, #07aee3);
          border: 1px solid #07b9f1;
          box-shadow: inset 0 1px 0 0 #9addf2;
          .icon {
            background-position: -40px -90px;
          }
        }
      }
    }
    .post-item {
      position: relative;
      float: right;
      cursor: pointer;
      margin: 10px 2.2em 0 0;
      line-height: 1.6em;
      font-size: 1em;
      font-weight: bold;
      color: $gris;
      text-transform: uppercase;
      background: transparent;
      border: none;
      &:hover:enabled {
        color: $rojo;
        .arrow {
          background-position: -190px -48px;
        }
      }
      &[disabled] {
        color: lighten($gris, 25%);
        cursor: not-allowed;
        .arrow {
          opacity: 0.5;
        }
      }
      .arrow {
        position: absolute;
        left: 108%;
        top: 2px;
        width: 26px;
        height: 22px;
        background-position: -190px 0;
          @media #{$til-small} {
            top: 0;
          }
        @extend %gray-modal-sprite;
      }
    }
  }
}
@mixin gp-panel {
	padding: $column-gutter;
	// border: 1px solid darken($gris-claro, 15%);
  border-radius: 3px;
  -moz-box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);
}

.gp-panel {
	@include gp-panel;
  @include linear-gradient(white, $blanco);
}

.gp-simple-panel {
  @include gp-panel;
  background-color: white;
}

@mixin gp-checkout-section{
  border: 1px solid #ddd;
  border-radius: 7px;
  background-color: #EFEFEF;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2);
  margin: 0 3px 40px 3px;
}

@mixin gp-checkout-box{
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #ddd;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.gp-info-panel {
  padding: $column-gutter;
  border: 1px dashed #999;
  font-size: 0.8em;
  color: #666;
  text-align: center;
  background-color: $blanco-oscuro;
}

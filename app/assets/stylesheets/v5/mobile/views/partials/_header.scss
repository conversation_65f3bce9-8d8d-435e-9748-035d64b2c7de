$mobile-header-height: 48px;
$mobile-header-bg-color: $black;

.mobile-header-phantom {
  @include wide-outer-container;
  height: $mobile-header-height;
  background-color: $mobile-header-bg-color;
}

.mobile-header {
  @include wide-outer-container;
  z-index: 50;
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  color: #2A2A2A;
  background-color: #FFFFFF;
  .h-btn-menu {
    @include unselectable;
    position: relative;
    float: left;
    display: block;
    border: none;
    margin-right: 12px;
    padding: 0 12px;
    border-right: 1px solid #D9D9D9;
    height: $mobile-header-height;
    cursor: pointer;
    color: #bdbdbd;
    background: none;
    &:active {
      background-color: #999;
      box-shadow: inset 0px 0px 3px #333;
      color: $mobile-header-bg-color;
    }
    .i-menu {
      position: relative;
      top: 0px;
      font-size: 23px;
    }
  }
  .h-logo {
    @include unselectable;
    float: left;
    display: block;
    cursor: pointer;
    height: $mobile-header-height;
    img {
      margin-top: 10px;
    }
  }
  .h-btn {
    @extend %form-reset-button;
    @include unselectable;
    $h-btn-h: $mobile-header-height;
    position: relative;
    float: right;
    display: block;
    margin-top: 1px;
    padding: 0 12px;
    height: $h-btn-h;
    cursor: pointer;
    &:active {
      .h-btn-icon { color: #666; }
    }
    .h-btn-icon {
      color: #bdbdbd;
      font-size: 23px;
    }
    .h-btn-count {
      position: absolute;
      right: 4px; top: 3px;
      border: 2px solid $mobile-header-bg-color;
      border-radius: 50%;
      padding: .3em .6em .3em .5em;
      font-size: .45em;
      line-height: 1em;
      text-align: center;
      font-weight: bold;
      font-family: monospace;
      color: white;
      background-color: $red;
      &[data-count="0"] {
        display: none;
      }
    }
  }
}

.mobile-header-overlay {
  z-index: 50;
  position: absolute;
  left: 0; top: 0;
  width: 100%; height: 100%;
  border-top: $mobile-header-height solid transparent;
  background-color: #E8E8E8;
  box-shadow: 0 -1px 14px black;
}

.header-slim-promo {
  font-size: 0.85em;
  color: #2A2A2A;
  font-weight: 800;
  text-align: center;
  text-transform: uppercase;
  padding: $grid-gutter/2;
  background: #D9D9D9;
  top: 0;
  left: 0;
  width: 100%;
  font-weight: bold;
}

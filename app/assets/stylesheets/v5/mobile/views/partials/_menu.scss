.menu-wrapper {
  z-index: 100;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  position: fixed;
  top: 0;
  left: 0;
  padding-bottom: .9em;
  height: 100%;
  max-width: 100%;
  width: 17em;
  background-color: #2a2a2a;

  display: none;

  .csstransforms3d & {
    display: block;
    @include transition(all .2s ease-in);
    @include transform(translate3d(-100%,0,0));
  }

  &.active {
    display: block;
    @include transform(translate3d(0,0,0));
    & + .menu-wrapper-overlay {
      display: block;
      .csstransitions & {
        visibility: visible;
        opacity: 1;
      }
    }
  }
}

.menu-wrapper-overlay {
  @include cover-all;
  position: fixed;
  z-index: 90;
  background-color: #fff;
  background-color: rgba(#fff, .7);
  cursor: pointer;
  @include transition(all .2s linear);
  display: none;

  .csstransitions & {
    display: block;
    visibility: hidden;
    opacity: 0;
  }
}
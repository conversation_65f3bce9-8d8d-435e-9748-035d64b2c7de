footer {
  @include wide-outer-container;
  .top{
    nav {
      overflow:hidden;
      ul{
        width: 50%;
        float:left;
      }
      h2 {
        color: $darker-gray;
        font-size: .8em;
        line-height: 1.45;
        font-weight: 900;
        text-transform: uppercase;
      }
      a, p {
        font-size: .7em;
        line-height: 1.45;
        color: $dark-gray;
        border-bottom: 1px solid transparent;
        @include transition(all .1s linear);
      }
      a:hover {
        color: $darker-gray;
        border-bottom: 1px solid #999;
      }
      padding: 1em;
      background-color: $lighter-gray;
    }
  }
  .networks{
    padding: 1em;
    background-color: $black;
    color:$white;
  }
  .input-wrapper {
    &.i-shipping{
      margin:4px 0 10px 0;
    }
    position: relative;
    &:before {
      position: absolute;
      left: .4em;
      color: $blue;
      font-size: 1.6em;
      line-height: 1.65em;
    }
  }

  input {
    display: block;
    margin: 0;
    padding: .8em;
    padding-left: 3.2em;
    border: none;
    max-width: 100%;
    width: 100%;
    height: auto;
    font-size: .9em;
    box-shadow: none;
    border-radius: 3px;
    background-color: white;
    font-family: inherit;
  }

  input.error{
    outline-color: red;
  }
}

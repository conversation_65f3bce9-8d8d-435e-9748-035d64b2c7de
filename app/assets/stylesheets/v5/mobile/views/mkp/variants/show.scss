#variants-v5-show{
  main {
    @include outer-container;
    padding-top: $grid-gutter/2;
    article{
      position: relative;
      .product-main{
        @include container;
        .pictures{
          position:relative
        }
      }
    }
  }
  @import './partials/product_name';
  @import './partials/pictures';
  @import './partials/rate_stars';
  @import './partials/buybox';
  @import './partials/extras';
  @import './partials/related_and_others_products';
  @import './partials/buybox/size_chart';

  .selectize-control.single .selectize-input{
    border:0px none;
    background: transparent;
    font-size: .9rem;
    box-shadow: none;
    padding: $grid-gutter/2 $grid-gutter/2;
    width: 100%;
    color: $gray;
  }

  .selectize-control {
    background-color: $lightest-gray;
    border-radius: $border-radius;
  }
  .select.select-theme-default .select-content {
    min-width: 10rem;
    .select-options {
      font-size: .9rem;
      .select-option{
        &:hover, &.select-option-highlight {
          background-color: $blue;
          color: $white;
        }
      }
    }
  }
  .selectize-dropdown {
    color: $gray;
    font-size: .9rem;
    [data-selectable], .optgroup-header{
      padding: $grid-gutter/2;
    }
    .active {
      border-weight: 3px;
      border-color: $blue;
    }
  }
}

.size-property {
  font-size: 1rem;
  .size {
    @include buttons-plain('white', 'normal');
    margin:0 $grid-gutter/4 $grid-gutter/4 0;
    border-width: 1px;
    padding: .7em .8em;
    text-align: center;
    line-height: 1.4em;
    min-width: 36px;
    @include transition(all .15s ease-out);
    &:hover {
      border-color: $blue;
      background-color: $light-blue;
    }
    &.selected {
      color: $white;
      border-color: $blue;
      background-color: $light-blue;
    }
    &.disabled {
      color: $gray;
      border-color: $gray;
      background-color: $lighter-gray;
      position: relative;
      // display: none;
      &:after, &::after{
        border-bottom: 2px solid $red;
        content: "";
        left: -5%;
        margin-top: calc(0.125em / 2 * -1);
        position: absolute;
        top: 50%;
        width: 110%;
        @include transform(rotate(-45deg));
      }
    }
  }
}
.size-chart-button{
  color:$gray;
  cursor:pointer;
  @include transition(all .15s ease-out);
  &:hover{
    color:$blue;
  }
}

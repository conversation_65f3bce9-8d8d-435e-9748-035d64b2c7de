.product-price {
  text-align: center;
  margin: $grid-gutter/4 0 0;
  .price, .sale-price{
    line-height: 1.6rem;
    letter-spacing: -0.5px;
    margin-right: $grid-gutter/2;
    display: inline-block;
    &:last-child{
      margin-right:0;
    }
    .amount, .currency {
      font-weight: 200;
    }
    .amount {
      font-size: 2rem;
    }
    .currency {
      font-size: 1.2rem;
    }
  }
  .price {
    color: $gray;
  }
  .sale-price{
    color: $gray;
    .amount{
      font-size: 2rem;
    }
    .discount{
      font-size: 1.2rem;
      margin-left: $grid-gutter/2;
      color: $light-green;
      font-style:italic;
    }
  }
  &.on-sale{
    .price {
      color: $light-gray;
      line-height: 2rem;
      letter-spacing: 0;
      position: relative;
      &:after, &::after{
        border-bottom: 2px solid $light-green;
        content: "";
        left: 0;
        margin-top: calc(0.125em / 2 * -1);
        position: absolute;
        right: 0;
        top: 50%;
        @include transform(rotate(-7deg));
      }
      .amount, .currency {
        font-size: 1.1rem;
      }
    }
  }

}

.product-extras{
  .tabs {
    @include clearfix;
    margin-top: $grid-gutter;
    border-top: 1px solid $lightest-gray;
    background-color: $white;
    > section {
      @media #{$after-large} {
        display: inline;
      }
      > a {
        color: $gray;
        display: block;
        padding: $grid-gutter $grid-gutter;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: normal;
        text-transform: uppercase;
        margin-top: -1px;
        @include transition(all .15s ease-out);
        @media #{$before-large} {
          border-top: 1px solid $lightest-gray;
        }
        @media #{$after-large} {
          display: inline-block;
        }
        &:hover {
          color: $blue;
          border-top: 1px solid $blue;
        }
        &:focus {
          outline: none;
        }
        &.active {
          border-top: 3px solid $blue;
          margin-top: -3px;
          color: $blue;
        }
      }

      > .content {
        color: $gray;
        display: none;
        padding: $grid-gutter/2 $grid-gutter $grid-gutter;
        width: 100%;
        border-bottom: 1px solid $lightest-gray;
        font-size: 0.9rem;
        line-height: 1.5rem;
        @media #{$after-large} {
          float: left;
        }
      }
    }
    @import './extras/questions';
    @import './extras/reviews';
    @import './extras/question_and_review_forms';
    @import './extras/brand_info';
  }
  .logged-in-required{
    color: $blue;
    text-align: center;
    cursor: pointer;
    @include transition(all .15s ease-out);
    &:hover{
      color:$light-blue;
    }
  }
}

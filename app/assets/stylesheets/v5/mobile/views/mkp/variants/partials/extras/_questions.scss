section.questions {
  .content {
    .no-questions{
      font-style:italic;
      text-align: center;
    }
  }
  .questions-list{
    @include container;
    .question{
      @include span(1 of 2);
      margin-bottom: $grid-gutter;
      &:nth-of-type(2n){
        @include omega;
      }
      @media #{$before-large} {
        @include span(1 of 1);
        @include omega;
      }
      .questioner{
        @include span(1 of 4);
        @media #{$before-large} {
          @include span(1 of 5);
        }
        font-size: 0.9em;
        .avatar{
          .avatar{
            width:48px;
            height:48px;
            border-radius: 30px;
            margin: 0 auto $grid-gutter/4;
          }
        }
        > span {
          display:block;
          text-align:center;
          line-height:1rem;
        }
        .date{
          color:$light-gray;
          font-style:italic;
        }
      }
      .content{
        @include span(3 of 4);
        @include omega;
        @media #{$before-large} {
          @include span(4 of 5);
          @include omega;
        }
        > .description{
          font-style:italic;
          font-size: 0.95em;
          color: $dark-gray;
        }
        .reply{
          margin-top: $grid-gutter/4;
          padding-top: $grid-gutter/4;
          border-top: 1px dashed $lightest-gray;
          text-align: right;
          > .description{
            font-style:italic;
            font-size: 0.95em;
          }
          .replier{
            font-size: 0.9em;
            span{
              font-style:italic;
              margin-right:$grid-gutter/4;
              color: $light-gray;
            }
          }
        }
      }
    }
  }
}

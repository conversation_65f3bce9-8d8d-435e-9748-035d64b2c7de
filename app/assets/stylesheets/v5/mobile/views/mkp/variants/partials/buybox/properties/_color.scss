.color-property {
  @include container;
  .color {
    position: relative;
    float: left;
    width: $grid-gutter * 1.8;
    height: $grid-gutter * 1.8;
    margin:0px $grid-gutter/4 $grid-gutter/2 0px;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid transparent;
    @include transition(all .1s linear);
    &.selected {
      border-width:2px;
      border-color: $blue !important;
      &:hover{
        border-color: $blue;
      }
      &:after{
        position: absolute;
        content: "";
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        left: 11px;
        top: 115%;
        border-bottom: 5px solid $blue;
      }
    }
    a {
      @include fill-container;
    }
    &:hover{
      border-color: $lighter-gray;
    }
  }
}

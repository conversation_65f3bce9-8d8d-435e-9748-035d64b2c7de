.size-chart-dropdown-popup {
  .mfp-container {
    padding: 0;
  }
  .mfp-content {
    height: 100%;
    max-width: 700px;
    max-height: 450px;
    overflow: hidden;
    .mobile & {
      max-height: 768px;
      max-width: 768px;
    }
  }
}
.size-chart {
  $padding-bottom: 1em;
  position: relative;
  padding: 1em 1em;
  max-width: 100%;
  height: 100%;
  font-size: 16px;
  background-color: $white;

  h2 {
    width: 100%;
    color: $black;
    font-weight: 900;
    font-size: 1em;
    text-transform: uppercase;
    line-height: 1.5;
    margin-bottom: 1em;
  }

  > h2:after {
    content: '';
    display: block;
    border-bottom: 1px dashed $light-gray;
  }

  .content {
    overflow: auto;
    height: 90%;

    .image {
      text-align: center;
      margin-bottom: 1em;
    }

    table {
      max-width: 100%;
    }

    th {
      text-align: left;
      text-transform: uppercase;
      background-color: $dark-gray;
      color: #fff;
      font-weight: normal;
    }

    .table {
      width: 100%;
      margin-bottom: 20px;
    }

    .table > thead > tr > th,
    .table > tbody > tr > th,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > tbody > tr > td,
    .table > tfoot > tr > td {
      padding: 8px;
      line-height: 1.428571429;
      vertical-align: top;
      border-top: 1px solid #ddd;
      text-align: center;
    }
    .table > tbody > tr > td {
      color: $dark-gray;
    }

    .table > thead > tr > th {
      vertical-align: bottom;
      border-bottom: 2px solid #ddd;
      .mobile & {
        font-size: 0.5em;
      }
    }

    .table > caption + thead > tr:first-child > th,
    .table > colgroup + thead > tr:first-child > th,
    .table > thead:first-child > tr:first-child > th,
    .table > caption + thead > tr:first-child > td,
    .table > colgroup + thead > tr:first-child > td,
    .table > thead:first-child > tr:first-child > td {
      border-top: 0;
    }

    .table > tbody + tbody {
      border-top: 2px solid #ddd;
    }

    .table .table {
      background-color: #fff;
    }

    .table-condensed > thead > tr > th,
    .table-condensed > tbody > tr > th,
    .table-condensed > tfoot > tr > th,
    .table-condensed > thead > tr > td,
    .table-condensed > tbody > tr > td,
    .table-condensed > tfoot > tr > td {
      padding: 5px;
    }

    .table-bordered {
      border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > tbody > tr > th,
    .table-bordered > tfoot > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > tbody > tr > td,
    .table-bordered > tfoot > tr > td {
      border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > thead > tr > td {
      border-bottom-width: 2px;
    }

    .table-striped > tbody > tr:nth-child(odd) > td {
      background-color: $light-gray;
    }
    .table-striped > tbody > tr:nth-child(even) > td {
      background-color: $lightest-gray;
    }

    .table-hover > tbody > tr:hover > td,
    .table-hover > tbody > tr:hover > th {
      background-color: #f5f5f5;
    }
  }
}

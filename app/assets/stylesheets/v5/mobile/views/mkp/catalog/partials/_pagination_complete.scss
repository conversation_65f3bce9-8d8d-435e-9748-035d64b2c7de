.pagination-complete {
  text-align: center;
  color: $dark-gray;
  span, em, a {
    border: 1px solid;
    border-color: $white;
    border-radius: $grid-gutter;
    padding: $grid-gutter/2.5 $grid-gutter/1.75;
    margin-right: $grid-gutter/3;
    display: inline-block;
    @media #{$before-large} {
      font-size: .9rem
    }
    @include transition(all .15s ease-out);
    &:last-child{
      margin-right: 0;
    }
    &.disabled {
      color: $gray;
      border-color: $gray;
    }
    &.current {
      color: $white;
      border-color: $blue;
      background-color:$light-blue;
      font-weight: bold;
    }
  }
  a {
    &:hover{
      border-color: $gray;
      background-color: $lightest-gray;
      &.previous_page, &.next_page{
        background-color: transparent;
      }
    }
  }
  .previous_page, .next_page{
    border: 0px none;
  }
}

#catalog-v5-index{
  main {
    @include outer-container;
    padding-top: $grid-gutter/2;
    .catalog-header{
      @include span(1 of 1);
      @include omega;
    }
    .catalog-content {
      @include span(1 of 1);
      @include omega;

      @media #{$til-smallish} {
        @include variants-list-display(1);
      }
      @media #{$after-smallish} {
        @include variants-list-display(2);
      }
    }
  }

  .catalog-content{
    .catalog-bottom{
      @include container;
      position: relative;
      border-top: 1px solid $lightest-gray;
      margin-top: $grid-gutter;
      .pagination-complete {
        @include span;
        @include omega;
        padding: $grid-gutter 0;
      }
    }
  }
  .breadcrumb-wrapper{
    display:none;
  }
  @import './partials/catalog_title';
  @import './partials/sort_and_pagination';
  @import './partials/cover';
  @import './partials/pagination_complete';
}

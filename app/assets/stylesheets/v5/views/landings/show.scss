#landings-v5-show, #landings {
  .main-container {
    @include outer-container;
  }

  .gray-color{
    background-color: #F4F3F4;
  }

  .page-title,
  .page-subtitle {
    @include hide-for-seo;
  }

  .component-title {
    margin: 0 0 $grid-gutter;
    font-size: 1.5em;
    color: $dark-gray;
    text-align: center;
    font-weight: 400;
    text-transform: uppercase;
    .mobile & {
      margin-bottom: $grid-gutter / 4;
    }
  }
}

.override-with-white-color {
  background-color: #FFFFFF !important;
}

.hero-main-vnrs {
  @include wide-outer-container;
  position: relative;
  overflow: hidden;
  background-color: $black;
  z-index: 0;

  .mobile & {
    margin-bottom: 10px;
  }

  .wrapper {
    @include clearfix;

    .sizer {
      @include outer-container;

      span {
        display: block;
        padding-top: 36%;
        @media #{$til-medium}{
          padding-top: 29%;
        }
      }
    }
  }

  article {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;

    display: none;
    &.active {
      display: block;
      z-index: 5;
    }

    .csstransitions & {
      display: block;
      opacity: 0;
      @include transition(opacity .3s ease-out .1s);

      &.active {
        opacity: 1;
        @include transition(opacity .3s ease-in);
      }
    }

    a {
      @include cover-all;
    }

    h2,
    p {
      @include hide-for-seo;
    }
  }

  .points {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    padding: 1em 0;
    font-size: .7em;
    z-index: 10;

    .mobile & {
      padding-bottom: 0;
      display: none;
    }

    strong {
      position: relative;
      display: inline-block;
      padding: .5em;
      cursor: pointer;

      .mobile & {
        margin-left: .5em;
        margin-right: .5em;
      }

      &:hover {
        span:after {
          background-color: $red;
        }
      }

      &.active {
        span {
          border-color: $red;
          &:after {
            background-color: $red;
          }
        }
      }
    }

    span {
      display: block;
      padding: 2px;
      border: 1px solid white;
      border-radius: 100%;
      @include transition(all .1s linear);
      &:after {
        content: '';
        display: block;
        width: .8em;
        height: .8em;
        border-radius: 100%;
        background-color: white;
        @include transition(all .1s linear);
      }
    }
  }

  .prev,
  .next {
    position: absolute;
    top: 0;
    width: 10%;
    height: 100%;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center center;
    @include transition(all .1s linear);
    opacity: .4;
    z-index: 5;

    .mobile & {
      background-size: 50% auto;
    }

    &:hover {
      opacity: .8;
    }

    &.prev {
      left: 0;
      background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI3NSIgdmlld0JveD0iMCAwIDQwIDc1Ij48cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTM5LjMzMiA2OS45NjVsLTMyLjI2MS0zMi4yNjEgMzIuMTY4LTMyLjE2OC0zLjUzNi0zLjUzNi0zNS43MDMgMzUuNzA0IDM1Ljc5NyAzNS43OTZ6Ii8+PC9zdmc+);
    }

    &.next {
      right: 0;
      background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI3NSIgdmlld0JveD0iMCAwIDQwIDc1Ij48cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTAgNS41MzVsMzIuMjYyIDMyLjI2MS0zMi4xNjggMzIuMTY4IDMuNTM1IDMuNTM2IDM1LjcwMy0zNS43MDQtMzUuNzk3LTM1Ljc5NnoiLz48L3N2Zz4=);
    }
  }
}

.hero-component {
  @include clearfix;
  margin-top:-30px;
  margin-bottom: 20px;
  .banner{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    @media #{$after-medium}{
      @include span(1 of 2, 20px);
      @include omega;
    }
    @media #{$til-medium}{
      margin-top:20px;
    }
    a {
      vertical-align: middle;
    }
    img {
      width: 100%;
    }
  }
  .main {
    @media #{$after-medium}{
      @include span(1 of 2, 20px);
    }
    position: relative;
    overflow: hidden;
    background-color: $black;
    z-index: 0;

    .mobile & {
      margin-bottom: 10px;
    }
    .wrapper {
      @include clearfix;
      .sizer {
        width: 100%;
        height: 0;
        padding-bottom: 89.6774194%;
      }
    }

    article {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;

      display: none;
      &.active {
        display: block;
        z-index: 5;
      }

      .csstransitions & {
        display: block;
        opacity: 0;
        @include transition(opacity .3s ease-out .1s);

        &.active {
          opacity: 1;
          @include transition(opacity .3s ease-in);
        }
      }

      a {
        @include cover-all;
      }

      h2,
      p {
        @include hide-for-seo;
      }
    }

    .points {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      text-align: center;
      padding: 1em 0;
      font-size: .7em;
      z-index: 10;

      .mobile & {
        padding-bottom: 0;
        display: none;
      }

      strong {
        position: relative;
        display: inline-block;
        padding: .5em;
        cursor: pointer;

        .mobile & {
          margin-left: .5em;
          margin-right: .5em;
        }

        &:hover {
          span:after {
            background-color: $red;
          }
        }

        &.active {
          span {
            border-color: $red;
            &:after {
              background-color: $red;
            }
          }
        }
      }

      span {
        display: block;
        padding: 2px;
        border: 1px solid white;
        border-radius: 100%;
        @include transition(all .1s linear);
        &:after {
          content: '';
          display: block;
          width: .8em;
          height: .8em;
          border-radius: 100%;
          background-color: white;
          @include transition(all .1s linear);
        }
      }
    }

    .prev,
    .next {
      position: absolute;
      top: 0;
      width: 10%;
      height: 100%;
      cursor: pointer;
      background-repeat: no-repeat;
      background-position: center center;
      @include transition(all .1s linear);
      opacity: .4;
      z-index: 5;

      .mobile & {
        background-size: 50% auto;
      }

      &:hover {
        opacity: .8;
      }

      &.prev {
        left: 0;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI3NSIgdmlld0JveD0iMCAwIDQwIDc1Ij48cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTM5LjMzMiA2OS45NjVsLTMyLjI2MS0zMi4yNjEgMzIuMTY4LTMyLjE2OC0zLjUzNi0zLjUzNi0zNS43MDMgMzUuNzA0IDM1Ljc5NyAzNS43OTZ6Ii8+PC9zdmc+);
      }

      &.next {
        right: 0;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI3NSIgdmlld0JveD0iMCAwIDQwIDc1Ij48cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTAgNS41MzVsMzIuMjYyIDMyLjI2MS0zMi4xNjggMzIuMTY4IDMuNTM1IDMuNTM2IDM1LjcwMy0zNS43MDQtMzUuNzk3LTM1Ljc5NnoiLz48L3N2Zz4=);
      }
    }
  }

}
.margin-landing {
  margin-bottom: 80px !important;
}

.cols2-generic-v1-vnrs {
  @include container;
  margin-bottom: $grid-gutter * 2;

  .cols-4 {
    margin: 0 2.5%;
    height: 160px;
    width: 20%;

    .brand-coverando {
      .avatar-wrapper {
        padding-top: 50%;
        max-height: 160px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;

        .avatar {
          position: absolute;
          top: 50%;
          left: 50%;
          margin-left: -12.5%;
          width: 25%;
          height: 50%;
          border: 2px solid white;
          font-size: 2em;
          border-radius: 100%;
          background-color: #fff;

          @media(max-width: 780px) {
            margin-left: -7.5%;
            width: 15%;
            height: 30%;
            top: 10%;
          }
          @media(max-width: 530px) {
            top: 10%;
          }
          @media(max-width: 360px) {
            top: 25%;
          }


          img {
            background-image: none;
            background-color: white;
            width: 100%;
            max-width: 100%;
            opacity: 1;
            height: 100%;
            padding-top: 0;
          }
        }
      }
    }

    .info {
      .links {
        font-size: 13px;
        line-height: 1.5;
        color: #2a2a2a;
        font-weight: normal;
      }
    }

    &:nth-of-type(2n) {
      clear: initial;
      margin: 0 2.5%;
    }
  }

  .featured-brand-new {
    .users-with-cover {
      article {
        width: 100%;
      }
    }
  }

  .mobile & {
    margin-bottom: 10px;
  }

  article {
    height: 214px;
    position: relative;
    margin-bottom: $grid-gutter;
    text-align: center;
    background-color: $darker-gray;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;

    @media #{$mobile} {
      background-size: contain;
      background-color: #F4F3F4;
      margin-bottom: 20px !important;
      height: auto !important;
    }

    @include span(1 of 2);
    &:nth-of-type(2n) {
      @include omega;
    }

    .mobile & {
      @include span;
      @include omega;
      margin-bottom: 5px;
    }

    .no-touch &:hover {
      .bg {
        background-color: rgba(black, .8);
      }
    }

    h2 {
      position: absolute;
      top: 0;
      left: 0;
      margin: 0;
      width: 100%;
      height: 100%;
    }
  }

  .bg {
    @include transition(all .1s linear);
    @include cover-all;
  }

  img {
    display: block;
    width: 100%;
    height: 0;
    padding-top: 43%;
    @include opacity(0);
  }

  .content-table {
    display: table;
    width: 100%;
    height: 100%;
  }

  .content-table-cell {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
  }

  .text {
    display: inline-block;
    font-size: .75em;
    padding: .625em;
    border: 1px solid white;
    text-transform: uppercase;
    color: white;
    background-color: #333;
    background-color: rgba(black, .4);
    @include transition(all .1s linear);

    .no-touch &:hover {
      color: $black;
      background-color: white;
    }
  }
}

.cols2-pic3-v1-vnrs {
  @include container;
  margin-bottom: $grid-gutter * 2;

  aside {
    float: right;
    width: 49% !important;

    @media #{$mobile} {
      width: 100% !important;
    }

    article {
      @media #{$mobile} {
        margin-bottom: 20px !important;
      }
    }

    .image {
      height: 14.8em !important;
    }

    .mobile &{
      .image {
        height: 6.6em !important;
      }
    }
  }
  section {
    float: left;
    width: 49% !important;

    @media #{$mobile} {
      width: 100% !important;
      margin-bottom: 20px;
    }

    .image {
      height: 31.0em !important;
    }
    .mobile &{
      .image {
        height: 13.6em !important;
      }
    }
  }

  article {
    position: relative;
    margin-bottom: $grid-gutter;
    text-align: center;
    background-color: $darker-gray;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    background-size: 100% 100%;



    .mobile &{
      @include span;
      @include omega;
      margin-bottom: 5px;
    }

    .no-touch &:hover {
      .bg {
        background-color: rgba(black, .8);
      }
    }

    h2 {
      position: absolute;
      top: 0;
      left: 0;
      margin: 0;
      width: 100%;
      height: 100%;
    }
  }

  .bg {
    @include transition(all .1s linear);
    @include cover-all;
  }

  img {
    display: block;
    width: 60%;
    height: 0;
    padding-top: 43%;
    @include opacity(0);
  }

  .content-table {
    display: table;
    width: 100%;
    height: 100%;
  }

  .content-table-cell {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
  }

  .text {
    display: inline-block;
    font-size: .75em;
    padding: .625em;
    border: 1px solid white;
    text-transform: uppercase;
    color: white;
    background-color: #333;
    background-color: rgba(black, .4);
    @include transition(all .1s linear);

    .no-touch &:hover {
      color: $black;
      background-color: white;
    }
  }
}

.cols5-generic-v1-vnrs {
  @include container;
  position: relative;
  margin-bottom: $grid-gutter * 2;

  .mobile & {
    .articles-list {
      margin-bottom: 0;
      width: auto;
      overflow-x: hidden;
      overflow-y: hidden;
      white-space: nowrap;

      article{
        display: inline-block;
        vertical-align: middle;
        float: none !important;
        border-right: 0px !important;
      }
    }
    @include articles-list-full-width;
  }

  article {
    z-index: 0;
    position: relative;
    margin-bottom: $grid-gutter;
    @include span(1 of 5);
    @include omega(5);

    .no-touch &:hover {
      div {
        background-color: rgba(black, .8);
      }
    }
    img {
      width: 100%;
    }
    .link {
      z-index: 5;
      @include cover-all;
    }
    div {
      position: absolute;
      left: 0;
      bottom: 1em;
      width: 100%;
      padding: 10px;
      text-align: center;
      background-color: #333;
      background-color: rgba(black, .4);
      @include transition(all .1s linear);
    }
    h2 {
      font-weight: 500;
      margin: 0;
      color: white;
      font-size: 1.4em;
      line-height: 1.1;
      font-kerning: normal;
    }
    p {
      .mobile & {
        display: none;
      }
      margin: 0;
      color: white;
      font-size: .8em;
      a {
        position: relative;
        z-index: 10;
        text-decoration: none;
        color: white;
        margin-right: .7em;
        &:after {
          content: ' / ';
          position: absolute;
          left: 105%;
          top: 50%;
          margin-top: -.45em;
        }
        &:last-child {
          margin-right: 0;
          &:after { content: normal; }
        }
        &:hover {
          border-bottom: 1px solid white;
        }
      }
    }
  }
}

.links-cmpt {
  @include container;
  margin-bottom: $grid-gutter * 2;

  .mobile & {
    display: none;
  }

  ul {
    @include span(1 of 4);
    &:nth-of-type(4n) {
      @include omega;
    }
  }
  li {
    text-align:center;
  }
  h2{
    font-size: 1em;
    margin-bottom: em($grid-gutter / 4);
  }
  a {
    font-size: .8em;
    line-height: 1.5em;
    color: $gray;
    border-bottom: 1px solid transparent;
    @include transition(all .1s linear);
    &:hover {
      color: $darker-gray;
      border-bottom: 1px solid #999;
    }
  }
}

.featured-brands-cmpnt {
  position: relative;
  margin-bottom: $grid-gutter * 2;

  .mobile & {
    margin-bottom: $grid-gutter;
    .users-with-cover {
      margin-bottom: 0;
    }
  }

  .users-with-cover {
    margin-bottom: $grid-gutter;

    .cover {
      &:before {
        content: initial;
      }
    }
  }

  @include users-with-cover-full-width;
}

.featured-brandy {
  margin-bottom: 40px;
  overflow:hidden;

  .full-width {
    width: 100%;
    text-align: center;
  }

  .medium-width {
    width: 50%;
    float: left;
    text-align: center;
    @media ( min-width: 621px ) {
      &:nth-child(2){
        padding-right:10px;
      }
      &:nth-child(3){
        padding-left:10px;
      }
    }
    img{
      width: 100%;
    }

    @media ( max-width: 620px ) {
      width: 100%;
      &:nth-child(3){
        margin-top:10px;
      }
    }
  }
}

.featured-variants-cmpnt {
  @include container;
  position: relative;
  margin-bottom: $grid-gutter * 2;

  .mobile & {
    margin-bottom: $grid-gutter;
    .variants-list {
      margin-bottom: 0;
      width: auto;
      overflow-x: hidden;
      overflow-y: hidden;
      white-space: nowrap;

      article{
        display: inline-block;
        vertical-align: middle;
        float: none !important;
        border-right: 0px !important;
      }
    }
  }

  @include variants-list-full-width;

  .variants-list {
    margin-bottom: $grid-gutter;
  }
}

.featured-posts-cmpnt {
  margin-bottom: $grid-gutter * 2;


  .load-more-wide {
    margin-top: $grid-gutter;
  }

  article {
    @include span(1 of 4);
    @media #{$desktop} {
      &:nth-of-type(4n) { @include omega; }
    }
    @media #{$before-large} {
      @include span(1 of 2);
      &:nth-of-type(2n) { @include omega; }
    }
    .mobile & {
      @include span;
      @media #{$after-small} {
        @include span(1 of 2);
        &:nth-of-type(2n) { @include omega; }
      }
    }
  }
}

.home-value-proposition {
  @include container;
  @include fade-in-on-fonts-loaded;
  @include transition(opacity .8s);

  article {
    &.shop .img {
      background-position: 0 0;
    }

    &.connect .img {
      background-position: 50% 0;
    }

    &.values .img {
      background-position: 100% 0;
    }
  }

  .img {
    display: inline-block;
    position: relative;
    margin-bottom: 1em;
    width: 8.125em;
    background-size: 300% auto;
    background-repeat: no-repeat;
    background-image: image-url('home-value.png');

    @media #{$retina} {
      background-image: image-url('<EMAIL>');
    }

    span {
      display: block;
      padding-top: 100%;
    }
  }

  h2 {
    color: $dark-gray;
    font-size: .9em;
    text-transform: uppercase;
    cursor: default;
  }

  p {
    color: $dark-gray;
    font-size: .9em;
    line-height: 1.2;
    cursor: default;
  }

  .mobile & {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1em;
    text-align: center;

    article {
      display: inline-block;
      width: 260px;

      &:last-child {
        margin-bottom: 1em;
      }
    }

    h2 {
      display: table;
      height: 4.5em;
      font-size: 1em;
      min-width: calc(100% - 6em);
      letter-spacing: 0.1px;
      line-height: 1.1;

      span {
        display: table-cell;
        vertical-align: middle;
        font-size: 1em;
        font-weight: 100;

        strong {
          font-size: 1.2em;
          font-weight: 900;
        }
      }
    }

    .img {
      float: left;
      margin-right: 1em;
      margin-bottom: 2em;
      width: 4.5em;
    }
  }

  .desktop & {
    article {
      @include span(1 of 3);
      margin-bottom: $grid-gutter * 2;
      text-align: center;
      &:last-child {
        @include omega;
      }
    }
    h2 {
      font-weight: 900;
    }
  }
}

.mobile{
  .featured-variants-cmpnt, .cols5-generic-v1-vnrs{
    margin-left: -10px;
    margin-right: -10px;
    .scroll-wrapper{
      overflow: hidden;
      width: 100%;
    }
    .variants-list, .articles-list{
      @include clearfix;
      position: relative;
      width: 600%;

      article{
        margin-top: 2em;
      }
    }
  }
}

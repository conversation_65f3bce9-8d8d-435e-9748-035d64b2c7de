.whatsups-form-popup {
  .mfp-container {
    padding: 0;
  }
  .mfp-content {
    max-width: 600px;
    .mobile & {
      max-width: 768px;
    }
  }
}

.whatsups-form {
  @include clearfix;
  padding: 1em;
  background-color: #fff;

  .whatsups-form-title {
    margin-bottom: .6em;
    color: $darker-gray;
    font-weight: 900;
    font-size: .9em;
    text-transform: uppercase;
    line-height: 1.5;
  }

  .media-wrapper {
    position: relative;
    padding: .4em .8em;
    margin-bottom: 1em;
    border: 1px solid $light-gray;
    border-radius: 1px;
    background-color: #fff;
    &:before {
      position: absolute;
      left: .8em;
      top: .5em;
      font-size: 1em;
      color: $light-gray;
    }
  }

  textarea {
    @include form-reset-textarea;
    display: block;
    width: 100%;
    max-width: 100%;
    height: 6em;
    max-height: 6em;
    resize: none;
    font-size: .9em;
    line-height: 1.5;
    text-indent: 1.7em;
    outline: none;
  }

  .sport-selector {
    margin-bottom: 1em;
    &.without-select2 {
      label {
        display: block;
        margin-top: .5em;
      }
      #social_whatsup_sport_ids {
        margin: 0;
        width: 100%;
      }
    }
  }

  .actions {
    @include clearfix();
    margin-bottom: .5em;
    text-align: center;
  }

  .add-img,
  .create-album {
    @include buttons-plain('gray', 'small');
    @include span(1 of 2,
                  $gutter: 1em,
                  $fallback-width: 300px,
                  $fallback-gutter: 20px);
    margin-bottom: 1em;
    &:last-child {
      @include omega;
    }
  }

  .metadata-container {
    position: relative;
    &.loading {
      min-height: 25px;
      &:after {
        position: absolute;
        top: 2%;
        right: 45%;
        content: '...';
      }
    }
    .content-thumb {
      img {
        max-width: 100%;
        max-height: 300px;
      }
    }
  }

  .facebook-share {
    @include buttons-plain('light-facebook', 'small');
    &.active {
      @include buttons-plain('facebook', 'small');
    }
  }

  .twitter-share {
    @include buttons-plain('light-twitter', 'small');
    &.active {
      @include buttons-plain('twitter', 'small');
    }
  }

  .facebook-share,
  .twitter-share {
     &, &.active {
      margin: 0 .5em;
      width: 100px;
    }
  }

  .share {
    clear: both;
    margin-bottom: 1em;
    padding: 1em 0;
    border: 1px dashed $light-gray;
    border-left: none;
    border-right: none;
    text-align: center;
    p {
      font-size: .8em;
      margin-bottom: .8em;
      color: $dark-gray;
    }
  }

  .post-item {
    @include buttons-plain('green', 'normal');
    display: block;
    margin: 0 auto;
  }
}

.whatsups-form-select2-container {
  .select2-choices,
  &.select2-container-active .select2-choices {
    padding-left: 30px;
    border: 1px solid $gray;
    border-top-color: lighten($gray, 9%);
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    box-shadow: none;
    background: white image-url('social/whatsups_form/icon-tag.png') 10px 7px no-repeat;
  }
  .select2-input {
    height: 2.25em;
  }
  .select2-choices {
    .select2-search-choice {
      margin: 5px 0 3px 5px;
      padding-left: 0;
      border: none;
      box-shadow: none;
      color: darkblue;
      background: none;
      div {
        &:before {
          content: '#';
          display: inline;
        }
      }
    }
    .select2-search-choice-close {
      top: 3px;
      background: none;
    }
  }
}

.whatsups-form-select2-drop {
  margin-top: .15em;
  background-color: #f3f3f3;
  border: 1px solid $gray;
  border-radius: 3px;
  .popular-tags {
    padding: .45em 1.25em;
    font-size: .7em;
    font-weight: bold;
    color: #909090;
    background-color: #e7e7e7;
    border-bottom: 1px solid #d1d1d1;
  }
  .select2-results {
    margin: 0em;
    padding-left: 0em;
  }
  .select2-result {
    border-bottom: 1px solid #dadada;
  }
  .select2-result-label, .select2-no-results {
    padding: 7px 14px;
    font-weight: bold;
    color: #5a5a5a;
  }
  .select2-highlighted {
    background-color: darken(#f3f3f3, 2%);
  }
}

.metadata {
  position: relative;
  padding: 6px;
  @include clearfix;
  background-color: #ffffff;
  a:not([href]) {
    color: $gray !important;
    text-decoration: none !important;
    cursor: default !important;
  }
  .close {
    position: absolute;
    right: 6px;
    top: 6px;
    cursor: pointer;
    font-size: 13px;
    line-height: 13px;
    text-align: center;
    font-weight: bold;
    width: 13px;
    height: 14px;
    border-radius: 2px;
    &:hover {
      color: #fff;
      background-color: $black;
    }
  }
  .content-thumb {
    position: relative;
    width: 100%;
    text-align: center;
    img {
      max-width: 100%;
      max-height: 100%;
      height: auto;
    }
    .play-icon {
      position: absolute;
      bottom:  18px;
      left: 18px;
      width: 85px;
      height: 59px;
      background-image: image-url('social/feed_item/play-large.png');
    }
  }
  .content-data {
    padding: 6px 0;
    width: 100%;
    .content-title {
      margin: 0 0 6px;
      a[href*="goodpeople.com/"] {
        cursor: pointer;
        color: lighten($red, 13%);
      }
    }
    .content-url {
      margin: 0 0 12px;
      word-wrap: break-word;
      line-height: 1.2em;
      a {
        color: darken($lighter-gray, 13%);
      }
    }
    .content-description {
      margin: 0;
    }
  }
  &.instagram {
    padding: 0;
    background-color: transparent;
    .content-thumb {
      width: 100%;
    }
  }
  &.with-thumb {
    @media (min-width: 460px) {
      .content-thumb {
        float: left;
        width: 37%;
        .play-icon {
          bottom: 10px;
          left: 10px;
          width: 51px;
          height: 37px;
          background-image: image-url('social/feed_item/play-small.png');
        }
      }
      .content-data {
        float: right;
        width: 60%;
        &.no-thumb {
          width: 100%;
        }
      }
    }
  }
}

.embeded-video {
  position: relative;
  margin-bottom: 0.45em;
  padding: 0em;
  background-color: transparent;

  img {
    max-width: 100%;
  }

  .video-title {
    margin: 0 0 0.375em;
    a {
      color: lighten($red, 13%);
    }
  }

  .video-url {
    margin: 0 0 0.75em;
    line-height: 1.2em;
    word-wrap: break-word;
    a {
      color: darken($lighter-gray, 20%);
    }
  }

  .thumb {
    height: 12em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .video-thumb {
    position: relative;
    text-align: center;
  }

  .video-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -22px 0 0 -32px;
    width: 65px;
    height: 45px;
    background-image: image-url('social/feed_item/play-medium.png');
  }
}

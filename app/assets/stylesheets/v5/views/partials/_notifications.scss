.notifications-popup {
  .mfp-container {
    padding: 0;
  }
  .mfp-content {
    max-width: 450px;
    .mobile & {
      max-width: 768px;
    }
  }
}

.notifications {
  max-height: 500px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  font-size: .8em;
  background-color: #fff;

  .mobile & {
    max-height: 768px;
  }

  &.empty {
    .empty-msg {
      display: block;
    }
  }

  .empty-msg {
    display: none;
    margin: 0;
    padding: 3em 1em;
    line-height: 1.4em;
    font-size: 1.7em;
    text-align: center;
  }

  .read-all {
    display: inline-block;
    font-size: .9em;
    font-weight: 900;
    text-align: left;
    cursor: pointer;
    color: $dark-gray;
    margin: .7em 1em 0;
    border-bottom: 1px solid transparent;
    .no-touch &:hover,
    &:active {
      border-bottom: 1px solid $dark-gray;
    }
  }

  .notifications-items-list {
    @include clearfix;
    padding-top: 1.3em;
    max-height: 100%;
  }

  .notifications-item {
    @include clearfix;
    display: block;
    position: relative;
    padding: 0 .4em .9em .5em;
    margin: 0 .9em 1.1em 1.3em;
    border-bottom: 1px dashed $lighter-gray;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
      box-shadow: none;
      margin-bottom: 0;
    }

    &.unread {
      .read-btn {
        display: block;
      }
    }

    .read-btn {
      display: none;
      position: absolute;
      left: -.6em; top: 1.1em;
      width: .55em; height: .5em;
      border-radius: 50%;
      background-color: $red;
    }

    .pic {
      float: left;
      margin-right: .5em;
      width: 2.6em;
    }

    .text {
      margin: 0;
      color: $dark-gray;
      line-height: 1.2em;
      font-size: 1em;
      a {
        font-weight: bold;
        border-bottom: 1px solid transparent;
        .no-touch &:hover,
        &:active {
          border-bottom: 1px solid $dark-gray;
        }
      }
    }

    .created-ago {
      display: block;
      font-size: .7em;
      line-height: .8em;
      margin-top: .4em;
      margin-left: 4.4em;
      color: $gray;
    }
  }
}

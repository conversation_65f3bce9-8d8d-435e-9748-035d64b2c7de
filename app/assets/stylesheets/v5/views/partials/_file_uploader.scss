.file-uploader {
  .file-uploader-msg {
    font-weight: 900;
    margin: 12px 0 6px;
  }

  .file-uploader-item {
    @include clearfix;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &.no-thumb {
      .file-uploader-item-thumb {
        display: none;
      }

      .file-uploader-item-progress {
        margin-left: 0;
      }
    }

    &.complete {
      .file-uploader-item-progress-bar {
        background-color: $light-green;
      }
    }
  }

  .file-uploader-item-thumb {
    float: left;
    width: 65px;
    height: 65px;
    border: 1px solid $black;
    border-radius: 3px;
    margin-right: 12px;
    background-size: cover;
  }

  .file-uploader-item-cancel {
    @include buttons-plain('white', 'normal');
    float: right;
    padding: 0;
    width: 34px;
    height: 32px;
    color: $red;
    &:hover {
      color: $dark-red;
    }
  }

  .file-uploader-item-name {
    margin: 3px 0 7px 0;
  }

  .file-uploader-item-progress {
    position: relative;
    margin-left: 74px;
    margin-right: 42px;
    height: 32px;
    text-align: center;
    border-radius: 1px;
    background-color: $lightest-gray;

    .file-uploader-item-progress-bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background-color: $red;
    }

    .file-uploader-item-progress-percentage {
      position: absolute;
      left: 47%;
      top: 0;
      display: block;
      color: white;
      font-weight: 900;
      line-height: 32px;
    }
  }
}
.header-search {
  @include fade-in-on-fonts-loaded;
  @include clearfix;
  position: absolute;
  float: right;
  height: 2em;
  border-radius: 1px;
  border: 1px solid #D9D9D9;
  background-color: $white;
  left: 25%;
  width: 50%;
  @include transition(all .1s ease-in);

  &.active {
    width: 32em;

    button {
      $c: $light-blue;
      background-color: $c;
      &:after { border-right-color: $c; }
      &:hover {
        background-color: $c;
        &:after { border-right-color: $c; }
      }
    }

    @media (max-width: 930px) {
      width:  22em;
    }
  }


  &.loading {
    .loader {
      display: block;
    }
  }

  .loader {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 4em;
    border-top: 1px solid #fff;
    background-repeat: no-repeat;
    background-color: $lightest-gray;
    background-position: center center;
    background-image: image-url('loaders/spinner-small.gif');

    .cssanimations & {
      background-image: none;

      &:before,
      &:after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -.6em 0 0 -.4em;
        width: 1em;
        height: 1em;
        opacity: .6;
        border-radius: 50%;
        background-color: #999;
        @include animation(header-search-loader 2s infinite ease-in-out);
      }

      &:after {
        @include animation-delay(-1s);
      }
    }
  }

  input {
    display: block;
    margin: .6em 0 .5em;
    padding: 0 3.6em 0 .8em;
    border: none;
    width: 100%;
    height: auto;
    border-radius: 0;
    line-height: 1.4em;
    font-family: inherit;
    font-size: .8em;
    color: $darker-gray;
    box-shadow: none;
    outline: 0;
    -webkit-appearance: none;
    background-color: transparent;
  }

  button {
    @extend %form-reset-button;
    position: absolute;
    right: 0;
    top: 0;
    padding-left: .1em;
    width: 2.1em;
    height: 2em;
    line-height: 2em;
    color: #fff;
    cursor: pointer;
    text-align: center;
    background-color: $light-blue;
    @include transition(all .1s linear);

    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      margin: -6px 0 0 -6px;
      @include triangle(12px, $light-blue, left);
      @include transition(all .1s linear);
    }

    &:hover {
      $c: lighten($lighter-gray, 5%);
      background-color: $c;
      &:after { border-right-color: $c; }
    }

    &:active {
      $c: $light-blue;
      background-color: $c;
      &:after { border-right-color: $c; }
    }

    span {
      position: absolute;
      color: transparent;
      left: 100%;
    }
  }

  .twitter-typeahead {
    width: 100%;

    .tt-hint {
      color: #FFFFFF;
    }
  }

  .tt-dropdown-menu {
    width: 100%;
    border-top: 1px solid #fff;
    background-color: $lightest-gray;

    h1 {
      padding: .5em;
      font-size: .7em;
      font-weight: normal;
      color: $gray;
      background-color: $lighter-gray;

      &:before {
        position: relative;
        top: -.1em;
        margin-right: .5em;
        font-size: .7em;
      }
    }
  }

  .tt-suggestion {
    cursor: pointer;

    &.tt-cursor {
      .item {
        background-color: darken($lightest-gray, 5%);
      }
    }

    .item {
      @include transition(all .1s ease-in);
      a {
        display: inline-block;
        width: 100%;
        height: 100%;
      }
    }
  }

  .tt-dataset-variants {
    .item {
      @include clearfix;
      position: relative;
      padding: .5em 4em .5em .5em;

      &.on-sale {
        .sale-price {
          margin-top: -.9em;
          color: $red;
        }

        .regular-price {
          margin-top: .25em;
          font-weight: 100;
          text-decoration: line-through;
          color: $gray;
          font-size: .6em;
        }
      }


      .regular-price,
      .sale-price {
        position: absolute;
        right: .7em;
        top: 50%;
        margin-top: -.5em;
        font-size: .7em;
        color: $dark-gray;
        font-weight: 900;
        cursor: inherit;
      }

      .pic {
        float: left;
        margin-right: .5em;
        width: 2.5em;
        background-size: cover;
        background-position: center center;
        background-color: white;

        span {
          display: block;
          padding-top: 100%;
        }
      }

      h2 {
        @include ellipsis;
        margin: 0;
        font-size: .8em;
        font-weight: 500;
        line-height: 3.2em;
        color: $dark-gray;
        cursor: inherit;
      }

      strong {
        color: $dark-gray;
        font-weight: bold;
      }
    }
  }

  .tt-dataset-categories {
    .item {
      padding: 1.3em;
      font-size: .8em;
      color: $gray;

      &:before {
        margin-right: 1em;
      }

      strong {
        color: $dark-gray;
        font-weight: bold;
      }
    }
  }

  .tt-dataset-suggester_search {
   .item {
      padding: 1.3em;
      font-size: .8em;
      color: $gray;

      &:before {
        margin-right: 1em;
      }

      strong {
        color: $dark-gray;
        font-weight: bold;
      }
    }
  }

  .tt-dataset-categories_facets {
    .item {
      padding: 1.3em;
      font-size: .8em;
      color: $gray;

      &:before {
        margin-right: 1em;
      }

      strong {
        color: $dark-gray;
        font-weight: bold;
      }
    }
  }

  .tt-dataset-manufacturer_facets {
    .item {
      padding: 1.3em;
      font-size: .8em;
      color: $gray;

      &:before {
        margin-right: 1em;
      }

      strong {
        color: $dark-gray;
        font-weight: bold;
      }
    }
  }

  .users-item {
    @include clearfix;
    padding: .5em;

    &.socialuser {
      p {
        @include ellipsis;
        line-height: 1.4;
      }

      .avatar {
        @include avatar-size(1.8em);
      }
    }

    &.nonprofit,
    &.brand {
      p {
        padding-left: 5px;
        @include multiline-ellipsis(
          $font-size: 10px,
          $line-height: 1.2,
          $lines-to-show: 2
        );

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 3.5px;
          width: 1px;
          height: 19px;
          background: #aaa;
        }
      }

      .avatar {
        @include avatar-size(2.5em);
      }
    }

    .avatar {
      float: left;
      margin-right: .5em;
    }

    strong {
      font-weight: bold;
    }

    h2 {
      @include ellipsis;
      margin: 0;
      padding-bottom: .18em;
      font-size: .93em;
      font-weight: 500;
      line-height: 1em;
      color: $dark-gray;
      cursor: inherit;
    }

    p {
      position: relative;
      margin: 0;
      padding-right: 5px;
      color: $gray;
      font-size: 10px;
      cursor: inherit;
    }

    .tt-highlight {
      font-weight: 900;
    }
  }
}

@include keyframes(header-search-loader) {
  0%,
  100% {
    @include transform(scale(0));
  }

  50% {
    @include transform(scale(1));
  }
}

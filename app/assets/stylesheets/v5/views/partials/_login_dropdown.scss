.login-dropdown-popup {
  .mfp-container {
    padding: 0;
  }
}

.login-dropdown {
  margin: 0 auto;
  padding: 1.2em 1.2em 0;
  border: 1px solid $light-gray;
  max-width: 17.5em;
  border-radius: 1px;
  background-color: #fff;
  font-size: 1.1em;

  .mobile & {
    max-width: 20em;
  }

  input {
    @include form-input;
  }

  label,
  input[type="checkbox"] {
    cursor: pointer;
  }

  p {
    font-size: .8em;
    text-align: center;
    margin-bottom: 1.5em;
    line-height: 1.3;
    color: $dark-gray;
    span {
      display: block;
    }
    a {
      color: $dark-gray;
      font-weight: bold;
      border-bottom: 1px solid transparent;
      &:hover {
        border-bottom: 1px solid $dark-gray;
      }
    }
  }

  .remember {
    float: right;
    input {
      position: relative;
      top: 2px;
      float: left;
      margin: 0 .5em 0 0;
    }
  }

  .forgot {
    float: left;
  }

  .submit {
    clear: both;
    margin-bottom: 1.5em;
    text-align: center;
    button {
      @include buttons-plain('green', 'small');
      margin-left: auto;
      margin-right: auto;
    }
  }

  .social-media {
    @include clearfix;
    margin-bottom: 1.5em;
    padding: 1em 0 0;
    border-top: 1px dashed $light-gray;
    text-align: center;
  }

  .i-logo-facebook {
    float: left;
    @include buttons-plain('facebook', 'small');
  }

  .i-logo-twitter {
    float: right;
    @include buttons-plain('twitter', 'small');
  }

  .i-logo-facebook,
  .i-logo-twitter {
    width: 48%;
    &:before {
      position: relative;
      right: .3em;
      top: .15em;
      height: .5em;
      line-height: .4em;
      font-size: 1.7em;
    }
  }

  .register {
    clear: both;
  }
}

  .cart-dropdown-popup {
  .mfp-container {
    padding: 0;
  }
  .mfp-container::before {
    .mobile & {
      height: 0px;
    }
  }
  .mfp-content {
    height: 100%;
    max-width: 700px;
    max-height: 450px;
    .mobile & {
      max-width: 700px;
      max-height: 700px;
      @media (min-height: 480px) {
        max-height: 480px;
      }
      @media (min-height: 568px) {
        max-height: 533px;
      }
      @media (min-height: 640px) {
        max-height: 640px;
      }
    }
    .mfp-close {
      font-size: 1.7em;
      right: 0.5em;
    }
  }
}

.cart {
  $padding-bottom: 1em;
  position: relative;
  padding: 3em 1em 5.5em;
  max-width: 100%;
  height: 100%;
  font-size: 16px;
  background-color: #fff;

  &.empty {
    .empty-msg {
      display: block;
      display: flex;
    }
    .actions {
      display: none;
    }
  }

  .empty-msg {
    @include with-middle-text;
    display: none;
    position: absolute;
    left: 0;
    top: 50%;
    margin: -2.7em 0 0;
    padding: 0 1em;
    width: 100%;
    height: 4.3em;
    line-height: 1.4em;
    font-size: 1.7em;
    text-align: center;
  }

  h2 {
    position: absolute;
    left: 0;
    top: 0;
    padding: .5em 1em 0;
    width: 100%;
    color: $black;
    font-weight: 900;
    font-size: 1em;
    text-transform: uppercase;
    line-height: 1.5;
    &:after {
      content: '';
      display: block;
      border-bottom: 1px dashed $light-gray;
    }
  }

  button {
    @extend %form-reset-button
  }

  .list {
    overflow: auto;
    max-height: 100%;
    -webkit-overflow-scrolling: touch;
  }

  .item {
    @include clearfix;
    overflow: hidden;
    position: relative;
    margin-bottom: .5em;
    padding: .5em;
    background-color: $lightest-gray;

    .img {
      float: left;
      margin-right: .5em;
      max-width: 41%;
      width: 8em;
      background-size: 100% 100%;
      background-size: contain;
      background-color: #fff;
      background-position: center center;
      background-repeat:  no-repeat;
      span {
        display: block;
        padding-top: 100%;
      }
    }

    .i-icon-trash-bin {
      float: right;
      right: -.5em;
      top: -.5em;
      position: relative;
      padding: .6em .5em;
      font-size: 1.1em;
      color: $dark-gray;
      cursor: pointer;
      &:hover {
        color: $black;
      }
    }

    .title {
      @include ellipsis;
      color: $darker-gray;
      font-size: 1em;
      font-weight: 900;
      text-transform: uppercase;
    }

    .mfr {
      @include ellipsis;
      color: $gray;
      font-size: .9em;
      line-height: 1.3;
    }

    .attrs,
    .qty {
      font-size: .8em;
      line-height: 1.4;
    }

    .price {
      position: absolute;
      right: .5em;
      bottom: .5em;
      font-size: 1em;
    }

    .buy-info {
      margin-top: 1.6em;
    }
  }

  .actions {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: .8em 0;
    width: 100%;
    text-align: center;
    .mobile & {
      padding: .8em;
    }
  }

  .total {
    margin-bottom: .8em;
    font-size: .8em;
    font-weight: 900;
    text-transform: uppercase;
    .mobile & {
      font-size: 1.4em;
      font-weight: 200;
    }
  }

  .continue {
    @include buttons-plain('gray', 'normal');
    .mobile & {
      width: 100%;
      margin-bottom: .8em;
    }
  }

  .checkout {
    @include buttons-plain('blue', 'normal');
    .mobile & {
      width: 100%;
    }
  }

  .continue, .checkout {
    .desktop & {
      min-width: 20%;
      max-width: 48%;
      margin: 0 .5em;
    }
  }
}

// Simple avatar of the user, will contain a fallback with the initials.
//
// .show-fallback - will hide the image and show the fallback text
//
// Styleguide 2.1

@mixin avatar-size($width) {
  &.simple {
    width: $width;
    height: $width;
  }

  &.hexagonal {
    width: $width;
    height: $width * 1.15;
  }
}

.avatar {
  position: relative;
  cursor: pointer;
  overflow: hidden;

  &:not(.show-fallback) {
    background-color: transparent !important;
  }

  &.show-fallback {
    img { display: none; }
    .fallback { display: block; }
  }

  .fallback {
    display: none;
    padding-top: 100%;
    font-size: 1em;

    span {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      text-align: center;
      color: white;
      font-weight: bold;
      margin-top: -.48em;
      line-height: 1em;
      letter-spacing: -.05em;
    }
  }

  img {
    max-width: 100%;
  }

  &.simple {
    img {
      width: 100%;
    }
  }

  &.hexagonal {
    -webkit-clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    img {
      width: 100%;
      height: 100%;
      .backgroundsize & {
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
        background-color: white;
        &:after {
          content: '';
          display: block;
          padding-top: 115%;
          max-height: 12em;
        }
      }
    }
  }
}

// User resume, with the cover as background
//
// Styleguide 3.1
@mixin users-with-cover-full-width {
  .users-with-cover {
    @include container;

    article {
      @include span(1 of 4);

      @media #{$from-large} {
        @include omega(4);
      }

      &:last-child {
        @include omega;
      }

      @media #{$before-large} {
        @include span(1 of 3);
        @include omega(3);
        &:nth-of-type(1n+7) {
          display: none;
        }
      }

      .mobile & {
        @include span(1 of 2);
        @include omega(2);
      }
    }
  }
}

.users-with-cover {
  position: relative;

  @mixin fill {
    padding-top: 50%;
    max-height: 160px;
  }

  %transit {
    @include transform(translateZ(0));
    @include transition(all .15s ease-out);
  }

  article {
    position: relative;
    // border-top: .3125em solid $red;
    text-align: center;
    overflow: hidden;

    .no-touch & {
      margin-bottom: $grid-gutter;
    }

    .mobile & {
      margin-bottom: 5px;
    }

    a {
      text-decoration: none;
    }

    .no-touch &:hover {
      p {
        bottom: 0;
      }

      h2 {
        top: -67px;
        color: $black;
        text-shadow: 1px 1px 1px rgba(255,255,255,0.3);
      }

      .cover {
        @include filter(none);
      }

      .avatar {
        margin-left: -10%;
        margin-top: -21%;
        width: 20%;
        height: 40%;
      }
    }

    .cover {
      position: relative;
      background-color: $black;
      background-size: auto 100%;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      @extend %transit;

      .no-touch & {
        @include filter(grayscale(100%));
      }

      &:before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 20%;
        @include background-image(linear-gradient(transparent, rgba(#000, .45)));
      }

      span {
        @extend %transit;
        @include fill;
        display: block;
        background-color: rgba(#fff, .5);
      }
    }

    .avatar-wrapper {
      @include fill;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .avatar {
      @extend %transit;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -12.5%;
      margin-top: -14%;
      width: 25%;
      height: 50%;
      border: 2px solid white;
      font-size: 2em;
      border-radius: 100%;
      background-color: #fff;

      img {
        background-image: none;
        background-color: white;
      }
    }

    h2 {
      @extend %transit;
      position: relative;
      top: 0;
      height: $grid-gutter * 3;
      line-height: 18px;
      font-weight: bold;
      font-size: 18px;
      padding: .7em 10px;
      color: $dark-gray;
      font-weight: 900;
    }

    p {
      @extend %transit;
      position: absolute;
      left: 0;
      bottom: -75px;
      padding: 10px;
      width: 100%;
      color: #fff;
      background-color: $green;

      &:before {
        @include triangle(8px, $green, up);
        content: '';
        position: absolute;
        top: -4px;
        left: 50%;
        margin-left: -4px;

      }

      span {
        @include multiline-ellipsis($font-size: 12px,
                                    $line-height: 1.4,
                                    $lines-to-show: 3);
      }
    }
  }
}

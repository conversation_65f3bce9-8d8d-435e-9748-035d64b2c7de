footer {
  @include wide-outer-container;
  font-size: .9em;

  .top {
    overflow: hidden;
    padding: 1em 0 1em 10px;
    background-color: $lighter-gray;
  }

  .bottom {
    padding: 1em 0;
    background-color: $black;
  }

  .row {
    position: relative;
    @include outer-container;
    padding-top:0px;
    &.with-qr {
      padding-right: 90px;
    }
  }

  .qr {
    position: absolute;
    right: 10px;
    top: 0;
  }

  .top h2 {
    position: relative;
    padding-left: 1.15em;
    position: relative;
    margin: 0;
    padding: 0;
    color: $darker-gray;
    font-size: .9em;
    line-height: 1.45;
    font-weight: 900;
    text-transform: uppercase;
    &:before {
      position: absolute;
      left: -1.4em;
      top: .4em;
      font-size: .7em;
    }
  }

  nav {
    @include container;
    @include span(3 of 4, $gutter: false);

    a, p {
      font-size: .9em;
      line-height: 1.45;
      color: $dark-gray;
      border-bottom: 1px solid transparent;
      @include transition(all .1s linear);
    }
    a:hover {
      color: $darker-gray;
      border-bottom: 1px solid #999;
    }

    ul {
      @include span(1 of 3, $gutter: false);
      padding-right: 1em;
    }
  }

  .input-wrapper {
    &.i-shipping{
      margin:4px 0 10px 0;
    }
    position: relative;
    &:before {
      position: absolute;
      left: .4em;
      color: $blue;
      font-size: 1.6em;
      line-height: 1.65em;
    }
  }

  input {
    display: block;
    margin: 0;
    padding: .8em;
    padding-left: 3.2em;
    border: none;
    max-width: 100%;
    width: 100%;
    height: auto;
    font-size: .9em;
    box-shadow: none;
    border-radius: 3px;
    background-color: white;
    font-family: inherit;
  }

  input.error{
    outline-color: red;
  }



  .done-msg {
    display: none;
    color: $green;
    font-weight: bold;
    margin: 0;
    font-size: 1.1em;
    line-height: 1;

    .i-tilde {
      font-size: 1.9em;
      margin-right: 0.3em;
      float: left;
      line-height: 1;
    }
  }

  .newsletter-subscription {
    padding-left: 2.5em;
    border-left: 1px dashed $gray;
    @include span(1 of 4, $gutter: false);

    p {
      font-size: .9em;
      line-height: 1.45;
      color: $darker-gray;
      margin-bottom: 1em;
      max-width: 100%;
    }


  }

  .bottom p {
    display: inline-block;
    margin: 0;
    margin-top: 1em;
    margin-bottom: 1em;
    color: white;
    font-size: .65em;
    text-transform: uppercase;
    text-align: right;
    line-height: 1;
  }

  .payment-methods,
  .mkp-promos,
  .social-accounts,
  .networks {
    @include span();
    border: none !important;
  }

  .payment-methods {
    p {
      margin-right: 1em;
      max-width: 7em;
    }


    .cards {
      display: inline-block;
      margin-right: 1em;
      div {
        $w: 28px;
        $h: 17px;
        display: inline-block;
        margin: .3em .5em 0 0;
        width: $w;
        height: $h;
        background: image-url('footer-sprite.png') no-repeat;
        @media #{$retina} {
          background-size: 230px auto;
          background-image: image-url('<EMAIL>');
        }
        &.disabled {
          @include opacity(.2);
        }
        &.mastercard  { background-position: -($w * 0) bottom; }
        &.visa        { background-position: -($w * 1) bottom; }
        &.mercadopago { background-position: -($w * 2) bottom; }
        &.american    { background-position: -($w * 3) bottom; }
        &.paypal      { background-position: -($w * 4) bottom; }
      }
    }


    .secure {
      position: relative;
      padding-right: 3.7em;
      display: inline-block;
      max-width: 11em;
      padding-left: 1.5em;
      &:before {
        position: absolute;
        right: 0;
        top: 0;
        color: white;
        float: right;
        line-height: .7em;
        font-size: 3em;
        padding-left: .2em;
      }
      &.excludes {
        max-width: 24em;
        @media #{$after-medium} {
          float:right;
        }
      }
    }
  }

  .mkp-promos {
    @include omega;
    text-align: center;
    p {
      position: relative;
      padding-right: 3.6em;
      margin-right: 2em;
      &:last-child {
        margin-right: 0;
      }
      &.live-chat{
        cursor: pointer;
      }
    }
    strong {
      position: relative;
      top: -.6em;
      line-height: 2;
      font-weight: 900;
    }
    .i-phone {
      top: -.38em;
    }
    span {
      display: block;
    }
    i {
      position: absolute;
      right: 0;
      top: -.2em;
      font-size: 3em;
    }
  }

  .social-accounts {
    text-align: center;
    padding: 1em 0;
    font-size: .8em;
  }

  .networks {
    @include omega;
    text-align: center;
    p {
      max-width: 8em;
    }
    div {
      display: inline-block;
      max-width: 180px;
      position: relative;
      top: .6em;
    }
  }

  @media #{$til-medium} {
    text-align:center;
    min-width: 0px;
    .payment-methods, .mkp-promos{
      width: 100% !important;
    }
    .row {
      min-width: 0px;
    }
  }

  @media #{$after-wide} {
    .bottom p {
      margin-top: 2.8em;
      margin-bottom: 2.8em;
    }

    .payment-methods {
      padding-left: 40px;
    }

    .mkp-promos {
      @include span(7 of 24);
    }

    .social-accounts {
      @include span(4 of 24);
    }

    .networks {
      @include span(6 of 24);
    }

    .social-accounts {
      border-right: 1px dashed $dark-gray;
      padding: 1.35em 0;
      font-size: .9em;
    }

    .networks {
      @include omega;
    }
  }
}

.todo_pago {
  width: 20px !important;
  height: 17px !important;
  background: image_url("todopago.png") !important;
  background-position: left center !important;
  background-size: cover !important;
}

.payment-icon-mercadopago {
  display: inline-block;
  font-size: 0.8em;
  margin-right: 4px;
  vertical-align: middle;

  img {
    max-height: 20px;
  }
}

.extend-todo_pago {
  display: inline-block !important;
  margin-left: 5px !important;
  width: 50px !important;
}

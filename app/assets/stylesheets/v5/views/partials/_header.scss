$h-wide-breakpoint: $from-wide;

.header-phantom {
  @include wide-outer-container;
  display: none;
  height: 4em;
  @media #{$h-wide-breakpoint} {
    height: 4em;
  }
}

.header-slim-promo {
  font-size: 0.75em;
  color: #2A2A2A;
  font-weight: 800;
  text-align: center;
  text-transform: uppercase;
  padding: $grid-gutter/2;
  background: #D9D9D9;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  font-weight: bold;
}

header {
  @include wide-outer-container;
  position: relative;
  font-size: 16px;
  z-index: 10;
  background-color: #FFFFFF;
  padding: 48px $grid-margin 10px;
  color: #2A2A2A;

  @media #{$h-wide-breakpoint} {
    max-width: none;
    font-size: 18px;
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 38px;
  }

  &.fixed {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;

    & + .header-phantom {
      display: block;
    }

    &.pinned {
      @include transform(translateY(0));
    }

    &.unpinned {
      @include transform(translateY(-100%));
    }

    &.top {
      padding-top: 0.6em;
    }

    &.not-top {
      z-index:20;
      position: fixed;
      padding-top: 0.6em;
      box-shadow: 0 0 .3em rgba(50,50,50,.2);
      @include transition(all .35s ease-in-out);
    }
  }

  .logo {
    span {
      @include hide-for-seo;
    }
    img {
      max-height: 45px;
      position: relative;
      vertical-align: middle;
      width: 13.2em;
    }
  }

  .broad-nav {
    @include fade-in-on-fonts-loaded;
    float: left;

    li {
      float: left;
      margin-right: 1em;
      overflow: hidden;

      &.logo {
        margin-right: 1.5em;
        @media #{$h-wide-breakpoint} {
          margin-right: 2em;
        }
      }
    }

    a {
      color: $gray;
      text-decoration: none;
      text-transform: uppercase;
      font-weight: 900;
      line-height: 2.3em;
      font-size: .8em;

      &:hover {
        @include transition(all .1s linear);
        color: $dark-gray;
      }
    }

    .fb {
      display: block;
      top: 0.4em;
      position: relative;
    }

    .fb-like {
      line-height: 1.2em;
    }
  }

  .separator {
    @include fade-in-on-fonts-loaded;
    float: right;
    border-right: 1px solid $light-gray;
    height: 2em;

    @media #{$before-large} {
      display: none;
    }
  }

  .mkp-promos {
    @include fade-in-on-fonts-loaded;
    float: right;
    margin-top: 6px;
    @media (max-width: 900px) {
      display: none;
    }

    p {
      position: relative;
      float: left;
      color: $red;
      text-align: center;
      text-transform: uppercase;
      font-size: .7em;
      margin: 1.4em .2em .6em .2em;
      padding: 0 2.7em 0 0;
      line-height: 1;

      &:last-child {
        margin-right: 1em;
      }
      &.live-chat{
        cursor: pointer;
      }
    }

    strong {
      line-height: 2;
      font-weight: 900;
    }

    span,
    strong {
      display: none;
    }

    i {
      float: right;
      position: absolute;
      right: 0;
      top: 50%;
      margin-top: -.5em;
      font-size: 2.1em;
    }

    @media #{$h-wide-breakpoint} {
      p {
        margin: .6em 1em .6em .625em;
        text-align: right;
        font-size: .6em;
      }

      span,
      strong {
        display: block;
      }
    }
  }

  .user-menu {
    @include fade-in-on-fonts-loaded;
    float: right;
    .separator {
      float: left;
    }

    button {
      @extend %form-reset-button;
      float: left;
      position: relative;
      margin: 0 .4em;
      line-height: 1.5em;
      font-size: 1.4em;
      color: #2A2A2A;
      cursor: pointer;
      @include transition(all .1s linear);

      span {
        position: absolute;
        right: -.6em;
        top: -.3em;
        border: 2px solid white;
        border-radius: 10px;
        padding: 2px 5px 0px;
        font-size: 11px;
        line-height: 14px;
        text-align: center;
        font-family: sans-serif;
        font-weight: 800;
        color: white;
        text-shadow: 1px 1px 0 rgba(0,0,0,0.3);
        letter-spacing: 0;
        font-kerning: none;
        background-color: $red;
        // Firefox Specific
        @-moz-document url-prefix() {
          padding: 1px 5px 0px;
          line-height: 14px;
        }
        // Safari 2/3.1 specific
        html[xmlns*=""]:root & {
          border-radius: 10px;
        }
        &[data-count="0"] {
          display: none;
        }
      }
    }

    .avatar-wrapper {
      float: left;
      position: relative;
      top: -.3em;
      margin: 0 .1em 0 .6em;

      .avatar {
        width: 2em;
        height: 2.3em;
      }

      .hexagon.user-avatar {
        width: 2em;
        height: 2.3em;
      }
    }

    .sub-menu {
      @extend %form-reset-button;
      float: left;
      position: relative;
      width: 1em;
      cursor: pointer;
      line-height: 2.1em;
      @include transition(all .1s linear);

      &:hover {
        .arrow {
          border-top: .4em solid $dark-gray;
        }
      }

      &.active {
        nav {
          display: block;
        }
      }

      .arrow {
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -.1em 0 0 -.25em;
        border-left: .3em solid transparent;
        border-right: .3em solid transparent;
        border-top: .4em solid $gray;
        @include transition(all .1s linear);
      }

      nav {
        display: none;
        z-index: 5;
        position: absolute;
        top: 80%;
        right: .2em;
        border: 1px solid $light-gray;
        background-color: $lightest-gray;

        a {
          display: block;
          padding: 0 1em;
          font-size: .7em;
          line-height: 2;
          color: $gray;
          text-decoration: none;

          &:hover {
            color: $dark-gray;
          }
        }

        strong {
          font-weight: 900;
        }
      }
    }
  }

  .login {
    @include fade-in-on-fonts-loaded;
    @include buttons-plain('white', 'small');
    float: right;
    margin-left: .8em;
  }
}
.header-categories-top{
  background-color: #FFFFFF;
  width: 100%;
  padding-top: 9px;
  border-bottom: 1px solid #D9D9D9;
  .header-categories {
    clear: both;
    position: relative;
    margin: 0 auto;
    min-width: $medium-screen;
    max-width: $large-screen;
    text-align: justify;
    -ms-text-justify: distribute-all-lines;
    text-justify: distribute-all-lines;
    cursor: default;
    font-size: 0;
    background-color: #FFFFFF;
    color: #2A2A2A;
    @include fade-in-on-fonts-loaded;

    @media #{$after-wide} {
      max-width: $wide-screen;
    }

    &:hover{
      z-index: 10;
    }

    .category {
      display: inline-block;
      font-size: 16px;
      @include transition(all .1s linear);

      @media #{$before-large} {
        font-size: 13px;
      }

      .text-black {
        color: #2a2a2a !important;
      }

      .no-touch &:hover, &.active {
        background-color: #F4F4F4;

        .subcategories-wrapper {
          display: block;
        }
      }

      .no-touch.csstransitions &:hover,
      .no-touch.csstransitions &.active {
        @include transition-delay(.125s);

        .subcategories-wrapper {
          visibility: visible;
          opacity: 1;
          @include transition-delay(.125s);
        }
      }

      .category-link {
        display: block;
        text-transform: uppercase;
        text-decoration: none;
        font-size: .8em;
        font-weight: 900;
        text-align: center;
        line-height: 1em;
        padding: .9em 1.8em;

        &.gray-one {
          color: #2A2A2A;
          &:hover {
            color: #2A2A2A;
          }
        }

        &.red-one { color: $red; }
      }

      a.category-link { display: none; }

      .no-touch &,
      &.without-childs {
        a.category-link { display: block; }
        span.category-link { display: none; }
      }
    }

    .subcategories-wrapper {
      display: none;
      position: absolute;
      left: 0;
      top: 2.2em;
      width: 100%;
      text-align: left;
      @include transition(all .1s linear);

      .no-touch.csstransitions & {
        display: block;
        visibility: hidden;
        opacity: 0;
      }
    }

    .subcategories {
      @include outer-container;
      min-width: 0 !important;
      padding: 1.1em;
      padding-right: 0;
      overflow-y: auto;
      background-color: $lightest-gray;

      .subcategories-column {
        float: left;
        width: 25%;
      }

      .subcategory {
        margin-bottom: 1em;
        padding-right: 1.1em;
      }

      strong {
        position: relative;
        &:before {
          position: absolute;
          left: -1.3em;
          top: 0;
          font-size: .8em;
          line-height: 1.7em;
        }
      }

      a {
        display: block;
        color: $dark-gray;
        text-decoration: none;
        padding-left: 1em;
        font-size: .85em;
        line-height: 1.75;
        @include transition(all .05s linear);
        &:hover {
          color: $black;
        }
      }
    }

    .last {
      display: inline-block;
      height: 0;
      width: 100%;
      line-height: 0;
      pointer-events: none;
      visibility: hidden;
    }
  }
}

.header-divider {
  border-bottom: 1px solid #f2f2f2;
}

.brand {
  @include clearfix;
  margin-bottom: 0.8em;
  .square {
    float: left;
    width: 32px;
    height: 32px;
    margin-right: 0.7em;
    background-color: #ffffff;
  }
  .ellipsis {
    margin-top: 0.75em;
    padding-top: 0.2em;
  }
}

.load-more {
  float: left;
  width: 100%;
  text-align: center;
  margin-top: $grid-gutter;

  a {
      display: block;
      color: $dark-gray;
      text-decoration: none;
      padding-left: 1em;
      font-size: .85em;
      line-height: 1.75;
      @include transition(all .05s linear);
      &:hover {
        color: $black;
      }
    }
  strong.link-brands {
    cursor: pointer;
    color: $dark-gray;
    text-transform: uppercase;
    @include transition(all .05s linear);
      &:hover {
        color: $black;
      }
  }
}

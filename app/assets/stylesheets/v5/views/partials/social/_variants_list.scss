.variants-list {
  @include clearfix;

  article {
    position: relative;
    margin-bottom: $grid-gutter;
    padding: 0 ($grid-gutter / 2) ($grid-gutter / 2);
    text-align: center;

    &.on-sale {
      .price {
        text-decoration: line-through;
        color: $gray;
        font-weight: 100;
        font-size: 11px;
      }
    }
  }

  a {
    text-decoration: none;
  }

  .img-wrapper {
    position: relative;
    margin-bottom: .625em;
    background-color: white;

    .no-touch &:hover {
      .more {
        display: block;
      }
    }

    .no-touch.csstransitions &:hover {
      .more {
        @include transition(all .3s linear .3s);
        visibility: visible;
        opacity: 1;
      }
    }
  }

  img {
    display: block;
    margin: 0 auto;
    height: 200px;
    max-width: 100%;
    .backgroundsize & {
      padding-top: 80%;
      width: 100%;
      height: 0;
      background: {
        size: contain;
        repeat: no-repeat;
        position: center center;
      }
    }
  }

  .more {
    @include cover-all;
    display: none;
    background-color: #333;
    background-color: rgba(#000, .5);

    .csstransitions & {
      @include transition(all .2s linear);
      display: block;
      visibility: hidden;
      opacity: 0;
    }

    div {
      z-index: 10;
      position: absolute;
      top: 50%;
      left: 0;
      margin: -1.2em 0 0;
      width: 100%;
      text-align: center;
      font-size: 1.125em;
    }

    span {
      display: inline-block;
      padding: .625em;
      border: 1px solid white;
      text-transform: uppercase;
      color: white;
      background-color: rgba(black, .4);
      @include transition(all .1s linear);
      .no-touch &:hover {
        color: $black;
        background-color: white;
      }
    }
  }

  h1,
  h2,
  h3,
  .price,
  .sale-price {
    font-size: 13px;
    line-height: 1.5;
    color: $black;
    font-weight: normal;
  }

  .sale-discount{
    position: absolute;
    color: $white;
    font-weight: 500;
    font-size: 0.9em;
    float: right;
    width: 3em;
    overflow: hidden;
    background: $blue;
    border-radius: 2px;
    border: 1px solid $blue;
    text-align: center;
    line-height: 17px;
    right: .1em;
    @media #{$before-wide} {
      width: 2.6em;
    }
  }


  h3 {
    @include ellipsis;
    color: $darker-gray;
    a {
      font-weight: bold;
      border-bottom: 1px solid transparent;
      @include transition(all .1s linear);
      &:hover {
        border-bottom: 1px solid $gray;
      }
    }
  }

  h2 {
    @include ellipsis;
    color: $dark-gray;
    a {
      font-weight: bold;
      border-bottom: 1px solid transparent;
      @include transition(all .1s linear);
      &:hover {
        border-bottom: 1px solid $gray;
      }
    }
  }

  .price {
    color: $dark-gray;
  }

  .sale-price {
    padding-left: 5px;
  }
}

.recommended-product {
  @include clearfix;

  position: relative;
  margin-top: .7em;

  .title,
  .manufacturer {
    color: $dark-gray;

    a {
      font-weight: bold;
      border-bottom: 1px solid transparent;
      @include transition(all .15s linear);

      &:hover {
        border-bottom: 1px solid $gray;
      }
    }
  }

  .title {
    @include ellipsis;
    line-height: 1.4;
  }

  .manufacturer {
    @include ellipsis;
    padding-bottom: .6em;
    font-size: .8em;
    line-height: 1;
  }

  .description {
    margin: 0;
    font-size: .6em;
    color: $gray;
    line-height: 1.4;
  }

  .price,
  .sale-price {
    float: right;
    color: $red;
    font-weight: bold;
    .currency {
      position: relative;
      top: -.3em;
      font-size: .7em;
    }
  }

  &.on-sale {
    .price {
      color: $dark-gray;
      font-size: .8em;
      text-decoration: line-through;
      position: relative;
      top: .1em;
      padding-right: .3em;
    }
  }

  .picture {
    float: left;
    position: relative;
    top: .3em;
    margin-right: .7em;
    width: 5.1em;
    height: 5.1em;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    a {
      @include cover-all;
    }
  }
}

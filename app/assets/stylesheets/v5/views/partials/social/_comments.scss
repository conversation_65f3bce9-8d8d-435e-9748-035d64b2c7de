.comments {
  &.empty {
    .comments-list {
      padding: 0;
      border-top: none;
    }
  }

  &.focus {
    .comments-form {
      &:before {
        border-color: transparent $light-gray transparent transparent;
      }
    }
  }

  .comments-list {
    margin: 0 -.7em;
    padding: .7em .7em;
    max-height: 20em;
    border-top: 1px solid $lightest-gray;
    border-bottom: 1px solid $lightest-gray;
    overflow: auto;
    background-color: #F8F8F8;
  }

  .comments-bottom {
    padding: .7em 0;
  }

  .comments-form {
    position: relative;
    display: inline-block;
    padding: 0 .6em 0 10px;
    vertical-align: top;
    width: 102px;
    width: calc(100% - 74px);

    @media #{$til-small} {
      width: 80px;
      width: calc(100% - 74px);
    }

    #social_comment_body {
      margin-bottom: 0;
    }

    input[type="text"] {
      @include form-input;
    }
  }

  .favorite-btn {
    float: left;
    border: 1px solid $light-gray;
  }
}
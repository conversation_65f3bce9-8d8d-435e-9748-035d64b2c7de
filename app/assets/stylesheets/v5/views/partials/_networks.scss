.networks {
  text-align: center;

  p {
    margin: 0 0 .6em;
    color: white;
    font-size: .65em;
    text-transform: uppercase;
  }

  a {
    $w: 23px;
    $h: 13px;
    display: inline-block;
    margin: .3em .5em 0 0;
    width: $w;
    height: $h;
    background: image-url('footer-sprite.png') no-repeat;
    @media #{$retina} {
      background-size: 230px auto;
      background-image: image-url('<EMAIL>');
    }
    &.disabled {
      @include opacity(.2);
    }
    &.us { background-position: -($w * 0) top; }
    &.ar { background-position: -($w * 1) top; }
    &.cl { background-position: -($w * 2) top; }
    &.pe { background-position: -($w * 3) top; }
    &.br { background-position: -($w * 4) top; }
    &.co { background-position: -($w * 5) top; }
    &.mx { background-position: -($w * 6) top; }
    &.ec { background-position: -($w * 7) top; }
    &.cr { background-position: -($w * 8) top; }
    &.uy { background-position: -($w * 9) top; }
  }
}

.v5-login-form {
  @include container;
  margin: 0 auto;
  padding: 1em $grid-gutter 0;
  border: 1px solid $light-gray;
  max-width: 17.5em;
  border-radius: 1px;
  background-color: #fff;
  font-size: 1em;

  input {
    @include form-input;
  }

  label,
  input[type="checkbox"] {
    cursor: pointer;
  }

  .msg {
    margin: 0 0 2em;
    font-size: .9em;
    color: $black;
    text-transform: uppercase;
    font-weight: 900;
  }

  h2 {
    font-size: .8125em;
    color: $light-gray;
    text-transform: uppercase;
    font-weight: 900;
    margin: 0 0 .6em;
  }

  p {
    font-size: .8em;
    text-align: center;
    margin-bottom: 1.5em;
    line-height: 1.3;
    color: $dark-gray;

    span {
      display: block;
    }

    a {
      color: $dark-gray;
      font-weight: bold;
      border-bottom: 1px solid transparent;
      &:hover {
        border-bottom: 1px solid $dark-gray;
      }
    }
  }

  .login-user {
    .remember {
      float: right;

      input {
        position: relative;
        top: 2px;
        float: left;
        margin: 0 .5em 0 0;
      }
    }

    .forgot {
      float: left;
    }

    .submit {
      clear: both;
      margin-bottom: 1.5em;
      text-align: center;
      button {
        @include buttons-plain('green', 'normal');
      }
    }
  }

  .login-social {
    @include clearfix;

    .i-logo-facebook {
      @include buttons-plain('facebook', 'normal');
      float: left;
    }

    .i-logo-twitter {
      @include buttons-plain('twitter', 'normal');
      float: right;
    }

    .i-logo-facebook,
    .i-logo-twitter {
      margin-bottom: 1em;
      text-align: center;
      min-width: 100%;

      &:before {
        position: relative;
        right: .3em;
        top: .15em;
        height: .5em;
        line-height: .4em;
        font-size: 1.7em;
      }
    }

    .register {
      clear: both;
    }
  }

  .login-guest {

  }

  @media #{$from-medium} {
    max-width: 40em;

    .login-user {
      @include span(1 of 2);
    }

    .login-social {
      @include span(1 of 2);
      @include omega;

      .i-logo-facebook,
      .i-logo-twitter {
        min-width: 48%;
      }
    }
  }
}

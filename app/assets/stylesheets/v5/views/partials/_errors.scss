#errors-show{
  .error-box{
    max-width: 600px;
    margin:0 auto;
    .background-error{
      font-weight: 800;
      color: #e0e0e0;
      position: absolute;
      z-index: -1;
      font-size: 165px;
      margin: -38px 0 0 311px;
      text-transform: uppercase;
    }
    h1{
      padding-top: 68px;
    }
    .description{
      margin-top:16px;
    }
    .home-button{
      @include buttons-plain('blue', 'normal');
      text-align: center;
      margin: 46px auto 0 auto;
      display: block;
      width: 139px;
    }
    .featured-variants-cmpnt {
      margin-bottom: -7px;
      margin-top: 49px;
    }
  }
  .header-slim-promo {
    display: none;
  }
  header {
    padding-top: 1em;
  }
  .header-phantom {
    height: 3.8em;
  }
  footer{
    display: none;
  }
}
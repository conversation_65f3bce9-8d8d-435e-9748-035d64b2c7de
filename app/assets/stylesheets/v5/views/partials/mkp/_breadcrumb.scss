.breadcrumb-wrapper{
  @include container;
  position: relative;
  .breadcrumb {
    @include span;
    @include omega;
    color: $gray;
    font-size: 0.85rem;
    line-height: 1.5rem;
    > span{
      padding-right: $grid-gutter/2;
      position: relative;
      &:after {
        content: '›';
        font-size: 1.1rem;
        font-weight:200;
        display: inline-block;
        padding-left: $grid-gutter/2;
      }
      &:last-child{
        padding-right: 0;
        &:after {
          content: '';
        }
      }
      .remove {
        display: none;
      }
      a {
        color: $gray;
        @include transition(all .15s ease-out);
        &:hover{
          color: $blue;
        }
      }
      > span {
        font-weight: bold;
      }
    }
  }
}

@mixin horizontal-line($line-style: dashed, $line-color: $light-gray) {
  position: relative;
  text-align: center;

  hr {
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -1px;
    border: 0;
    border-top: 1px $line-style $line-color;
    width: 100%;
    height: 0;
    z-index: -1;
  }
}

// Load more button, with white background, that will ocupy 100% of the width.
//
// Styleguide 1.3
.load-more-wide {
  z-index: 0;
  padding-bottom: 1px;
  @include horizontal-line(dashed);

  &.black {
    a {
      @include buttons-plain('black', 'normal');
    }
  }

  &.light-green {
    a {
      @include buttons-plain('light-green', 'normal');
    }
  }

  &.light-blue {
    a {
      @include buttons-plain('light-blue', 'normal');
    }
  }
  &.white-blue{
    a {
      @include buttons-plain('white-blue', 'normal');
    }
  }

  div {
    display: inline-block;
    padding: 0 12px;
    background: #F4F3F4;
  }
}

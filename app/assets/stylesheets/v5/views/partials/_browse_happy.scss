.browse-happy-wrapper {
  padding: 1em;
  border-bottom: 1px solid #b3a569;
  background-color: #fae692;
}

.browse-happy {
  padding-left: 3.3em;
  line-height: 1.3;
  font-size: 14px;
  color: #353535;
  background: image-url('alert.png') no-repeat left top;

  a {
    color: #4183c4;
    &:hover {
      text-decoration: underline;
    }
  }

  .more {
    float: right;
    color: #555;
    line-height: 2;
  }

  .button {
    margin-top: 1.6em;
    color: #f5f5f5;

    &:hover {
      text-decoration: none;
    }
  }

  h5 {
    font-size: 14px;
    padding: .8em 5.2em .6em 0;
  }

  p {
    margin: 0;
  }
}
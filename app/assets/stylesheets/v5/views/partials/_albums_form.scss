.albums-form-popup {
  .mfp-container {
    padding: 0;
  }
  .mfp-content {
    max-width: 768px;
  }
}

.albums-form {
  position: relative;
  padding: 1em;
  background-color: #fff;

  .title {
    margin-bottom: .6em;
    color: $darker-gray;
    font-weight: 900;
    font-size: .9em;
    text-transform: uppercase;
    line-height: 1.5;
  }

  .inputs {
    @include container;

    .col {
      position: relative;
      @include span(1 of 2);
      &:last-child {
        @include omega;
      }
    }

    label {
      display: block;
      font-weight: 900;
      line-height: 1.7;
      font-size: .8em;
    }

    #social_album_title {
      @include form-input;
    }
  }

  .media-wrapper {
    max-height: 100%;
    overflow: auto;
  }

  .post-item {
    @include buttons-plain('green', 'normal');
    display: block;
    margin: 0 auto;
  }
}

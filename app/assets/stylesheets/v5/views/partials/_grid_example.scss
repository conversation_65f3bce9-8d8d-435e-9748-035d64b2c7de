.grid-example-wrapper {
  @include outer-container;
}

.grid-example {
  @include outer-container;
  @include unselectable;
  clear: both;
  min-height: 500px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 500;
  pointer-events: none;

  div {
    height: 100%;
    color: white;
    font-weight: bold;
    text-align: center;
    background-color: rgba($red, .2);

    @include span(1 of 16);
    @include omega(16);

    @media #{$mobile} {
      @include span(1 of 4);
      @include omega(4);
      &:nth-child(1n+5) { display: none; }
    }

    @media #{$tablet} {
      @include span(1 of 8);
      @include omega(8);
      &:nth-child(1n+9) { display: none; }
    }
  }
}
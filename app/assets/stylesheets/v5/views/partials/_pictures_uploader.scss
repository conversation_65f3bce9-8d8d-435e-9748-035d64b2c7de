.pictures-uploader {
  .pictures-uploader-items {
    position: relative;
    overflow: auto;
  }

  .pictures-uploader-item {
    position: relative;
    float: left;
    border: 1px solid $light-gray;

    // Mobile First
    margin-bottom: 5%;
    width: 100%;

    @media #{$after-small} {
      margin-left: 1%;
      margin-right: 1%;
      margin-bottom: 3%;
      &:nth-child(2n+1) {
        margin-left: 0;
        margin-right: 2%;
      }
      &:nth-child(2n) {
        margin-right: 0;
        margin-left: 2%;
      }
      width: 48%;
    }

    @media #{$from-medium} {
      margin-bottom: 2%;
      // Margin fallbacks
      margin-left: 1%;
      margin-right: 1%;
      // Left and right columns
      &:nth-child(n) {
        margin-left: 0;
        margin-right: 0;
      }
      // Middle colum
      &:nth-child(3n+2) {
        margin-left: 2%;
        margin-right: 2%;
      }
      width: 32%;
    }

    &.loading {
      .pictures-uploader-item-thumb {
        @include opacity(0);
      }
      .pictures-uploader-item-thumb-background,
      .pictures-uploader-item-progress {
        @include opacity(1);
      }
    }

    &.add-photos {
      cursor: pointer;
      border-style: dashed;
      &:hover {
        background-color: #eee;
      }
      .add-photos-selector {
        padding-top: 68%;
        padding-bottom: 30px;
      }
      .add-photos-text {
        position: absolute;
        top: 47%;
        left: 0;
        width: 100%;
        text-align: center;
      }
      .pictures-uploader-item-description {
        visibility: hidden;
      }
    }
  }

  .pictures-uploader-item-thumb {
    padding-top: 68%;
    border-bottom: 1px solid $light-gray;
    background-color: white;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    @include transition(opacity .5s ease-in-out);
  }

  .pictures-uploader-item-progress,
  .pictures-uploader-item-thumb-background {
    @include opacity(0);
    @include transition(opacity .5s ease-in-out);
  }

  .pictures-uploader-item-thumb-background {
    position: absolute;
    left: 0;
    top: 31px;
    padding-top: 68%;
    width: 100%;
    border-bottom: 1px solid $light-gray;
    background-color: white;
  }

  .pictures-uploader-item-progress {
    position: absolute;
    top: 48%;
    left: 28%;
    width: 44%;
    height: 11px;
    background-color: #B9B9B9;
    .pictures-uploader-item-progress-bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background-color: #858585;
    }
  }

  .pictures-uploader-item-description {
    margin: 0;
    border-radius: 0;
    border: none;
    padding: 0.48em;
    height: auto;
    box-shadow: none;
    width: 100%;
    line-height: 19px;
  }

  .pictures-uploader-item-name {
    @include unselectable;
    @include ellipsis;
    border-bottom: 1px solid $light-gray;
    cursor: default;
    font-weight: bold;
    color: $light-gray;
    text-align: right;
    line-height: 30px;
    font-size: 12px;
    max-width: 100%;
  }

  .pictures-uploader-item-cancel {
    @include form-reset-button;
    float: right;
    cursor: pointer;
    color: $red;
    line-height: 30px;
    padding: 0 .4em;
    border-left: 1px solid $light-gray;
    border-bottom: 1px solid $light-gray;
    &:hover {
      color: $dark-red;
    }
  }
}
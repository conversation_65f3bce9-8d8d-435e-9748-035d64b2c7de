// List of posts, dependant on homologous partial and js
//
// Styleguide 4.1
.posts-list {
  overflow: hidden;

  &.loading {
    article {
      opacity: 0;
    }
  }

  .posts-list-wrapper {
    position: relative;
    &.inited {
      article {
        margin: {
          left: 0 ;
          right: 0;
        }
      }
    }
  }

  article,
  .load-more-wide {
    @include transition(opacity .5s ease-in-out);
  }

  article {
    position: relative;
    margin-bottom: $grid-gutter;
    padding: ($grid-gutter / 2) $grid-gutter;
    border: 1px solid $light-gray;
    border-top: 5px solid $gray;
    background-color: #fff;

    .mobile & {
      margin-bottom: 5px;
    }

    &:before {
      content: '';
      position: absolute;
      top: -5px; left: -1px;
      width: 1px; height: 5px;
      background-color: $gray;
    }

    &:after {
      content: '';
      position: absolute;
      top: -5px; right: -1px;
      width: 1px; height: 5px;
      background-color: $gray;
    }

    .no-touch &:hover {
      .more {
        display: block;
        z-index: 15;
        .share {
          .buttons {
            display: block;
          }
          .favorites-count {
            display: block;
          }
        }
      }
    }

    .no-touch.csstransitions &:hover {
      .more {
        @include transition(all .3s linear .3s);
        opacity: 1;
      }
    }
  }

  h1,
  h2,
  h3,
  p {
    font-size: 12px;
    line-height: 1.4;
    color: $black;
    font-weight: normal;
  }

  h1,
  h2,
  h3 {
    margin-bottom: 5px;
  }

  h1 {
    font-weight: bold;
    @include ellipsis;
  }

  p {
    margin-bottom: 10px;
    word-break: break-word;
    word-wrap: break-word;
  }

  .author {
    @include clearfix;

    .avatar {
      float: left;
      margin-right: .4em;
      @include avatar-size(2.25em);
    }

    h2 {
      margin-left: 3.85em;
      padding-top: .5em;
      min-height: 3.1em;
      line-height: 1.4;
      font-size: .75em;
      color: $darker-gray;
      font-weight: normal;
    }

    a {
      font-weight: bold;
    }
  }

  .date {
    margin-bottom: .3125em;
    line-height: 1.4;
    color: $black;
    font-size: .6875em;

    a {
      font-weight: bold;
    }
  }

  .pictures {
    @include container;
    img {
      margin-bottom: 10px;
      width: 100%;
      height: 200px;
      .backgroundsize & {
        height: auto;
        background: {
          size: contain;
          position: center center;
          repeat: no-repeat;
          color: $light-gray;
        }
        &:after {
          content: '';
          display: block;
          padding-top: 100%;
        }
      }
    }
    @mixin pic-with($a) {
      @include span(1 of $a,
                    $width: 100%,
                    $gutter: 10px,
                    $fallback-width: 100%,
                    $fallback-gutter: 5%);
    }
    &.with-2,
    &.with-4,
    &.with-5 {
      img { @include pic-with(2); }
      a:nth-of-type(2n){
        img { @include omega; }
      }
    }
    &.with-3,
    &.with-6 {
      img { @include pic-with(3); }
      a:nth-of-type(3n){
        img { @include omega; }
      }
    }
  }

  .i-play {
    color: #fff;
    background-color: rgba(#fff, .4);
  }

  .embedded {
    img,
    h1,
    p {
      cursor: pointer;
    }
    img {
      max-width: 100%;
      margin-bottom: 10px;
    }
    .embedded-img-wrapper {
      position: relative;
      max-width: 140px;
    }
  }

  .more {
    z-index: 5;
    display: none;
    position: absolute;
    left: -1px;
    top: -5px;
    width: 100%;
    width: calc(100% + 2px);
    height: 100%;
    height: calc(100% + 5px);
    min-height: 3em;
    font-size: .7em;
    background-color: rgba(#000, .5);

    @media #{$after-wide} {
      font-size: 1em;
    }

    .csstransitions & {
      @include transition(all .2s linear);
      display: block;
      opacity: 0;
    }

    .show {
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      &:before, &:after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        background-color: #fff;
      }
      &:before {
        width: 2em;
        height: .4em;
        margin: -.2em 0 0 -1em;
      }
      &:after {
        width: .4em;
        height: 2em;
        margin: -1em 0 0 -.2em;
      }
    }

    .share {
      position: absolute;
      left: 0;
      top: 100%;
      width: 100%;
      background-color: rgba(#000, .8);
    }

    .buttons {
      @include clearfix;
      padding: ($grid-gutter / 2) $grid-gutter;
      display: none;
    }

    .favorite-btn {
      float: left;
    }

    .round-social-icon {
      float: right;
    }

    .favorites-count {
      display: none;
      padding: .3em $grid-gutter;
      font-size: .8em;
      color: #fff;
      background-color: rgba(#000, .9);
    }
  }
}

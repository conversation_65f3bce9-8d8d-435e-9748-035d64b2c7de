.item-body {
  @include clearfix;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.1em;
  max-width: 500px;
  background-color: #F2F2F2;
  @include transition (opacity .3s ease-in);

  &.has-media {
    max-width: $wide-screen;
    @media #{$from-medium} {
      .item-body-media {
        float: left;
        width: 65%;
      }
      .item-body-content {
        float: right;
        width: 35%;
      }
    }
  }

  &.loading {
    @include opacity(0);
  }

  .mfp-close {
    @extend .i-close;
    position: absolute;
    top: -44px;
    right: 0;
    padding-top: .6em;
    opacity: 1;
    color: #fff;
    font-size: .8em;
    text-align: right;

    &:hover,
    &:active {
      opacity: .8;
    }
  }

  .metadata {
    margin: 0 (-($grid-gutter / 2));
  }

  hr {
    margin: 0 (-($grid-gutter / 2));
    border: none;
    border-top: 1px solid $lightest-gray;
  }

  .item-body-media {
    position: relative;
    max-width: 100%;
    min-height: 6em;
    text-align: center;
    background-color: $dark-gray;
    overflow: hidden;
    text-align: center;
    img {
      display: block;
      margin: 0 auto;
      max-width: 100%;
      max-height: 790px;
      &.item {
        display: none;
        &.active {
          display: inline-block;
        }
      }
    }
    .navigation-left, .navigation-right {
      @include unselectable;
      position: absolute;
      top: 0;
      margin-top: 0;
      width: 35%;
      height: 100%;
      z-index: 2;
      cursor: pointer;
      .icon {
        position: absolute;
        top: 50%;
        margin-top: -28px;
        width: 34px;
        height: 56px;
        background-image: image-url('social/gallery.png');
      }
      &.navigation-left {
        left: 0;
        .icon {
          left: 10px;
          background-position: 0 56px;
        }
      }
      &.navigation-right {
        right: 0;
        .icon {
          right: 10px;
        }
      }
    }
    .embed-video-wrapper {
      position: relative;
      padding-bottom: 75%;
      padding-top: 25px;
      height: 0;
      min-height: 100%;
      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }

  .feed-item-author {
    position: relative;
    text-align: left;
    padding: ($grid-gutter / 2) 0;
  }

  .item-body-content {
    position: relative;
    padding: 10px 10px 0;
    overflow: hidden;
    .item-title {
      margin: 0;
    }
    .text {
      margin: 0 0 ($grid-gutter / 2);
      font-size: .75em;
      line-height: 1.6;
      font-weight: 400;
      text-rendering: geometricPrecision;
      color: $dark-gray;
      a {
        font-weight: 700;
      }
    }
    .album-picture-description {
      display: none;
      margin: 0 0 ($grid-gutter / 2);
      &.active {
        display: block;
      }
      a {
        font-weight: 700;
      }
    }
    .social-actions {
      padding: 0 0 0.5em;
      .social-action {
        display: inline-block;
        line-height: 26px;
        font-size: emCalc(13px);
        margin: 0 1.2em 0 0;
        vertical-align: middle;
        &:last-child {
          margin-right: 0;
        }
        @media #{$til-medium} {
          margin-right: 11px;
        }
        .icon {
          display: inline-block;
          float: left;
          margin-right: 8px;
          height: 26px;
          background-repeat: no-repeat;
          background-image: image-url('social/feed_item/social-actions-sprite.png');
        }
        .text {
          margin: 0;
        }
        &.social-actions-comments {
          &.no-comments {
            display: none;
          }
          &.active {
            color: $red;
            .icon {
              background-position: -101px -47px;
            }
          }
          .icon {
            width: 24px;
            background-position: 0 -47px;
          }
        }
        &.social-actions-shares {
          .icon {
            width: 32px;
            background-position: 0 -97px;
          }
        }
      }
    }
    .favorites-count {
      position: relative;
      padding: 1em 0 1em 32px;
      p {
        margin: 0;
        font-size: .75em;
        color: $dark-gray;
      }
      a { font-weight: bold; }
      .icon {
        position: absolute;
        left: 0; top: 50%;
        margin-top: -14px;
        display: inline-block;
        width: 24px;
        height: 26px;
        background-repeat: no-repeat;
        background-image: image-url('social/feed_item/social-actions-sprite.png');
        background-position: 0 0px;
      }
    }
  }

  .item-body-user-menu {
    position: relative;
    display: inline-block;
    float: right;
    &.active {
      .item-body-user-menu-bubble {
        display: block;
      }
    }
    .item-body-user-menu-button {
      @include buttons-plain('white', 'small');
      position: relative;
      padding-left: 1em;
      padding-right: 1em;
      span {
        display: block;
        color: #999;
        line-height: 1.7;
      }
    }
    .item-body-user-menu-bubble {
      display: none;
      position: absolute;
      right: 0;
      bottom: 100%;
      margin-bottom: 1em;
      border: 1px solid darken(#fff, 15%);
      border-radius: 3px;
      background-color: white;
      box-shadow: 0 1px 2px 1px rgba(0,0,0,0.1);
      li {
        position: relative;
        min-width: 93px;
        text-align: center;
        padding: 0.7em 1em 0.9em;
        border-bottom: 1px solid darken(#fff, 15%);
        &:last-child {
          border-bottom: none;
          &:before, &:after {
            content: '';
            position: absolute;
            top: 100%;
            border-color: transparent;
            border-style: solid;
          }
          &:before {
            right: 1.3em;
            border-width: 8px;
            border-top-color: darken(#fff, 15%);
          }
          &:after {
            right: 1.45em;
            border-width: 6px;
            border-top-color: white;
          }
        }
        .item {
          display: block;
          &:hover {
            background-color: darken(#fff, 5%);
          }
          &:last-child {
            &:hover:after {
              border-top-color: darken(#fff, 5%);
            }
          }
        }
        .text {
          text-align: center;
        }
        .share-button {
          display: inline-block;
          margin: 0.7em 1em 0 0;
          cursor: pointer;
          @include size(25px);
          border: 1px solid darken(#fff, 15%);
          border-radius: 3px;
          background-color: white;
          background-repeat: no-repeat;
          background-image: image-url('social/sprite-social-popup.png');
          &.facebook-share {
            background-position: 5px 4px;
            &:hover { background-position: 5px -16px; }
            &:active { background-position: 5px -36px; }
          }
          &.twitter-share {
            background-position: -38px 5px;
            &:hover { background-position: -38px -15px; }
            &:active { background-position: -38px -35px; }
          }
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .recommended-products {
    padding-bottom: 10px;
  }
}

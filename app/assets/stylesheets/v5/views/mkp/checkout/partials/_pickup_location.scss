.pickup-location {
  padding: $grid-gutter;

  .full-name, .email{
    @include span(3 of 8, $gutter: $grid-gutter/2);
  }
  .phone {
    @include span(2 of 8, $gutter: $grid-gutter/2);
    @include omega;
  }

  .pickup-location-search-wrapper {
    margin-bottom: 5px;
  }

  .pickup-selector-wrapper {
    margin-bottom: $grid-gutter;
  }

  .pickup-location-info-item {
    position: relative;
    padding-left: 26px;

    i {
      position: absolute;
      left: 0;
      top: 0;
      color: $green;
    }

    h1 {
      font-size: 1em;
      line-height: 1.6;
      color: $dark-gray;
    }

    p {
      font-size: .9em;
      line-height: 1.2em;
      color: $gray;
    }
  }
 }

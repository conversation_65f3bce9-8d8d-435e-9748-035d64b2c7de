.summary {
  > h4 {
    padding-bottom: $grid-gutter/4;
  }
  .product-list {
    border-bottom: 1px dotted $lighter-gray;
    .cart-item {
      @include container;
      position: relative;
      font-size:1em;
      color: $gray;
      padding-bottom: $grid-gutter;
      .img {
        z-index: 0;
        text-align: center;
        overflow: hidden;
        @include span(2 of 12, $gutter: $grid-gutter/2);
        img {
          @include fill-container;
          border:1px solid $lighter-gray;
        }
      }
      .data{
        z-index: 0;
        @include span(7 of 12, $gutter: $grid-gutter/2);
        .name{
          color: $darker-gray;
          @include ellipsis;
          font-size:0.9em;
          line-height: 1em;
          padding-bottom: 2px;
        }
        .variant, .quantity{
          font-size:0.75em;
          line-height: 1.4em;
        }
        .property {
          position: relative;
          margin-right: $grid-gutter/4;
          &.with-color-box{
            margin-right: 18px + $grid-gutter/4;
          }
          &:last-child{
            margin-right: 0px;
          }
          .color-box{
            display: inline-block;
            position: absolute;
            top: 0px;
            right: -17px;
            border-radius: $border-radius;
            height: 15px;
            width: 15px;
          }
        }
        .remove-item{
          font-size: 0.65em;
          color: $dark-gray;
          text-transform: uppercase;
          font-weight: 800;
          @include transition(color .2s ease-in-out);
          &:hover{
            color: $red
          }
        }
      }
      .price{
        z-index: 0;
        position: relative;
        text-align: right;
        font-size:0.9em;
        @include span(3 of 12, $gutter: $grid-gutter/2);
        @include omega;
      }
    }
  }
  .subtotal{
    @include container;
    position: relative;
    padding-top: $grid-gutter;
    border-bottom: 1px dotted $lighter-gray;
    color: $gray;
    .delivery_cost, .subtotal_amount, .discount, .handling-discount, .promotion {
      @include container;
      position: relative;
      padding-bottom: $grid-gutter;
      > h4 {
        color: $dark-gray;
        margin-bottom: 0px;
        font-size:0.95em;
        font-weight: 400;
        @include span(8 of 12, $gutter: $grid-gutter/4);
      }
      .price{
        text-align: right;
        font-size:1em;
        @include span(4 of 12, $gutter: $grid-gutter/4);
        @include omega;
      }
    }
    .discount, .handling-discount, .promotion {
      h4 {
        color: $green;
      }
      .price {
        color: $green;
        font-weight: bold;
      }
    }
  }
  .total {
    @include container;
    position: relative;
    padding-top: $grid-gutter;
    border-bottom: 1px dotted $lighter-gray;
    .order_total{
      @include container;
      position: relative;
      padding-bottom: $grid-gutter;
      color: $dark-gray;

      h3.order_price {
        margin-bottom: 0;
        font-size: 1.8em;
        font-weight: 800;
        text-transform: uppercase;
        float: left;
        margin-left: 2.5%;
        margin-right: 2.5%;
        width: calc(50% + -2.5px);
        margin-left: calc(0px);
        margin-right: calc(5px);
      }

      h3.order_price_with_financing {
        margin-bottom: 0px;
        font-size: 1em;
        font-weight: 800;
        text-transform: uppercase;
        @include span(6 of 12, $gutter: $grid-gutter/4);
      }

      .price {
        text-align: right;
        font-size: 1.8em;
        float: left;
        margin-left: 2.5%;
        margin-right: 2.5%;
        width: calc(50% + -2.5px);
        margin-left: calc(0px);
        clear: right;
        margin-right: calc(0px);
      }

      .price_with_financing {
        text-align: right;
        font-size: 1em;
        @include span(6 of 12, $gutter: $grid-gutter/4);
        @include omega;

        margin-bottom: 15px
      }

      .financing_information {
        .fees_and_interests, .taxes, .financed_price {
          h3 {
            margin-bottom: 15px;
            font-size: 1em;
            font-weight: 800;
            text-transform: uppercase;
            @include span(6 of 12, $gutter: $grid-gutter/4);
          }

          span {
            margin-bottom: 15px;
            text-align: right;
            font-size: 1em;
            @include span(6 of 12, $gutter: $grid-gutter/4);
            @include omega;
          }
        }

        .taxes {
          span.cft {
            font-size: 1.6em;
            font-weight: 100;
            text-align: left;
          }

          span.tea {
            margin-top: 8px;
            font-size: .75em;
            font-weight: 100;
            text-align: right;
            width: 50%;
          }
        }

        .financed_price {
          clear: both;

          h3 {
            font-size: 2em;
          }

          span {
            margin-top: 5px;
            font-size: 1.5em;
            display: inline-block;
          }
        }
      }
    }
  }

  .pay-button{
    .button {
      @include container;
      cursor: pointer;
      position: relative;
      text-transform: uppercase;
      text-align: center;
      font-weight: 800;
      font-size: 1.2em;
      background-color: $green;
      color: $white;
      padding: $grid-gutter/2;
      border-radius: $border-radius;
    }
    &.disabled{
      .button{
        cursor: default;
        background-color: $gray;
        color: $lightest-gray;
      }
    }
  }
}

#MY_btnPagarConBilletera {
  background: #307f39;
  border: medium none;
  border-spacing: 0;
  color: white;
  line-height: 1.42rem;
  list-style: none outside none;
  margin: 0;
  text-decoration: none;
  text-indent: 0;
  text-align: center;
  width: 100%;
  cursor: pointer;
  padding: 10px;
  text-transform: uppercase;
  font-weight: 800;
  font-size: 1.2em;
}
.shipping {
  .address, .methods {
    padding: $grid-gutter;
    > h4 {
      padding-bottom: $grid-gutter/4;
    }
    &:last-child{
      padding-top: 0px;
    }
  }
  .addresses{
    @include container;
    font-size:1em;
    color: $gray;
    .address-option{
      @include span(1 of 3, $gutter: $grid-gutter);
      margin-bottom: $grid-gutter;
      padding-left: $grid-gutter + 5px;
      position: relative;
      cursor: pointer;
      &:nth-of-type(3n) {
        @include omega;
      }
      i{
        position: absolute;
        top: 2px;
        left: 0px;
        &.i-radio-on{
          color: $green;
        }
      }
      h5 {
        font-size:0.8em;
        line-height: 1.2em;
        overflow: hidden;
        padding-bottom: 2px;
        text-overflow: ellipsis;
      }
      .full_name, .full_location, .country {
        font-size:0.85em;
        line-height: 1.2em;
      }
      .actions{
        padding-top: 3px;
        >span{
          font-size: 0.65em;
          color: $red;
          text-transform: uppercase;
          padding-right: $grid-gutter/2;
          margin: $grid-gutter/4 $grid-gutter/2 $grid-gutter/4 0;
          border-right: 1px solid $red;
          cursor: pointer;
          &:last-child{
            margin-right: 0px;
            border-right: 0px;
          }
          &:hover{
            text-decoration: underline;
          }
        }
      }
      .confirmation {
        font-size: 0.8em;
        text-align: center;
        p{
          margin-bottom:5px;
          font-size: 0.9em;
        }
        > span {
          display: inline-block;
          padding: $grid-gutter/4 $grid-gutter/2;
          border-width: 1px;
          border-style: solid;
          cursor: pointer;
          border-radius: $border-radius;
          &.yes{
            color: $green;
            border-color: $green;
            margin-right: $grid-gutter/2;
          }
          &.no{
            color: $red;
            border-color: $red;
          }
        }
      }
      &.new-address-option{
        padding: $grid-gutter/2;
        border: 1px dashed $lightest-gray;
        border-radius: $border-radius;
        font-size:0.8em;
        text-align: center;
        margin-top: $grid-gutter;
        @include transition(background-color .3s);
        cursor: pointer;
        h4{
          margin-bottom: 0px;
          cursor: pointer;
        }
        &:hover{
          background-color: $lightest-gray;
        }
      }
    }
  }
  .address-form {
    @include container;
    position: relative;
    > form > div {
      z-index: 0;
      padding-bottom: $grid-gutter/4;
    }
    form div {
      @media #{$til-medium} {
        width: 100% !important;
      }
    }
    .edit-controls{
      @include span(1 of 1);
      @include omega;
      position: relative;
      padding: $grid-gutter/2;
      margin: $grid-gutter/4 0px;
      border-top: 1px solid $lightest-gray;
      font-size: 0.75em;
      text-align: right;
      text-transform: uppercase;
      font-weight: 800;
      > span {
        cursor: pointer;
      }
      .cancel{
        color: $red;
        margin-right: $grid-gutter;
      }
    }
  }
  .address-form{
    .full-name, .email{
      @include span(3 of 8, $gutter: $grid-gutter/2);
    }
    .phone {
      @include span(2 of 8, $gutter: $grid-gutter/2);
    }
    .street_1{
      @include span(6 of 12, $gutter: $grid-gutter/2);
    }
    .street_number{
      @include span(2 of 12, $gutter: $grid-gutter/2);
    }
    .street_2 {
      @include span(4 of 12, $gutter: $grid-gutter/2);
    }
    .country, .state {
      @include span(3 of 12, $gutter: $grid-gutter/2);
    }
    .city {
      @include span(4 of 12, $gutter: $grid-gutter/2);
    }
    .zip-code {
      @include span(2 of 12, $gutter: $grid-gutter/2);
    }
    .phone, .street_2, .zip-code {
      @include omega;
    }
  }

  .delivery-options{
    @include container;
    font-size:1em;
    .notifications{
      @include span(1 of 1);
      @include omega;
      font-size: 0.75em;
      color: $gray;
      text-align: center;
      text-transform: uppercase;
      padding: $grid-gutter/2;
      background: lighten($lightest-gray, 5%);
    }
    .option{
      @include span(1 of 3, $gutter: $grid-gutter);
      margin-bottom: $grid-gutter;
      padding-left: $grid-gutter + 5px;
      position: relative;
      cursor: pointer;
      &:nth-of-type(3n) {
        @include omega;
      }
      i{
        position: absolute;
        top: 2px;
        left: 0px;
        color: $gray;
        &.i-radio-on{
          color: $green;
        }
      }
      h5 {
        font-size:0.85em;
        line-height: 1.2em;
        padding-bottom: 2px;
        color: $darker-gray;
        span{
          color: $gray;
          font-size:0.95em;
          font-weight: 400;
          text-transform: none;
        }
      }
      .bonification{
        color:$red;
        .old-price{
          color:$red;
          font-weight: 800;
          margin-right: $grid-gutter/4;
          text-decoration: line-through;
        }
      }
    }
  }
}

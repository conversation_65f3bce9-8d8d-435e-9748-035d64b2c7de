@media #{$til-medium} {
  main, header{
    min-width: 0px !important;
  }
}
.thanks{
  @include span(6 of 6);
  text-align: center;

  .thanks-body {
    @include span(6 of 6);
    margin: 0 auto;
    float: none;
    margin-top: $grid-gutter * 2;
    @media #{$til-wide} {
      margin-top: $grid-gutter/2;
    }
    h2 {
      font-size: 2em;
      @media #{$til-medium} {
        font-size: 1.6em;
      }
    }
    h3{
      color: $green;
      font-size: 1.4em;
      text-align: center;
      @media #{$til-medium} {
        font-size: 1.2em;
      }
    }
    .i-tilde {
      font-size: 3.3em;
      line-height: 1;
      color: $green;
      margin: $grid-gutter*1.2 0;
      @media #{$til-medium} {
        margin: $grid-gutter 0;
      }
    }

    .text {
      font-size: 1.2em;
      text-align: center;
      h2, .suborders, .message, .email_awareness {
        margin-bottom: $grid-gutter;
        line-height: 1.4em;
        @media #{$til-medium} {
          margin-bottom: $grid-gutter/1.5;
          line-height: 1.3em;
        }
      }
      .suborders{
        font-weight: 700;
      }
      .message, .email_awareness {
        font-size: 0.9em;
        color: $gray;
      }
      .email_awareness {
        border-top: 1px dashed $light-gray;
        padding-top: $grid-gutter;
        .title, strong{
          color:$black;
          font-weight: 700;
        }
        .email{
          color: $blue;
          font-weight: 800;
        }
        @media #{$til-medium} {
         padding-top: $grid-gutter/1.5;
        }
      }
      @media #{$til-medium} {
        font-size: 1em;
        line-height: 1.2em;
      }
    }
  }
}

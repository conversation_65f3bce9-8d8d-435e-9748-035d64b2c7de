.pickup-selector {
  @include container;
  padding: 20px;

  .pickup-selector-option {
    @include buttons-plain('white', 'normal');

    &.active {
      @include buttons-plain('black', 'normal');

      &:after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin: 0 0 -6px -6px;
        @include triangle(12px, $black, down);
      }
    }

    i {
      position: relative;
      top: 0.17em;
      margin-right: 0.4em;
      line-height: 0;
      font-size: 2em;

      &:before {
        line-height: 0;
      }
    }
  }

  // weird selector to take precendence over `margin: 0` defined on `buttons`
  .pickup-selector-option.pickup-selector-option {
    @include span(1 of 2);

    @media #{$til-smallish} {
      @include span(1 of 1);
      @include omega;
      margin-bottom: $grid-gutter / 2;
    }

    &:last-of-type {
      @include omega;
    }
  }
}

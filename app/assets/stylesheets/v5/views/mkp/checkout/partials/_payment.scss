@charset "UTF-8";

.payment {
  .methods {
    padding: $grid-gutter;
    > h4 {
      padding-bottom: $grid-gutter/4;
    }
  }
  .header {
    margin-bottom: $grid-gutter/2;
  }
  .errors {
    @include container;
    display:none;
    font-size: 1.1em;
    background-color: rgba($red, 0.15);
    padding: $grid-gutter/2;
    border-radius: $border-radius;
    border: 1px solid $light_red;
    margin-top: $grid-gutter/2;
    .error {
      @include span(1 of 1);
      @include omega;
      color: $light_red;
      text-align: center;
      line-height: 1.5em;
      font-weight: 800;
    }
  }
  .active-payments{
    .payment-option{
      margin-bottom: $grid-gutter;
      padding-left: $grid-gutter + 5px;
      position: relative;
      cursor: pointer;
      .installments-more-info {
        cursor: pointer;
        color: $light_red;
        font-weight: bold;
        letter-spacing: normal;
        text-decoration: none;
        font-size: 0.9em;
      }
      i{
        position: absolute;
        top: 2px;
        left: 0px;
        &.i-radio-on{
          color: $green;
        }
      }
      h5 {
        font-size:0.8em;
        display: inline-block;
        margin-right: $grid-gutter/2;
        cursor: pointer;
        line-height: 1.4em;
      }
      span{
        display: inline-block;
        font-size: 0.8em;
        margin-right: $grid-gutter/5;
      }
      .payment-icon{
        vertical-align: middle;
        &:last-child{
          margin-right: 0px;
        }
        img {
          max-height: 20px;
        }
      }
      form div {
        @media #{$til-medium} {
          width: 100% !important;
        }
      }
    }
  }
  .braintree, .mercadopago, .todopago {
    @include container;
    i, .header{
      color: $gray;
    }
    .cvv{
      z-index: 100 !important;
    }
    .tooltip-item{
      position: relative;
      display:inline-block;
      color: $gray;
      margin-left: $grid-gutter/4;
      @media #{$til-medium} {
        width: 50px !important;
      }
      &:hover .tooltip {
        opacity: 1;
        visibility: visible;
      }
      .tooltip {
        @include position(absolute, null null 25px -112px);
        @include transition (all 0.2s ease-in-out);
        background-color: $dark_gray;
        background: $dark_gray;
        border-radius: 2;
        box-shadow: $light_gray;
        color: $white;
        font-size: 0.9em;
        line-height: 1.5em;
        margin: 0 auto;
        width: 240px;
        opacity: 0;
        padding: $grid-gutter/3;
        text-align: center;
        visibility: hidden;
        z-index: 10;
        @media #{$til-medium} {
          width: 240px !important;
        }
        p {
          margin: 0;
        }

        &:after {
          @include position(absolute, null 0 null 0);
          color: $dark_gray;
          content: '▼';
          font-size: 1.4em;
          margin-left: 0px;
          text-align: center;
          text-shadow: $light_gray;
          bottom: -10px;
        }
      }
    }

  }
  .braintree .active-payments{
    .payment-option.loaded{
      i{
        top: 4px;
      }
      .payment-icon{
        img {
          max-height: 24px;
        }
      }
      &.paypal{
        i{top: 7px}
        &.loggedin{
          cursor: default !important;
          h5 {
            cursor: default !important;
          }
          i{
            top: 20px;
            cursor: default !important;
          }
        }
      }
      #paypal-container{
        display:inline-block;
        vertical-align: middle;
        #braintree-paypal-button{
          height: 30px !important;
          width: 78px !important;
        }
      }
    }
    .creditcard-form {
      @include container;
      position: relative;
      > div {
        z-index: 0;
        padding-bottom: $grid-gutter/4;
      }
      .card-number {
        @include span(12 of 37, $gutter: $grid-gutter/2);
      }
      .cvv {
        @include span(5 of 37, $gutter: $grid-gutter/2);
      }
      .full-name{
        @include span(10 of 37, $gutter: $grid-gutter/2);
      }
      .expiration-month {
        @include span(5 of 37, $gutter: $grid-gutter/2);
      }
      .expiration-year {
        @include span(5 of 37, $gutter: $grid-gutter/2);
        @include omega;
      }
      .billing-address {
        @include span(1 of 1, $gutter: false);
        .header{
          border-bottom: 1px solid $lightest_gray;
          padding: $grid-gutter/2 0px $grid-gutter/4;
          margin-bottom: $grid-gutter/4;
        }
        .fields {
          @include container;
          position: relative;
        }
        .street-address {
        @include span(13 of 37, $gutter: $grid-gutter/2);
        }
        .postal-code {
          @include span(5 of 37, $gutter: $grid-gutter/2);
        }
        .state {
          @include span(10 of 37, $gutter: $grid-gutter/2);
        }
        .country {
          @include span(9 of 37, $gutter: $grid-gutter/2);
          @include omega;
        }
      }
      div {
        @media #{$til-medium} {
          width: 100% !important;
        }
      }
    }
  }
  .mercadopago .active-payments{
    .doc-type{
      @include span(2 of 12, $gutter: $grid-gutter/2);
    }
    .doc-number{
      @include span(4 of 12, $gutter: $grid-gutter/2);
      @include omega;
    }
    .creditcard-form form {
      @include container;
      position: relative;
      > div {
        z-index: 0;
        padding-bottom: $grid-gutter/4;
      }
      .full-name{
        @include span(5 of 10, $gutter: $grid-gutter/2);
      }
      .card-number {
        @include span(5 of 10, $gutter: $grid-gutter/2);
      }
      .cvv {
        @include span(2 of 12, $gutter: $grid-gutter/2);
      }
      .expiration-month {
        @include span(2 of 12, $gutter: $grid-gutter/2);
      }
      .expiration-year {
        @include span(2 of 12, $gutter: $grid-gutter/2);
        @include omega;

      }
      .installments{
        @include span(1 of 1, $gutter: $grid-gutter/2);
      }

      .emaily{
        @include span(1 of 1, $gutter: $grid-gutter/2);
      }
    }
    .errors{
      .title {
        @include span(1 of 1);
        color:$light_red;
        font-weight: 800;
        padding-bottom: $grid-gutter / 4;
        border-bottom: 1px solid $light_red;
        margin-bottom: $grid-gutter / 4;
      }
      .error {
        padding-left: $grid-gutter;
        color:$dark-gray;
        text-align: left;
        font-weight: 400;
      }
    }
    .tickets-form {
      @include container;
      position: relative;
      > form > div {
        z-index: 0;
        padding-bottom: $grid-gutter/4;
      }
      .ticket-type{
        @include span(1 of 2, $gutter: $grid-gutter/2);
      }
    }
  }
  .todopago .active-payments{
    .doc-type{
      @include span(2 of 12, $gutter: $grid-gutter/2);
    }
    .doc-number{
      @include span(4 of 12, $gutter: $grid-gutter/2);
      @include omega;
    }
    .creditcard-form form {
      @include container;
      position: relative;
      > div {
        z-index: 0;
        padding-bottom: $grid-gutter/4;
      }
      .full-name{
        @include span(5 of 10, $gutter: $grid-gutter/2);
      }
      .card-number {
        @include span(4 of 12, $gutter: $grid-gutter/2);
        @include omega;
      }
      .cvv {
        @include span(4 of 12, $gutter: $grid-gutter/2);
        @include omega;

        #labelCodSegTextId {
          font-size: 0.55em !important;
        }
      }
      .expiration-month {
        @include span(4 of 12, $gutter: $grid-gutter/2);
      }
      .expiration-year {
        @include span(4 of 12, $gutter: $grid-gutter/2);
      }
      .installments{
        @include span(1 of 1, $gutter: $grid-gutter/2);
      }

      .emaily{
        @include span(1 of 1, $gutter: $grid-gutter/2);
      }

      .payment-method-bank{
        @include span(4 of 12, $gutter: $grid-gutter/2);
      }

      .payment-method-name{
        @include span(4 of 12, $gutter: $grid-gutter/2);
      }

      .select-wrapper select{
        background: transparent;
        border: none;
        width: 100%;
        font-size: 14.4px;
        height: 29px;
        padding-left: 5px;
        padding-right: 5px;
      }
    }
    .errors{
      .title {
        @include span(1 of 1);
        color:$light_red;
        font-weight: 800;
        padding-bottom: $grid-gutter / 4;
        border-bottom: 1px solid $light_red;
        margin-bottom: $grid-gutter / 4;
      }
      .error {
        padding-left: $grid-gutter;
        color:$dark-gray;
        text-align: left;
        font-weight: 400;
      }
    }
    .tickets-form {
      @include container;
      position: relative;
      > form > div {
        z-index: 0;
        padding-bottom: $grid-gutter/4;
      }
      .ticket-type{
        @include span(1 of 2, $gutter: $grid-gutter/2);
      }
    }
  }
}

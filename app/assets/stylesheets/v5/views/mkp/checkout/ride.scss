#checkout-v5-ride {
  main{
    @include outer-container;
  }
  @media #{$til-medium} {
    main, header{
      min-width: 0px;
    }
  }
  .main-container {
    @include container;
    position: relative;
    margin-bottom: $grid-gutter * 2;
    margin-top: $grid-gutter;
    .mobile & {
      margin-bottom: 10px;
    }
  }
  h4, h5 {
    text-transform: uppercase;
  }
}
.general-errors{
  z-index: 0;
  position: relative;
  margin-bottom: $grid-gutter;
  @include span(1 of 1);
  @include omega;
  .mobile & {
    @include span;
    margin-bottom: 5px;
    @media (min-width: 431px) and (max-width: 599px) {
      @include span(1 of 1);
      @include omega;
    }
    @media (min-width: 600px) {
      @include span(1 of 1);
    }
  }
  background-color: lighten($red, 40%);
  color: $red;
  text-align: center;
  border: 1px solid $light-red;
  p {
    padding: $grid-gutter/2 $grid-gutter;
  }
}
.steps {
  position: relative;
  margin-bottom: $grid-gutter;
  @include span(4 of 6);
  @media #{$before-large} {
    @include span(1 of 1);
  }
  .mobile & {
    @include span;
    margin-bottom: 5px;
    @media (min-width: 431px) and (max-width: 599px) {
      @include span(1 of 1);
      @include omega;
    }
    @media (min-width: 600px) {
      @include span(1 of 1);
    }
  }
  .shipping, .payment  {
    border: 1px solid $lightest-gray;
    margin-bottom: $grid-gutter / 2;
    @include container;
  }
}

.summary {
  position: relative;
  margin-bottom: $grid-gutter;
  background-color: $lightest-gray;
  @include span(2 of 6);
  @include omega;
  padding: $grid-gutter;
  @media #{$before-large} {
    @include span(1 of 1);
    @include omega;
  }
  .mobile & {
    @include span;
    margin-bottom: 5px;
    @media (min-width: 431px) and (max-width: 599px) {
      @include span(1 of 1);
      @include omega;
    }
    @media (min-width: 600px) {
      @include span(1 of 1);
      @include omega;
    }
  }
}

label, .label {
  font-size: 0.75em;
  line-height: 1.2em;
  color: $gray;
  margin-bottom: $grid-gutter/4;
  display: inline-block;
  &.with-errors {
    color: lighten($red, 15%);
    font-weight: 800;
  }
}

.input-wrapper, .select-wrapper {
  background-color: $lightest-gray;
  border-radius: $border-radius;
  input {
    border:0px none;
    background: transparent;
    font-size: 0.9em;
    padding: $grid-gutter/3 $grid-gutter/2;
    width: 100%;
    outline: none;
  }
  &.with-errors {
    background-color: rgba($red,0.2);
  }
}

.select-wrapper{
  a {
    font-size: 0.9em;
    outline: none;
    b{
      font-size: 1.2em;
    }
  }
  .selectize-control.single .selectize-input{
    border:0px none;
    background: transparent;
    font-size: 0.9em;
    box-shadow: none;
    padding: $grid-gutter/2.5 $grid-gutter/2;
    width: 100%;
  }
  &.with-errors{
    .selectize-control {
      background-color: transparent;
    }
  }
}

.payment-method-bank {
  .select-wrapper {
    .selectize-control {
      .selectize-input {
        .item {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 85%;
        }
      }
    }
  }
}

.selectize-control {
  background-color: $lightest-gray;
  border-radius: $border-radius;
}
.select.select-theme-default .select-content {
  max-width: 13rem;
  min-width: 10rem;
  .select-options {
    font-size: 0.9em;
    .select-option{
      &:hover, &.select-option-highlight {
        background-color: $green;
        color: #fff;
      }
    }
  }
}

.coupon {
  border: 1px solid #e2e2e2;
  margin-bottom: 10px;
  padding: 20px;

  .i-flecha-right {
    &:before {
      content: "\F12D";
    }
  }

  h4 {
    font-weight: 600!important;
    text-transform: uppercase;
  }

  form {
    background-color: #e2e2e2;
    border-radius: 2px;
    margin-top: 10px;

    input {
      border: 0 none;
      background: transparent;
      font-size: .9em;
      padding: 6.66667px 10px;
      width: 100%;
      outline: none;
    }
  }

  a.apply-coupon-button {
    cursor: pointer;
    position: relative;
    text-transform: uppercase;
    text-align: center;
    font-size: 0.7em;
    line-height: 0.9em;
    background-color: #3498DB;
    color: #fdfdfd;
    padding: 10px 0;
    border-radius: 2px;
    width: 25%;
    clear: right;
    margin-top: 10px;
    display: block;
  }

  #coupon-errors {
    display: none;
    margin-top: 10px;
    border: 1px solid #c7c7c7;
    border-radius: 2px;
    padding: 10px;
    font-size: .7em;
    color: #7d7d7d;
    width: calc(100% + 0px);
    margin-left: 0px;
    clear: right;
    margin-right: 0px;
  }

  .coupon-display {
    display: none;

    form {
      background: none;
      font-size: 0.9em;
    }

    h3 {
      color: #3B3B3B;
      font-size: 0.95em;
      font-weight: 400;

      .coupon-applied {
        color: #3B3B3B;
        font-size: 0.95em;
        font-weight: 800;
        text-transform: uppercase;
      }

      .remove-coupon {
        color: #d81e05;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
}



@import 'v5/views/partials/load_more_wide';

#checkout-v5-stompedit {
  color: $black;
  header{
    padding-top: 28px;
    @media #{$til-medium} {
      padding: 10px;
    }
  }
  main {
    @include outer-container;
  }

  .main-container {
    @include container;
    position: relative;
    margin-bottom: $grid-gutter * 2;
    margin-top: $grid-gutter;

    .continue-button, .ticket-button{
      margin-top: $grid-gutter;
      float: none;
      width: 30%;
      display: inline-block;
      cursor: pointer;
      text-transform: uppercase;
      text-align: center;
      font-weight: 800;
      font-size: 1em;
      background-color: $blue;
      color: $white;
      padding: $grid-gutter/2;
      border-radius: $border-radius;

      @media #{$before-wide} {
        width: 35%;
      }
      @media #{$before-medium} {
        width: 55%;
      }
    }
    .continue-button {
      &:before {
        margin-right: 0.5em;
      }
    }
    .ticket-button {
      margin-top: $grid-gutter/2;
      margin-bottom: .8em;
      background-color: $gray;
      &.disabled{
          cursor: default;
          background-color: $gray;
          color: $lightest-gray;
      }
    }

    .signup{
      padding: 1em;
      @include span(2 of 6);
      @include omega;
      background: $lightest-gray;
      border-radius: 0.4em;

      .mobile & {
        @media (min-width: 432px) and (max-width: 799px) {
          @include span(2 of 6);
        }

        @media (max-width: 431px) {
          margin-top: 5%;
          display: block;
          margin-right: 5px;
          @include span(3 of 6);
        }

      }
    }

    .divider {
      margin-top: 2em;
      margin-bottom: 3em;
      border-top: 1px solid $lightest-gray;
      @include span(6 of 6);
    }

    .brands{
      @include span(12 of 12);

      @media #{$before-large} {
        @include span(6 of 6);
      }
      position: relative;
      margin-bottom: $grid-gutter * 2;

      .mobile & {
        margin-bottom: $grid-gutter;
        .users-with-cover {
          margin-bottom: 0;
        }
      }

      .users-with-cover {
        margin-bottom: $grid-gutter;
      }

      @include users-with-cover-full-width;
    }
  }
}

#variants-v5-show{

  .gray-color{
    background-color: #F4F3F4;
  }
  main {
    @include outer-container;
    padding-top: $grid-gutter/2;
    .product-breadcrumb{
      margin-bottom: $grid-gutter;
    }
    article{
      position: relative;
      .product-main{
        @include container;
        .pictures{
          @include span(15 of 24);
        }
        .buybox{
          @include span(9 of 24);
          @include omega;
        }
      }
    }
  }

  @import '../../partials/mkp/breadcrumb';
  @import './partials/pictures';
  @import './partials/rate_stars';
  @import './partials/buybox';
  @import './partials/extras';
  @import './partials/related_and_others_products';

  .selectize-control.single .selectize-input{
    border: solid 1px #dedede;
    background: $white;
    border-radius: 5px;
    font-size: .9rem;
    box-shadow: none;
    padding: $grid-gutter/2 $grid-gutter/2;
    width: 100%;
    color: $gray;
  }
  .select.select-theme-default .select-content {
    min-width: 10rem;
    .select-options {
      font-size: .9rem;
      .select-option{
        &:hover, &.select-option-highlight {
          background-color: $blue;
          color: $white;
        }
      }
    }
  }
  .selectize-dropdown {
    color: $gray;
    font-size: .9rem;
    [data-selectable], .optgroup-header{
      padding: $grid-gutter/2;
    }
    .active {
      border-weight: 3px;
      border-color: $blue;
    }
  }
}

.pointer {
  cursor: pointer;
}

.product-specs-data, .product-specs-data_shipment {
  width: 100%;

  th {
    text-align: left !important;

    &:first-child {
      width: 30%;
    }
  }

  tr {
    &:nth-child(even) {
      background-color: #ffffff;
    }

    .border-grey-bottom {
      border-bottom: 1px solid #ccc;
    }

    .padding-10-left {
      padding-left: 10px;
    }
  }
}

@import './partials/buybox/size_chart';

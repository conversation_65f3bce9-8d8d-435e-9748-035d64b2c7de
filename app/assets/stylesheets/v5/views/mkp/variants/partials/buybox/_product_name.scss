.product-name {
  h1 {
    text-transform: capitalize;
    color:$darker-gray;
    font-weight: 600;
    @media #{$before-large} {
      font-size: 1.3em;
      letter-spacing: -0.3px;
      line-height: 1.3em;
    }
    @media #{$after-large} {
      font-size: 1.4em;
      letter-spacing: -0.3px;
      line-height: 1.4em;
    }
    @media #{$after-wide} {
      font-size: 1.4em;
      letter-spacing: -0.5px;
      line-height: 1.4em;
    }
  }
}
.brand-name-wrapper{
  @include horizontal-line(solid, $lightest-gray);
  text-align: left;
  margin: 3px 0px 10px 0px;
  .brand-name {
    display: inline-block;
    padding: $grid-gutter/4 $grid-gutter/1.5;
    background-color: $info;
    font-weight: 900;
    border-radius: 6px;
    color: $white;
    line-height: .8em;
    > span{
      font-size: .8rem;
      @media #{$before-large} {
        font-size: .7rem;
      }
      font-style: italic;
      color: $lightest-gray;
      padding-right: $grid-gutter/4;
    }
    a {
      font-size: .8rem;
      @media #{$before-large} {
        font-size: .7rem;
      }
      @include transition(all .15s ease-out);
      &:hover{
        color: $white;
      }
    }
  }
}

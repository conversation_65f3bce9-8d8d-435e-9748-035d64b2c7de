.pictures{
  .slider-wrapper {
    > img {
      visibility: hidden;
      height:0px;
      width:0px;
      max-width: 550px;
      max-height: 500px;
    }
  }
  @import './services';
  .services{
    @media #{$from-wide} {
      display: none;
    }
    margin-top:$grid-gutter;
    @include container;
    .service{
      @include span(1 of 2);
      &:nth-of-type(2n) {
        @include omega;
      }
      &:last-child {
        @include span(1 of 1);
        @include omega;
        .extra{
          margin-top: 0;
          display:inline-block;
        }
      }
      &:nth-of-type(1n+3) {
        display: none;
      }
      .extra, .installments-more-info{
        margin-top: $grid-gutter/4;
        display:block;
      }
      @media #{$before-large} {
        .name{
          font-size: 0.7rem;
          @include ellipsis;
        }
        .extra, .installments-more-info{
          display:none
        }
      }
    }
  }
}

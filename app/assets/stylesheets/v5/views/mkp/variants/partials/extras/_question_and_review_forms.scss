.question-form, .review-form {
  font-size: 0.9rem;
  @include container;
  margin-bottom: $grid-gutter;
  .status{
    margin-bottom: $grid-gutter/2;
    padding-bottom: $grid-gutter/2;
    font-size: 1.1rem;
    font-weight: 200;
    text-align:center;
    &.done{
      color: $green;
    }
    &.fail{
      color: $light-red;
    }
  }
  .avatar-container {
    padding-right: $grid-gutter;
    display:table-cell;
    vertical-align: top;
    .avatar{
      width:48px;
      height:48px;
      border-radius: 30px;
      cursor: default;
      line-height:0em;
    }
  }
  form {
    display:table-cell;
    width:100%;
    > .content {
      @include container;
    }
    textarea, input[type="text"], input[type="email"] {
      color:$dark-gray;
      resize: none;
      font-family: "Lato", "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
      font-size: 0.95em;
      width: 100%;
      border: 1px solid $lightest-gray;
      padding: $grid-gutter/4 $grid-gutter/2;
      border-radius:3px;
      background-color: $white;
      @include transition(all .15s ease-out);
      &.with-error{
        border-color: $light-red;
        background-color: lighten($light-red, 40%);
      }
      &:disabled{
        color: $gray;
        background-color: $lightest-gray;
        font-style:italic;
      }
    }
  }
}
.question-form{
  form > .content{
    .textarea{
      @include span(2 of 3);
    }
    .actions{
      @include span(1 of 3);
      @include omega;
      button{
        @include buttons-plain('blue', 'normal');
        width:100%;
        margin-bottom: $grid-gutter/4;
      }
      .faqs-link{
        display:block;
        text-align: center;
        font-style:italic;
        font-size: 0.8rem;
        color:$gray;
        @include transition(all .15s ease-out);
        &:hover{
          color:$blue;
        }
      }
    }
  }
}
.review-form{
  form > .content{
    .fields {
      @include span(2 of 3);
      .title{
        margin-bottom:$grid-gutter/4;
      }
    }
    .actions{
      @include span(1 of 3);
      @include omega;
      .product-rate {
        margin-bottom:$grid-gutter/2;
        text-align: center;
        .title{
          font-size: 0.8rem;
          font-style:italic;
          display: inline-block;
          margin-right:$grid-gutter/4;
        }
        .rate-stars{
          display: inline-block;
          .no-touch & .rate {
            @include transition(width .2s ease-in-out);
          }
          .star{
            cursor: pointer;
          }
        }
      }
      button{
        @include buttons-plain('blue', 'normal');
        width:100%;
      }
    }
  }
}

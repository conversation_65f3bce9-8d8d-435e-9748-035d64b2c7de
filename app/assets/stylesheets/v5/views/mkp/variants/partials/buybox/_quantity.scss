.quantity-wrapper{
  margin-top: $grid-gutter/1.5;
}
.quantity {
  display:inline-block;
  label{
    color:$gray;
    text-transform: uppercase;
    font-weight:bold;
    font-size:.85rem;
    margin-right: $grid-gutter/2;
  }
  .input {
    display:inline-block;
    position:relative;
    input {
      border: 1px solid $lighter-gray;
      border-radius: 3px 0 0 3px;
      display: inline-block;
      font-size: 1rem;
      text-align: center;
      padding: $grid-gutter/3;
      margin-bottom: 0;
      font-kerning: normal;
      font-size: .9rem;
      font-weight: 900;
      background-color:$white;
      max-width: 48px;
      line-height: 1.4rem;
    }
    .increment, .decrement {
      @include unselectable();
      font-size: 8px;
      position: absolute;
      right: -20px;
      width: 21px;
      height: 15px;
      line-height: 16px;
      font-weight: bold;
      text-align: center;
      border: 1px solid $lighter-gray;
      color:$gray;
      cursor: pointer;
      height: 19px;
      background: $white;
      @include transition(all .15s ease-out);
      &:hover {
        color: $white;
        background: $blue;
        border-color: $blue;
      }
      &:active {
      }
    }
    .increment {
      top: 0;
      border-radius: 0px 3px 0px 0px;
      border-bottom:0px none;
    }
    .decrement {
      bottom: 0;
      border-radius: 0px 0px 3px 0px;
    }
  }
}
.stock {
  display:inline-block;
  margin-left: $grid-gutter*1.5;
  vertical-align: middle;
  .msg {
    color: $light-red;
    .comment {
      color:$gray;
      font-size: 0.85rem;
      margin-left: $grid-gutter/2;
      font-style: italic;
    }
  }
}

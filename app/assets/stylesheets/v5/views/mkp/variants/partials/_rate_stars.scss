.rate-stars {
  $height: 12px;
  position: relative;
  width: ( $height * 5 ) + 20px;
  height: $height;
  background-color: $lighter-gray;
  z-index: 0;
  cursor: default;
  &.stars-1   .rate { width: 20%; }
  &.stars-1-5 .rate { width: 32%; }
  &.stars-2   .rate { width: 40%; }
  &.stars-2-5 .rate { width: 52%; }
  &.stars-3   .rate { width: 60%; }
  &.stars-3-5 .rate { width: 72%; }
  &.stars-4   .rate { width: 80%; }
  &.stars-4-5 .rate { width: 92%; }
  &.stars-5   .rate { width: 100%; }
  .rate {
    position: absolute;
    top: 0; left: 0;
    width: 0; height: $height;
    background-color: $yellow;
    z-index: 5;
  }
  .stars {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: $height;
    z-index: 10;
  }
  .star {
    @include form-reset-button;
    float: left;
    width: 20%;
    height: $height;
    background-image: image-url('mkp/variants/star.png');
    background-image: image-url('mkp/variants/star.svg'), none;
    background-size: 100% 100%;
  }
}

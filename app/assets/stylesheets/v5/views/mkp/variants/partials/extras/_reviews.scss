section.reviews {
  .content {
    .no-reviews{
      font-style:italic;
      text-align: center;
    }
  }
  .reviews-list{
    @include container;
    .review{
      @include span(1 of 2);
      margin-bottom: $grid-gutter;
      &:nth-of-type(2n){
        @include omega;
      }
      @media #{$before-large} {
        @include span(1 of 1);
        @include omega;
      }
      .reviewer{
        @include span(1 of 4);
        @media #{$before-large} {
          @include span(1 of 5);
        }
        font-size: 0.9em;
        .avatar{
          .avatar{
            width:48px;
            height:48px;
            border-radius: 30px;
            margin: 0 auto $grid-gutter/4;
          }
        }
        > span {
          display:block;
          text-align:center;
          line-height:1rem;
        }
        .date{
          color:$light-gray;
          font-style:italic;
        }
      }
      .content{
        @include span(3 of 4);
        @include omega;
        @media #{$before-large} {
          @include span(4 of 5);
          @include omega;
        }
        .title{
          .stars{
            display:inline-block;
            margin-right: $grid-gutter/2;
          }
          > span{
            font-weight: bold;
          }
        }
        .description{
          font-style:italic;
          font-size: 0.95em;
        }
        .actions{
          margin-top: $grid-gutter/4;
          padding-top: $grid-gutter/4;
          border-top: 1px dashed $lightest-gray;
          text-align: right;
          > span {
            margin-right: $grid-gutter;
            &:last-child{
              margin-right:0;
            }
            &:first-child{
              font-style: italic;
              font-size: .80rem;
              color: $light-gray;
            }
          }
          .i-flag, .i-like{
            font-size: 1.3em;
            cursor:pointer;
            color: $gray;
            @include transition(all .15s ease-out);
            &:hover{
              color: $blue;
            }
            &.active {
              color: $blue;
            }
          }
        }
      }
    }
  }
}

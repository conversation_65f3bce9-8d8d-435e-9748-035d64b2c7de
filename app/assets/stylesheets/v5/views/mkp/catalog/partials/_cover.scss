.content-cover {
  margin-top: -$grid-gutter;
  .cover {
    background: {
      size: 100% auto;
      size: cover;
      position: center center;
      repeat: no-repeat;
    }
    margin-bottom: $grid-gutter;
    height: 240px;
    @media #{$before-large} {
      height: 180px;
    }
  }
  .description{
    margin-top: -($grid-gutter/1.5);
    margin-bottom: $grid-gutter;
    color:$gray;
    font-style:italic;
    font-weight:200;
    text-align: center;
    @media #{$before-large} {
      font-size: 0.9em;
      line-height: 1.3em;
    }
    @media #{$after-large} {
      font-size: 1em;
      line-height: 1.3em;
    }
    @media #{$after-wide} {
      font-size: 1.1em;
      line-height: 1.4em;
    }
  }
}

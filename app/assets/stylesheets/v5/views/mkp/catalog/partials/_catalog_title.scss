@mixin catalog-font {
  text-transform: uppercase;
  color:$darker-gray;
  font-weight: 200;
}
.catalog-title {
  @include container;
  padding: $grid-gutter/3 0 $grid-gutter/2;
  border-bottom: 1px solid $lightest-gray;
  img {
    float: left;
    max-height: $grid-gutter * 2.5;
    max-width: $grid-gutter * 2.5;
    border-radius: $grid-gutter * 2;
    margin-right: $grid-gutter/2;
  }
  h1 {
    @include catalog-font;
    @media #{$before-large} {
      font-size: 1.9em;
      letter-spacing: -0.3px;
    }
    @media #{$after-large} {
      font-size: 2.2em;
      letter-spacing: -0.3px;
    }
    @media #{$after-wide} {
      font-size: 2.5em;
      letter-spacing: -0.5px;
    }
  }
  h2 {
    @include catalog-font;
    @media #{$before-large} {
      font-size: 1.0em;
      letter-spacing: -0.3px;
    }
    @media #{$after-large} {
      font-size: 1.2em;
      letter-spacing: -0.3px;
    }
    @media #{$after-wide} {
      font-size: 1.4em;
      letter-spacing: -0.5px;
    }
  }
  &.with-logo {
    h1 {
      line-height: 3rem;
    }
  }
}

.filter{
  .filter-option{
    h3 {
      color:$dark-gray;
      text-transform: uppercase;
      font-weight:200;
      padding: $grid-gutter*1.5 0 $grid-gutter/2;
      margin-bottom: $grid-gutter/2;
      border-bottom: 1px solid $lightest-gray;
    }
    > ul {
      > li {
        color:$dark-gray;
        line-height: 1.6em;
        font-size: .9rem;
        > a {
          &.facet-item {
            &.multi {
              word-wrap: break-word;
              &:before {
                margin: 0 $grid-gutter/4 0 1px;
                position: relative;
                content: "\f13a";
                display: inline-block;
                font-family: "icons";
                font-style: normal;
                font-weight: 400;
                font-variant: normal;
                line-height: 1.1;
                font-size: 1.1em;
                color: $light-gray;
                text-decoration: inherit;
                text-transform: none;
                text-rendering: optimizeLegibility;
                -moz-osx-font-smoothing: grayscale;
                -webkit-font-smoothing: antialiased;
                font-smoothing: antialiased;
              }
            }
          }
        }
      }
      .children {
        li {
          padding-left: $grid-gutter/2;
          color:$gray;
          font-size: .8rem;
          line-height: 1.8em;
          &.active{
            > a {
              &:hover{
                &:after {
                  top: 6px;
                }
              }
            }
          }
        }
      }
    }
    li {
      &.active{
        > a {
          font-weight: bold;
          color: $blue;
          position: relative;
          &.facet-item.multi {
            &:before {
              color: $blue;
              content: "\f139";
            }
          }
          &:hover{
            &:after {
              position: absolute;
              top: 7px;
              right: -10px;
              content: "\f133";
              color: $gray;
              font-family: "icons";
              font-style: normal;
              font-weight: 400;
              font-variant: normal;
              line-height: 1;
              font-size: 0.5em;
              text-decoration: inherit;
              text-rendering: optimizeLegibility;
              text-transform: none;
              -moz-osx-font-smoothing: grayscale;
              -webkit-font-smoothing: antialiased;
              font-smoothing: antialiased;
            }
          }
        }
      }
    }
    a {
      @include transition(all .15s ease-out);
      &:hover{
        color: $blue;
      }
    }
    .js-show-all, .js-hide-all{
      display:block;
      color:$gray;
      line-height: 1.6em;
      font-size: .8rem;
      cursor: pointer;
      &:hover{
        color: $blue;
      }
    }
    &.color{
      > ul > li {
        border-width: 1px;
        border-color: $light-gray;
        border-style: solid;
        border-radius: 2px;
        display: inline-block;
        height: 36px;
        width: 36px;
        margin-right: $grid-gutter/3;
        margin-top: $grid-gutter/3;
        vertical-align: middle;
        -webkit-box-sizing:border-box;
        -moz-box-sizing:border-box;
        box-sizing:border-box;
        cursor: pointer;
        position: relative;
        @include transition(all .15s ease-out);
        a {
          color: transparent;
          overflow: hidden;
          text-indent: 100%;
          white-space: nowrap;
          @include fill-container;
        }
        &:hover {
          border-width: 2px;
          border-color: $blue;
        }
        &.active{
          border-width: 3px;
          border-color: $blue;
        }
      }
    }
    .price_wrapper{
      @include container;
      padding: $grid-gutter/3 0;
      .input-wrapper{
        @include span(6 of 15, $gutter: $grid-gutter/3);
        background-color: $lightest-gray;
        border-radius: $border-radius;
        input{
          border:0px none;
          background: transparent;
          font-size: 0.9rem;
          padding: $grid-gutter/3 $grid-gutter/2;
          width: 100%;
          outline: none;
        }
      }
      button{
        @include span(3 of 15, $gutter: $grid-gutter/3);
        @include omega;
        border-radius: $border-radius;
        padding: $grid-gutter/3 $grid-gutter/2;
        border:0px none;
        outline: none;
        font-size: 0.9rem;
        color: $white;
        cursor: pointer;
        background-color: $lighter-gray;
        @include transition(all .1s ease-in);
        &:hover{
          background-color: $light-blue;
        }
      }
    }
  }
}

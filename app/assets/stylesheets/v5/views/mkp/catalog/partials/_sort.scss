.sort {
  color:$gray;
  font-size:0.85rem;
  .title{
    padding-right: $grid-gutter/3;
    color:$light-gray;
    @media #{$before-large} {
      display:none;
    }
  }
  .options{
    display:inline-block;
    span{
      padding-right: $grid-gutter/3;
      position: relative;
      a {
        @include transition(all .15s ease-out);
        &:hover{
          color:$blue;
        }
      }
      &:after {
        content: '-';
        font-size: 1.1rem;
        font-weight:200;
        display: inline-block;
        padding-left: $grid-gutter/3;
        color: $gray;
      }
      &:last-child{
        padding-right: 0;
        &:after {
          content: '';
        }
      }
      &.active{
        font-weight:bold;
        color: $blue;
      }
    }
  }
}

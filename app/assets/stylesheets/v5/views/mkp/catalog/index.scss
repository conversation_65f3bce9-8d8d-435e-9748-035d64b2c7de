#catalog-v5-index{
  .gray-color {
    background-color: #F4F3F4;
  }
  main {
    @include outer-container;
    padding-top: $grid-gutter/2;
    .catalog-header{
      @include span(1 of 1);
      @include omega;
    }
    aside {
      @include span(2 of 12);
      @media #{$before-large} {
        @include span(5 of 18);
      }
    }
    .catalog-content {
      @include span(10 of 12);
      @include omega;

      @include variants-list-display(3, $display-all-variants: true);
      @media #{$before-large} {
        @include span(13 of 18);
        @include omega;
        @include variants-list-display(2, $display-all-variants: true);
      }
    }
  }

  .catalog-content{
    .catalog-bottom{
      @include container;
      position: relative;
      border-top: 1px solid $lightest-gray;
      margin-top: $grid-gutter;
      .pagination-complete {
        @include span;
        @include omega;
        padding: $grid-gutter 0;
      }
    }
  }
  @import '../../partials/mkp/breadcrumb';
  @import './partials/catalog_title';
  @import './partials/sort_and_pagination';
  @import './partials/cover';
  @import './partials/filter';
  @import './partials/pagination_complete';
}

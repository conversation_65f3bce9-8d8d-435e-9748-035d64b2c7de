#profiles-v5-nonprofit_show {
  .nonprofit-social-resume {
    @include container;
    position: relative;
    margin-top: 34.5%;
    margin-bottom: $grid-gutter;
    border-top: 5px solid $gray;
    border-bottom: 1px dashed $light-gray;
    padding: $grid-gutter;
    color: $dark-gray;
    background-color: #fff;

    .col {
      @include span(1 of 3);
      border-right: 1px dashed $light-gray;

      &.third {
        @include omega;
        border-right: 0;
      }
    }

    @media #{$before-large} {
      font-size: .9em;
    }

    @media #{$after-wide} {
      .col {
        &.first {
          @include span(1 of 3);
        }
        &.second {
          @include span(1 of 3);
        }
        &.third {
          @include span(1 of 3);
          @include omega;
        }
      }
    }
  }

  .user-resume-mobile {
    .user-follow-stats {
      border-top: none;
    }
  }
}

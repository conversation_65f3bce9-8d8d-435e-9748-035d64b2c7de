.profiles-user_show {
  background-color: #F4F3F4;
  .variants-list {
    h2.manufacturer-name {
      display: none;
    }
  }

  .main-container {
    @include outer-container;
  }

  main {
    @include wide-outer-container;
    margin-bottom: $grid-gutter * 2;
  }

  .info-content {
    @include span(1 of 4);

    .mobile & {
      @include span;
    }
  }

  .main-content {
    @include span(3 of 4);
    @include omega;

    .mobile & {
      @include span;
    }
  }

  .user-subtitle {
    margin: 0 0 $grid-gutter;
    color: $gray;
    text-transform: uppercase;
    text-align: center;
    line-height: .9;

    div {
      padding-bottom: 13px;
      font-size: 80px;
      line-height: 80px;
    }

    strong {
      font-size: 30px;
      font-weight: 300;
    }
  }

  .user-main-cover {
    position: absolute;
    max-width: 100%;
    width: 100%;
    width: 100vw;
    z-index: -1;
    overflow: hidden;

    span {
      display: block;
    }

    .with-img {
      background: {
        size: 100% auto;
        size: cover;
        position: center center;
        repeat: no-repeat;
      }
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 20%;
        @include background-image(linear-gradient(rgba(#000, .45), transparent));
      }
    }

    .bg-1 {
      float: left;
      margin-top: -1px;
      width: 100%;
      width: 100vw;
      background-color: $black;
      padding-top: 40px;
      .mobile & {
        padding-top: 10px;
      }
    }

    .bg-2 {
      position: relative;
      @include outer-container;
    }

    .bg-3 {
      position: relative;
      top: -40px;
      padding-top: 28.7%;
      margin: 0 (-$grid-margin);
      .mobile & {
        top: -10px;
      }
    }

    .nonprofit-flag {
      position: absolute;
      right: $grid-margin;
      top: -40px;
      z-index: 10;
    }
  }

  .user-resume {
    position: relative;
    margin: $grid-gutter 0;
    text-align: center;
    background-color: $gray;
    .separator {
      border-top: 1px dashed $light-gray;
    }
    .avatar {
      margin: 0 auto $grid-gutter;
      max-height: 300px;
      max-width: 300px;
      width: 100%;
      .fallback {
        font-size: 5em;
        @media #{$after-wide} {
          font-size: 8em;
        }
      }
    }
    h1 {
      padding: 0 $grid-gutter $grid-gutter/2;
      font-size: 1.5em;
      font-weight: 300;
      color: #fff;
      line-height: 1.2;
      text-transform: uppercase;
      letter-spacing: -1px;
      word-wrap: break-word;
    }
    .follow-btn {
      margin-bottom: $grid-gutter;
    }
    .desc {
      font-size: .8em;
      line-height: 1.6;
      color: #fff;
      padding: $grid-gutter;
    }
    .website {
      display: inline-block;
      padding: 0 ($grid-gutter / 2);
      color: $black;
      font-size: 12px;
      line-height: 1.5;

      p {
        width: 200px;
        @include ellipsis;
      }

      &:hover {
        color: $blue;
      }
      &:last-child {
        padding-bottom: $grid-gutter;
      }
    }
  }

  .user-badges {
    display: none\9; // Hide for IE lt 9
    font-size: 34px;
    padding: $grid-gutter ($grid-gutter/ 2);

    div {
      position: relative;
      display: inline-block;
      width: 1em;
      height: 1em;
      margin: 0 .11em;
    }

    span {
      position: absolute;
      left: 0;
      top: 0;
      line-height: 1.2;
      &[class*="-bg"] {
        color: #fff;
      }
    }
  }

  .user-posts {
    .posts-list-wrapper {
      @include container;
    }

    article {
      @include span(1 of 3);
      @media #{$desktop} {
        &:nth-of-type(3n) { @include omega; }
      }
      @media #{$after-small} and #{$before-large} {
        @include span(1 of 2);
        &:nth-of-type(2n) { @include omega; }
      }
      @media #{$til-small} {
        @include span(1 of 1);
        &:nth-of-type(2n) { @include omega; }
      }
    }
  }

  aside {
    section{
      margin-bottom: $grid-gutter * 2;
      &:last-child{
        margin-bottom: $grid-gutter;
      }
    }
  }

  .user-variants {
    @include outer-container;
    padding-top:0px;

    .mobile & {
      @include container;
      margin-bottom: $grid-gutter;
      .variants-list {
        margin-bottom: 0;
        width: auto;
        overflow-x: hidden;
        overflow-y: hidden;
        white-space: nowrap;

        article{
          display: inline-block;
          vertical-align: middle;
          float: none !important;
          border-right: 0px !important;
        }
      }
    }

    @include variants-list-full-width;

    .load-more-wide {
      @include outer-container;
      padding-top: 0px;
    }
  }

  .user-featured-products {
    border: 1px solid $lighter-gray;
    background-color: #fff;
    .user-featured-products-title {
      margin: -1px -1px 10px;
      padding: 13px 20px;
      color: #fff;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 300;
      text-align: center;
      background: $dark-red;
    }
    .variants-list {
      article {
        @include variants-list-last;
      }
    }
  }

  .user-follow-stats {
    height: 6.25em;
    text-align: center;

    p {
      float: left;
      width: 50%;
      font-size: .8125em;
      line-height: normal;
    }

    strong {
      display: block;
      font-weight: normal;
      text-transform: uppercase;
      font-size: .875em;
      line-height: 2;
    }

    span {
      display: block;
      font-weight: bold;
      line-height: 1.3;
    }
  }

  .user-social-items-count {
    position: relative;
    padding-top: 1.25em;
    height: 6.25em;
    text-align: center;

    div {
      font-size: 1em;
      float: left;
      width: 50%;
    }

    span {
      display: block;
      font-size: 1.5625em;
      line-height: 1.5;
    }

    strong {
      font-size: .875em;
    }
  }

  .user-social-icons {
    text-align: center;
    a {
      line-height: 3.125em;
    }
  }

  .user-related-users {
    @include outer-container;
    padding-top:0px;
    text-align: center;

    article {
      @include span(1 of 4);
      display: inline-block;
      float: none;

      @media #{$from-large} {
        @include omega(4);
      }

      &:last-child {
        @include omega;
      }

      @media #{$before-large} {
        @include span(1 of 3);
        @include omega(3);
        float: none;
        &:nth-of-type(1n+7) {
          display: none;
        }
      }

      .mobile & {
        @include span;
        @include omega;
        float: none;
        @media #{$after-small} {
          @include span(1 of 2);
          @include omega(2);
          float: none;
        }
      }
    }
  }

  .user-resume-mobile {
    position: relative;
    max-width: 100%;
    overflow: auto;
    padding-top: 24%;

    p,
    h1,
    span {
      @include unselectable;
    }

    &.with-1 {
      .scroll-wrapper {
        @include clearfix;
        width: 100%;
      }
      .scroll-item {
        width: 100%;
      }
      .points {
        display: none;
      }
    }

    &.with-2 {
      .scroll-wrapper {
        @include clearfix;
        position: relative;
        width: 200%;
      }
      .scroll-item {
        width: 50%;
      }
    }

    .scroll-wrapper {
      @include clearfix;
    }

    .scroll-item {
      float: left;
      padding: 0 ($grid-gutter / 2);
    }

    .points {
      position: absolute;
      bottom: .8em;
      left: 0;
      width: 100%;
      text-align: center;
      span {
        display: inline-block;
        margin: 0 .15em;
        width: .75em;
        height: .75em;
        border-radius: 50%;
        background-color: $gray;
        @include transition(all .1s linear);
        &.active {
          background-color: $red;
        }
      }
    }

    .resume {
      margin-bottom: $grid-gutter / 2;
      border: 1px solid $gray;
      text-align: center;
      background-color: $gray;
      .avatar {
        float: left;
        padding-right: $grid-gutter / 2;
        max-width: 50%;
        height: 6.875em;
        &.show-fallback {
          font-size: 2em;
          .fallback {
            width: 3em;
          }
        }
        img {
          width: auto;
          max-height: 100%;
          max-width: 100%;
        }
      }
      h1, .follow-btn-wrapper {
        text-align: center;
      }
      h1 {
        padding: $grid-gutter 0 ($grid-gutter / 2) 0;
        font-size: 1em;
        font-weight: 300;
        color: #fff;
        line-height: 1;
        text-transform: uppercase;
        letter-spacing: -1px;
        word-wrap: break-word;
      }
      .follow-btn {
        margin-bottom: 1em;
        @include buttons-plain('black', 'small');
      }
      .desc {
        color: #fff;
        padding: $grid-gutter / 2;
        text-align: center;
        font-weight: 400;
      }
      .website {
        display: inline-block;
        padding: 0 ($grid-gutter / 2) ($grid-gutter / 2);
        color: $black;
        font-size: .75em;
        line-height: 1.5;
        word-wrap: break-word;
        &:hover {
          color: $blue;
        }
      }
    }

    .social-resume {
      position: relative;
      padding: .5em;
      border: 1px dashed $light-gray;
      margin-bottom: $grid-gutter;
      color: $dark-gray;
      font-size: .9em;

      .property {
        float: left;
        padding-bottom: .5em;
        width: 50%;
        text-align: center;

        &:first-child {
          padding-right: .25em;
        }

        &:last-child {
          padding-left: .25em;
        }

        span {
          display: block;
          font-size: 2em;
        }

        p {
          display: inline-block;
          font-weight: bold;
          text-transform: uppercase;
          line-height: 2em;
          vertical-align: top;
        }
      }

      .user-follow-stats {
        border-bottom: none;
        height: auto;

        p {
          width: 50%;
          text-align: center;
          float: left;
          text-transform: uppercase;
          &:first-child {
            padding-right: .25em;
          }
          &:last-child {
            padding-left: .25em;
          }
        }

        strong {
          padding-left: .315em;
        }
      }

      .user-social-icons {
        text-align: center;
        a {
          line-height: 2;
        }
      }
    }
  }
}

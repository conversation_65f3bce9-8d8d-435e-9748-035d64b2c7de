#profiles-v5-brand_show {
  .brand-social-resume {
    @include container;
    position: relative;
    margin-top: 34.5%;
    margin-bottom: $grid-gutter;
    border-top: 5px solid $gray;
    border-bottom: 1px dashed $light-gray;
    padding: $grid-gutter;
    color: $dark-gray;
    background-color: #fff;

    .col {
      @include span(1 of 3);
      border-right: 1px dashed $light-gray;
      &.third {
        @include omega;
        border-right: 0;
      }
    }

    .user-follow-stats,
    .user-social-items-count {
      height: auto;
    }

    .user-follow-stats {
      border-bottom: 1px dashed $light-gray;
    }

    @media #{$from-medium} and #{$before-wide}{
      .col{
        &.second {
          padding-right:$grid-gutter;
        }
      }
      .user-follow-stats {
        span {
          margin-bottom: .375em;
        }
      }
    }

    @media #{$before-large} {
      font-size: .9em;
    }

    @media #{$after-wide} {
      .user-follow-stats {
        display: inline-block;
        width: 60%;
        border: 0;
        border-right: 1px dashed $light-gray;
      }

      .user-social-items-count {
        display: inline-block;
        width: 40%;
      }

      .col {
        &.first {
          @include span(1 of 4);
        }
        &.second {
          @include span(2 of 4);
        }
        &.third {
          @include span(1 of 4);
          @include omega;
        }
      }
    }
  }

  .brand-properties {
    .property {
      float: left;
      display: block;
      text-align: center;
      width: 50%;

      a {
        font-size: 4.8125em;

        &.inactive:before {
          color: $light-gray;
        }
      }

      p {
        margin: .3125em 0 0;
        font-size: .6875em;
        font-weight: bold;
        text-transform: uppercase;
        word-wrap: break-word;
      }
    }
  }
}

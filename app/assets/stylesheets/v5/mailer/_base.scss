
body{
  font-size: 13px;
  width: 100% !important;
  background-color: $white;
  margin: 6px 0px;
  padding:0;
  & p, table {
    font-family: Helvetica Neue, Helvetica, Arial, Geneva, sans-serif;
  }
}

table{
  .w600 {
    width: 600px;
    border: 0px;
    #content {
      line-height: 18px;
      padding-bottom: 20px;
      padding-top: 30px;
      color: $black;
    }
  }

  .w570{
    #content{
      width: 570px;
      height: 10px
    }
  }
}

p, li {
  font-size: 13px;
  color: $darker-gray;
  margin: 0;

}

h1 {
  text-transform: uppercase;
  font-size: 18px;
  color: $black;
}

h3 {
  text-transform: uppercase;
  font-size: 14px;
  color: $gray;
}

h4{
  color: $black;
}

h5 {
  font-size: 13px;
  color: $black;
}

hr{
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 10px 0;
  padding: 0;
}

th {
  text-align: right;
  color: $gray;
}

.divide{
  width: 100%;
  border-top: 1px solid $lightest-gray;
}

a {
  &.thin, .thin {
    font-weight: 200;
  }
}
.product{
  margin-bottom: 0px;
}

.destination-address{
  margin-left: 10px
}
.w530{
  align: center;
  border-spacing: 0;
  margin-left: 30px;
  margin-top: 3px;
  width: 530;
}

.w600 { width: 600px; }
.w570 {
  width: 570px;
  font-size: 13px;
  > p {
    padding:10px 0px;
    color: $black;
  }
}
.w15 { width:15px; }

#background-table {
  padding-top: 20px;
  background-color: $lightest-gray;
  width: 100;
  border: 0;
  tr, td {
    border-collapse: collapse;
  }
}

.email-link {
    color: black;
}

table.email_item{
  margin-left: 30px;
  margin-top: 10px;
  border: 0px;
  width: 530px;
}

#top-bar{
  -webkit-font-smoothing:antialiased;
  background-color: $white;
  color: $black;
}

#header{
  background-color:$white;
  color: $black;
  img{
    margin: 20px;
  }
}

#marketplace_menu {
  background: none repeat scroll 0 0 $white;
  font-size: 10px;
  font-weight: 200;
  td{
    background: repeat scroll 0 0 $white;
    border-top: 1px solid $lightest-gray;
    border-bottom: 1px solid $lightest-gray;
    padding: 6px;
    text-align: center;
  }
  a{
    font-size: 10px;
    background-color: $white;
    color: $gray;
    font-weight: bold;
    text-decoration: none;
    text-transform: uppercase;
  }
}

#body{
  background-color: $white;
  color: $gray;
}
#content{
  padding: 20px;
}

.body-color {
  color: $gray;
  background-color: $white;
}

#footer {
  -webkit-font-smoothing:antialiased;
  background-color: $black;
  color: $lightest-gray;
  padding: 20px 0px;
  margin-bottom: 40px;
  h3 {
    font-size: 12px;
    color: $lightest-gray;
  }
  .social_icons {
    span{
      margin: 20px 10px;
      &.first{
        margin-left: 0px;
      }
      &.last{
        margin-right: 0px;
      }
    }
    img {
      height: 36px;
      width: 36px;
      -webkit-border-radius: 18px;
      -moz-border-radius: 18px;
      border-radius: 18px;
    }
  }
  .w570{
    text-align: center;
    p {
      padding:0px 0px;
      &.big{
        font-size: 1em;
        line-height: 1.5em;
        color: $light-gray;
      }
      &.contact_us{
        color: $red;
        font-weight: 800;
        font-size: 0.9em;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.8)
      }
    }
  }
}

  .buy {
    position: relative;
    text-align: center;
    margin-top: 1em;
    .button {
      width: 100%;
      padding: 0.85em 1.4em;
      &.buy-product {
        margin-top: 0.8em;
        background-color: $red;
      }
    }
  }
.footer-text{
  text-align: center;
  margin: 10px 7%;
  color: $light-gray;
  &.last{
    margin-bottom: 0px;
  }
}

.custom-button{
  text-align:center;
  margin-bottom:20px;
  span{
    margin-right:10px;
    padding:10px;
    background-color:$red;
    a{
    text-decoration:none;
    color: $white;
    }
  }
}

.shipping-own-labels{
  width: 300px;
  border: 3px solid $yellow;
  border-radius: 5px;
  background-color: #FFFAE7;
  text-align: center;
  margin: 0 auto;
  padding: 10px 20px;
  .title {
    color: $yellow;
    margin-bottom: 10px;
  }
}
.shipping-table{
  padding-left: 20px;
  font-size: 13px;
}
.order-items{
 font-size: 13px;
}

.gateway{
  font-weight: bold;
  color: #eb3d3d;
}

.order-item{
  border-collapse: collapse;
  .border-bottom {
    border-bottom:1px solid #e2e2e2;
    margin-bottom:15px;
    td{ padding:12px}
  }
}

.highlight {
  background-color: rgba(86,61,124,.15);
  .container-highlight{
    margin: 20px 0px 0px 0px;
    padding: 20px;
  }
  p {
    font-family:Helvetica Neue, Helvetica, Arial, Geneva, sans-serif;
    &.first{
    }
    &.last{
      font-size:22px;
    }
  }
}

.image-style{
  width: 30px;
}

.recommended-table{
  width: 100;
  border-collapse: collapse;
}

.cell-width{
  width: 15px;
  vertical-align: top
}

.dot-style{
  border-top: 2px dotted #ddd;
}

.product-image{
  max-width: 120px;
  max-height: 140px;
}

.paragraph-styling{
  padding-top: 12px;
  padding-left: 8px;
  font-size: 16px;
  font-weight: 500;
  &.paragraph-color{
    color: #D81E05;
  }
}

.paragraph-variants{
  font-size: 12px;
  padding-bottom: 3px;
}

.selecting-paragraph{
  padding-bottom: 16px;
  font-size: 15.3px;
}

.h1-style{
  padding-bottom: 9px;
  margin-bottom: 0;
  color: black
}

.w560b{
  align: center;
  border: 0px;
  width: 560px;
  margin: 0 auto;
  margin-top: 13px;
  border-collapse: separate;
  border-spacing: 0 0em;

}


.more-attributes{
  padding-top: 16px;
  width: 25%;
  &.common-style{
    text-align: center;
    width: 25%;
  }
}

.common-style{
    text-align: center;
    width: 25%;
  }

.row-styling{
  width: 25%;
  align: center;
  text-align: center
}

.anchor-styling{
  max-width:120px;
  max-height: 140px;
}



.hide,
.hidden { display: none !important; }
.clear { clear: both !important; }

@mixin unselectable {
  @include user-select(none);
  -webkit-user-drag: none;
  user-drag: none;
  cursor: default;
}

@mixin ellipsis {
  padding-right: .4em;
  white-space: nowrap;
  overflow: hidden !important;
  @include prefixer(text-overflow, ellipsis, o ms spec);
  -moz-binding: url('/xml/ellipsis.xml#ellipsis');
}

@mixin multiline-ellipsis($font-size: 12px,
                          $line-height: 1.6,
                          $lines-to-show: 5) {
  display: block; // Fallback for non-webkit
  display: -webkit-box;
  height: $font-size * $line-height * $lines-to-show; // Fallback for non-webkit
  font-size: $font-size;
  line-height: $line-height;
  -webkit-line-clamp: $lines-to-show;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin with-middle-text {
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
}

@mixin hide-for-seo {
  position: absolute;
  right: 100%;
  top: 100%;
  height: 0;
  width: 0;
  margin: 0;
  padding: 0;
  font-size: 0;
  color: transparent;
  overflow: hidden;
  @include opacity(0);
}

@mixin opacity($value: 1) {
  opacity: $value;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity='$value * 100')';
  filter: alpha(opacity= $value * 100);
  zoom: 1;
}

@mixin cover-all {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('about:blank')\9; // IE6-8 :/
}

@mixin fade-in-on-fonts-loaded {
  opacity: 0;

  .fonts-loaded & {
    opacity: 1;
  }
}

@mixin fill-container{
  position: absolute;
  left: 0; top: 0;
  width: 100%; height: 100%;
}

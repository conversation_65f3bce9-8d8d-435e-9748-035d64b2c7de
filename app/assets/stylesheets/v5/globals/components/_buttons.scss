// Applies plain button styles to an element.
//
// $size  ['normal'|'small'] | 'normal' - Size
// $color ['black'] | 'black'           - Color
//
// Styleguide 1.1.1.
@mixin buttons-plain-disabled {
  opacity: 0.5;
  color: $dark-gray;
  background-color: $light-gray;
  cursor: default;
  &:hover {
    opacity: 0.5;
    color: $dark-gray;
    background-color: $light-gray;
  }
}

@mixin buttons-plain($color, $size) {
  display: inline-block;
  position: relative;
  margin: 0;
  border: 1px solid transparent;
  font-size: .75em;
  font-weight: 900;
  line-height: normal;
  cursor: pointer;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 3px;
  font-family: inherit;
  font-kerning: normal;
  @include transition(all .1s linear);


  @if $size == 'normal' {
    padding: 1em 1.85em .9em;
  }

  @if $size == 'small' {
    padding: .7em .8em .65em;
  }


  @if $color == 'black' {
    color: #fff;
    background-color: $black;
    &:hover {
      background-color: #000;
    }
  }

  @if $color == 'gray' {
    color: $dark-gray;
    background-color: $light-gray;
    &:hover {
      color: $white;
      background-color: $gray;
    }
  }

  @if $color == 'white' {
    color: $gray;
    border: 1px solid $lighter-gray;
    background-color: #fff;
    &:hover {
      color: $white;
      border: 1px solid $gray;
      background-color: $gray;
    }
  }

  @if $color == 'red' {
    color: #fff;
    background-color: $red;
    &:hover {
      background-color: $dark-red;
    }
  }

  @if $color == 'green' {
    color: #fff;
    background-color: $green;
    &:hover {
      background-color: $light-green;
    }
  }

  @if $color == 'light-green' {
    border: 1px solid $light-green;
    color: $light-green;
    background-color: #fff;
    &:hover {
      color: #fff;
      border-color: $lighter-green;
      background-color: $lighter-green;
    }
  }

  @if $color == 'white-green' {
    color: $green;
    background-color: #fff;
    &:hover {
      color: #fff;
      background-color: $lighter-green;
    }
  }

  @if $color == 'blue' {
    color: $white;
    background-color: $blue;
    &:hover {
      background-color: $light-blue;
    }
  }

  @if $color == 'light-blue' {
    border: 1px solid $light-blue;
    color: $light-blue;
    background-color: #fff;
    &:hover {
      color: #fff;
      border-color: $light-blue;
      background-color: $light-blue;
    }
  }
    @if $color == 'white-blue' {
    border: 1px solid #5CAADE;
    color: $white;
    background-color: #5CAADE;
    &:hover {
      color: #fff;
      border-color: $light-blue;
      background-color: $light-blue;
    }
  }

  @if $color == 'facebook' {
    color: #fff;
    background-color: $facebook;
    &:hover {
      background-color: darken($facebook, 15%);
    }
  }

  @if $color == 'light-facebook' {
    color: $facebook;
    border: 1px solid $facebook;
    background-color: #fff;
    &:hover {
      color: darken($facebook, 15%);
      border: 1px solid darken($facebook, 15%);
    }
  }

  @if $color == 'twitter' {
    color: #fff;
    background-color: $twitter;
    &:hover {
      background-color: darken($twitter, 15%);
    }
  }

  @if $color == 'light-twitter' {
    color: $twitter;
    border: 1px solid $twitter;
    background-color: #fff;
    &:hover {
      color: darken($twitter, 15%);
      border: 1px solid darken($twitter, 15%);
    }
  }
  &.disabled, &[disabled] {
    @include buttons-plain-disabled
  }
}

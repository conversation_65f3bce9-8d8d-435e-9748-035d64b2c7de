@mixin articles-list-last {
  border-right: 0;
}

@mixin articles-list-display($nbr-per-row){
  .articles-list {
    width: 300% !important;
    padding: {
      left: $grid-gutter/2;
      right: $grid-gutter/2;
    }
    article {
      .mobile & {
        @include span(1 of $nbr-per-row, $gutter: $grid-gutter);
      }
    }
  }
}

@mixin articles-list-full-width {
  @include articles-list-display(5) ;
}

@mixin variants-list-last {
  border-right: 0;
}

@mixin variants-list-display($nbr-per-row, $should-reponse-til-large: false, $display-all-variants: false){
  .variants-list {
    article {
      @include span(1 of $nbr-per-row, $gutter: false);
      @if $should-reponse-til-large {
        @media #{$til-large} {
          $nbr-per-row-til-large: $nbr-per-row;
          @if $nbr-per-row > 2 {
            $nbr-per-row-til-large: $nbr-per-row - 1;
            &:nth-of-type(#{$nbr-per-row}n) {
              display: none;
            }
          }
          @include span(1 of $nbr-per-row-til-large, $gutter: false);
          &:nth-of-type(#{$nbr-per-row}n+#{$nbr-per-row-til-large}) {
            @include variants-list-last;
          }
        }
        @media #{$after-large} {
           &:nth-of-type(#{$nbr-per-row}n) {
            @include variants-list-last;
          }
        }
      } @else {
        &:nth-of-type(#{$nbr-per-row}n) {
          @include variants-list-last;
        }
      }
      .mobile & {
        $nbr-per-row-mobile: $nbr-per-row;
        @if $display-all-variants == false {
          @if $nbr-per-row % 2 == 0 {
            $nbr-per-row-mobile:$nbr-per-row ;
          } @else {
            $nbr-per-row-mobile: ceil($nbr-per-row/2);
            &:nth-of-type(1n+#{$nbr-per-row}n) {
              display: none;
            }
          }
        } @else {
          $nbr-per-row-mobile: 8;
        }
        @include span(1 of $nbr-per-row-mobile, $gutter: false);
        &:nth-of-type(#{$nbr-per-row-mobile}n) {
          @include variants-list-last;
        }
      }
    }
  }
}

@mixin variants-list-full-width {
  @include variants-list-display(4, $display-all-variants: true) ;
}

@mixin variants-list-half-width {
  @include variants-list-display(2);
}

.mobile-remove-border {
  border: none !important;
}

.sort-mobile {
  margin-top: 12px;
  margin-bottom: 7.5px;

  button.mobile-button {
    background: #FFFFFF;
    box-shadow: 0 0 2px 0 rgba(0,0,0,0.50);
    border: none;
    border-radius: 2px;
    padding: 4px 8px;

    &:focus {
      border: none;
      outline: 0;
    }

    img {
      vertical-align: middle;
      margin-right: 5px;
    }
  }
}

.options-mobile {
  padding: 10px 20px;
  background: white;
  border: 1px solid #D9D9D9;
  display: inline-block;
  position: absolute;
  z-index: 28;

  .option-mobile {
    font-family: Lato;
    font-size: 14px;
    line-height: 36px;

    &.active {
      color: #3498DB;
    }
  }
}

.catalog-card-mobile {
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  box-shadow: 0 0 2px 0 rgba(147,147,147,0.50);
  padding: 5px !important;
  text-align: left !important;
  margin-bottom: 10px !important;
  width: 100% !important;

  .background-image-mobile {
    display: block;
    float: left;
    width: 110px;
    height: 90px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;

    .on-sale-mobile {
      background-color: #EE2E3B;
      padding: 2px 8px;
      font-size: 12px;
      font-style: normal;
      color: #FFF;
      border-radius: 2px;
    }

    @media screen and (max-width: 355px) {
      height: 120px;
    }
  }

  .product-info-mobile {
    float: left;
    width: calc(100% - 115px);
    margin-left: 5px;

    span.h2 {
      display: block;
      line-height: 22px;
      white-space: normal;
      overflow: inherit !important;
      text-overflow: inherit !important;

      a {
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        color: #000000;
      }
    }

    span.h3 {
      display: block;
      border-bottom: 1px solid #d9d9d9;
      padding-bottom: 5px;

      a {
        font-style: normal;
        font-weight:  300;
        font-size: 16px;
        color: #000000;
      }
    }

    .prices-container-mobile {
      display: inline-block;
      margin-top: 25px;

      .prices-mobile {
        font-family: Lato;
        font-style: normal;
        color: #3498DB;
        font-size: 21px;
        position: absolute;
        bottom: 5px;
        right: 5px;

        .sale-price-mobile {
          margin-left: 10px;
        }
      }
    }
  }

  &.on-sale {
    .product-info-mobile {
      .prices-mobile {
        .price-mobile {
          font-size: 14px !important;
          position: relative;

          &:before {
            position: absolute;
            content: "";
            left: 0;
            top: 50%;
            right: 0;
            border-top: 1px solid;
            border-color: inherit;
            -webkit-transform:rotate(-12deg);
            -moz-transform:rotate(-12deg);
            -ms-transform:rotate(-12deg);
            -o-transform:rotate(-12deg);
            transform:rotate(-12deg);
          }
        }
      }
    }
  }
}

.catalog-content--mobile {
  margin-left: -10px !important;
  margin-right: -10px !important;
  background: #f8f8f8;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: content-box;
}
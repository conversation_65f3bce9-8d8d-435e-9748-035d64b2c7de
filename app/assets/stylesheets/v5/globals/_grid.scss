//==============================================================================
// Screen Sizes
// to handle grids and breakpoints samelessly.
//==============================================================================

$small-screen: 320px;
$smallish-screen: 480px;
$medium-screen: 768px;
$large-screen: 1024px;
$wide-screen: 1280px;

//==============================================================================
// Media Queries
// e.g.: @media #{$from-small} { Awesome css for mobile }
//==============================================================================

// Breakpoints per screen
$til-small: "(max-width: #{$small-screen})";
$from-small: "(min-width: #{$small-screen})";
$before-small: "(max-width: #{$small-screen - 1})";
$after-small: "(min-width: #{$small-screen + 1})";

$til-smallish: "(max-width: #{$smallish-screen})";
$from-smallish: "(min-width: #{$smallish-screen})";
$before-smallish: "(max-width: #{$smallish-screen - 1})";
$after-smallish: "(min-width: #{$smallish-screen + 1})";

$til-medium: "(max-width: #{$medium-screen})";
$from-medium: "(min-width: #{$medium-screen})";
$before-medium: "(max-width: #{$medium-screen - 1})";
$after-medium: "(min-width: #{$medium-screen + 1})";

$til-large: "(max-width: #{$large-screen})";
$from-large: "(min-width: #{$large-screen})";
$before-large: "(max-width: #{$large-screen - 1})";
$after-large: "(min-width: #{$large-screen + 1})";

$til-wide: "(max-width: #{$wide-screen})";
$from-wide: "(min-width: #{$wide-screen})";
$before-wide: "(max-width: #{$wide-screen - 1})";
$after-wide: "(min-width: #{$wide-screen + 1})";

// Breakpoint for retina displays: http://miekd.com/articles/using-css-sprites-to-optimize-your-website-for-retina-displays/
$retina: 'only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-device-pixel-ratio: 2)';

// Breakpoints by device
$mobile: #{$before-medium};
$tablet: #{$from-medium} and #{$before-large};
$desktop: #{$from-large};

$small-mobile: #{$til-small};
$big-mobile: #{$after-small} and #{$before-medium};
$small-desktop: #{$from-large} and #{$til-wide};
$big-desktop: #{$after-wide};


//==============================================================================
// The Grid
//==============================================================================

// Settings
$grid-gutter: 20px;
$grid-margin: 10px;

// Mixins
@mixin container {
  @include b-container;
}

@mixin wide-outer-container {
  @include container;
  @media screen {
    min-width: $medium-screen;
    .mobile & {
      min-width: 0;
      max-width: $medium-screen - 1;
    }
  }
}

@mixin outer-container {
  @include container;
  margin-left: auto;
  margin-right: auto;
  padding-left: $grid-margin;
  padding-right: $grid-margin;
  padding-top: $grid-gutter * 2;
  width: $large-screen;

  .mobile & {
    margin: {
      left: 0;
      right: 0;
    }
  }

  @media screen {
    width: auto;
    min-width: $medium-screen;
    max-width: $large-screen;
    .mobile & {
      min-width: 0;
      max-width: $medium-screen - 1;
    }
  }

  @media #{$big-desktop} {
    max-width: $wide-screen;
    .mobile & {
      max-width: $medium-screen - 1;
    }
  }
}

// Override of B grid with default GP fallbacks
@mixin span($span: 1 of 1,
            $gutter: $grid-gutter,
            $width: 100%,
            $direction: ltr,
            $fallback-width: 100%,
            $fallback-gutter: 5%) {

  $amount: nth($span, 1);
  $columns: nth($span, 3);

  @include b-span($span: $amount of $columns,
                  $gutter: $gutter,
                  $width: $width,
                  $direction: $direction,
                  $fallback-width: $fallback-width,
                  $fallback-gutter: $fallback-gutter);
}

@mixin omega($every: false) {
  @if $every {
    &:nth-of-type(#{$every}n) {
      @include b-omega;
    }
  } @else {
    @include b-omega;
  }
}

//==============================================================================
// Grid's Html, for global non-semantic usage
//==============================================================================

.cf {
  @include clearfix;
}

@import 'globals/colors';
@import 'v5/globals/colors';
@import 'v5/globals/helpers';
@import 'social/globals/variables';
@import 'globals/base_variables';

// Libs
@import 'reset';
@import 'foundation/components/global';
@import 'foundation/components/type';
@import 'foundation/components/grid';
@import 'bourbon';

// Foundation Overrides and GP Components
@import 'components/global';
@import 'components/block-grid';
@import 'components/buttons';

// Globals
@import 'globals/layout';

// Partials
@import 'partials/hexagon';
@import 'v5/views/partials/social/recommended_product';
@import 'variant_list';

#widgets-shop_products {
  .products-list {
    padding: 0 0.3em;
  }
  .variants-list-module {
    .variant {
      margin-right: 0 !important;
      margin-bottom: 1.6em;
      width: 47%;
      @media (min-width: 0) and (max-width: 299px) {
        width: 100%;
      }
      @media (min-width: 450px) and #{$before-medium} {
        width: 30%;
      }
      @media #{$from-medium} and #{$til-large} {
        width: 23%;
      }
      @media #{$from-large} {
        width: 19%;
      }
    }
  }
}

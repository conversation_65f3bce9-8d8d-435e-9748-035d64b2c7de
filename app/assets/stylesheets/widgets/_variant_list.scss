.variants-list-module {
  @extend .cf;
  @include block-grid-container;
  .variant {
    @include block-grid-item;
    width: 100%;
    @media #{$after-small} {
      width: 47%;
      margin-bottom: 1.5em;
    }
    @media #{$from-medium} {
      width: 30%;
      @include block-grid-item-last-odd-on-3-columns {
        margin-right: 35%;
      }
    }
    @media #{$til-small} {
      &:last-child{
        .installments {
          border-bottom: 0px none;
        }
      }
    }
    position: relative;
    margin-bottom: 1em;
    .ranking{
      position: absolute;
      background-color: $white;
      right:0;
      top: 0;
      padding:5px;
      font-size:.9em;
      z-index: 1000;
      .explain{
        color: $gray;
        font-size: .9em;
      }
    }
    &.on-sale {
      .flag {
        position: absolute;
        top: -5px;
        left: -5px;
        width: 69px;
        height: 68px;
        background-image: image-url('mkp/variants/bg_flag_sale.png');
        z-index: 3;
        pointer-events: none;
      }
      .percent-off{
        position: absolute;
        top: 15px;
        right: 0px;
        width: 65px;
        height: 28px;
        z-index: 2;
        background-color: $negro;
        color: #fff;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
        font-weight: 600;
        font-size: emCalc(14px);
        line-height: 2em;
        text-align: left;
      }
      .regular-price {
        position: absolute;
        font-weight: 400;
        font-size: emCalc(12px);
        right: 0.85em;
        top: 1.8em;
        &:after {
          background-color: $gris-oscuro;
          content: "";
          height: 1px;
          position: absolute;
          right: -5px;
          top: 50%;
          transform: rotate(-10deg);
          width: 70%;
        }
      }
    }
    .thumb {
      position: relative;
      padding-top: 80%;
      height: auto;
      border-radius: 3px 3px 0 0;
      @media #{$after-small} {
        border-bottom: 1px solid #ddd;
      }
      background-color: white;
      background-size: auto 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center center;
      a { @extend .full-link; }
    }
    .product-data{
      position:relative;
      .prices{
        position: absolute;
        top: -2.4em;
        right: 4px;
        padding: 4px 10px 4px 10px;
        font-weight: 600;
        font-size: emCalc(16px);
        z-index: 1;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
        @include gp-fake-button($red);
        &.on-sale {
          .price {
            position: relative;
            color: #ccc;
            &:after {
              content: '';
              position: absolute;
              width: 85%;
              height: 2px;
              top: 50%;
              right: 5%;
              background-color: #ED0505;
              @include transform(rotate(-15deg));
            }
          }
        }
      }
    }
    .product-title-wrapper, .free-shipping, .installments {
      margin: 3px 0 6px 9px;
    }
    .product-title {
      line-height: 24px;
      font-weight: bold;
      font-size: emCalc(14px);
      text-align: left;
      @extend .ellipsis;
      a {
        color:#333;
      }
    }

    .free-shipping, .installments {
      line-height: 13px;
      font-size: emCalc(12px);
      left: 9px;
      @extend .ellipsis;
      a {
        color:$green;
      }
    }

    .installments {
      font-weight: bold;
      @media #{$til-small} {
        border-bottom: 1px solid #ddd;
        padding: 0 0 10px 0;
        width: 95%;
      }
    }

    .mfr {
      font-size: emCalc(12px);
      position: relative;
      max-width: 80%;
      span{
        @extend .ellipsis;
        display: inline-block;
        max-width: 90%;
        a {
          font-weight: bold;
          color: #333;
        }
      }
    }
    .mfr-thumb {
      position: absolute;
      top: 10px;
      left: 10px;
      @include square(38px);
      background-color: white;
    }
  }
}

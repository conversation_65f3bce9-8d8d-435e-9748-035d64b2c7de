.header {
  position: relative;
  padding: 26px 0;
  background-size: auto 100%;
  background-position: center top;
  background-image: image-url('distribution/bg-header-dia-nino.jpg');
  // @media #{$retina} {
  //   background-image: image-url('distribution/<EMAIL>');
  // }
  .logo {
    margin-bottom: 10px;
  }
  .title {
    margin-bottom: 15px;
    font-size: 22px;
    text-transform: uppercase;
    font-weight: bold;
    .title-bg {
      padding: 1px 6px;
      background-color: white;
    }
    .title-clipped {
      color: rgb(90,90,80);
      -webkit-text-fill-color: transparent;
      background: -webkit-linear-gradient(transparent, transparent),
                  image-url('distribution/bg-headerwow.jpg') left center;
      background: -o-linear-gradient(transparent, transparent);
      -webkit-background-clip: text;
    }
  }
  .desc {
    margin-bottom: 10px;
    max-width: 336px;
    color: white;
    font-size: 18px;
    line-height: 1.2;
    letter-spacing: 1px;
    font-weight: lighter;
  }
  .contacto {
    margin: 0;
    font-size: 16px;
    font-style: italic;
  }
  .subscribe {
    position: relative;
    float: right;
    max-width: 100%;
    margin-bottom: 15px;
  }
  .subscribe-form {
    position: relative;
    float: left;
    width: 315px;
    max-width: 80%;
    @include calc(max-width, "100% - 57px");
    input {
      margin: 0;
      border: none;
      width: 100%;
      box-shadow: none;
      line-height: 1.5;
      padding: 5px 46px 5px 10px;
      font-size: 16px;
    }
    .button {
      position: absolute;
      top: 4px;
      right: 4px;
      padding: 4px 10px;
      color: white;
      font-weight: lighter;
      text-transform: uppercase;
      background-color: black;
      &:hover, &:focus {
        background-color: lighten(black, 30%);
      }
    }
  }
  .facebook {
    display: block;
    float: right;
    margin: 0 0 0 7px;
    padding: 0;
    height: 34px;
    width: 50px;
    background-size: auto 90%;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: image-url('distribution/facebook.svg');
  }
}

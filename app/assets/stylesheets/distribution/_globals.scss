// Globals

// Media Query for retina displays: http://miekd.com/articles/using-css-sprites-to-optimize-your-website-for-retina-displays/
$retina: "only screen and (-webkit-min-device-pixel-ratio: 2), screen and (max--moz-device-pixel-ratio: 2)";

@mixin calc($property, $expression) {
  #{$property}: -webkit-calc(#{$expression});
  #{$property}: calc(#{$expression});
}

// Foundation Overrides
.button {
  background-color: darken($rojo, 10%);
  &:hover, &:focus {
    background-color: darken($rojo, 20%);
  }
}


// Layout
body {
  background-color: #F7F7F7;
}
.pictures-uploader {
  .pictures-uploader-items {
    position: relative;
    overflow: auto;
    .pictures-uploader-item {
      position: relative;
      float: left;
      border: 1px solid $gris-claro;

      // Mobile First
      margin-bottom: 5%;
      width: 100%;

      @media #{$after-small} {
        margin-left: 1%;
        margin-right: 1%;
        margin-bottom: 3%;
        &:nth-child(2n+1) {
          margin-left: 0;
          margin-right: 2%;
        }
        &:nth-child(2n) {
          margin-right: 0;
          margin-left: 2%;
        }
        width: 48%;
      }

      @media #{$from-medium} {
        margin-bottom: 2%;
        // Margin fallbacks
        margin-left: 1%;
        margin-right: 1%;
        // Left and right columns
        &:nth-child(n) {
          margin-left: 0;
          margin-right: 0;
        }
        // Middle colum
        &:nth-child(3n+2) {
          margin-left: 2%;
          margin-right: 2%;
        }
        width: 32%;
      }

      &.loading {
        .pictures-uploader-item-thumb {
          @include opacity(0);
        }
        .pictures-uploader-item-thumb-background,
        .pictures-uploader-item-progress {
          @include opacity(1);
        }
      }
      .pictures-uploader-item-thumb {
        padding-top: 68%;
        border-bottom: 1px solid $gris-claro;
        background-color: white;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        @include transition(opacity .5s ease-in-out);
      }
      .pictures-uploader-item-progress,
      .pictures-uploader-item-thumb-background {
        @include opacity(0);
        @include transition(opacity .5s ease-in-out);
      }
      .pictures-uploader-item-thumb-background {
        position: absolute;
        left: 0;
        top: 0;
        padding-top: 68%;
        width: 100%;
        border-bottom: 1px solid $gris-claro;
        background-color: white;
      }
      .pictures-uploader-item-progress {
        position: absolute;
        top: 38%;
        left: 28%;
        width: 44%;
        height: 11px;
        background-color: #B9B9B9;
        .pictures-uploader-item-progress-bar {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          background-color: #858585;
        }
      }
      .pictures-uploader-item-description {
        margin: 0;
        border-radius: 0;
        border: none;
        border-bottom: 1px solid $gris-claro;
        padding: 0.48em;
        height: auto;
        box-shadow: none;
      }
      .pictures-uploader-item-name {
        @include unselectable;
        cursor: default;
        font-weight: bold;
        color: lighten($gris, 10%);
        text-align: right;
        line-height: 30px;
        font-size: 12px;
        max-width: 100%;
      }
      .pictures-uploader-item-cancel {
        @include reset-button;
        float: left;
        position: relative;
        display: block;
        height: 30px;
        width: 30px;
        border-right: 1px solid $gris-claro;
        cursor: pointer;
        &:hover:after {
          background-position: left -13px;
        }
        &:active:after {
          background-position: left -26px;
        }
        &:after {
          content: '';
          position: absolute;
          left: 50%; top: 50%;
          margin: -7px 0 0 -5px;
          width: 12px; height: 13px;
          background-position: left top;
          background-repeat: no-repeat;
          background-image: image-url('social/whatsups_form/small-x-sprite.png');
        }
      }
      &.add-photos {
        cursor: pointer;
        border-style: dashed;
        &:hover {
          background-color: #eee;
        }
        .add-photos-selector {
          padding-top: 68%;
          padding-bottom: 30px;
        }
        .add-photos-text {
          position: absolute;
          top: 47%;
          left: 0;
          width: 100%;
          text-align: center;
        }
        .pictures-uploader-item-description {
          visibility: hidden;
        }
      }
    }
  }
}
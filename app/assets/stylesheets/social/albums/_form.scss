.albums-form  {
  @extend .gray-modal;
  position: fixed;
  top: 0;
  height: 100%;
  overflow: hidden;
  @media (min-width: 550px) {
    @include reveal-modal-base(false, 98%);
  }
  @media (min-width: 960px) {
    @include reveal-modal-base(false, 960px);
  }
  form {
    display: inline;
    position: static;
  }
  .album-inputs {
    padding: 0.7em 0.7em 0.7em 1.6em;
    label {
      display: block;
      padding: 0 0.7em 0 0;
      margin: 0;
      line-height: 2em;
    }
    #social_album_title {
      width: 100%;
      margin: 0;
      padding-top: 0.4em;
      padding-bottom: 0.4em;
      height: auto;
    }
    #social_album_sport_ids {
      max-height: 30px;
      width: 100%;
      margin-bottom: 0.5em;
    }
    .select2-choices {
      border-radius: 3px;
      border-top-color: #ccc;
    }
    @media #{$before-medium} {
      padding: 0.7em;
      label {
        float: none !important;
        text-align: left;
      }
    }
  }
  .gray-modal-top {
    .title {
      border-bottom: 1px solid #e2e2e2;
    }
  }
  .gray-modal-content {
    height: 100%;
    overflow: hidden;
    padding-bottom: 8.1em;
    .media-wrapper {
      max-height: 100%;
      overflow: auto;
    }
    .button {
      font-weight: bold;
      &.add-photos {
        float: right;
      }
    }
  }
  .gray-modal-bottom {
    position: absolute;
    width: 100%;
    bottom: 0;
  }
}
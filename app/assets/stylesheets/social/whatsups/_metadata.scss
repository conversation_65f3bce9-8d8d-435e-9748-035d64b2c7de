.metadata {
  position: relative;
  padding: emCalc(6px);
  @extend .clearfix;
  background-color: #ffffff;
  a:not([href]) {
    color: $gris !important;
    text-decoration: none !important;
    cursor: default !important;
  }
  .close {
    position: absolute;
    right: 6px;
    top: 6px;
    cursor: pointer;
    font-size: 13px;
    line-height: 13px;
    text-align: center;
    font-weight: bold;
    width: 13px;
    height: 14px;
    border-radius: 2px;
    &:hover {
      color: $blanco;
      background-color: $negro;
    }
  }
  .content-thumb {
    position: relative;
    width: 100%;
    text-align: center;
    img {
      max-width: 100%;
      max-height: 100%;
      height: auto;
    }
    .play-icon {
      position: absolute;
      bottom:  18px;
      left: 18px;
      width: 85px;
      height: 59px;
      background-image: image-url('social/feed_item/play-large.png');
    }
  }
  .content-data {
    padding: emCalc(6px) 0;
    width: 100%;
    .content-title {
      margin: 0 0 emCalc(6px);
      a[href*="goodpeople.com/"] {
        cursor: pointer;
        color: lighten($rojo, 13%);
      }
    }
    .content-url {
      margin: 0 0 emCalc(12px);
      word-wrap: break-word;
      line-height: 1.2em;
      a {
        color: darken($gris-claro, 13%);
      }
    }
    .content-description {
      margin: 0;
    }
  }
  &.instagram {
    padding: 0;
    background-color: transparent;
    .content-thumb {
      width: 100%;
    }
  }
  &.with-thumb {
    @media (min-width: 460px) {
      .content-thumb {
        float: left;
        width: 37%;
        .play-icon {
          bottom: 10px;
          left: 10px;
          width: 51px;
          height: 37px;
          background-image: image-url('social/feed_item/play-small.png');
        }
      }
      .content-data {
        float: right;
        width: 60%;
        &.no-thumb {
          width: 100%;
        }
      }
    }
  }
}

.embeded-video {
  position: relative;
  margin-bottom: 0.45em;
  padding: 0em;
  background-color: transparent;

  img {
    max-width: 100%;
  }

  .video-title {
    margin: 0 0 0.375em;
    a {
      color: lighten($rojo, 13%);
    }
  }

  .video-url {
    margin: 0 0 0.75em;
    line-height: 1.2em;
    word-wrap: break-word;
    a {
      color: darken($gris-claro, 20%);
    }
  }

  .thumb {
    height: 12em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .video-play-icon {
    position: absolute;
    bottom: 1em;
    left: 1em;
    width: 65px;
    height: 45px;
    background-image: image-url('social/feed_item/play-medium.png');
  }
}

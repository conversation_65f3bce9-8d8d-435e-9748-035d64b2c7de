.whatsups-form {
  @extend .gray-modal;
  .gray-modal-content {
    padding-bottom: 0;
    .media-wrapper {
      @extend textarea;
      margin-bottom: 0em;
      height: auto;
      border-bottom: none;
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0px;
    }
    #social_whatsup_body {
      margin: 0;
      padding: 0;
      border: 0;
      box-shadow: none;
      background-color: transparent;

      margin-bottom: 1em;
      height: 3.5em;
      font-size: 1em;
      line-height: emCalc(30px);
      text-indent: 2em;
      background-repeat: no-repeat;
      background-position: 1px 2px;
      background-image: image-url('social/whatsups_form/icon-pencil.png');
    }
    .sport-selector {
      margin-bottom: 1em;
      &.without-select2 {
        label {
          display: block;
          margin-top: 0.5em;
        }
        #social_whatsup_sport_ids {
          margin: 0;
          width: 100%;
        }
      }
    }
    .add-img, .create-album {
      position: relative;
      display: inline-block;
      font-weight: bold;
      width: 100%;
      margin-bottom: 1em;
      text-align: center;
      @media (min-width: 550px) {
        width: 48%;
        &.create-album {
          float: right;
        }
      }
    }
    .metadata-container {
      position: relative;
      &.loading {
        min-height: 25px;
        &:after {
          position: absolute;
          top: 2%;
          right: 45%;
          content: '...';
        }
      }
    }
  }
}

.whatsups-form-select2-container {
  .select2-choices,
  &.select2-container-active .select2-choices {
    padding-left: 30px;
    border: 1px solid $input-border-color;
    border-top-color: lighten($input-border-color, 9%);
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    box-shadow: none;
    background: white image-url('social/whatsups_form/icon-tag.png') 10px 7px no-repeat;
  }
  .select2-input {
    height: 2.25em;
  }
  .select2-choices {
    .select2-search-choice {
      margin: 5px 0 3px 5px;
      padding-left: 0;
      border: none;
      box-shadow: none;
      color: darkblue;
      background: none;
      div {
        &:before {
          content: '#';
          display: inline;
        }
      }
    }
    .select2-search-choice-close {
      top: 3px;
      background: none;
    }
  }
}

.whatsups-form-select2-drop {
  margin-top: 0.15em;
  background-color: #f3f3f3;
  border: 1px solid $input-border-color;
  border-radius: 3px;
  .popular-tags {
    padding: 0.45em 1.25em;
    font-size: 0.7em;
    font-weight: bold;
    color: #909090;
    background-color: #e7e7e7;
    border-bottom: 1px solid #d1d1d1;
  }
  .select2-results {
    margin: 0em;
    padding-left: 0em;
  }
  .select2-result {
    border-bottom: 1px solid #dadada;
  }
  .select2-result-label, .select2-no-results {
    padding: 7px 14px;
    font-weight: bold;
    color: #5a5a5a;
  }
  .select2-highlighted {
    background-color: darken(#f3f3f3, 2%);
  }
}
.media-filters {
  margin-bottom: 1em;
  padding: 0.35em 0.35em 0.4em;
  @include profile-box;
  @media #{$til-medium} {
    margin-top: 1.5em;
  }
  ul {
    margin-bottom: 0em;
  }
  li {
    width: 15.7%;
  }
}
.filter > a {
  display: inline-block;
  padding: 0.35em 0.8em;
  font-size: 0.78em;
}
.filter.zero:not(.active) {
  @include opacity(0.4);
}
.filter.active {
  a {
    color: $blanco;
    background-color: $rojo;
    border-radius: 4px;
  }
  .whatsups {
    width: 22px;
    background-position: 0px 0px;
  }
  .pictures {
    width: 23px;
    background-position: -22px 0px;
  }
  .videos {
    width: 20px;
    background-position: -45px 0px;
  }
  .spots {
    width: 16px;
    background-position: -65px 1px;
  }
  .events {
    width: 19px;
    background-position: -81px 0px;
  }
}
.filter-icon {
  display: inline-block;
  margin-top: 0.2em;
  margin-bottom: -0.2em;
  height: 22px;
  background-image: image-url('social/profiles/userbox-sprite-small.png');
  background-repeat: no-repeat;
  &.whatsups {
    width: 22px;
    background-position: 0px -22px;
  }
  &.pictures {
    width: 23px;
    background-position: -22px -22px;
  }
  &.videos {
    width: 20px;
    background-position: -45px -22px;
  }
  &.spots {
    width: 16px;
    background-position: -65px -21px;
  }
  &.events {
    width: 19px;
    background-position: -81px -22px;
  }
}
.filter-counter {
  position: relative;
  top: -0.25em;
  margin-left: 0.3em;
  @media (max-width: 425px) {
    display: none;
  }
}
.remove-filters {
  position: relative;
  top: 0.95em;
  font-size: 0.8em;
  font-weight: 700;
  @include opacity(0.4);
  &.active {
    color: $rojo;
    @include opacity(1);
  }
}
.show {
  @media (max-width: 425px) {
    display: none;
  }
}
.brand-profile {
  @include profile-box;
  margin: 0 10px;
  .brand-cover {
    position: relative;
    width: 100%;
    background-size: auto 104%;
    background-repeat: repeat-x;
    background-position: center;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    &:before {
      content: '';
      display: block;
      padding-top: 22.65%;
    }
  }
  .brand-bio {
    position: absolute;
    top: 0;
    bottom: 0%;
    left: 0;
    right: 0;
  }
  .cover-mask {
    float: right;
    margin-top: 1.8em;
    margin-right: 0.8em;
    padding: 0.5em;
    width: 32em;
    max-height: 10.8em;
    background-color: rgba(0, 0, 0, 0.3);
  }
  .description {
    margin-bottom: 0em;
    font-size: 1em;
    color: $blanco;
  }
  .brand-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.4em 0.8em 0.5em 0.5em;
    @include linear-gradient(transparent, black 110%);
  }
  .brand-avatar-wrapper {
    float: left;
    margin-right: 1em;
  }
  .brand-name {
    h1 {
      margin: 0em;
      line-height: 1.25em;
      font-size: 1.25em;
      color: $blanco;
    }
    p {
      font-size: 0.8em;
      margin: 0;
      color: $blanco;
    }
    a {
      color: $blanco;
    }
  }
  .main-follow {
    margin-top: 1.2em;
  }

  .mobile-brand-cover {
    display: none;
  }

  .brand-stats {
    margin: 0.3em 0.81em;
  }
  .stat-wrapper{
    @include follow-stats;
    padding: 0.7em 0.7em 0.4em;
    width: 50%;
    border-right: 1px solid #d7d7d7;
  }
  .stat-name {
    font-size: 0.6em;
  }
  .push-5 .main-follow {
    display: none;
  }
  .profile-media-stats {
    @extend %profile-media-stats;
    padding-top: 0.2em;
    ul {
      margin-bottom: 0em;
    }
  }

  @media #{$from-wide} {
    .cover-mask {
      margin-top: 2.1em;
      width: 22em;
    }
  }

  @media (min-width: 961px) and (max-width: 1023px) {
    .medium-4 { width: 33.33%; }
    .medium-3 { width: 25%; }
    .medium-5 { width: 41.66%; }
  }

  @media (max-width: 960px) {
    border-radius: 0px;
    .brand-cover {
      display: none;
    }

    .mobile-brand-cover {
      display: block;
      height: 13.5em;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
    }
    .royalSlider {
      width: 100%;
    }
    .rsDefault,
    .rsDefault .rsOverflow,
    .rsDefault .rsSlide,
    .rsDefault .rsVideoFrameHolder,
    .rsDefault .rsThumbs,
    .rsBullets {
      background-color: transparent;
    }
    .rsBullets {
      height: 20px;
    }

    .mobile-brand-info {
      padding: 0.5em;
      height: 13.5em;
      @include linear-gradient(transparent 53%, rgba(0,0,0,0.9) 110%);
    }
    .mobile-brand-avatar-wrapper {
      float: none;
      margin-right: 0em;
      padding-top: 3.5em;
      height: 8.5em;
      .square {
        margin: auto;
      }
    }
    .mobile-brand-name {
      height: 3em;
      text-align: center;
      h1 {
        margin: 0em;
        line-height: 1em;
        font-size: 1em;
        color: $blanco;
      }
      p {
        margin: 0;
      }
      a {
        color: $gris-claro;
        font-weight: bold;
      }
    }

    .mobile-brand-bio {
      padding-top: 1em;
      height: 13.5em;
      background-color: rgba(0, 0, 0, 0.75);
    }
    .mobile-cover-mask {
      float: none;
      margin: 0em;
      padding: 0.8em 0.6em;
      max-width: none;
      max-height: none;
      text-align: center;
      background-color: transparent;
      p {
        margin-bottom: 0em;
        font-size: 1em;
        color: $blanco;
      }
    }

    .brand-stats {
      margin: 0em 0.81em;
    }
    .push-5 {
      left: auto;
    }
    .push-5 .main-follow {
      display: block;
      margin-top: 0.4em;
    }
    .pull-3 {
      right: auto;
      border-top: 1px solid #d7d7d7;
    }
  }

  @media (min-width: 768px) and (max-width: 960px) {
    .medium-4 { width: 66.66%; }
    .medium-3 { width: 33.33%; }
    .medium-5 { width: 100%; }
  }
}

@media (min-width: 321px) and (max-width: 419px) {
  .push-5 .main-follow {
    width: 110%;
  }
}

.brand-feed {
  margin-top: 12px;
}
.see-more-products {
  clear: both;
  a {
    float: right;
    margin-right: 0.5em;
    font-size: 1.1em;
    font-weight: 700;
    color: #333;
    text-transform: uppercase;
    text-shadow: 1px 1px 1px white;
  }
}

.v4_user_profile {
  > .row {
    margin-top: 1em;
  }
}
.user-profile {
  @include profile-box;
  margin-bottom: 1.5em;
  .profile-header {
    @include profile-header(9em);
    position: relative;
    .user-avatar {
      display: block;
      margin: 1em auto 0;
    }
    .brand-avatar {
      display: block;
      margin: 1.2em auto 0.55em;
    }
    &:before {
      content: ' ';
      position: absolute;
      bottom: 0em;
      left: 0em;
      width: 100%;
      height: 4.3em;
      background-color: rgba(16, 16, 16, .50);
      background-position: 10px 10px;
    }
    .profile-name {
      p {
        margin-bottom: 0em;
        padding-top: 0.35em;
        font-size: 0.9em;
        letter-spacing: 1px;
        text-align: center;
        text-shadow: 0px 2px 1px rgba(0, 0, 0, 0.8);
        color: white;
        a { color: inherit; }
      }
    }
    .edit-button {
      position: absolute;
      top: 5px;
      right: 5px;
      text-transform: uppercase;
      z-index: 3;
      display: none;
    }
  }
  .profile-stats {
    .follow-stats {
      @include follow-stats;
      padding: 0.8em 1.2em 0.5em;
      .stat-name {
        font-size: 0.2em;
      }
    }
    .main-follow {
      margin-top: -0.2em;
      margin-right: -0.6em;
    }
    @media (max-width: 430px) {
      .small-3 {
        width: 50%;
      }
      .small-6 {
        width: 100%;
        .main-follow {
          margin-top: 0.6em;
          margin-right: 0em;
          width: 100%;
        }
      }
    }
  }
  .profile-bio {
    padding: 0em 0.9em;
    p {
      margin-top: 0.8em;
      margin-bottom: 0.8em;
      font-size: 0.8em;
      color: #6a6a68;
    }
    .divider {
      height: 1px;
      background-color: #d7d7d7;
    }
  }
}

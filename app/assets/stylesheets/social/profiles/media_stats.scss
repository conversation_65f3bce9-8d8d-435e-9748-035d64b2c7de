@mixin profile-box {
  background-color: #fafafa;
  border: 1px solid #b3b3b3;
  border-radius: 4px;
  box-shadow: 0 1px 1px 1px $box-shadow-color;
}

@mixin profile-header($height) {
  height: $height;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

@mixin follow-stats {
  .stat-value {
    margin-bottom: 2px;
    font-size: 0.8em;
    font-weight: 700;
    color: #2c2c2a;
  }
  .stat-name {
    color: #717171;
  }
}

%profile-media-stats {
  .stat {
    position: relative;
    display: inline-block;
    padding: 2px;
    width: 39px;
    text-align: center;
    .image {
      height: 23px;
      margin: 0 auto;
      display: inline-block;
      background-image: image-url('social/profiles/userbox-sprite-small.png');
    }
    .stat-value {
      margin: 0;
      display: block;
      font-size: 14px;
      font-weight: bold;
      text-align: center;
      color: #444444;
      line-height: 15px;
    }
    &.zero {
      @include opacity(0.4);
    }
    &.whatsups {
      .image {
        width: 22px;
        background-position: 0px -22px;
      }
    }
    &.pictures {
      .image {
        width: 23px;
        background-position: -22px -22px;
      }
    }
  }
  .stat.active {
    background-color: $rojo;
    border-radius: 4px;
    opacity: 1;
    .stat-value {
      color: $blanco;
    }
    &.whatsups {
      .image { background-position: 0px 0px; }
    }
    &.pictures {
      .image { background-position: -22px 0px; }
    }
  }
  .remove-filters {
    top: 1.15em;
    font-size: 1em;
  }
}

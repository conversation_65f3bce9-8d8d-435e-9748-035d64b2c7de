.feed-item {
  float: left;
  width: 100%;
  &:hover {
    .feed-item-content > .feed-item-share {
      opacity: 1;
    }
  }
  hr {
    border-bottom: 1px solid #ffffff;
    border-top: 1px solid #d9d9d9;
    margin-top: emCalc(8px);
    margin-bottom: emCalc(13px);
  }
  #close-followers-module{
    position: absolute;
    right: 0.9em;
    top: 0.7em;
    cursor: pointer;
  }
  .feed-item-content {
    position: relative;
    .thumb-wrapper {
      position: relative;
      display: block;
      text-align: center;
    }
    .pic {
      margin-bottom: 8px;
      max-height: 500px;
    }
    .play-icon {
      content: '';
      position: absolute;
      left: 1em;
      bottom: 1.5em;
      width: 85px;
      height: 59px;
      background-image: image-url('social/feed_item/play-large.png');
    }
    .whatsup-video-thumb {
      position: relative;
    }
    .feed-item-share {
      @extend .clearfix;
      position: absolute;
      right: 0;
      opacity: 0;
      @include transition (opacity .20s linear);
      z-index: 10;
      .button {
        margin-right: 4px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .text {
      overflow: hidden;
      position: relative;
    }
  }
  .metadata {
    margin-bottom: 8px;
    img {
      cursor: pointer;
    }
  }
  &.album {
    .pictures {
      cursor: pointer;
      @include clearfix;
      margin-bottom: 0.5em;
      padding: 0.125em;
      background-color: #fff;
      .picture {
        float: left;
        width: 25%;
        position: relative;
        border: 0.125em solid #fff;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        &:after{
          content: '';
          display: block;
          padding-top: 100%;
        }
        a {
          cursor: pointer;
        }
      }
      &.has-1 {
        text-align: center;
        .picture {
          float: none;
          width: auto; height: auto;
          max-width: 100%;
          max-height: 20em;
          &:after{ display: none; }
        }
      }
      &.has-2 {
        .picture { width: 50%; }
      }
      &.has-3 {
        .picture {
          width: 33.33%;
        }
      }
      &.has-4, &.has-8 {
        .picture { width: 25%; }
      }
      &.has-5, &.has-6, &.has-7 {
        .picture {
          width: 25%;
          &:first-child {
            width: 50%;
          }
        }
      }
    }
  }
  &.whatsup {
    .pic {
      cursor: pointer;
    }
  }
}

.feed-item-modal {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
  &.has-media {
    max-width: $wide-screen;
  }
  // Extra space for close X
  &.has-media .item-body .feed-item-author,
  .item-body .feed-item-author:first-child,
  .item-body .metadata .content-data {
    padding-right: 1.8em;
  }

}
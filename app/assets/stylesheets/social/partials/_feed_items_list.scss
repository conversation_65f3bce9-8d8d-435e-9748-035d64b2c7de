@mixin feed-items-list-with-1-columns {
  .feed-item {
    width: 100%;
    @include calc(width, '100% - 20px');
    margin-right: 10px;
  }
}
@mixin feed-items-list-with-2-columns {
  .feed-item {
    width: 47.5%;
    margin-right: 2.5%;
  }
}
@mixin feed-items-list-with-3-columns {
  .feed-item {
    width: 31%;
    margin-right: 2%;
  }
}

.feed-items-list {
  position: relative;
  margin: 0 0 65px;
  overflow: hidden;
  @include feed-items-list-with-1-columns;
  @media (min-width: 450px) and #{$before-medium} {
    @include feed-items-list-with-2-columns;
  }
  @media #{$from-medium} {
    @include feed-items-list-with-3-columns;
  }
}

.feed-items-list-items {
  @include clearfix();
  position: relative;
  margin: 0 -10px 0 10px;
}

.load-more-items {
  margin: emCalc(10px) 0 1px;
  text-align: center;
  .load-more-items-button {
    display: none;
    position: relative;
    width: 100%;
    max-width: 450px;
    font-size: 1em;
    font-weight: 700;
    .load-more-items-button-icon {
      display: inline-block;
      position: absolute;
      left: 14px;
      top: 50%;
      margin-top: -18px;
      width: 30px;
      height: 37px;
      background-size: 30px 37px;
      background-image: image-url('social/feed_item/gp-loading-empty.png');
      background-repeat: no-repeat;
      .load-more-items-button-animation {
        display: none;
      }
      @media #{$til-small} {
        margin-top: -14px;
        width: 20px;
        height: 27px;
        background-size: 20px 27px;
      }
    }
    // Loading animation, only works on webkit.
    &.disabled {
      .load-more-items-button-icon {
        .load-more-items-button-animation {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-size: 30px 37px;
          background-image: image-url('social/feed_item/gp-loading-full.png');

          -webkit-mask-image: #{'linear-gradient(to top, black, transparent)'};
          -webkit-mask-size: 100%;
          -webkit-mask-repeat: no-repeat;
          -webkit-animation: fade-show 1.5s infinite;

          @media #{$til-small} {
            background-size: 20px 27px;
          }
        }
      }
    }
  }
}

@-webkit-keyframes fade-show {
  0% {
    -webkit-mask-position: 0 37px;
  }
  100% {
    -webkit-mask-position: 0 0;
  }
}

.joined-on {
  position: absolute;
  top: 100%;
  margin-bottom: 100px;
  width: 100%;
  display: none;
  .contents {
    @include linear-gradient (#ffffff, lighten($gris-claro, 6%));
    padding: 1.6em 1em 1.3em;
    font-size: 0.8em;
    text-align: center;
    color: lighten($gris, 20%);
    border: 1px solid darken($gris-claro, 6%);
    border-radius: 3px;
    box-shadow: 0 1px 1px 0px $box-shadow-color;
    img {
      position: relative;
      top: 50%;
      margin-top: -0.7em;
      float: left;
      width: 33px;
      height: 36px;
    }
    @media #{$til-small} {
      img {
        margin-top: -0.5em;
        width: 23px;
        height: 26px;
      }
    }
  }
}
.feed-item-author {
  @include clearfix;
  line-height: 1em;
  .feed-avatar {
    float: left;
    display: inline-block;
    margin-right: 11px;
  }
  .name {
    margin-bottom: 0.2em;
    padding-top: emCalc(7px);
    padding-right: emCalc(4px);
    font-size: emCalc(14px);
    a { font-weight: bold; }
  }
  .time, .via {
    font-size: emCalc(11px);
    position: relative;
    top: -0.4em;
  }
  .time {
    color: lighten($gris, 25%);
  }
  .via {
    a { font-weight: bold; }
  }
  .delete-post {
    float: right;
    margin-top: 7px;

    .button {
      position: relative;
      padding-left: 3.2em;
      padding-right: 1.4em;
      font-size: 0.65em;
      font-weight: normal;
      line-height: normal;
      .icon {
        position: absolute;
        top: 50%;
        left: 0.7em;
        margin-top: -1em;
        width: 21px;
        height: 21px;
        background-image: image-url('social/feed_item/post-edit-sprite.png');
        background-position: -21px -21px;
      }
      &:hover:enabled {
        color: lighten(#686868, 10%);
        .icon {
          background-position: -21px 0px;
        }
      }
      &:active:enabled {
        color: $rojo;
        .icon {
          background-position: -21px -42px;
        }
      }
      &[disabled] {
        .icon {
          background-position: -21px 0px;
        }
      }
    }
  }
}
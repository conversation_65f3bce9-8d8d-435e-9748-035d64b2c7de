.avatar-uploader {
  text-align: center;
  .avatar-uploader-thumb {
    display: block;
    @include hexagon(emCalc(140px));
    margin: 0 auto 15px;
    background-color: #efefef;
  }
  .choose-photo {
    margin-bottom: 15px;
    position: relative;
    input {
      cursor: pointer;
    }
  }
  &.loading {
    .avatar-uploader-thumb {
      background-size: 90%;
      background-image: image-url('loaders/uploader-loading.gif') !important;
    }
  }
}

html.csstransforms .avatar-uploader {
  .avatar-uploader-thumb {
    background-color: transparent !important;
    .hexagon-in2 {
      background-color: #efefef;
    }
  }
  &.loading {
    .avatar-uploader-thumb {
      background-image: transparent !important;
      .hexagon-in2 {
        background-size: 90%;
        background-image: image-url('loaders/uploader-loading.gif') !important;
      }
    }
  }
}
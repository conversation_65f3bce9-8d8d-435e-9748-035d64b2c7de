.file-uploader {
  .file-uploader-msg {
    font-weight: bold;
    margin: 12px 0 6px;
  }
  .file-uploader-item {
    @extend .cf;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
    &.no-thumb {
      .file-uploader-item-thumb {
        display: none;
      }
      .file-uploader-item-progress {
        margin-left: 0;
      }
    }
    .file-uploader-item-thumb {
      float: left;
      width: 65px;
      height: 65px;
      border: 1px solid $negro;
      border-radius: 3px;
      margin-right: 12px;
      background-size: cover;
    }
    .file-uploader-item-cancel {
      float: right;
      padding: 0;
      width: 34px;
      height: 32px;
      background-repeat: no-repeat;
      background-position: 8px 6px;
      background-image: image-url('social/whatsups_form/progress-cancel.png');
      &:hover {
        background-position: 8px -38px;
      }
    }
    .file-uploader-item-name {
      margin: 3px 0 7px 0;
    }
    .file-uploader-item-progress {
      position: relative;
      margin-left: 74px;
      margin-right: 42px;
      height: 32px;
      text-align: center;
      background-size: 100% 64px;
      background-repeat: no-repeat;
      background-position: top left;
      background-image: image-url('social/whatsups_form/progress-bar.png');
      .file-uploader-item-progress-bar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background-size: 100% 64px;
        background-repeat: no-repeat;
        background-position: left bottom;
        background-image: image-url('social/whatsups_form/progress-bar.png');
      }
      .file-uploader-item-progress-percentage {
        position: absolute;
        left: 47%;
        top: 0;
        display: block;
        color: white;
        font-weight: bold;
        line-height: 32px;
      }
    }
  }
}
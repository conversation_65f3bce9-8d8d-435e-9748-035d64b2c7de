.favorite-button {
  position: relative;
  display: inline-block;
  border-radius: 3px;
  border: 1px solid #d1d1d1;
  cursor: pointer;
  &.with-title {
    .favorite-title { display: inline; }
  }
  .favorite-title {
    display: none;
    position: relative;
    top: 1px;
    text-transform: uppercase;
    margin: 0 0 0 13px;
    font-size: 12px;
    line-height: 40px;
    color: $gris;
    font-weight: bold;
  }
  .favorite-hand {
    float: right;
    display: block;
    width: 45px;
    height: 40px;
    background-repeat: no-repeat;
    background-position: 12px 8px;
    background-image: image-url('social/feed_item/social-actions-sprite.png');
  }
  &:hover:not(.favorited) {
    .favorite-hand {
      background-position: -38px 8px;
    }
    .favorite-title {
      color: $gris-oscuro;
    }
  }
  &:active {
    .favorite-hand {
      background-position: -88px 8px !important;
    }
    .favorite-title {
      color: $rojo !important;
    }
  }
  &.favorited {
    background-color: $rojo;
    .favorite-hand {
      background-position: -138px 8px;
    }
    .favorite-title {
      color: white;
    }
  }
  .high-fived {
    position: absolute;
    top: -70px;
    left: -5px;
    width: 210px;
    height: 65px;
    background-image: image-url('social/feed_item/bg_module_highfive.png');
    display: none;
    p {
      margin-bottom: 0;
      padding: 16px 0;
      font-size: 14px;
      color: $blanco;
      text-align: center;
    }
  }
}
%pages-animate-loading-with-opacity {
  @include opacity(1);
  @include transition (opacity .3s ease-in);
  &.loading {
    @include opacity(0);
  }
}

.pages-action {
  background: white;
  #subheader {
    margin-bottom: 0;
  }
  .join {
    margin-top: 1.5em;
  }
  .user-resume {
    width: 100%;
    float: left;
    @media (min-width: 620px) and #{$til-large} {
      width: 46%;
      margin-right: 4%;
    }
    @media #{$after-large} {
      width: 30%;
      margin-right: 3%;
    }
  }
}

.pages-title-wrapper {
  min-height: 200px;
  background-size: cover;
  background: $negro image-url('pages/company_large_bg.jpg') no-repeat center center;
}

.pages-title-row {
  position: relative;
}

.pages-title {
  padding-top: 3.7em;
  padding-bottom: 2.1em;
  h2 {
    @extend %pages-animate-loading-with-opacity;
    display: none;
    margin: 0;
    font-family: 'Ostrich Sans Inline', $body-font-family;
    font-weight: normal;
    font-size: 120px;
    line-height: 0.9em;
    color: #DA4337;
    letter-spacing: -2px;
  }
}

.pages-menu-wrapper {
  position: absolute;
  left: 0; top: 0;
  padding-top: 2.7em;
  padding-right: 2em;
}

.pages-menu {
  position: relative;
  font-size: 0.9em;
  background-color: white;
  li {
    border: 1px solid $gris-claro;
    border-bottom: none;
    text-align: right;
    &:last-child {
      border-bottom: 1px solid $gris-claro;
    }
    &.active {
      a, a:hover{
        font-weight: bold;
        color: #DA4337;
        border-right-color: #DA4337;
      }
    }
    a {
      display: block;
      padding: 0.9em 1.4em;
      border-right-width: 5px;
      border-right-style: solid;
      border-right-color: white;
      &:hover {
        border-right-color: #7c7c7c;
      }
    }
  }
}

.pages-content-wrapper {
  @extend %pages-animate-loading-with-opacity;
  min-height: 33em;
}

.pages-content-row-wrapper {
  padding: 1em 0 2.8em;
  &.grey {
    background-color: #F6F6F6;
  }
}

.pages-content {
  a {
    color: #DA4337;
  }
  h2 {
    font-size: 1.3em;
    margin: 1.8em 0 1em;

    &.faq-title{
      font-size: 2.3em;
    }
    &.first-term {
      margin-top: 0;
    }
    &.static_big_title{
      margin: 1em 0;
    }
  }
  .sheet {
    padding:0em 1em 0.5em;
    margin:1em;
    border: 1px solid #E2E2E2;
    border-radius: 5px;
    .header {
      color:#DA4337;
      .topic{
        color: #565656;
      }
      .type_location{
        font-size: 0.9em !important;
        color: #333333;
        text-transform: none !important;
      }
      .subject_reference{
        font-size: 0.8em !important;
        color: #949494;
        font-style: italic;
        text-transform: none !important;
      }
    }
  }
  h3 {
    font-size: 1em;
    margin-top: 1.2em;
    margin-bottom: 0.7em;
    font-weight: bold;
    text-transform: uppercase;
    &.first-term {
      margin-top: 0;
    }
  }
  p {
    font-size: 0.9em;
    margin-bottom: 0.75em;
  }
  ul, ol {
    margin-left: 1.4em;
    font-size: 0.95em;
  }
  ul li {
    list-style-type: disc;
  }
  ol li {
    list-style-type: lower-latin;
  }
  .square {
    &.feed-avatar {
      @include square(emCalc(130px));
    }
  }
}

.pages-personita {
  margin: 2em auto;
  text-align: center;
  img {
    margin-bottom: 1em;
  }
  p {
    margin-bottom: 0;
  }
  .personita-social {
    margin-top: 10px;
    a {
      display: inline-block;
      margin: 0 8px;
      width: 25px;
      height: 25px;
      background-image: image-url('pages/team/sn-sprite.png');
      background-repeat: no-repeat;
    }
    .twitter {
      background-position: 2px top;
    }
    .linkedin {
      background-position: -73px 2px;
    }
    .github {
      background-position: -35px top;
    }
  }
}

.apply-now-form {
  position: relative;
  margin: 0 auto;
  max-width: 580px;
  padding: 14px;
  border-radius: 3px;
  box-shadow: 0 0 9px 0px rgba(0,0,0,0.3);
  background-color: white;
  &.loading {
    .loader { display: block; }
  }
  .loader {
    display: none;
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background-color: white;
    background-color: rgba(white, 0.8);
    z-index: 2;
  }
  .apply-now-form-title {
    background-color: $negro;
    .apply-now-form-title-icon {
      float: left;
      height: 97px;
      width: 97px;
      margin-right: 17px;
      background: #A2A2A2 image-url('pages/brands/form.png') center center no-repeat;
    }
    h2, p {
      color: white;
      margin: 0;
      line-height: normal;
      text-transform: uppercase;
    }
    h2 {
      padding: 25px 17px 0;
      line-height: 28px;
      font-weight: bold;
      font-size: 20px;
      letter-spacing: 1px;
    }
    p {
      padding: 0 17px 25px;
      font-size: 14px;
    }
  }
  .apply-now-form-content {
    padding: 20px;
    background-color: #F6F6F6;
    p {
      line-height: 1.7em;
      font-size: 0.9em;
      font-weight: 600;
      opacity: 0.7;
    }
    input, textarea {
      width: 100%;
      padding: 0.8em;
      border: 1px solid $gris-claro;
      border-radius: 0;
      box-shadow: none;
      letter-spacing: 1px;
      font-size: 0.85em;
      line-height: normal;
      font-weight: bold;
      margin: 0 0 1.5em;
    }
    textarea {
      resize: auto;
    }
    button {
      @include reset-button;
      margin-top: 0.4em;
      padding: 0.75em 2em;
      cursor: pointer;
      text-transform: uppercase;
      color: white;
      font-weight: bold;
      font-size: 0.85em;
      background-color: #DA4337;
      &:hover {
        background-color: darken(#DA4337, 10%);
      }
    }
  }
}

#pages-brands,
#pages-brands_ar {
  .mfp-bg {
    background-color: white;
  }
  .pages-title {
    display: none;
  }
  .apply-now-button {
    @include reset-button;
    display: inline-block;
    padding: 1em 3em;
    margin-top: 1.2em;
    border: 1px solid #DA4337;
    border-radius: 5px;
    color: #DA4337;
    font-size: 1em;
    cursor: pointer;
    text-transform: uppercase;
    background-color: #F6F6F6;
    &:hover {
      background-color: rgba(white, 0.4);
    }
  }
  .pages-brands-coso {
    .imagen {
      width: 100%;
      height: 142px;
      background-repeat: no-repeat;
      background-position: left center;
    }
  }
  .pages-brands-steps {
    @include clearfix;
    margin-bottom: 3em;
    img {
      float: left;
      margin-right: 3em;
    }
    h3 {
      position: relative;
      left: -1.4em;
      margin-top: 0;
      color: #DA4337;
      font-weight: normal;
    }
    ul {
      font-size: 1.15em;
      color: #929292;
      li {
        padding-bottom: 0.5em;
        line-height: 1.3em;
        font-weight: bold;
        span {
          color: $gris;
          font-size: 0.8em;
          line-height: normal;
          font-weight: normal;
          padding-bottom: 0.2em;
        }
      }
    }
  }
}

.pages-mobile-menu-wrapper {
  z-index: 100;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  position: fixed;
  top: 0;
  right: 0;
  padding-bottom: .9em;
  height: 100%;
  max-width: 100%;
  width: 13em;
  background-color: #eb1a20;

  display: none;

  .csstransforms3d & {
    display: block;
    @include transition(all .2s ease-in);
    @include transform(translate3d(100%,0,0));
  }

  &.active {
    display: block;
    @include transform(translate3d(0,0,0));
    & + .pages-mobile-menu-wrapper-overlay {
      display: block;
      .csstransitions & {
        visibility: visible;
        opacity: 1;
      }
    }
  }
}

.pages-mobile-menu-wrapper-overlay {
  @include cover-all;
  position: fixed;
  z-index: 90;
  background-color: #fff;
  background-color: rgba(#fff, .7);
  cursor: pointer;
  @include transition(all .1s linear);
  display: none;

  .csstransitions & {
    display: block;
    visibility: hidden;
    opacity: 0;
  }
}

.pages-mobile-menu-header {
  position: relative;
  height: 46px;
  background-color: #eb1a20;
  cursor: pointer;
  display: none;
  @media #{$before-medium} {
    display: block;
  }
  .i-menu {
    position: absolute;
    right: 13px;
    top: 9px;
    font-size: 21px;
    color: black;
  }
}

.pages-mobile-menu {
  position: relative;
  padding: 12px;
  .menu-item {
    border-bottom: 1px dashed #f45f5b;
    border-left: 10px dashed #eb1a20;
    border-right: 10px dashed #eb1a20;
    line-height: 49px;
    font-size: 13px;
    a {
      display: block;
      color: white;
    }
    &:active, &.active {
      border-left: 10px dashed black;
      border-right: 10px dashed black;
      background-color: black;
    }
  }
}
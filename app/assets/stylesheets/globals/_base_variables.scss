// FOUNDATION

// foundation/variables
$base-font-size: 100%;
$em-base: 16px;

@function emCalc($px-width) {
  @return $px-width / $em-base * 1em;
}

@function s3-image-url($name) {
  @if $rails-env == 'development' {
    @return image-url($name);
  } @else {
    @return unquote("url('https://s3.amazonaws.com/gpcdn-static" + image-path($name) + "')");
  }
}

$experimental: true;

// foundation/global
$include-html-classes: false;
$body-font-family: "Open Sans", "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;

$primary-color: $gris;
$secondary-color: $blanco;
$alert-color: $rojo;
$success-color: #5da423;

$small-screen: 320px;
$medium-screen: 768px;
$large-screen: 1024px;
$wide-screen: 1280px;

$til-small: "(max-width: #{$small-screen})";
$from-small: "(min-width: #{$small-screen})";
$before-small: "(max-width: #{$small-screen - 1})";
$after-small: "(min-width: #{$small-screen + 1})";

$til-medium: "(max-width: #{$medium-screen})";
$from-medium: "(min-width: #{$medium-screen})";
$before-medium: "(max-width: #{$medium-screen - 1})";
$after-medium: "(min-width: #{$medium-screen + 1})";

$til-large: "(max-width: #{$large-screen})";
$from-large: "(min-width: #{$large-screen})";
$before-large: "(max-width: #{$large-screen - 1})";
$after-large: "(min-width: #{$large-screen + 1})";

$til-wide: "(max-width: #{$wide-screen})";
$from-wide: "(min-width: #{$wide-screen})";
$before-wide: "(max-width: #{$wide-screen - 1})";
$after-wide: "(min-width: #{$wide-screen + 1})";

// Media Query for retina displays: http://miekd.com/articles/using-css-sprites-to-optimize-your-website-for-retina-displays/
$retina: "only screen and (-webkit-min-device-pixel-ratio: 2), screen and (max--moz-device-pixel-ratio: 2)";

// components/forms
$include-html-form-classes: true;
$input-font-size: .8em;
$input-box-shadow: none;
$input-include-glowing-effect: false;
$legend-bg: transparent;
$form-spacing: 1.65em;
$input-focus-border-color: $gris;
$glowing-effect-fade-time: .3s;
$glowing-effect-color: rgba($gris, .2);

// components/custom-forms
$include-html-custom-form-classes: true;
//$custom-select-height: 2.5em;

// components/grid
$row-width: emCalc(1024px);
$column-gutter: emCalc(24px);

// components/top-bar
$include-html-top-bar-classes: false;
// $topbar-bg: $negro;
// $topbar-height: 90px;
// $topbar-margin-bottom: 0;
// $topbar-dropdown-bg: $negro;
// $topbar-link-weight: 400;
// $topbar-link-font-size: emCalc(14px);
// $topbar-link-bg-hover: lighten($topbar-bg, 8%);
// $topbar-link-bg-active: lighten($topbar-bg, 8%);
// $topbar-menu-link-font-size: emCalc(14px);
// $topbar-menu-link-weight: 700;
// $topbar-menu-link-color-toggled: #a9a9a9;
// $topbar-menu-icon-color-toggled: #a9a9a9;
$topbar-breakpoint: 960px;

// components/type
$include-html-type-classes: true;
$header-font-family: $body-font-family;
$header-font-color: $body-font-color;
$header-text-rendering: geometricPrecision;
$small-font-color: lighten($gris, 20%);
$small-font-size: 45%;
$paragraph-font-size: emCalc(12px);
$paragraph-text-rendering: geometricPrecision;
$anchor-font-color: $body-font-color;
$anchor-font-color-hover: lighten($anchor-font-color, 15%);

// components/reveal
$reveal-overlay-bg: rgba(#000, .88);
$reveal-close-font-size: 1em;

// components/sub-nav
$include-html-nav-classes: true;
$sub-nav-font-size:          emCalc(16px);
$sub-nav-font-color:         $body-font-color;
$sub-nav-active-bg:          $rojo;
$sub-nav-active-color:       $blanco;

// components/tables
$include-html-table-classes: true;

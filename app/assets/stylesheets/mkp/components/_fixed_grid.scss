/*
* Grid for Marketplace by mjlescano@github
*/

$grid-column: 70px;
$grid-gutter: 10px;
$grid-columns: 16;
$desktop-columns: 12;
$tablet-columns: 8;
$mobile-columns: 4;
$grid-width: ($grid-column + $grid-gutter) * $grid-columns;
$desktop-width: ($grid-column + $grid-gutter) * $desktop-columns;
$tablet-width: ($grid-column + $grid-gutter) * $tablet-columns;
$mobile-width: ($grid-column + $grid-gutter) * $mobile-columns;

@mixin media-default {
  @media screen and (min-width: 1290px) {
    @content;
  }
}

@mixin media-desktop {
  @media screen and (max-width: 1289px) and (min-width: 960px) {
    @content;
  }
}

@mixin media-tablet {
  @media screen and (max-width: 959px) and (min-width: 640px) {
    @content;
  }
}

@mixin media-tablet-mobile {
  @media screen and (max-width: 959px) {
    @content;
  }
}

@mixin media-mobile {
  @media screen and (max-width: 639px) {
    @content;
  }
}


@mixin grid-set-width($cols) {
  width: $grid-column * $cols + $grid-gutter * $cols - $grid-gutter;
}

@mixin grid-container {
  @extend .cf;
  width: $grid-width;
  margin-left: auto;
  margin-right: auto;
  @include media-desktop { width: $desktop-width; }
  @include media-tablet { width: $tablet-width; }
  @include media-mobile { width: $mobile-width; }
}

@mixin grid-columns-container($i) {
  @extend .cf;
  width: $grid-column * $i + $grid-gutter * $i;
  float: left;
}

@mixin grid-margins {
  margin-left: $grid-gutter / 2;
  margin-right: $grid-gutter / 2;
  margin-bottom: $grid-gutter;
}

@mixin grid-columns($cols, $rows: "") {
  @include grid-set-width($cols);
  @if $rows != "" {
    @include grid-columns-height($rows);
  }
  float: left;
  @include grid-margins;
}

@mixin grid-columns-height($rows, $prop: height) {
  #{$prop}: ($grid-column * $rows) + ($grid-gutter * ceil($rows)) - $grid-gutter;
}

footer {
  margin-top: 1em;
  padding: 0.5em 0em;
  background-color: #efefef;
  h4 {
    margin-bottom: 0.4em;
    font-size: 0.95em;
    font-weight: normal;
    color: #4f4f4f;
    text-shadow: #9d9d9d 1px 0px;
  }
  ul {
    margin-bottom: 0em;
    li {
      margin-bottom: 0.5em;
      a {
        font-size: 1.2em;
        color: #6a6a6a;
      }
    }
  }
  p {
    margin-top: 0.5em;
    font-size: 0.85em;
  }
}

.join-us-links {
  h4 {
    margin-top: 1.3em;
    @media #{$before-large} { margin-top: 0.2em; }
  }
}

.secure-icon {
  span {
    display: inline-block;
    margin-bottom: 10px;
    width: 45px;
    height: 46px;
    background-image: image-url('social/footer/secure-logo.png')
  }
  @media #{$before-medium} {
    float: left;
    margin-top: 1em;
    width: 31%;
  }
}

.payment-methods-icons {
  ul > li {
    margin-bottom: 0em;
    height: 25px;
  }
  .webpay{
    background-image: image-url('social/footer/webpay.png');
    height: 50px;
    width: 212px;
  }
  span {
    display: inline-block;
    height: 24px;
    background-image: image-url('social/footer/sprite_merchants.png');
    &.credit-cards {
      margin-right: 6px;
      width: 104px;
    }
    &.mercadopago {
      width: 96px;
      background-position: 154px 0px;
    }
    &.paypal {
      width: 52px;
      background-position: 52px 0px;
    }
  }
  .amex {
    display: inline-block;
    width: 35px;
    height: 24px;
    background-image: image-url('social/footer/americanexpress.png');

  }
  @media #{$before-medium} {
    float: left;
    margin-top: 1em;
    width: 69%;
  }
  @media #{$before-large} {
    li {
      display: inline-block;
      margin-right: 1em;
    }
  }
}

.network-links {
  ul {
    margin: 0.8em 0em;
    li {
      display: inline;
      margin-right: 0.2em;
    }
  }
  span {
    display: inline-block;
    width: 33px;
    height: 27px;
    background-image: image-url('social/footer/sprite_flags.png');
    cursor: pointer;
    &.us { background-position: 1px -2px; }
    &.ar { background-position: -39px -2px; }
    &.cl { background-position: -79px -2px;; }
  }
}

.social-media-links {
  ul {
    margin-top: 0.85em;
    li {
      display: inline;
      margin-right: 1.25em;
    }
  }
  span {
    display: inline-block;
    width: 28px;
    height: 28px;
    background-image: image-url('social/footer/social_sprite.png');
    border-radius: 4px;
    &.facebook { background-position: 0px 86px; }
    &.twitter { background-position: 148px 86px; }
    &.instagram { background-position: 118px 86px; }
    &.youtube { background-position: 28px 86px; }
    &.pinterest { background-position: 88px 86px; }
  }
  .foursquare {
    background-image: image-url('social/footer/foursquare-icon.png');
  }
}

.newsletter-form {
  form {
    position: relative;
    height: 42px;
    input[type = 'text'] {
      margin-bottom: 0em;
      padding-left: 4em;
      width: 80%;
      @media #{$before-medium} { width: 100%; }
      &:focus {
        border-color: lighten($header-clr-2, 35%);
        box-shadow: 0 0 1px 1px rgba($header-clr-2, 0.3);
      }
    }
    span {
      position: absolute;
      top: 11px;
      left: 11px;
      display: inline-block;
      width: 29px;
      height: 20px;
      background-image: image-url('social/footer/sprite_newsletter.png');
      background-repeat: no-repeat;
    }
    .clear {
      display: none;
    }
  }
}

.hide-on-large {
  @media #{$before-large} { display: block; }
  @media #{$from-large} { display: none; }
}

.show-on-large {
  @media #{$before-large} { display: none; }
  @media #{$from-large} { display: block; }
}

.show-on-medium {
  @media #{$before-medium} { display: none; }
  @media (min-width: 768px) and (max-width: 1023px) { display: block; }
  @media #{$from-large} { display: none; }
}

.show-on-small {
  @media #{$before-large} { display: block; }
  @media #{$from-large} { display: none; }
}

.show-only-small {
  @media #{$before-medium} { display: block; }
  @media #{$from-medium} { display: none; }
}

.custom-width {
  @media (min-width: 768px) and (max-width: 1023px) {
    &.medium-5 {
      padding-right: 5em;
      width: 44%;
    }
    &.medium-3 { width: 22%; }
  }
}

.bottom-footer {
  background-color: #202020;
}
.middle-bar {
  padding: 1.3em 0 1.1em;
  background-color: #202020;
  a {
    color: $blanco;
    &:hover {
      color: darken($blanco, 5%);
    }
  }
  .row {
    overflow: hidden;
    .small-2 {
      margin-bottom: -500px;
      padding-left: 2em;
      padding-bottom: 500px;
      width: 20%;
      border-right: 0.125em solid #9e9e9e;
      &:last-child {
        border-right: none;
      }
    }
  }
  .icon {
    display: block;
    height: 28px;
    background-image: image-url('mkp/footer/footer_marketplace_sprite.png');
    &.shipping {
      width: 25px;
      background-position: 0px 0px;
    }
    &.payment {
      width: 30px;
      background-position: -38px 0px;
    }
    &.info {
      width: 20px;
      background-position: -83px 0px;
    }
    &.contact {
      width: 28px;
      background-position: -119px 0px;
    }
    &.q-a {
      width: 26px;
      background-position: -160px 0px;
    }
  }
  .section-name {
    margin-top: 0.6em;
    margin-bottom: 0.2em;
    font-size: 0.75em;
    color: $blanco;
    &.q-a {
      width: 120px;
    }
  }
  .section-body {
    margin-bottom: 0em;
    font-size: 0.55em;
    color: $blanco;
  }
}

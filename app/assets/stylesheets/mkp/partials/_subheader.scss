.sub-header-wrapper{
  background: $blanco;
  border-top: 1px solid $gris-claro;
  border-bottom: 1px solid $gris-claro;
  margin-bottom: 15px;
  @media (max-width: #{$topbar-breakpoint - 1px}) {
    display: none;
  }
  .sub-header {
    .sub-header-menu {
      float: left;
      position: relative;
      .categories {
        position: relative;
        margin-bottom: 0;
        margin-top: 2px;
        .category-item {
          font-size: $em-base - 1;
          position: relative;
          border-radius: 3px 3px 0 0;
          h2 {
            cursor: pointer;
            position: relative;
            left: 1px;
            top: 1px;
            margin: 0;
            line-height: 3.6em;
            padding: 0 1em;
            color: $gris;
            font-size: 0.9em;
            text-transform: uppercase;
          }
          @media #{$til-large} {
            font-size: $em-base - 2;
            h2 {
              line-height: 2.8em;
            }
          }
          &:hover, &.active {
            h2 {
              color: $rojo;
            }
          }
          &.active {
            &.with-submenu {
              .category-submenu {
                display: block;
              }
            }
          }
          &.js-submenu-position-fixed {
            .category-submenu {
              border-top-left-radius: 3px;
            }
          }
          .category-submenu {
            @include gp-panel;
            display: none;
            z-index: 50;
            position: absolute;
            top: 102%;
            left: 1px;
            padding: 5px;
            background-color: white;
            border-top-left-radius: 0px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
            h2 {
              position: absolute;
              bottom: 100%;
              top: auto;
              left: 0;
              border-top-left-radius: 3px;
              border-top-right-radius: 3px;
              background-color: white;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
              &:after {
                content: ' ';
                position: absolute;
                left: 0;
                top: 80%;
                width: 100%;
                height: 40%;
                background-color: white;
              }
            }
            .category-column {
              position: relative;
              float: left;
              padding-right: 20px;
            }
            &.columns-1 {
              width: 200px;
              .category-column { width: 100%; }
            }
            &.columns-2 {
              width: 350px;
              .category-column { width: 50%; }
            }
            &.columns-3 {
              width: 600px;
              .category-column { width: 33.33%; }
            }
            &.no-childs {
              width: 250px;
              .category-column { width: 100%; }
              .submenu-item {
                h3 { border-bottom: none; }
              }
            }
            .submenu-item {
              width: 100%;
              padding: 5px;
              li{
                display: block;
                padding:6px 0px;
                line-height: 16.796875px;
              }
              h3 {
                font-weight: bold;
                font-size: 16px;
                line-height: 18px;
                padding-bottom: 4px;
                margin-bottom: 4px;
                font-weight: bold;
              }
              a {
                color: #333;
                text-decoration: none;
                font-size: 14px;
                span.title{
                  font-size: 15px;
                }
                .title{
                  -moz-border-radius: 2px;
                  -webkit-border-radius: 2px;
                  border-radius: 2px;
                }
                &:hover{
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }
}

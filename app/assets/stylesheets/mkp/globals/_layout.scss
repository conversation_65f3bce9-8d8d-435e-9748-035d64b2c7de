// MustExtend class, hides the element
// 'till typekit has finished downloading

.wf-loading %typekit-loading {
  visibility: hidden;
}

@mixin on-typekit-loading {
  .wf-loading &, .wf-inactive & {
    @content;
  }
}

// MustUse: mixin for font-family declaration.
// Sets fonts by default or defined typekit font
@mixin font-family($font: "") {
  @if $font == "" {
    font-family: Helvetica, Arial, sans-serif;
  }
  @if $font != "" {
    @extend %typekit-loading;
    font-family: #{$font}, Helvetica, Arial, sans-serif;
  }
}

body {
  background-color: white;
}

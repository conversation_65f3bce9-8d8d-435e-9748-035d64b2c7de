module UserCreator
  extend self

  def perform(identifier, provider, attrs = {})
    user = case provider
      when 'twitter' then Twitter.perform(identifier, attrs)
      when 'facebook'then Facebook.perform(identifier, attrs)
    end

    user
  end

  protected

  def generate_valid_login(login)
    count = 0
    result = login
    loop do
      break if User.find_by_login(result).blank?
      count += 1
      result = "#{login}#{count}"
    end

    return result
  end

  def generate_password
    SecureRandom.uuid
  end

  module Twitter
    extend UserCreator
    extend self

    def perform(twitter_login, attrs = {})
      user = SocialUser.new
      user.login = potential_login(twitter_login)
      user.email = generate_email(twitter_login)
      user.password = user.password_confirmation = generate_password
      user.roles_mask = 1
      user.network = attrs[:network]

      user.create_profile first_name: attrs[:first_name].to_s,
                          last_name:  attrs[:last_name].to_s

      user.save
      user
    end

    protected

    def potential_login(twitter_login)
      @potential_login ||= generate_valid_login(twitter_login)
    end

    def generate_email(twitter_login)
      "#{twitter_login}@gptwitter.com"
    end
  end

  module Facebook
    extend UserCreator
    extend self


    def perform(email, attrs = {})
      user = SocialUser.new
      user.email = generate_email(email, generate_valid_login(email, attrs))
      user.login = generate_valid_login(email, attrs)
      user.password = user.password_confirmation = generate_password
      user.roles_mask = 1
      user.network = attrs[:network]

      user.create_profile first_name: attrs[:first_name].to_s,
                          last_name:  attrs[:last_name].to_s
      user.save
      user
    end

    protected
    def generate_valid_login(email, attrs)
      potential_login = if email.present?
        email.match(/(.*)@.*/)[1]
      else
        I18n.transliterate (attrs[:first_name].to_s + attrs[:last_name].to_s).downcase.gsub(/ /, '')
      end
      super(potential_login)
    end

    def generate_email(email, login)
      (email.blank? || User.where(email: email).any?) ?
        "#{login}@gpfacebook.com" :
        email
    end
  end

end

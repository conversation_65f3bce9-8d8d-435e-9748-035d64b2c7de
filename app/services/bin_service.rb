class BinService
  def self.get(network)
    bines = Bin.unexpired(network).where("store_id = ?", Mkp::Store.find_by_name("avenida").id)

    result = []

    bines.each do |bin|
      bin_data = {bin: bin.number.to_s, installments: []}
      bin.installments_availables.each do |installment|
        bin_data[:installment] << {number: installment.number, cft: installment.cft.to_f, tea: installment.tea.to_f, coef: installment.coef.to_f}
      end
      result.push bin_data
    end

    result
  end
end
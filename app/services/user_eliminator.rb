########################################################################
########################### USER ELIMINATOR ############################
########################################################################
#                      ______
#                    <((((((\\\
#                    /      . }\
#                    ;--..--._|}
# (\                 '--/\--'  )
#  \\                | '-'  :'|
#   \\               . -==- .-|
#    \\               \.__.'   \--._
#    [\\          __.--|       //  _/'--.
#    \ \\       .'-._ ('-----'/ __/      \
#     \ \\     /   __>|      | '--.       |
#      \ \\   |   \   |     /    /       /
#       \ '\ /     \  |     |  _/       /
#        \  \       \ |     | /        /
#         \  \       \       /
#
# This class from the future is created in order to properly destroy everything
# related to a User when is beign destroyed. It's important to be careful on
# every case so I created this mini doc to review the stuff to be destroyed.
#
########################################################################
######################## MISSING THINGS: ###############################
########################################################################
#
#     - Cached products
#     - Related Shop and all their related stuff
#
########################################################################
#
#
########################################################################
########################## DONE THINGS: ################################
########################################################################
# Related stuff:
#     - Profile                 (destroy dependent from the User)
#     - Addresses               (destroy dependent from the User)
#     - AggregatedProfiles      (destroy dependent from the User)
#     - Albums                  (destroy dependent from the User)
#     - Carts                   (destroy dependent from the User)
#     - Followers / Followings  (destroy dependent from the User)
#     - Whatsups                (destroy dependent from the User)
#     - Albums                  (destroy dependent from the User)
#     - Pictures                (destroy dependent from the Whatsup and Album)
#     - Comments                (destroy dependent from the User, Whatsup and Album)
#
# Isolated stuff:
#     - Notifications (as actor and as user )
#     - CuratedUsers (as the user)
#     - Favorites (Hi Fives as the user and related to their contents)
#     - CuratedFeedItems       (destroy dependent from the Whatsup and Album)
#     - Authentications
#
# Marketplace stuff
#     - ShopAdmins             (destroy dependent from the User)
#     - Questions & Anwsers    (destroy dependent from the User)
#
# Cache stuff
#     - Cached recommendations
#     - Cached feeds
#     - Cached posts (whatsups and albums)
#     - Cached general
#


class UserEliminator
  def self.perform(user)
    UserEliminator.new(user).perform
  end

  def initialize(user)
    @user = user
  end

  def perform
    # Isolated stuff:
    remove_related_notifications
    remove_as_curated_user
    remove_favorites
    remove_authentications

    # Cache stuff
    clear_recommendations_cache
    #clear_feeds_cache
    @user.destroy
  end

  private

  def remove_authentications
    Authentication.where(user_id: @user.id).destroy_all
  end

  def remove_related_notifications
    Notification.where("actor_id = ? OR user_id = ?",
                       @user.id, @user.id).destroy_all
  end

  def remove_as_curated_user
    Social::CuratedUser.destroy_all(user_id: @user.id)
  end

  def remove_favorites
    Favorite.destroy_all(user_id: @user.id)
  end

  def clear_recommendations_cache
    $redis.del("cache:community_follow_recommendations_#{@user.id}")
    clear_cache_where_is_recommended
  end

  def clear_cache_where_is_recommended
    if recommendations_keys = $redis.keys("cache:community_follow_recommendations*").presence
      recommendations_keys.each do |follow_recommendation_key|
        follow_recommendation_key.gsub!(/^cache:/, '')
        recommended_users = Rails.cache.read(follow_recommendation_key)
        recommended_users.delete_if { |u| u.id == @user.id }
        Rails.cache.write(follow_recommendation_key, recommended_users)
      end
    end
  end

  def clear_feeds_cache
    $redis.del("#{Social::HomeFeedCache::VERSION}_home_feed_#{@user.id}")
    ::Social::HomeFeedRefreshWorker.perform_in(10.seconds,
                                               @user.followers.map(&:id))
  end
end

module Import
  class GenerationPackages
    attr_accessor :packages, :row

    PACKAGES = %i[width height weight length].freeze

    def initialize(row)
      @packages = row[:packages] || 1
      @row = row
    end

    def build
      @packages = 1 if @packages.to_i.zero?
      1.upto(@packages.to_i).map{ |i| build_package(i - 1) }
    end

    private

    def build_package(i)
      {}.tap do |hash|
        PACKAGES.each do |field|
          hash[field] = spliter_by(row[field], i)
        end
        hash[:length_unit] = valid_length_unit?(row[:length_unit]) ? row[:length_unit] : Network[Network.default].length_unit
        hash[:mass_unit] = valid_mass_unit?(row[:mass_unit]) ? row[:mass_unit] : Network[Network.default].mass_unit
      end
    end

    def spliter_by(field, i)
      return 0.0 unless field.present?
      field.split('|')[i].to_f
    end

    def valid_length_unit?(unit)
      value = (unit == 'inches' || unit == 'millimeters') ? true : false
    end

    def valid_mass_unit?(unit)
      value = (unit == 'kilograms' || unit == 'pounds') ? true : false
    end
  end
end

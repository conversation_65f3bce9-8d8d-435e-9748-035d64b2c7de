module Import
  # combine 2 string separated by |
  class Combinate
    SPLIT = '|'.freeze
    attr_accessor :keys, :values

    def initialize(keys, values)
      @keys = separate(keys)
      @values = separate(values)
    end

    def params
      return {} unless validate?
      Hash[keys.zip(values)]
    end

    private

    def validate?
      @keys.count == @values.count
    end

    def separate(string)
      string.split(Combinate::SPLIT).map!(&:strip)
    end
  end
end

module Import
  module Splitter

    def process(object, delimiter, &block)
      param = block ? block : :strip
      object.split(delimiter).map(&param)
    end

    def get_hash(keys, values = [])
      values.map do |variant_value|
        variant_value.each_with_index.inject({}) do |h, (value, i)|
          h.merge({ keys[i].downcase.to_sym => normalize(value)})
        end
      end
    end

    def normalize(text)
      text.capitalize
    end
  end
end

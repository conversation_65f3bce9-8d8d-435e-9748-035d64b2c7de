module Import
  class GenerationVariants
    include Splitter
    attr_accessor :keys, :values, :properties, :row

    def initialize(properties_names, property_values, row)
      @keys = process(properties_names, '|')
      @values = [process(property_values,"|")]
      @row = row
    end

    def build
      match.map{ |variant| build_variant(variant) }
    end

    private

    def build_variant(variant)
       build_variant_params(variant)
    end

    def build_variant_params(variant)
      { properties: Hash[ properties_keys.zip(variant) ] }.tap do |hash|
        if variant_present?(variant)
          position = find_variant(variant)
          hash[:quantity] = quantity(position)
          hash[:sku] = sku(position)
          hash[:ean] = ean(position)
          hash[:points_price] = point_price(position)
          hash[:discount_top] = discount_top(position)
        end
      end
    end

    def quantity(i = nil)
      @row[:property_quantity].present? ? process(@row[:property_quantity], '|')[i] : 0
    end

    def point_price(i = nil)
      @row[:property_points_price].present? ? process(@row[:property_points_price], '|')[i] : nil
    end

    def discount_top(i = nil)
      @row[:property_discount_top].present? ? process(@row[:property_discount_top], '|')[i] : nil
    end

    def sku(i = nil)
      @row[:property_sku].present? ? process(@row[:property_sku], '|')[i] : nil
    end

    def ean(i = nil)
      @row[:ean].present? ? process(@row[:ean], '|')[i] : nil
    end

    def variant_present?(variant)
      find_variant(variant).present?
    end

    def find_variant(variant)
      process(@row[:property_values], '|').index{|s| variant.join(" ").include?(normalize(s))}
    end

    def match
      group_by_properties[0].product(*group_by_properties.drop(1))
    end

    def group_by_properties
      properties_keys.map{|propiedad| properties.group_by{|h| h[propiedad]}.keys}
    end

    def properties_keys
      properties.first.keys
    end

    def properties
      @properties ||= get_hash(keys, values)
    end
  end
end

class ProcessNewLabel
  attr_accessor :suborder, :public_id, :label_info
  def initialize(attributes)
    @deliver_status = attributes[:deliver_status]
    @public_id = attributes[:public_id]
    @label_info = attributes.slice(:tracking_number, :courier, :price, :fulfilled_by_gp)
    @current_shop_id = attributes[:current_shop_id]
  end
  def call
    @errors = []
    initialize_public_id
    @suborder = Mkp::Suborder.find(@suborder_id)
    if valid?
      create_label(@suborder, @label_info)
    elsif @suborder.shipment.labels.where(tracking_number: @label_info[:tracking_number]).exists? && @deliver_status == "1" && @current_shop_id == @shop_id
      Mkp::StatusChange::EntityStatusManage.status_change(suborder.shipment, 'delivered')
    else
      Rails.logger.info('Datos inválidos')
      nil
    end
  end
  def create_label(suborder, label_info)
    label = process_other_label(label_info)
    if label.present?
      if @deliver_status == "1"
        Mkp::StatusChange::EntityStatusManage.status_change(suborder.shipment, 'delivered')
      else
        Mkp::StatusChange::EntityStatusManage.status_change(suborder.shipment, 'shipped')
      end
      @suborder.update(fulfilled_by_gp: true) if label_info.dig(:fulfilled_by_gp) == "true"
      Rails.logger.info("Se generó la etiqueta con tracking_number #{@label_info[:tracking_number]}")
      label
    else
      Rails.logger.info("Error al procesar etiqueta con tracking_number #{@label_info[:tracking_number]}")
      nil
    end
  end
  # method extracted from labels_controller
  def process_other_label(params)
    @suborder.shipment.labels.create do |label|
      label.tracking_number = params[:tracking_number].strip
      label.courier = params[:courier].strip
      label.price = params[:price].tr(',', '.').to_f
    end
  end
  def valid?
    if @suborder.shipment.labels.where(tracking_number: @label_info[:tracking_number]).exists?
      return false
    end
    if @suborder.order.id == @order_id && @suborder.shop.id == @shop_id
      return @current_shop_id == nil || @current_shop_id == @shop_id
    end
    false
  end
  def initialize_public_id
    public_data = process_public_id
    if public_data.empty?
      Rails.logger.info('public_id no válido')
      raise StandardError if public_data.empty?
    end
    @suborder_id = public_data[:suborder_id]
    @order_id = public_data[:order_id]
    @shop_id = public_data[:shop_id]
  end
  def process_public_id
    public_id = @public_id.match(/(\d+)-(\d+)-(\d+)/)
    if public_id
      { order_id: public_id[1].to_i, suborder_id: public_id[2].to_i, shop_id: public_id[3].to_i }
    else
      {}
    end
  rescue => error_log
    {}
  end
end

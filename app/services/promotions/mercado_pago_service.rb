module Promotions
  class MercadoPagoService
    attr_reader :gateway

    def initialize
      @gateway = Promotions::MercadoPago.new
    end

    def perform
      attributes = gateway.payment_methods.map do |payment_method|
        @payment_method = payment_method
        gateway.banks(@payment_method['id']).map do |bank|
          @bank = bank
          gateway.installments(payload).map do |installment|
            build_promotion(installment)
          end
        end
      end.flatten
      Mkp::PaymentPromotion.import(attributes)
    end

    private
    def payload
      {
        payment_method_id: @payment_method['id'],
        amount: '10000',
        'issuer.id': @bank['id']
      }
    end

    def build_promotion(installment)
      installment.fetch('payer_costs', []).map do |payer_cost|
        brand = set_brand(installment['payment_method_id'])
        next unless brand.present?
        labels = handle_labels(payer_cost['labels'])
        coefficient = payer_cost['installment_rate'].fdiv(100) + 1
        Mkp::PaymentPromotion.new({
          start: nil,
          expire: nil,
          gateway: 'mercadopago',
          coefficient: coefficient,
          tna: 0.00,
          tea: labels.last.to_f,
          cft: labels.first.to_f,
          brand: brand,
          bank_id: av_bank_id,
          installment: payer_cost['installments'],
          active: coefficient.zero?,
          message: msg(payer_cost),
          legal: nil
        })
      end.flatten.compact
    end

    def handle_labels(labels)
      labels.find do |e|
        (e.start_with?('CFT_') ||  e.start_with?('TEA_')) && e.end_with?('%')
      end.scan(/(\d+[.,]\d+)/).flatten
    end

    def set_brand(payment_method_id)
      return payment_method_id if Mkp::PaymentPromotion.brands.keys.include?(payment_method_id)
      'mastercard' if payment_method_id == 'master'
    end

    def msg(i)
      cant = i['installments']
      cuota = (cant.to_i > 1) ? 'Cuotas' : 'Cuota'
      "#{cant} #{cuota} de $#{i['installment_amount'].to_f}"
    end

    def av_bank_id
      a_name = @bank['name'].upcase
      Mkp::Bank.find_by_name(a_name)&.id || Mkp::Bank.create(name: a_name, is_issuer: false ).id
    end
  end
end

module Promotions
  class TodoPagoService
    API_URL = 'https://forms.todopago.com.ar/t/1.1/api/PaymentMethods/GetPromotions'
    MERCHANT_ID = '348422'
    AMOUNT = '10000'

    attr_reader :key, :gateway

    def initialize(key = 'u4326634b-d293-49f5-b284-659e46f4673f')
      @key = key
      @gateway = Promotions::TodoPago.new
    end

    def perform
      attributes = gateway.payment_methods.map do |payment_method|
        @payment_method = payment_method
        gateway.banks_with(@payment_method['Id']).map do |bank|
          @bank = bank
          promotions[:results].map{|prom| build_promotion(prom)}
        end
      end.flatten
      Mkp::PaymentPromotion.import(attributes)
    end

    private
    def url
      "#{API_URL}/MERCHANT/#{MERCHANT_ID}/PAYMENTMETHOD/#{@payment_method['Id']}/BANK/#{@bank['Id']}/AMOUNT/#{AMOUNT}/PUBLICREQUESTKEY/#{key}"
    end

    def promotions
      promotions = get_data['PromotionsCollection']['Promotion']
      deep_transform(promotions)
    end

    def deep_transform(response)
      data = [response].flatten.map do |element|
        element.deep_transform_keys!{ |key| key.underscore }
      end
      { results: data }
    end

    def get_data
      response = RestClient.get(url)
      body = Hash.from_xml(response.body)
    rescue RestClient::BadRequest => e
      { message: e.message, status: e.http_code }
    end

    def build_promotion(response)
      Mkp::PaymentPromotion.new({
        start: nil,
        expire: nil,
        gateway: 'todopago',
        coefficient: response['coefficient'],
        tna: response['tna'],
        tea: response['tea'],
        cft: response['cft'],
        brand: @payment_method['Name'].underscore.gsub(' ', '_'),
        bank_id: av_bank_id,
        installment: response['installments'],
        active: response['buyer_cf_bonus'],
        message: msg(response['installments']),
        legal: nil
      })
    end

    def msg(installment)
      cuota = (installment.to_i > 1) ? 'Cuotas' : 'Cuota'
      "#{installment} #{cuota} de $"
    end

    def av_bank_id
      Mkp::Bank.find_or_create_by(name: @bank['Name'], code: @bank['Code']).id
    end
  end
end

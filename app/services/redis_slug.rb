module RedisSlug
  extend self

  def save(model)
    network = model.respond_to?(:network) ? model.network : nil
    get_slugs_of(model).each do |slug|
      $redis.set key(model, slug, network), model.id
    end
  end

  def destroy(model)
    network = model.respond_to?(:network) ? model.network : nil
    get_slugs_of(model).each do |slug|
      $redis.del key(model, slug, network)
    end
  end

  def destroy_all(klass = nil, network = nil)
    key = klass.present? ? key(klass, '*', network) : 'slugs:*'
    keys = $redis.keys(key)
    keys.present? ? $redis.del(keys) : 0
  end

  def find(klass, slug, network = nil)
    $redis.get key(klass, slug, network)
  end

  def find_all(klass, network = nil)
    slugs = []
    each_slug(klass, network) { |s| slugs << s }
    slugs
  end

  def each_slug(klass, network = nil, &block)
    keys = $redis.keys key(klass, '*', network)
    keys.each do |k|
      slug = k.split(':')[network.present? ? 3 : 2]
      block.call slug
    end if keys.present?
    keys
  end

  private

  def get_slugs_of(model)
    if model.respond_to?(:full_path_slug)
      [model.full_path_slug]
    elsif model.respond_to?(:slugs)
      model.slugs.pluck(:slug).push(model.slug.parameterize).uniq
    else
      [model.slug.parameterize]
    end
  end

  def key(model, slug, network = nil)
    if network.present?
      "slugs:#{network}:#{table_name(model)}:#{slug}"
    else
      "slugs:#{table_name(model)}:#{slug}"
    end
  end

  def table_name(obj)
    obj.respond_to?(:table_name) ? obj.table_name : obj.class.table_name
  end
end

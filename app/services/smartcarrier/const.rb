module Smartcarrier
  module Const
    CARRIER_MAP = {
      andreani:  {
        delivery:  {
          carrier: "andreani",
          service: "standard",
          mode: "address"
        }
      },
      motonorte: {
         delivery:  {
          carrier: "motonorte",
          service: "priority",
          mode: "address"
        }
      },
      krabpack: {
         delivery: {
          service: "standard",
          mode: "address"
         }
      }
    }.freeze

    CARRIERS = CARRIER_MAP.keys.map{|k| k.to_s.camelcase}

    STATUS_MAP = {
      'DRAFT' => 'unfulfilled',
      'PROCESSED' => 'unfulfilled',
      'IN_TRANSIT' => 'shipped',
      'NOT_DELIVERED' => 'shipped',
      'DELIVERED' => 'delivered',
    }.freeze
  end
end

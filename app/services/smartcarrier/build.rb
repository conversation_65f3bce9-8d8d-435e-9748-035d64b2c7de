module Smartcarrier
  module Build
    def post_data_shipment
      customer = @shipment.order.customer
      public_id = @shipment.items.first.suborder.public_id
      carrier_name = carrier.try(:carrier).try(:to_sym) || :krabpack
      {
        delivery: Smartcarrier::Const::CARRIER_MAP[carrier_name][:delivery],
        declared_value: @shipment.items.to_a.sum(0, &:total),
        reference_id: public_id,
        created_by: "kp-#{@shipment.order.store.name}-001",
        from: {
            warehouse_id: @from.smartcarrier_id.to_s
        },
        to: {
          street_name: @to.address,
          street_number: @to.street_number,
          floor: '',
          apartment: @to.address_2.to_s.gsub(/[^0-9A-Za-z.°-]/, ' ').strip,
          city: @to.city,
          province: @to.state,
          postal_code: @to.zip
        },
        contact: {
          firstname: @to.first_name,
          lastname: @to.last_name,
          personal_id: "",
          company: "",
          phone: @to.telephone,
          mobile: "",
          email: customer.email
        },
        parcels: build_parcels
      }
    end

    def build_parcels
      #centimeters, kilograms
      packages = Mkp::Shipping::Packages.new(@shipment.items).get_package_values(length: :centimeters, mass: :kilograms)

      packages.map do |package|
        [{
          weight: package["peso"].to_f,
          width: package["ancho"].to_f,
          height: package["alto"].to_f,
          length: package["largo"].to_f,
          description: package["sku"]
        }] * package['cantidad'].to_i
      end.flatten
    end

    def set_data
      @to = @shipment.destination_address
      @from = @shipment.origin_address
    end

    def set_purchase_data(response)
      OpenStruct.new( tracking_code: response['label']['tracking_number'],
                      selected_rate: selected_rate(response['cost']),
                      id: response['id'],
                      to_hash: to_hash(response)
                    )
    end

    def selected_rate(total = nil)
      OpenStruct.new(carrier: carrier.name, rate: total)
    end

    def to_hash(data = nil)
      {
        shipment_id: @shipment.id,
        label_id: @id,
        purchase_info: data
      }
    end

    def by_pdf(label)
      suborder = @shipment.suborders.first
      info = label[:gateway_data][:purchase_info][:label]
      to = @shipment.destination_address
      OpenStruct.new(
        tracking_number: info[:tracking_number],
        destination: to.full_name,
        street: "#{to.address} #{to.street_number}",
        floor: to.address_2,
        city: to.city,
        zip:to.zip,
        distribution_branch: info[:distribution_branch],
        barcode: info[:tracking_number]
      )
    end

    def get_package(shipment)
      return {} unless shipment.present?
      Mkp::Shipping::Packager.new(shipment.items).get_package_values(length: :meters, mass: :kilograms)
    end
  end
end

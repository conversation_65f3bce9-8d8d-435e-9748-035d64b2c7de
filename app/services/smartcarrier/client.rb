# frozen_string_literal: true
#
#

require 'rest_client'
module Smartcarrier
  module Client
    class KrabpackUnexpectedResponse < Exception;end

    def determinate_carrier(id)
      if Mkp::Shipment.find(id).order.store_id == 21
        KRABPACK_MACRO_APIKEY
      else
        KRABPACK_APIKEY
      end
    end

    def post_shipments(params)
      request(:post, "#{KRABPACK_URL}/shipments", params.to_json)
    end

    def get_shipments(tracking_number, carrier)
      request(:get, "#{KRABPACK_URL}/shipments?tracking_number=#{tracking_number}&carrier=#{carrier}")
    end

    def get_status_shipment_with(id, tracking_number, carrier)
      request(:get, "#{KRABPACK_URL}/shipments/#{id}?tracking_number=#{tracking_number}&carrier=#{carrier}")
    end

    def delivery_options(params)
      Rails.logger.info(params)
      url = URI("#{KRABPACK_URL}/shippingquote")
      http = Net::HTTP.new(url.host, url.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      request = Net::HTTP::Post.new(url)
      request["apikey"] = SMART_CARRIER_API_KEY
      request["content-type"] = 'application/json'
      request.body = params.to_json
      response = http.request(request)
      result = JSON.parse(response.body)
      result['address'].present? ? result['address'] : []
    rescue JSON::ParserError => e
      []
    end

    private


    def headers
      { apikey: SMART_CARRIER_API_KEY, content_type: :json }
    end

    def request(method, url, params = nil)
      Rails.logger.info(params)
      begin
        response = RestClient::Request.execute( method: method, url: url, payload: params, headers: headers )
        JSON.parse(response.body)
      rescue RestClient::ExceptionWithResponse => err
        nil
      end
    end

    def send_error_notification(error)
      ExceptionNotifier.notify_exception(error)
      return nil
    end
  end
end

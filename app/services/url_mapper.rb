module UrlMapper
  extend self

  PATH_KEY = :path.freeze

  WILDCARD_MODELS = [
    "Pages::Landing",
    "Mkp::Gender",
    "Mkp::Manufacturer",
    "Sport",
    "Mkp::Category",
    "Mkp::Product",
    "Lux::Product",
    "Suggestion"
  ]

  CATALOG_MODELS_ORDER = {
    "Mkp::Gender" => :g,
    "Mkp::Manufacturer" => :b,
    "Sport" => :sp,
    "Mkp::Category" => :c
  }

  def match(params)
    return unless params.key?(PATH_KEY)

    if response = retrieve(params[PATH_KEY], params[:network])
      normalize_params(params, response)
    end
  end

  def map_out(models, network = nil)
    models = Array.wrap(models)
    single_models = single_models_only(models)

    return unless (path = get_path(single_models)).present?

    opts = set_options(single_models)

    set(path, opts, network)
  end

  def purge_map(models, network = nil)
    models = Array.wrap(models)

    return unless (path = get_path(models)).present?

    purge(path, network)

    path
  end

  def purge_path(paths, network = nil)
    paths = Array.wrap(paths)

    paths.each { |path| purge(path, network) }
  end

  def path_exist?(models, network = nil)
    models = Array.wrap(models)
    single_models = single_models_only(models)

    return false unless (path = get_path(single_models)).present?

    retrieve(path, network).present?
  end

  private

  def retrieve(path, network)
    RedisUrl.find(path, network)
  end

  def set(path, opts, network)
    RedisUrl.save(path, opts[:params], network, opts[:controller], opts[:action])
    retrieve(path, network)
  end

  def purge(path, network)
    RedisUrl.destroy(path, network)
  end

  def normalize_params(params, response)
    response = JSON.parse(response).deep_symbolize_keys
    params[:controller] = response[:controller]
    params[:action] = response[:action]
    params.merge!(response[:params])
  end

  def set_options(models)
    if model = detect_model(models, 'Product')
      set_product_options(model)
    elsif model = detect_model(models, 'Landing')
      set_landing_options(model)
    elsif model =  detect_model(models, 'Suggestion')
      set_suggestion_options(model)
    else
      set_catalog_options(models)
    end
  end

  def set_landing_options(landing)
    {
      controller: LandingsController,
      action: :show,
      params: {
        id: landing.id
      }
    }
  end

  def set_catalog_options(models)
    {
      controller: Mkp::CatalogController,
      action: :index,
      params: {}
    }.tap do |opts|
      models.each do |model|
        opts[:params]["#{CATALOG_MODELS_ORDER[model.class.name]}"] = model.id
      end
    end
  end

  def set_suggestion_options(suggestion)
    {
      controller: Mkp::CatalogController,
      action: :index,
      params: suggestion[:data]
    }
  end


  def set_product_options(product)
    {
      controller: Mkp::VariantsController,
      action: :show,
      params: {
        network: product.network.downcase,
        manufacturer_id: product.manufacturer.slug,
        product_slug: product.slug
      }
    }
  end

  def detect_model(models, class_name)
    models.detect { |m| m.class.name.demodulize == class_name }
  end

  def get_path(models)
    if (WILDCARD_MODELS & models.map { |m| m.class.name }).present?
      UrlMapperHelper.mapper_path(models)
    end
  end

  def single_models_only(models)
    models.group_by{|model| model.class.name }.select do |klass, group|
      group.length == 1
    end.values.flatten
  end
end

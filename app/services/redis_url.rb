module RedisUrl
  extend self

  def find(slug, network = nil)
    $redis.get key(slug, network)
  end

  def save(slug, params, network = nil, controller = Mkp::CatalogController, action = :index)
    data = {
      params: params,
      controller: controller.controller_path,
      action: action
    }

    $redis.set key(slug, network), data.to_json
  end

  def destroy(slug, network = nil)
    $redis.del key(slug, network)
  end

  private

  def key(slug, network = nil)
    if network.present?
      "url:#{network.upcase}:#{slug}"
    else
      "url:#{slug}"
    end
  end

end

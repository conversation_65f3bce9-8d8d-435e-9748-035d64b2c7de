require 'net/http'

module Macro
  class ValidateUserMacro
    attr_accessor :token, :user, :valid, :user_info

    def initialize(params, current_store)
      @macro_token = params[:macro_token]
      @current_store = current_store
      @valid = false
    end

    def perform
      uri = URI("#{MACRO_LOGIN_ENDPOINT}/validate?macro_token=#{@macro_token}")
      http = Net::HTTP.new(uri.host, uri.port)

      request = Net::HTTP::Get.new(uri.request_uri)
      request['api-key'] = MACRO_LOGIN_API_KEY
      response = http.request(request)
      response = JSON.parse(response.body)

      if response.has_key?('error')
        @valid = false
      else
        if response['valid']
          login_or_create_user(response['user_info'])
          @valid = @user.present? ? true : false
          @user_info = response['user_info']
        else
          valid = false
        end
      end

      @valid
    end

    private

    def login_or_create_user(user_info)
      mail_by_idcobis = MailByIdcobi.find_by_idcobis (user_info['client_code'])
      email = (mail_by_idcobis ? mail_by_idcobis.email : "#{user_info['client_code']}@macropremia.com")
      @user = @current_store.customers.find_by(uuid: user_info['client_code']) # build email from prisma user_code@avenida

      unless @user.present?
        fullname = user_info['fullname'].split(',')
        password = Devise.friendly_token[0, 7]
        macro_category = user_info['is_select_user'] ? 'selecta' : 'regular'

        @user = @current_store.customers.create({
                                                  email: email,
          password: password,
          password_confirmation: password,
          last_name: fullname[0].strip,
          first_name: fullname[1].strip,
          provider: 'macro_premia',
          uuid: user_info['client_code'],
          temporary_email: !mail_by_idcobis.present?,
          macro_category: macro_category
                                                })
      end
    end

  end
end
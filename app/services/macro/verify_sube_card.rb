require 'net/http'

module Macro
	class VerifySubeCard
		attr_accessor :card_number, :valid, :message

		def initialize(card_number)
			@card_number = card_number
			@valid = false
		end

		def perform
			url = URI("#{SUBE_URL}cards/verify?card_number=#{@card_number}")
			http = Net::HTTP.new(url.host, url.port)

			request = Net::HTTP::Get.new(url.request_uri)

			response = http.request(request)
			response = JSON.parse(response.body)

			if response.has_key?('success')
				@valid = response['success']
				@message = response['message']
			else
				@message = 'Error validando tarjeta'
			end
		end
	end
end
require 'rest_client'

module Irsa
  module Transaction
    include Irsa::Base
    include Irsa::Log
    extend self
    def set_order(order, dni)
      data = build_transaction(order, dni)
      logger_title(order, data)
      response = request_auth(:post, "#{GATEWAY_URL}/api/transacciones/generar", data.to_json)
      logger_response(response)
      unless response # Send an email when a transaction fails.
        notify_mailer(data)
      end
    end

    def build_transaction(order, dni)
      address = order.customer.addresses.last
        {
          "idTransaccionAve" => order.id.to_s,
          "monto" => order.total.to_i,
          "fechaCreacion" => order.created_at.to_s,
          "estadoTransaccion" => order.payment.status,
          "lugarRetiro" => address.address_2,
          "idClienteAve" => order.customer.id,
          "documento" => dni || '0' ,
          "nombre" => address.full_name,
          "telefono" => address.telephone,
          "email" => order.customer.email,
          "idShippingAve" => order.shipments.last.id,
          "documentoRetira" => dni || '0' ,
          "nombreRetira" => address.full_name,
          "telefonoRetira" => address.telephone,
          "emailRetira" => order.customer.email,
          "observacionRetira" => '',
          "productos" => build_products(order)
        }
    end

    def build_products(order)
      order.items.map do |item|
        {
          "codigo" => item.variant.sku,
          "cantidad" => item.quantity,
          "precioUnitario" => item.unit_price_charged.to_i
        }
      end
    end

    def notify_mailer(data)
      current_store = Mkp::Store.find_by_name("chequeregalo")
      Stores::ErrorMailer.throw_error_in_store("Fallo Solicitud de transacción", "No se genero transacción para #{data.inspect}", current_store).deliver_later
    end
  end
end

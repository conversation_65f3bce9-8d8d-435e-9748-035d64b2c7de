require 'rest_client'

module Irsa
  module Base
    GATEWAY_URL = IRSA_CHEQUEREGALO['url']
    AUTH_URL = GATEWAY_URL + '/connect/token'
    KEYS = IRSA_CHEQUEREGALO['keys']

    private

    def header
      @header ||= {Authorization: "#{@token.token_type} #{@token.access_token}", "Content-type": @token.content_type || "application/x-www-form-urlencoded" } if @token.present?
    end

    def authorization
      oauth unless @token.present?
    end

    def oauth
      response = request(:post, AUTH_URL, KEYS)
      token(response)
    end

    def token(params)
      @token ||= OpenStruct.new(params.merge({content_type: "application/json; charset=utf-8"}))
    end

    def request_auth(method, path_info, params = nil)
      authorization
      request(method, path_info, params)
    end

    def request(method, path_info, params = nil)
      response = RestClient::Request.execute( method: method, url: path_info, payload: params, headers: header )
      if path_info.include?("api/transacciones/generar")
        return response.code == 200
      end
      JSON.parse(response.body)
    rescue => exception
      current_store = Mkp::Store.find_by_name("chequeregalo")
      Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, current_store).deliver_later
    end
  end
end

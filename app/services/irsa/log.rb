module Irsa
  module Log
    extend ActiveSupport::Concern

    def checkout_log
      date = Date.today.strftime("%Y%m%d")
      @checkout_log ||= Logger.new("#{Rails.root}/log/chequeregalo-orders-#{date}.log")
    end

    def logger_title(order, params)
      checkout_log.info("TRACE_BULLET : @#{"="*100}@")
      checkout_log.info("TRACE_BULLET : @#{"-"*40} Order: #{ order.id } #{"-"*40}@")
      checkout_log.info("TRACE_BULLET : @#{"="*100}@")
      checkout_log.info("TRACE_BULLET : Parametros:")
      checkout_log.info("TRACE_BULLET : #{params.inspect}")
    end


    def logger_response(response = nil)
      checkout_log.info("TRACE_BULLET : @#{"-"*40} TRACING: Response #{"-"*40}@")
      checkout_log.info(response.inspect)
      checkout_log.info("TRACE_BULLET : ")
    end
  end
end

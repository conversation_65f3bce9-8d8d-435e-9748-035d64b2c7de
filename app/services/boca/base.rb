require 'rest_client'

module Boca
  module Base
    GATEWAY_URL = BOCA_URL

    private
    def header
      { content_type: :json }
    end

    def request(method, path_info, params = nil)
      response = RestClient::Request.execute( method: method, url: path_info, payload: params, headers: header ,ssl_ca_file: "lib/certificates/bocajrs.crt", verify_ssl: false )
      body = JSON.parse(response.body)
      body.deep_transform_keys! { |key| key.underscore }
    rescue => exception
      store = Mkp::Store.find_by_name("tddboca")
      Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, store).deliver_later
    end
  end
end

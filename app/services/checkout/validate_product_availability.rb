module Checkout
  class ValidateProductAvailability
    attr_accessor :valid, :error

    def initialize(items, store)
      @items = items
      @store = store
      @valid = true
    end

    def perform # rubocop:disable Metrics/MethodLength
      return if @items.blank?

      @items.each do |item|
        variant = Mkp::Variant.find item[:variant_id]
        if variant.present?
          unless valid_variant?(variant)
            @valid = false
            @error = "Producto sin stock: #{variant.product.title}"
          end
        else
          @valid = false
          @error = 'Error verificando stock'
        end
      end
    end

    def valid_variant?(variant)
      skip_validation = @store.id == 46 && variant.product.category&.id == 3346
    
      requires_validation = variant.product.reservable? &&
                            variant.product.third_party_code_required &&
                            !skip_validation
    
      valid = !requires_validation || third_party_code_validation?(variant)
    
      variant.product.available? && variant.quantity.positive? && valid
    end

    def third_party_code_validation?(variant)
      ThirdPartyCode.where(store: @store,
                           manufacturer: variant.product.manufacturer,
                           available: true).count.positive?
    end
  end
end

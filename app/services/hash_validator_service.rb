class HashValidatorService
  attr_reader :error

  VALID_LENGTH = { 'cuil' => 11, 'fecha' => 8 }.freeze
  PARAMS_ORDER = %w[cuil idSolicitud nombre apellido monto Sucursal email programa perfil fecha].freeze

  def initialize(params)
    @params = params
    @params_encoded = encode_params
    @error = ""
  end

  def invalid_params
    return true unless valid_param?("hash", @params["hash"])
    valid = @params.except(:hash).map { |key, value| valid_param?(key, value) }.uniq
    valid.include? false
  end

  def build_json
    bna_info = {}
    @params.each { |key, value| bna_info[key] = value }

    bna_info
  end

  private

  def encode_params
    params_encoded = {}
    @params.each { |key, value| params_encoded[key] = URI.encode(value) }
    params_encoded
  end

  def valid_param?(key, value)
    special_validations = %w[cuil sucursal fecha email hash]
    if value.blank?
      @error = "Error en #{key}, sin valor"
      return false 
    end
    key = key.downcase
    return true unless special_validations.include?(key)

    ans = send("valid_#{key}?", value, key)
    @error = "Error en #{key}, #{value}" unless ans
    ans
  end

  def valid_cuil?(cuil, key)
    cuil.length == VALID_LENGTH[key]
  end

  def valid_fecha?(date, key)
    date.length == VALID_LENGTH[key] && !HashValidatorService.expired_date?(date)
  end

  def self.expired_date?(date)
    (DateTime.now.to_date - 7.days) >= Date.strptime(date, "%Y%m%d")
  end

  def valid_sucursal?(store_id, _key = nil)
    ::Mkp::Bna::Office.where(token: store_id).exists?
  end

  def valid_email?(email, _key = nil)
    regex = /^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$/.freeze
    (email =~ regex).present?
  end

  def valid_hash?(hash, _key = nil)
    build_query.include? hash
  end

  def build_query
    query = query_encoded = ''
    PARAMS_ORDER.each do |key|
      if key != 'hash'
        query += "#{key}=#{@params[key]}&"
        query_encoded += "#{key}=#{@params_encoded[key]}&"
      end
    end
    query = (query.chomp('&') + BNA_SALT).gsub(' ', '%20')
    query_encoded = (query_encoded.chomp('&') + BNA_SALT).gsub(' ', '%20')
    [Digest::SHA2.new(256).hexdigest(query), Digest::SHA2.new(256).hexdigest(query_encoded)]
  end
end

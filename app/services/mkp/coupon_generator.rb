# WARN: It creates network coupons only
module Mkp
  class CouponGenerator
    CODE_SIZE     = 6

    def self.perform_for(network, options = {}, restrictions = {})
      coupons = []
      (options[:quantity] || 1).to_i.times do
        coupon_generator = new(network, options, restrictions)
        coupons << coupon_generator.perform
      end
      coupons
    end

    def initialize(network, opts = {}, restrictions = {})
      @network      = network
      @coupon       = Mkp::Coupon::Network.new
      @opts         = opts
      @restrictions = restrictions
    end

    def perform
      assign_code
      set_policy_fields
      set_fields
      set_restrictions
      @coupon.save!

      @coupon
    end

    protected

    def assign_code
      code = generate_code(@opts[:prefix])
      while Mkp::Coupon.where(code: code).any? do
        code = generate_code
      end
      @coupon.code = code
    end

    def generate_code(prefix = nil)
      [ prefix, random_code ].compact.join
    end

    def set_fields
      @coupon.discount_limit          = @opts[:discount_limit]          || 0
      @coupon.minimum_value           = @opts[:minimum_value]           || minimum_value_default
      @coupon.description             = @opts[:description]             || "Auto generated"
      @coupon.network                 = @network
      @coupon.starts_at               = @opts[:starts_at]               || Time.now
      @coupon.expires_at              = @opts[:expires_at]              || 1.month.from_now
      @coupon.total_available         = @opts[:total_available]         || 1
      @coupon.repetitive_amount_limit = @opts[:repetitive_amount_limit] || 1
      @coupon.apply_on_sale           = @opts[:apply_on_sale]           || false
      @coupon.store_id                = @opts[:store_id]
    end

    def set_policy_fields
      @coupon.percent = @opts[:percent] || percent_default
      @coupon.amount  = @opts[:amount]  || amount_default
      @coupon.policy  = @opts[:policy]  || 'percent'
    end

    def percent_default
      Network[@network].coupon_defaults['percent']
    end

    def amount_default
      Network[@network].coupon_defaults['amount']
    end

    def minimum_value_default
      Network[@network].coupon_defaults['minimum_value']
    end

    def set_restrictions
      @coupon.restrictions = @restrictions
    end

    private

    def random_code
      Array.new(8){[*'0'..'9', *'A'..'Z'].sample}.join
    end
  end
end

module Mkp
  module Shipping
    module RatesCalculator
      class Easypost
        def rates_for(items, destination, shipping_method)
          package_values = Mkp::Shipping::Packager.new(items).get_package_values(mass: :ounces)
          opts = { to_address: adapt(destination),
                   from_address: adapt(shipping_method.zone.warehouse),
                   options: {currency: 'USD'},
                   parcel: package_values.except(:volume)
                 }.merge(customs_info(items, destination, shipping_method))

          get_rates(opts, shipping_method)
        end

        def get_rate(shipment_id, rate_id)
          rate = EasyPost::Shipment.retrieve(shipment_id).rates.find do |r|
            r[:id] == rate_id
          end
          speed = EasypostLevelsMapper.get_level_for(rate['carrier'].downcase, rate['service'])
          speed_title = I18n.t('mkp.checkout.speeds.' + speed, carrier: rate['carrier'])

          ::Mkp::Shipping::Rate.new(rate['rate'], speed, speed_title, rate['carrier'])
        end

        protected

        def get_rates(options, method)
          rates = create_easypost_shipment(options)
          rates = filter_carriers(rates, method.carriers)
          filter_speeds(rates, method.speeds)
        end

        def create_easypost_shipment(options)
          EasyPost::Shipment.create(options)['rates']
        end

        def adapt(address)
          Mkp::Address::EasypostFormat.for(address)
        end

        def filter_carriers(rates, carriers)
          rates.select {|rate| carriers.include?(rate.carrier.downcase) }
        end

        def filter_speeds(rates, speeds)
          speeds.map do |speed|
            if (rate = cheapeast(EasypostLevelsMapper.apply(rates, speed))).present?
              ::Mkp::Shipping::Rate.new(
                rate['rate'],
                I18n.t('mkp.checkout.speeds.' + speed, carrier: rate['carrier']),
                speed,
                rate['carrier'],
                rate['service'],
                rate['shipment_id'],
                rate['id'])
            end
          end.compact
        end

        def customs_info(items, destination, shipping_method)
          return {} if destination.country == shipping_method.warehouse.country
          {
            customs_info:  EasyPost::CustomsInfo.create(
              eel_pfc: 'NOEEI 30.37(a)',
              customs_certify: true,
              customs_signer: shipping_method.shop.customs_signer_name,
              contents_type: 'merchandise',
              contents_explanation: '',
              restriction_type: 'none',
              restriction_comments: '',
              non_delivery_option: 'return',
              customs_items: customs_items(items, shipping_method.warehouse.country))
            }
        end

        def cheapeast(rates)
          rates.min_by{|r| r.rate.to_f }
        end

        def customs_items(items, origin_country)
          items.map { |item|
            { 'description' => item.title,
              'quantity' => item.quantity,
              'weight' => (item.product.weight.to_f > 0 ? item.product.weight_with_unit : Mkp::Shipping::Packager::DEFAULT_WEIGHT).to.ounces.to_f.round(2),
              'value' => item.price.to_f,
              'hs_tariff_number' => item.hs_tariff_number,
              'origin_country' => origin_country
            }
          }
        end

        module EasypostLevelsMapper
          extend self
          CARRIERS_SERVICES_BY_LEVELS =
            {
              '1day' =>
                {
                  'ups' =>   ['NextDayAirSaver', 'NextDayAir', 'NextDayAirEarlyAM'],
                  'usps' =>  ['Express'],
                  'fedex' => ['STANDARD_OVERNIGHT', 'PRIORITY_OVERNIGHT', 'FIRST_OVERNIGH']
                },
              '2days' =>
                {
                  'ups' =>   ['2ndDayAir', '2ndDayAirAM'],
                  'usps' =>  ['Priority'],
                  'fedex' => ['FEDEX_2_DAY_AM', 'FEDEX_2_DAY']
                },
              '3days' =>
                {
                  'ups' =>   ['3DaySelect'],
                  'usps' =>  ['First'],
                  'fedex' => ['FEDEX_EXPRESS_SAVER']
                },
              'ground' =>
                {
                  'ups' =>   ['Ground'],
                  'usps' =>  ['ParcelSelect'],
                  'fedex' => ['FEDEX_GROUND']
                },
              'intl_priority' =>
                {
                  'ups' =>   ['Expedited'],
                  'usps' =>  ['FirstClassPackageInternationalService', 'PriorityMailInternational'],
                  'fedex' => ['INTERNATIONAL_ECONOMY']
                },
              'intl_express' =>
                {
                  'ups' =>   ['UPSSaver'],
                  'usps' =>  ['ExpressMailInternational'],
                  'fedex' => ['INTERNATIONAL_PRIORITY']
                }
            }.freeze

          USPS_DOMESTIC_SERVICES_SPEED_ORDER = [
            'Express',
            'Priority',
            'First',
            'ParcelSelect'
          ].freeze
          FEDEX_DOMESTIC_SERVICES_SPEED_ORDER = [
            'FIRST_OVERNIGH',
            'PRIORITY_OVERNIGHT',
            'STANDARD_OVERNIGHT',
            'FEDEX_2_DAY_AM',
            'FEDEX_2_DAY',
            'FEDEX_EXPRESS_SAVER',
            'FEDEX_GROUND'
          ].freeze
          UPS_DOMESTIC_SERVICES_SPEED_ORDER = [
            'NextDayAirEarlyAM',
            'NextDayAir',
            'NextDayAirSaver',
            '2ndDayAirAM',
            '2ndDayAir',
            '3DaySelect',
            'Ground'
          ].freeze

          def apply(rates, speed)
            rates.select do |rate|
              CARRIERS_SERVICES_BY_LEVELS[speed].values.flatten.include?(rate['service'])
            end
          end

          def get_level_for(carrier, service)
            levels = CARRIERS_SERVICES_BY_LEVELS.keys
            service_levels = {}
            levels.map do |level|
              service_levels[level] = CARRIERS_SERVICES_BY_LEVELS[level][carrier]
            end

            service_levels.detect{ |level, services| services.include?(service) }.try(:first)
          end
        end
      end
    end
  end
end

module Mkp
  module Shipping
    module RatesCalculator
      class Fixed
        def rates_for(items, destination, shipping_method)
          value = shipping_method.states.try(:any?) ?
            shipping_method.states[destination.state] :
            shipping_method.price

          title = shipping_method.title

          [::Mkp::Shipping::Rate.new(ensure_a_number(value), title)]
        end

        private

        def ensure_a_number(data)
          return data unless data.kind_of? String
          data.gsub(/[^0-9\.]/, '').to_f
        end
      end
    end
  end
end

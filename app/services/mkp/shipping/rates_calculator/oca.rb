# encoding: utf-8
module Mkp
  module Shipping
    module RatesCalculator
      class Oca
        def initialize(price_table = nil, default_price = 100)
          @price_table = price_table || default_price_table
          @default_price = default_price
        end

        def rates_for(items, destination, shipping_method)
          value = @price_table[destination.state] || @default_price
          title = shipping_method.title

          [ ::Mkp::Shipping::Rate.new(value, I18n.t("mkp.checkout.v5.partials.shipping_methods.estimated_delivery_times.standard", locale: :es), "standard") ]
        end

        private

        def default_price_table
          {
            'Capital Federal'=>       169,
            'GBA'=>                   199,
            'Bs As Interior'=>        219,
            'Catamarca'=>             395,
            'Chaco'=>                 279,
            'Chubut'=>                395,
            'Córdoba'=>               239,
            'Corrientes'=>            269,
            'Entre Ríos'=>            239,
            'Formosa'=>               395,
            'Jujuy'=>                 395,
            'La Pampa'=>              269,
            'La Rioja'=>              395,
            'Mendoza'=>               269,
            'Misiones'=>              279,
            'Neuquén'=>               279,
            'Río Negro'=>             279,
            'Salta'=>                 395,
            'San Juan'=>              395,
            'San Luis'=>              395,
            'Santa Cruz'=>            395,
            'Santa Fe'=>              239,
            'Santiago del Estero'=>   270,
            'Tierra del Fuego'=>      395,
            'Tucumán'=>               279
          }
        end
      end
    end
  end
end

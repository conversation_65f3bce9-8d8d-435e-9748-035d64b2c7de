module Mkp
  module Shipping
    class Rates
      def self.calculate(items, destination, shipping_method, service_options = {})
        calculator = choose_calculator(shipping_method)
        calculator.rates_for(items, destination, shipping_method)
      end

      private

      def self.choose_calculator(shipping_method)
        service_name = shipping_method.service
        class_name = "::Mkp::Shipping::RatesCalculator::#{service_name}"
        class_name.constantize.new
      end
    end
  end
end

module Mkp
  module Purchase<PERSON><PERSON>le<PERSON>
    extend self

    TWO_PRODUCTS_TITLE_SIZE = 16
    MORE_THAN_TWO_PRODUCTS_TITLE_SIZE = 19

    def perform(title, quantity, network)
      return if title.blank?

      I18n.t(
        'mkp.purchase_titleizer',
         locale: Network[network].locale,
         first: sanitize_title(title, quantity),
         count: quantity,
         number_more: quantity - 1
      )
    end

    protected

    def sanitize_title(title, quantity)
      case quantity
        when 1 then title
        when 2 then title[0, TWO_PRODUCTS_TITLE_SIZE]
        else
          title[0, MORE_THAN_TWO_PRODUCTS_TITLE_SIZE]
      end
    end
  end
end

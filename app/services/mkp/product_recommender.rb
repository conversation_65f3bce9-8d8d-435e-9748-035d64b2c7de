module Mkp
  # This module's responsibility is to find products related to social content.
  module ProductRecommender
    def self.recommend(feed_item, network)
      slots = 2
      products = []

      fill_with_products_by_shop(products, feed_item, slots, network)
      fill_with_products_by_manufacturer(products, feed_item, slots, network)
      fill_with_products_by_sports(products, feed_item, slots, network)
      fill_with_products_by_default_shop(products, slots, network)

      products.uniq.map { |p| p.variants.first }
    end

    private

    def self.fill_with_products_by_shop(products, feed_item, slots, network)
      if products.count < slots
        remaining_slots = slots - products.count
        shop = feed_item.author.owned_shop

        if shop && shop.network == network
          products.concat(shop.products
                              .joins(:variants)
                              .active
                              .with_stock
                              .random(remaining_slots))
        end
      end
    end

    def self.fill_with_products_by_manufacturer(products, feed_item, slots, network)
      if products.count < slots
        remaining_slots = slots - products.count
        author = feed_item.author
        manufacturer = author.respond_to?(:manufacturer) && author.manufacturer

        if manufacturer
          products.concat(manufacturer.products
                                      .joins(:variants)
                                      .active
                                      .with_stock
                                      .by_network(network)
                                      .random(remaining_slots))
        end
      end
    end

    def self.fill_with_products_by_sports(products, feed_item, slots, network)
      if products.count < slots && feed_item.sports.present?
        remaining_slots = slots - products.count

        products.concat(Product.joins(:variants)
                               .active
                               .with_stock
                               .by_network(network)
                               .with_sports(feed_item.sports)
                               .random(remaining_slots))
      end
    end

    def self.fill_with_products_by_default_shop(products, slots, network)
      if products.count < slots
        remaining_slots = slots - products.count
        shop = Mkp::Shop.find_by_id(Network[network].default_shop_id)

        if shop
          products.concat(shop.products
                              .joins(:variants)
                              .active
                              .with_stock
                              .random(remaining_slots))
        end
      end
    end
  end
end

module Mkp
  module Catalog
    module <PERSON><PERSON><PERSON><PERSON><PERSON>
      extend self

      PARAMS = [
        :query,  # params[:query] - query
        :p,      # params[:p] - page
        :b,      # params[:b] - manufacturer (slug or ID)
        :g,      # params[:g] - gender (slug or ID)
        :c,      # params[:c] - category (slug or ID)
        :d,      # params[:d] - on_sale
        :sh,     # params[:sh] - shop (slug or ID)
        :clr,    # params[:clr] - color
        :pr,     # params[:pr] - price - Format: 'MIN..(MAX)'
        :sz,     # params[:sz] - size
        :sp,     # params[:sp] - sport (slug or ID)
        :o,      # params[:o] - Order By, is a compound %(ATTR-ORDER)
      ]

      SLUGS_MAX = 4
      SLUGS_KEYS = (1..SLUGS_MAX).to_a.map { |s| "slug_#{s}".to_sym }
      SLUGS_MODELS = [::Mkp::Gender,
                      ::Mkp::Manufacturer,
                      ::Sport,
                      ::Mkp::Category]
      SLUGS_PARAMS = [:g, :b, :sp, :c]
      SOLR_FACETS = [:genders_ids,
                     :manufacturer_id,
                     :sports_ids,
                     :categories_ids]

      SLUGS_NETWORK = []
      SLUGS_MODELS.each do |m|
        has = (m.instance_methods + m.column_names).grep(/network/).present?
        SLUGS_NETWORK << has
      end

      def parse_slugs(params)
        valid_url = true
        models_to_search = SLUGS_MODELS.dup

        each_slug(params) do |slug, slug_key|
          models_to_search.each do |klass|
            network = has_network(klass) ? params[:network].upcase : nil
            id = RedisSlug.find(klass, slug, network)
            if id.present?
              models_to_search.delete(klass)
              prepend_value!(params, param_of(klass), slug)
              params.delete(slug_key)
              break
            else
              valid_url = false if klass == models_to_search.last
            end
          end
        end

        valid_url
      end

      def path(pr = {}, options = {})
        params = pr.deep_dup.symbolize_keys
        params.except!(:action, :controller)
        slugs_keys = SLUGS_KEYS.dup

        if params[:network].blank?
          raise ActionController::RoutingError, "Needs a 'network' parameter"
        end

        if options[:slugify] != false
          SLUGS_PARAMS.each do |param|
            break if slugs_keys.blank?
            next if params[param].blank? || params[param].is_a?(Array)
            params[slugs_keys.shift] = pluck_first_value!(params, param)
          end
        end

        if has_slugs(params)
          url_helpers.mkp_slugged_catalog_path(params)
        else
          url_helpers.mkp_catalog_root_path(params)
        end
      end

      def url(*args)
        "http://#{HOSTNAME}#{path(*args)}"
      end

      def canonical_url(*args)
        p = PARAMS - [:p, :s, :o] + [:network]
        p.map!(&:to_s)
        args[0] = args[0].slice(*p).deep_dup
        url(*args)
      end

      def each_slugged_url_for_sitemap(&block)
        gender_excluded = Mkp::Gender.all.map!{|g| g.slug if g.ancestry.nil? }.compact
        Network.all_full.each do |network|
          models_pending = SLUGS_MODELS.dup
          params = { network: network.downcase }
          search = Mkp::Catalog::Finder.find(params)
          shop_slugs = []

          SLUGS_MODELS.each do |klass|
            models_pending.delete(klass)
            _models_pending = models_pending.dup

            facet = facet_of(klass)
            next if search.facet(facet).blank?
            ids = search.facet(facet).rows.map(&:value)
            slugs = get_all_slugs(klass, ids)
            shop_slugs |= slugs if klass == Mkp::Shop
            p = params.dup
            param = param_of(klass)

            slugs.each do |s|
              next if klass == Mkp::Gender && gender_excluded.include?(s)
              next if klass == Mkp::Manufacturer && shop_slugs.include?(s)
              p[param] = s
              block.call url(p)

              _search = Mkp::Catalog::Finder.find(p)
              _shop_slugs = []
              _models_pending.each do |_klass|
                _models_pending.delete(_klass)
                _facet = facet_of(_klass)
                next if _search.facet(_facet).blank?
                _ids = _search.facet(_facet).rows.map(&:value)

                _slugs = get_all_slugs(_klass, _ids)
                _shop_slugs |= _slugs if _klass == Mkp::Shop

                _p = p.dup
                _param = param_of(_klass)

                _slugs.each do |_s|

                  next if (_klass == Mkp::Manufacturer && _shop_slugs.include?(_s)) || s == _s
                  _p[_param] = _s
                  block.call url(_p)

                  __shop_slugs = []
                  __search = Mkp::Catalog::Finder.find(_p)
                  _models_pending.each do |__klass|
                    __facet = facet_of(__klass)
                    next if __search.facet(__facet).blank?
                    __ids = __search.facet(__facet).rows.map(&:value)
                    __slugs = get_all_slugs(__klass, __ids)
                    __shop_slugs |= __slugs if __klass == Mkp::Shop

                    __p = _p.dup
                    __param = param_of(__klass)

                    __slugs.each do |__s|
                      next if (__klass == Mkp::Manufacturer && __shop_slugs.include?(__s)) || _s == __s
                      __p[__param] = __s
                      block.call url(__p)
                    end
                  end
                end
              end
            end
          end
        end
      end

      private

      def get_all_slugs(klass, ids = [])
        if klass == Mkp::Category
          klass.where(id: ids).map(&:full_path_slug)
        else
          klass.where(id: ids).pluck(:slug)
        end
      end

      def has_slugs(params)
        params[SLUGS_KEYS.first].present?
      end

      def each_slug(params, &block)
        SLUGS_KEYS.each do |k|
          break if params[k].nil?
          block.call params[k], k
        end
        params
      end

      def url_helpers
        Rails.application.routes.url_helpers
      end

      def param_of(klass)
        SLUGS_PARAMS[SLUGS_MODELS.index(klass)]
      end

      def facet_of(klass)
        SOLR_FACETS[SLUGS_MODELS.index(klass)]
      end

      def has_network(klass)
        SLUGS_NETWORK[SLUGS_MODELS.index(klass)]
      end

      def prepend_value!(hash, key, value)
        if hash[key].blank?
          hash[key] = value
        elsif hash[key].is_a?(String)
          hash[key] = [value, hash[key]]
        elsif hash[key].is_a?(Array)
          hash[key] = hash[key].unshift value
        end
        hash
      end

      def pluck_first_value!(hash, key)
        if hash[key].is_a?(Array)
          value = hash[key].shift
          hash[key] = hash[key].first if hash[key].length == 1
          hash.delete(key) if hash[key].length == 0
        else
          value = hash[key]
          hash.delete(key)
        end
        value
      end

      def base_catalog_path(network)
        @base_catalog_path ||= url_helpers.mkp_catalog_root_path(network: network)
      end
    end
  end
end

module Mkp
  module Catalog
    class CustomRange < Struct.new(:minimum, :maximum)
      def has_max_limit?
        maximum.present?
      end

      def max
        maximum || Float::INFINITY
      end

      def min
        minimum || 0
      end

      def to_s
        return "#{min}..#{max}" if has_max_limit?
        "#{min}.."
      end
    end

    class Filter
      SIZE_FILTERS = ['clothing', 'footwear', 'indumentaria', 'calzado'].freeze
      GENDER_TO_FILTER = ['Men', 'Women', 'Kids', 'Hombres', 'Mujeres', 'Niños'].freeze

      def initialize(search, params, models, store = nil)
        @search = search
        @params = params.dup
        @params.delete(:p)
        @network = @params[:network]
        @filter = {}
        @models = models
        @filters = current_filters
        @store = store
      end

      def arrange
        arrange_genders
        arrange_categories
        arrange_manufacturers
        arrange_sizes if filter_by_size?
        arrange_on_sale
        arrange_price
        arrange_colors

        @filter
      end

      private

      def current_filters
        current_filters = @params.select do |key, value|
          UrlMapperHelper::FILTERS_KEYS.include?(key.to_sym)
        end

        HashWithIndifferentAccess.new(current_filters)
      end

      def filter_by_size?
        if (category_ids = Array.wrap(@params[:c])).present?
          category = Mkp::Category.where(id: category_ids).first

          if category.present?
            parent_category = category.parent.presence || category
            return SIZE_FILTERS.include?(parent_category.name.downcase)
          end
        end

        false
      end

      # CATEGORIES PART OF THE FILTER
      def arrange_categories
        category_facets = get_facet_values(:categories_ids)
        categories_ids = category_facets.map(&:value)

        @filter[:category] = {
          title: I18n.t('mkp.catalog.v5.filter.title.category'),
          parameter: 'c',
          items: []
        }
        if categories_ids.present?
          # There are category facets, so let's understand what we want to
          # display and how:
          # First let's understand if it's navigating a category or not the facets
          # are true facets and nothing more.

          choosen_category = @models.detect{ |model| model.is_a?(Mkp::Category) }
          other_mapper_models = @models.select{ |model| !model.is_a?(Mkp::Category) }

          if choosen_category
            # The visitor is in an specific category
            if choosen_category.has_parent?
              parent_category = choosen_category
              parent_mapper_models = other_mapper_models + [ parent_category ]
              item = {
                href: UrlMapperHelper.absolute_mapper_path(parent_mapper_models, @network, @filters),
                name: parent_category.name,
                slug: parent_category.slug,
                active: false
              }.tap do |_item|
                _item[:childs] = []
                same_level_categories = sorted_childrens_of(choosen_category, categories_ids)
                same_level_categories.each do |category|
                  child = {
                    href: UrlMapperHelper.absolute_mapper_path(category, @network, @filters),
                    name: category.name,
                    slug: category.slug,
                    active: category.id == choosen_category.id ? true : false
                  }.tap do |_child|
                    _child[:href_remove] = UrlMapperHelper.absolute_mapper_path(parent_mapper_models, @params[:network], @filters)
                  end
                  _item[:childs] << child
                end
              end
              @filter[:category][:items] << item
            else
              same_level_categories = siblings_of(choosen_category)
              same_level_categories.each do |category|
                unless @store.nil?
                  next unless @store.categories.include?(category)
                end

                active = category.id == choosen_category.id
                item = {
                  href: UrlMapperHelper.absolute_mapper_path(category, @network, @filters),
                  name: category.name,
                  slug: category.slug,
                  active: active ? true : false
                }.tap do |_item|
                  if active
                    parent_category = choosen_category.parent
                    mapper_models = parent_category ? other_mapper_models + [ parent_category ] : other_mapper_models
                    _item[:href_remove] = UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], @filters)
                    _item[:childs] = []
                    children = sorted_childrens_of(category, categories_ids)
                    children.each do |child_category|
                      child = {
                        href: UrlMapperHelper.absolute_mapper_path(other_mapper_models + [child_category], @network, @filters),
                        name: child_category.name,
                        slug: child_category.slug
                      }
                      _item[:childs] << child
                    end
                  end
                end
                @filter[:category][:items] << item
              end
            end
          else
            if @store.nil?
              faceted_categories = Mkp::Category.select("mkp_categories.id, mkp_categories.name, mkp_categories.slug, mkp_categories.ancestry, mkp_categories.ancestry_depth").where(id: categories_ids).order("field(id, #{categories_ids.join(',')})")
            else
              faceted_categories = @store.categories.select("mkp_categories.id, mkp_categories.name, mkp_categories.slug, mkp_categories.ancestry, mkp_categories.ancestry_depth").where(id: categories_ids).order("field(mkp_categories.id, #{categories_ids.join(',')})")
            end

            base_depth = faceted_categories.map(&:ancestry_depth).uniq.min
            categories_to_filter = faceted_categories.select do |category|
              category.ancestry_depth == base_depth
            end
            categories_to_filter.each do |category|
              item = {
                href: UrlMapperHelper.absolute_mapper_path(other_mapper_models + [category], @network, @filters),
                name: category.name,
                slug: category.slug,
                active: false
              }.tap do |_item|
                _item[:childs] = []
                children = sorted_childrens_of(category, categories_ids)
                children.each do |child_category|
                  child = {
                    href: UrlMapperHelper.absolute_mapper_path(other_mapper_models + [child_category], @network, @filters),
                    name: child_category.name,
                    slug: child_category.slug
                  }
                  _item[:childs] << child
                end
              end
              @filter[:category][:items] << item
            end
          end
        else
          # There are no category facets, so let's show the roots at least
          if @store.nil?
            roots = Mkp::Category.select([:id, :name, :slug, :ancestry])
              .where(ancestry: nil, network: @network, active: true)
              .order(:name)
          else
            roots = @store.categories
              .select("mkp_categories.id, mkp_categories.name, mkp_categories.slug, mkp_categories.ancestry")
              .where("mkp_categories.ancestry IS NULL AND mkp_categories.network = ? and mkp_categories.active = ?", @network, true)
              .order("mkp_categories.name ASC")
          end

          roots.each do |category|
            item = {
              href: UrlMapperHelper.absolute_mapper_path([category], @network, @filters),
              name: category.name,
              slug: category.slug,
              active: false
            }
            @filter[:category][:items] << item
          end
        end
      end

      def siblings_of(category)
        if category.is_root?
          category.siblings.where(active: true, network: @network).order(:name)
        else
          category.siblings.where(active: true).order(:name)
        end
      end

      def sorted_childrens_of(category, sorted_array_of_ids)
        category.children.select([:id, :name, :slug, :ancestry, :ancestry_depth]).where(active: true, id: sorted_array_of_ids).order("field(id, #{sorted_array_of_ids.join(',')})")
      end
      # END - CATEGORIES

      # MANUFACTURERS PART OF THE FILTER
      def arrange_manufacturers
        filters = @filters.dup
        filters.delete(:b)
        manufacturer_facets = get_facet_values(:manufacturer_id)

        if manufacturer_facets.present?
          selected_manufacturers_ids = Array.wrap(@params[:b]).map(&:to_i)

          @filter[:brand] = {
            title: I18n.t('mkp.catalog.v5.filter.title.brand'),
            parameter: 'b',
            items: []
          }

          manufacturer_facets.each do |facet|
            instance = facet.instance

            item = {
              name: instance.name,
              count: facet.count,
              slug: instance.slug
            }

            mapper_models = @models + [instance]

            if selected_manufacturers_ids.include?(facet.value)
              manufacturer_params = selected_manufacturers_ids - [facet.value]
              options = filters.merge(manufacturer_params.present? ? { b: manufacturer_params } : {})

              item.tap do |hash|
                hash[:href_remove] = catalog_root_path_with_applied_filters(options)
                hash[:active] = true
              end
            else
              manufacturer_params = if (selected = selected_manufacturers_ids.dup).present?
                selected << facet.value
                selected
              else
                facet.value
              end

              options = filters.merge({ b: manufacturer_params })

              item.tap do |hash|
                hash[:href] = catalog_root_path_with_applied_filters(options)
                hash[:color_hex] = facet.value
                hash[:active] = false
              end
            end

            @filter[:brand][:items] << item
          end

          @filter[:brand][:items].sort_by! { |k| k[:name] } if @filter[:brand][:items].any?
        end
      end
      # END - MANUFACTURERS

      # SIZES PART OF THE FILTER
      def arrange_sizes
        filters = @filters.dup
        filters.delete(:sz)
        sizes_facets = get_facet_values(:available_sizes)

        if (sizes = sizes_facets.map(&:value)).present?
          selected_sizes_ids = Array.wrap(@params[:sz])

          @filter[:size] = {
            title: I18n.t('mkp.catalog.v5.filter.title.size'),
            parameter: 'sz',
            selected_items: [],
            items: []
          }

          sizes.each do |size|
            item = {
              name: size.to_s.upcase
            }

            if selected_sizes_ids.include?(size)
              size_params = selected_sizes_ids - [size]
              options = filters.merge(size_params.present? ? { sz: size_params } : {})

              href_remove = unless @models.present?
                catalog_root_path_with_applied_filters(options)
              else
                UrlMapperHelper.absolute_mapper_path(@models, @params[:network], options)
              end

              item.tap do |hash|
                hash[:href_remove] = href_remove
                hash[:active] = true
              end
              @filter[:size][:selected_items] << item
            else
              size_params = if (selected = selected_sizes_ids.dup).present?
                selected << size
                selected
              else
                size
              end

              options = filters.merge({ sz: size_params })

              href = unless @models.present?
                catalog_root_path_with_applied_filters(options)
              else
                UrlMapperHelper.absolute_mapper_path(@models, @params[:network], options)
              end

              item.tap do |hash|
                hash[:href] = href
                hash[:active] = false
              end

              @filter[:size][:items] << item
            end
          end

        end
      end
      # END - SIZES

      # GENDERS PART OF THE FILTER
      def arrange_genders
        # genders_facets = get_facet_values(:genders_ids)

        # if genders_facets.present?
        #   selected_genders_ids = Array.wrap(@params[:g]).map(&:to_i)

        #   @filter[:gender] = {
        #     title: I18n.t('mkp.catalog.v5.filter.title.gender'),
        #     parameter: 'g',
        #     items: [],
        #     selected_items: []
        #   }

        #   genders_facets.each do |facet|
        #     next unless GENDER_TO_FILTER.include?(facet.instance.name)

        #     instance = facet.instance

        #     item = {
        #       name: instance.name,
        #       count: facet.count
        #     }

        #     if selected_genders_ids.include?(facet.value)

        #       mapper_models = @models - [instance]

        #       item.tap do |hash|
        #         hash[:href_remove] = UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], @filters)
        #         hash[:active] = true
        #       end

        #       @filter[:gender][:selected_items] << item
        #     else
        #       value = if (new_selection = selected_genders_ids.dup).present?
        #         new_selection << facet.value
        #         new_selection
        #       else
        #         facet.value
        #       end

        #       mapper_models = @models + [instance]

        #       item.tap do |hash|
        #         hash[:href] = UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], @filters)
        #         hash[:active] = false
        #       end

        #       @filter[:gender][:items] << item
        #     end
        #   end
        # end
      end
      # END - GENDERS

      # SPORTS PART OF THE FILTER
      def arrange_sports
        # sports_facets = get_facet_values(:sports_ids)

        # if sports_facets.present?
        #   selected_sports_ids = Array.wrap(@params[:sp]).map(&:to_i)

        #   @filter[:sport] = {
        #     title: I18n.t('mkp.catalog.v5.filter.title.sport'),
        #     parameter: 'sp',
        #     items: [],
        #     selected_items: []
        #   }

        #   sports_facets.each do |facet|
        #     instance = facet.instance
        #     item = {
        #       name: instance.name,
        #       count: facet.count
        #     }

        #     if selected_sports_ids.include?(facet.value)
        #       mapper_models = @models - [instance]

        #       item.tap do |hash|
        #         hash[:href_remove] = UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], @filters)
        #         hash[:active] = true
        #       end

        #       @filter[:sport][:selected_items] << item
        #     else
        #       value = if (new_selection = selected_sports_ids.dup).present?
        #         new_selection << facet.value
        #         new_selection
        #       else
        #         facet.value
        #       end

        #       mapper_models = @models + [instance]

        #       item.tap do |hash|
        #         hash[:href] = UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], @filters)
        #         hash[:active] = false
        #       end

        #       @filter[:sport][:items] << item
        #     end
        #   end
        # end
      end
      # END - SPORTS

      # SALE PART OF THE FILTER
      def arrange_on_sale
        filters = @filters.dup
        filters.delete(:d)
        sale_facets = get_facet_values(:on_sale)
        if sale_facets.present?
          name = I18n.t('mkp.catalog.v5.filter.sale.on_sale')
          @filter[:sale] = {
            title: I18n.t('mkp.catalog.v5.filter.title.sale'),
            parameter: 'd',
            items: [],
          }
          mapper_models = @models
          active = @params[:d].present? ? true : false
          if active
            href = unless mapper_models.present?
              catalog_root_path_with_applied_filters(filters)
            else
              UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], filters)
            end
            item = {
              name: name,
              count: sale_facets.count,
              href_remove:  href,
              active:  true
            }
          else
            options = filters.merge({ d: 1 })
            href = unless mapper_models.present?
              catalog_root_path_with_applied_filters(options)
            else
              UrlMapperHelper.absolute_mapper_path(mapper_models, @params[:network], options)
            end
            item = {
              name: name,
              count: sale_facets.count,
              href:  href,
              active:  false
            }
          end

          @filter[:sale][:items] << item

        end
      end
      # END - SALE

      # PRICE PART OF THE FILTER
      def arrange_price
        filters = @filters.dup
        filters.delete(:pr)
        prices = get_price_ranges

        @filter[:price] = {
          title: I18n.t('mkp.catalog.v5.filter.title.price'),
          parameter: 'pr',
          items: []
        }

        if selected_price_range.present?
          range_part = price_range_part(selected_price_range)

          slug = I18n.t(
            "mkp.catalog.v5.filter.price.range#{range_part}",
            min: selected_price_range.min,
            max: selected_price_range.max,
            currency: currency_format
          )

          href_remove = unless @models.present?
            catalog_root_path_with_applied_filters(filters)
          else
            UrlMapperHelper.absolute_mapper_path(@models, @params[:network], filters)
          end

          item = {
            href_remove: href_remove,
            name: slug,
            active: true
          }

          @filter[:price][:items] << item
        else
          prices.each do |price_range|
            range = CustomRange.new(*price_range[:id].split('..').map(&:to_i))

            range_part = price_range_part(range)

            slug = I18n.t(
              "mkp.catalog.v5.filter.price.range#{range_part}",
              min: range.min,
              max: range.max,
              currency: currency_format
            )

            range_text = range.to_s

            options = filters.merge({ pr: range_text })

            href = unless @models.present?
              catalog_root_path_with_applied_filters(options)
            else
              UrlMapperHelper.absolute_mapper_path(@models, @params[:network], options)
            end

            item = {
              href: href,
              name: slug,
              slug: range_text,
              active: false
            }

            @filter[:price][:items] << item
          end

        end
      end

      def get_price_ranges
        return [] if @search.hits.blank?

        begin
          mean   = @search.stats(:price).mean
          stddev = @search.stats(:price).standard_deviation
        rescue
          return []
        end

        ranges = []
        # We built only 2 or 3 options depending on the mean
        # and the relation between the standard deviation
        if mean > (stddev/4)
          range_min = (mean-stddev/4).floor.round(-1)
          range_max = (mean+stddev/4).ceil.round(-1)
          ranges << { id: "..#{range_min}" }
          ranges << { id: "#{range_min}..#{range_max}" }
          ranges << { id: "#{range_max}.." }
        else
          mean_int = mean.round.round(-1)
          ranges << { id: "..#{mean_int}" }
          ranges << { id: "#{mean_int}.." }
        end
      end

      def selected_price_range
        return if (price_range = @params[:pr]).blank?
        CustomRange.new(*price_range.split('..').map(&:to_i))
      end

      def currency_format
        network = Network[@params[:network]] || Network[Network.default]
        currency = Mkp::Currency.find(network.currency_id)

        network.thin? ? currency.identifier : currency.symbol
      end

      def price_range_part(range)
        return '_up' unless range.has_max_limit?
        return '_down' if range.min.zero?
      end
      # END - PRICE

      # COLOR PART OF THE FILTER
      def arrange_colors
        filters = @filters.dup
        filters.delete(:clr)
        colors_facets = get_facet_values(:color_hex)

        if colors_facets.present?
          selected_colors_ids = Array.wrap(@params[:clr])

          @filter[:color] = {
            title: I18n.t('mkp.catalog.v5.filter.title.color'),
            parameter: 'clr',
            items: []
          }

          colors_facets.each do |facet|
            color = facet.value
            item = {
              name: color,
              count: facet.count,
              color_hex: facet.value
            }

            if selected_colors_ids.include?(facet.value)
              color_params = selected_colors_ids - [color]
              options = filters.merge(color_params.present? ? { clr: color_params } : {})

              href_remove = unless @models.present?
                catalog_root_path_with_applied_filters(options)
              else
                UrlMapperHelper.absolute_mapper_path(@models, @params[:network], options)
              end

              item.tap do |hash|
                hash[:href_remove] = href_remove
                hash[:active] = true
              end
            else
              color_params = if (selected = selected_colors_ids.dup).present?
                selected << color
                selected
              else
                color
              end

              options = filters.merge({ clr: color_params })

              href = if @models.present?
                UrlMapperHelper.absolute_mapper_path(@models, @params[:network], options)
              else
                catalog_root_path_with_applied_filters(options)
              end

              item.tap do |hash|
                hash[:href] = href
                hash[:color_hex] = facet.value
                hash[:active] = false
              end
            end

            @filter[:color][:items] << item
          end
        end
      end
      # END - COLOR

      def get_facet_values(facet_name)
        @search.facet(facet_name).rows
      end

      def catalog_root_path_with_applied_filters(filters)
        [
          Rails.application.routes.url_helpers.mkp_catalog_root_path(@params[:network]),
          filters.to_query
        ].join('?')
      end
    end
  end
end

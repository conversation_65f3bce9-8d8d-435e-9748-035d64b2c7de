module Mkp
  module Discount<PERSON><PERSON><PERSON>
    extend self

    def applied?(checkout_cart)
      find_coupon(checkout_cart).present?
    end

    def determine_amount(checkout_cart)
      determine_amount!(checkout_cart)
    rescue StandardError
      checkout_cart.remove_coupon if checkout_cart.class == CheckoutFlow::CheckoutCart
      0
    end

    def determine_amount!(checkout_cart)
      return 0 unless (coupon = find_coupon(checkout_cart)).present?

      init_handler(checkout_cart, coupon).determine
    end

    def determine_amount_for(discountable)
      determine_amount_for!(discountable)
    rescue StandardError
      0
    end

    def determine_amount_for!(discountable)
      if discountable.respond_to?(:coupon)
        coupon = Mkp::Coupon::Network.find(discountable.coupon[:id])
        Network.new(discountable, coupon).determine
      else
        0
      end
    end

    def get_code(checkout_cart)
      coupon = find_coupon(checkout_cart)
      return false unless coupon.present?
      coupon.code
    end

    def get_coupon(checkout_cart)
      find_coupon(checkout_cart)
    end

    def perform(action, checkout_cart, coupon = nil)
      perform!(action, checkout_cart, coupon)
    rescue StandardError
      false
    end

    def perform!(action, checkout_cart, coupon = nil)
      return false unless (handler = init_handler(checkout_cart, coupon)).present?
      handler.send(action)
    end

    private

    def find_coupon(checkout_cart)
      if checkout_cart.coupon.present?
        Mkp::Coupon::Network.find(checkout_cart.coupon[:id])
      else
        false
      end
    end

    # TODO: Pending definitions.
    def init_handler(checkout_cart, coupon)
      Network.new(checkout_cart, coupon) if coupon.is_a?(Coupon::Network)
    end

    class Network
      def initialize(checkout_cart, coupon = nil)
        @checkout_cart = checkout_cart
        @coupon = coupon
        @customer = checkout_cart.customer if(checkout_cart.customer.present?)
      end

      def apply
        !!appropriate?
      end

      def determine
        return 0 unless appropriate?
        @coupon.coupon_amount(amount_applicable)
      end

      def remove
        # no-op
      end

      private

      def amount_applicable
        qualified_items = @checkout_cart.checkout_items.select { |i| qualifies_for_subtotal?(i) }
        raise Mkp::Coupon::CouponNotOnsaleError if qualified_items.size == 0
        qualified_items.map(&:total).sum
      end

      def appropriate?
        return unless @checkout_cart
        return unless @coupon
        raise Mkp::Coupon::PromotionNotAllowCouponError if @checkout_cart.promotion.present? && !@checkout_cart.promotion[:allow_coupon]
        raise Mkp::Coupon::InvalidNetworkError unless @coupon.network == @checkout_cart.network
        if(@customer.present?)
          raise Mkp::Coupon::UserLimitedReachedError if customer_exceed_repetitive_limit?
        end
        Coupon.get_elegible_by_code_and_network?(@coupon.code, @checkout_cart.network, amount_applicable)
      end

      def qualifies_for_subtotal?(item)
        return false if item.on_sale? && !@coupon.apply_on_sale
        return false unless @coupon.applies_to?(item.variant.product)
        if(@checkout_cart.customer.present?)
          return false unless @coupon.can_be_applied_by?(@checkout_cart.customer)
        end
        true
      end

      def customer_exceed_repetitive_limit?
        return false if @coupon.repetitive_amount_limit.blank?
        orders = @customer.orders
        return false if orders.empty?

        orders.map(&:coupon_id).count { |n| n == @coupon.id } >= @coupon.repetitive_amount_limit
      end
    end
  end
end

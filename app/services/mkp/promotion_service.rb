module Mkp
  class PromotionService

    def initialize(checkout_cart)
      @checkout_cart = checkout_cart
      @active_promotions = active_promotions
    end

    def applied_promotion
      qualified_promotions.sort_by { |p| p[:discount] }.reverse.first
    end

    def get_shipping_promotion
      shipping = {}

      @active_promotions.each_with_object([]) do |promotion, active_promotions|
        next unless promotion.action[:method] == 'shipping'
        condition = promotion.condition
        operant, operator = condition.values_at(:operant, :operator).map(&:to_sym)
        promotion_cart = PromotionCart.new(@checkout_cart, promotion)
        operant_value = get_operant_value(condition, promotion_cart)
        if operator = get_operant_operator(operant, operator)
          value = condition[:value].to_f
          if operant_value.public_send(operator, value)
            shipping = {discount: promotion.action[:amount], name: promotion[:display_name], display_name: promotion.try(:display_name)}
          end
        end
      end

      shipping
    end

    private

    def active_promotions
      return Mkp::Promotion.by_store(@checkout_cart.store_id).today(@checkout_cart.network) unless @checkout_cart.coupon.present?
      Mkp::Promotion.by_store(@checkout_cart.store_id).today(@checkout_cart.network).allow_coupon
    end

    def qualified_promotions
      @active_promotions.each_with_object([]) do |promotion, active_promotions|
        condition = promotion.condition
        operant, operator = condition.values_at(:operant, :operator).map(&:to_sym)
        promotion_cart = PromotionCart.new(@checkout_cart, promotion)
        operant_value = get_operant_value(condition, promotion_cart)
        if operator = get_operant_operator(operant, operator)
          value = condition[:value].to_f
          if operant_value.public_send(operator, value) && \
              (promotion = promotion_hash(promotion, promotion_cart)).present?
            active_promotions << promotion
          end
        else
          active_promotions << promotion_hash(promotion, promotion_cart) if operant_value
        end
      end
    end

    def get_operant_operator(operant, operator)
      if Mkp::Promotion::CONDITIONS_SETTINGS[operant][:has_operators]
        Mkp::Promotion::OPERATORS[operator]
      end
    end

    def get_operant_value(condition, promotion_cart)
      if condition[:arguments].present?
        promotion_cart.send(condition[:operant], condition[:arguments])
      else
        promotion_cart.send(condition[:operant])
      end
    end

    def promotion_hash(promotion, promotion_cart)
      discount = promotion.discount_amount(promotion_cart.send(promotion.action[:method]))
      if discount > 0
        {
          id: promotion.id,
          name: promotion.name,
          display_name: promotion.display_name,
          allow_coupon: promotion.allow_coupon,
          discount: discount
        }
      end
    end

  end

  PromotionCart = Struct.new(:checkout_cart, :promotion) do

    # Conditions

    def order_subtotal
      checkout_cart.subtotal
    end

    def items_quantity
      @involved_items = checkout_cart_items.dup
      quantity(@involved_items)
    end

    def item_quantity(product_ids)
      product_ids.map!(&:to_i)
      @involved_items = checkout_cart_items.select { |item| product_ids.include?(item[:product][:id]) }
      quantity(@involved_items)
    end

    def item_quantity_per_user(product_ids)
      return 0 unless checkout_cart.address_id.present? && checkout_cart.items.present?

      address = Mkp::Address.find checkout_cart.address_id
      return 0 unless address.present?

      orders = Mkp::Order.includes(:items => [:product])
        .where(store_id: checkout_cart.store_id)
        .where(mkp_products: { id: checkout_cart.items.map{|i| i[:product][:id]} })

      dnis = Mkp::Guest.includes(:addresses).where(id: orders.pluck(:customer_id), mkp_addresses: { dni: address.dni })
      return 0 if dnis.any?

      product_ids.map!(&:to_i)
      @involved_items = checkout_cart_items.select { |item| product_ids.include?(item[:product][:id]) }
      quantity(@involved_items)
    end

    def manufacturer_items_quantity(manufacturer_ids)
      manufacturer_ids.map!(&:to_i)

      @involved_items = checkout_cart_items.select do |item|
        manufacturer_ids.include?(item[:product][:manufacturer][:id])
      end

      quantity(@involved_items)
    end

    def manufacturer_items_subtotal(manufacturer_ids)
      manufacturer_ids.map!(&:to_i)

      @involved_items = checkout_cart_items.select do |item|
        manufacturer_ids.include?(item[:product][:manufacturer][:id])
      end

      scoped_subtotal(@involved_items)
    end

    def category_items_quantity(category_paths)
      @involved_items = category_paths.each_with_object([]) do |path, list|
        list << checkout_cart_items.select do |item|
          item[:product][:category][:full_path_name].include?(path)
        end
      end

      quantity(@involved_items.flatten!)
    end

    def category_items_subtotal(category_paths)
      @involved_items = category_paths.each_with_object([]) do |path, list|
        list << checkout_cart_items.select do |item|
          item[:product][:category][:full_path_name].include?(path)
        end
      end

      scoped_subtotal(@involved_items.flatten!)
    end

    def landings(landing_ids)
      checkout_cart.landing_id && ([checkout_cart.landing_id] & landing_ids).any?
    end

    # Actions

    def free_item_involved
      promotion = self.promotion
      free_products_ids = promotion[:action][:arguments].map!(&:to_i)
      involved_items = checkout_cart.items.select { |item| free_products_ids.include?(item[:product][:id]) }
      if involved_items.present?
        if promotion.action[:type] == "fixed"
          return promotion.action[:amount].to_f
        else
          expensive_one = involved_items.sort_by do |item|
            item_price(item)
          end.last
          price = item_price(expensive_one)
          percent_discount = promotion.action[:amount].to_f
          return item_price(expensive_one)
        end
      else
        0
      end
    end

    def cheaper_item
      cheaper = @involved_items.sort_by do |item|
        item_price(item)
      end.first

      item_price(cheaper)
    end

    def involved_items
      @involved_items.reduce(0) do |sum, item|
        sum += item_price(item)
      end
    end

    def shipping
      0
    end

    private

    def quantity(items)
      items.reduce(0) { |sum, item| sum += item[:quantity] }
    end

    def scoped_subtotal(items)
      items.sum { |i| item_price(i) }
    end

    def item_price(item)
      if item[:product][:sale_price].present?
        item[:product][:sale_price].to_f
      else
        item[:product][:regular_price].to_f
      end
    end

    def checkout_cart_items
      return checkout_cart.items if promotion.allow_items_on_sale
      checkout_cart.items.present? ? checkout_cart.items.reject { |item| item[:product].key?(:sale_price) } : []
    end

  end

end

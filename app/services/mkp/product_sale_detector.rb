module Mkp
  class ProductSaleDetector
    MAX = 100

    def self.is_on_sale?(product)
      new(product).is_on_sale?
    end

    def self.calculate(product)
      new(product).calculate
    end

    attr_accessor :product, :setting

    def initialize(product)
      @product = product
      @setting = product.shop.setting
    end

    def is_on_sale?
      setting.discount_applying_now? || product.on_sale?
    end

    def calculate
      return active_wide_discount if calculate_with_discount?
      product.price
    end

    def discount
      discount = (calculate * MAX / product.regular_price)
      discount.nan? ? 0 : discount.round
    end

    def percent_off
      MAX - discount
    end

    def calculate_without_taxes
      return active_wide_discount_without_taxes if calculate_with_discount?
      product.price_without_taxes
    end

    protected

    def calculate_with_discount?
      setting.discount_applying_now? &&
      setting.discount_is_over_product_sale.eql?("1") &&
      !product.on_sale?
    end

    def active_wide_discount_without_taxes
      product.regular_price_without_taxes - product.regular_price_without_taxes * setting.discount_percent / MAX if product.regular_price_without_taxes.present?
    end

    def active_wide_discount
      product.regular_price - product.regular_price * setting.discount_percent / MAX
    end
  end
end

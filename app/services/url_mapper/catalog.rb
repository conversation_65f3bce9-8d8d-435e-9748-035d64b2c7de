class UrlMapper::Catalog
  def self.sitemap_index_builder(store)

    generator = MainModelsURLGenerator.new(store)

    generator.produce_paths!

    generator.paths.each do |path|
      yield "https://avenida.com.ar/#{path}"
    end
  end

  class MainModelsURLGenerator
    MODELS = {
      Mkp::Gender.name => {
        conditions: 'ancestry IS NOT NULL',
        key: :g,
        needs_network: true
      },
      Mkp::Manufacturer.name => {
        key: :b,
      },
      Sport.name => {
        key: :sp
      },
      Mkp::Category.name => {
        key: :c,
        needs_network: true
      }
    }.freeze

    attr_reader :paths

    def initialize(store)
      @paths = []
      @store = store
    end

    def produce_paths!
      each_ordered_model_instances do |instance, options|
        key, network = options.values_at(:key, :network)

        params = {
          key => instance.id,
          :store_id => @store.id,
          :network => network
        }

        if (search = Mkp::Catalog::Finder.find(params)).total > 0
          instance = instance.parent if gender_requires_parent?(instance)

          @paths << UrlMapperHelper.mapper_path(instance, network)

          cascade_calls = case instance.class.name
          when Mkp::Gender.name
            %w(get_manufacturers_urls get_categories_urls)
          when Mkp::Manufacturer.name
            %w(get_categories_urls)
          when Sport.name
            %w(get_categories_urls)
          else
            []
          end

          cascade_calls.each do |method|
            send(method, search, params.dup, [instance])
          end
        end
      end
    end

    private

    def each_ordered_model_instances
      Network.all_visible.each do |network|
        MODELS.each do |klass, options|
          options.merge!(network: network)

          klass_source(klass, options).each do |instance|
            yield(instance, options)
          end
        end
      end
    end

    def get_manufacturers_urls(search, params, models)
      if (manufacturers = get_facet_instances(search, :manufacturer_id)).present?
        manufacturers.each do |manufacturer|
          params.merge!(b: manufacturer.id)

          if (cascade_search = Mkp::Catalog::Finder.find(params)).total > 0
            @paths << UrlMapperHelper.mapper_path([*models, manufacturer], params[:network])

            get_categories_urls(cascade_search, params.dup, [*models, manufacturer])
          end
        end
      end
    end

    def get_categories_urls(search, params, models)
      if (categories = get_facet_instances(search, :categories_ids)).present?
        categories.each do |category|
          params.merge!(c: category.id)

          if Mkp::Catalog::Finder.find(params).total > 0
            @paths << UrlMapperHelper.mapper_path([*models, category], params[:network])
          end
        end
      end
    end

    def get_sports_urls(search, params, models)
      if (sports = get_facet_instances(search, :sports_ids)).present?
        sports.each do |sport|
          params.merge!(sp: sport.id)

          if Mkp::Catalog::Finder.find(params).total > 0
            @paths << UrlMapperHelper.mapper_path([*models, sport], params[:network])
          end
        end
      end
    end

    def klass_source(klass, options)
      klass = klass.constantize

      klass = klass.where(options[:conditions]) if options[:conditions]
      klass = klass.where(network: options[:network]) if options[:needs_network] && options[:network]

      klass.where(nil) # Defaults to an ActiveRecord::Relation object.
    end

    def gender_requires_parent?(model)
      model.is_a?(Mkp::Gender) && %w(girls boys chicas chicos).any? { |s| s == model.slug }
    end

    def get_facet_instances(search, facet)
      search.facet(facet).rows.map(&:instance).compact
    end
  end
end

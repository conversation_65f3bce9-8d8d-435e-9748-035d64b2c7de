module SystemPoints
  module Worker
    class GenerateUserWorker
      include Sidekiq::Worker

      def perform(user_id)
        customer = Mkp::Customer.find(user_id)
        sys_customer = SystemPoints::User.new(customer)
        sys_customer.create
        if customer.store.id == 14 # only to TDD
          amount = customer.subscription&.points.to_i
          # sys_customer.accredit(amount)
        end
      end
    end
  end
end

module SystemPoints
  class Transaction

    attr_accessor :type, :id, :value, :transaction_identify, :created_at, :updated_at

    def initialize(args)
      args.each do |k,v|
        instance_variable_set("@#{k}", v) unless v.nil?
      end
    end

    class << self
      def get_list(user, args)
        data = get_data(:index, user, args)
        return [] unless data
        data.map do |transaction|
          attributes = transaction.try(:[], "attributes")
          next unless attributes
          new(attributes)
        end.compact
      end

      def show(user, id)
        exec(__method__, user, id)
      end

      def accredit(user, points)
        exec(__method__, user, { points: points})
      end

      def debit(user, points)
        exec(__method__, user, { points: points})
      end

      private

      def get_data(method, user, params = nil)
        store_key = SYSTEM_POINT_KEY[user.store.name]['private_key']
        client = SystemPoints::Api::Transactions.new(user.user_identify, store_key)
        response = params.nil? ? client.send(method) : client.send(method, params)
        response.try(:[], "data")
      end

      def exec(method, user, params)
        data = get_data(method, user, params)
        return unless data
        attributes = data.try(:[], "attributes")
        new(attributes)
      end
    end
  end
end

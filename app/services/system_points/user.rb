module SystemPoints
  class User
    attr_reader :user
    attr_accessor :response

    def initialize(user)
      @user = user
      @response = {}
    end

    def create
      self.response = client.create({ email: user.email })
      user_identify = response_attr.try(:[], "user_identify")
      user.update(user_identify: user_identify) if user_identify
    end

    def update
      self.response = client.update({ email: user.email })
    end

    def show
      self.response = client.show(user.user_identify)
      response_attr
    end

    def points
      self.show.try(:[], 'points')
    rescue
      nil
    end

    #user transactions
    def transactions(args)
      Transaction.get_list(user, args)
    end

    def accredit(points)
      Transaction.accredit(user, points)
    end

    def debit(points)
      Transaction.debit(user, points)
    end

    def transaction(id)
      Transaction.show(user, id)
    end

    private
    def client
      store_key = SYSTEM_POINT_KEY[user.store.name]['private_key']
      @client ||= SystemPoints::Api::Users.new(store_key)
    end

    def response_attr
      self.response.try(:[], "data").try(:[], "attributes")
    end
  end
end

module SystemPoints
  module Api
    module Base
      include Validate::Authorization
      API_URL = SYSTEM_POINT_URL

      private

      attr_reader :token, :store_key

      def header
        { content_type: :json }.tap do |head|
          head[:'client-key'] = store_key
          head[:'auth-token'] = token if token.present?
        end
      end

      def authorization
        oauth unless token.present?
      end

      def oauth
        sanitize_oauth(user_identify_params)
        response = request(:post, "#{API_URL}/tokens", user_identify_params)
        @token ||= response["auth_token"]
      end

      def request_auth(method, path_info, params = {})
        authorization
        request(method, path_info, params)
      end

      def request(method, path_info, args = {})
        response = RestClient::Request.execute( method: method, url: path_info, payload: args, headers: header)
        body = JSON.parse(response.body)
        body.deep_transform_keys! { |key| key.underscore }
      rescue RestClient::BadRequest => e
        exception_message(e)
      rescue RestClient::ResourceNotFound => e
        exception_message(e)
      rescue RestClient::UnprocessableEntity => e
        exception_message(e)
      rescue RestClient::Unauthorized => e
        exception_message(e)
      rescue RestClient::PreconditionFailed => e
        exception_message(e)
      rescue => e
        store = Mkp::Store.find(14)
        Stores::ErrorMailer.throw_error_in_store(e.to_s, e.backtrace, store).deliver_later
        exception_message(e)
      end

      def exception_message(e)
        { message: e.message, status: e.http_code }
      end
    end
  end
end

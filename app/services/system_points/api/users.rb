module SystemPoints
  module Api
    class Users
      include Base
      include Validate::Users

      USERS_URL = "#{API_URL}/users"

      def initialize(store_key)
        @store_key = store_key
      end

      #users
      def index
        request(:get, USERS_URL)
      end

      def create(params = {})
        sanitize(params)
        request(:post, USERS_URL, params)
      end

      def update(id = nil, params = {})
        sanitize(params)
        request(:post, "#{USERS_URL}/#{id}", params)
      end

      def show(id = nil)
        request(:get, "#{USERS_URL}/#{id}")
      end

      def destroy(id = nil)
        request(:delete, "#{USERS_URL}/#{id}")
      end
    end
  end
end

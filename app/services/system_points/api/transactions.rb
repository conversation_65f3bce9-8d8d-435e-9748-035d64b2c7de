module SystemPoints
  module Api
    class Transactions
      include Base
      include Validate::Transactions

      attr_accessor :user_identify
      TRANSACTION_URL = "#{API_URL}/transactions"

      def initialize(user_identify, store_key)
        @user_identify = user_identify
        @store_key = store_key
      end

      #transactions
      def index(params)
        request_auth(:get, "#{TRANSACTION_URL}/?#{params.to_query}")
      end

      def show(id)
        request_auth(:get, "#{TRANSACTION_URL}/#{id}")
      end

      def accredit(params = {})
        sanitize(params)
        request_auth(:post, "#{TRANSACTION_URL}/accredit", params)
      end

      def debit(params = {})
        sanitize(params)
        request_auth(:post, "#{TRANSACTION_URL}/debit", params)
      end

      def user_identify_params
        { user_identify: user_identify }
      end
    end
  end
end

class PaymentsCredentials < ApplicationService
	SECS = 300

	def initialize(store)
		@store = store
	end

	def call
		Struct.new(:public_key, :private_key).new(public_key, private_key)
	end

	private

	def public_key
		redis_public_key_store || keys_store[:public_key]
	end

	def private_key
		redis_private_key_store || keys_store[:private_key]
	end

	def keys_store
		if strategy_first_payments?
			keys = keys_from_payments_service
			# Leer de platform en caso de tener estrategia payments_platform
			keys = keys_from_platform if keys.compact.blank? && GatewayAlternativeStrategy.use_platform_as_alternative?(
				@store, name
			)
		else
			keys = keys_from_platform
		end
		{ public_key: save_public_key(keys[:public_key]),
			private_key: save_private_key(keys[:private_key]) }
	end

	def keys_from_payments_service
		# Esta anulado el endpoint que devuelve la public_key
		# se usa la key decidir-public-key de PaymentCredential hasta que vuelva a funcionar
		# config = @store.payments_service.credentials(self.class::PAYMENTS_GATEWAY_ID)
		decidir_public_key = @store.payment_credentials.find_by(name: key_field_public_key)&.value
		decidir_private_key = nil # @store.payment_credentials.find_by(name: 'decidir_private_key')
		{ public_key: decidir_public_key, private_key: decidir_private_key }
	end

	def keys_from_platform
		{ public_key: credentials_platform.public_key, private_key: credentials_platform.private_key }
	end

	def strategy_first_payments?
		@strategy_first_payments ||= GatewayAlternativeStrategy.use_payments?(@store, name)
	end

	def credentials_platform
		@credentials_platform ||= {}
	end

	def save_private_key(key)
		$redis.setex "#{name}_private_key_store_#{@store.id}", SECS, key
		key
	end

	def save_public_key(key)
		$redis.setex "#{name}_public_key_store_#{@store.id}", SECS, key
		key
	end

	def redis_private_key_store
		$redis.get "#{name}_private_key_store_#{@store.id}"
	end

	def redis_public_key_store
		$redis.get "#{name}_public_key_store_#{@store.id}"
	end
end
module Tdd::Invoices
  class Colppy

    COMPANY_ID = COLPPY['sportclub']['company_id'].freeze
    USER = COLPPY['sportclub']['user']['username']
    PASSWORD = COLPPY['sportclub']['user']['password']
    CLIENT_USER = COLPPY['sportclub']['client']['username']
    CLIENT_PASSWORD = COLPPY['sportclub']['client']['password']
    PDF_URL = "https://login.colppy.com/resources/php/fe/FE_ImprimirEnviarFactura.php?"

    attr_accessor :client, :mode, :user, :company, :customer, :service, :invoice_number, :payment

    def initialize
      @mode = Rails.env.production? ? 'live' : 'sandbox'
      @user = ::Colppy::User.new(USER, PASSWORD)
      @client = ::Colppy::Client.new(CLIENT_USER, CLIENT_PASSWORD, user, mode)
      @company = client.company(COMPANY_ID)
    end

    def build_invoice(customer, payment)
      @invoice_number = payment.recurrent? ? '0004' : '0002'
      self.customer = customer
      self.service = customer.subscription.service
      self.payment = payment
      raise "ERROR: validations" unless valid?
      colppy_customer = get_or_create_customer
      raise "ERROR: No existe el customer en colppy" unless colppy_customer.present?
      create_invoice!(colppy_customer)
    end

    def get_invoice_url(customer, external_id)
      customer = get_customer_by_email(customer.email)
      params_url = { 'idEmpresa': COMPANY_ID, 'idCliente': customer&.id, 'idFactura': external_id,
                     'idUsuario': CLIENT_USER, 'correo': 'no' }.to_query
      PDF_URL + params_url
    end

    private
    include Methods
    def get_or_create_customer
      member = get_customer_by_email(customer.email)
      return member if member.present?
      create_customer
    end

    def create_customer
      address = customer.addresses.last
      state = parse_state(address)

      params = default_params.merge({
        name: customer.full_name,
        fantasy_name: customer.full_name,
        doc_number: customer.doc_number,
        email: customer.email,
        address: address.full_address,
        address_city: address.city,
        address_zipcode: address.zip,
        address_state: state,
        legal_address: address.full_address,
        legal_address_city: address.city,
        legal_address_zipcode: address.zip,
        legal_address_state: state,
        tax_condition_id: '3'
      })
      ::Colppy::Customer.new(params).save
    end

    def get_customer_by_email(email)
      data = company.customers({filter:[ field: "Email", op: "=", value: email]})[:results] if email.present?
      Array.wrap(data).last
    end

    def build_service
      colppy_service = get_or_create_services
      {
        product: colppy_service,
        unit_price: payment.amount,
        discount_percentage: 0,
        quantity: 1,
        tax: 21.0,
        ccosto1: "Ingresos",
        ccosto2: "INGR - boca +"
      }
    end

    def get_or_create_services
      colppy_service = company.product_by_code(code_service)
      colppy_service || create_service
    end

    def code_service
      "#{customer.store.name}-#{customer.subscription.service.id}-#{self.service.amount.to_i}"
    end

    def create_service
      product_params = default_params.merge({
        name: service.name,
        code: code_service,
        detail: "",
        sell_price: payment.amount,
        inventory_account: "Servicios",
        sales_costs_account: "Costo de Servicios Vendidas",
        sales_account: "INGRESOS SOCIOS PROGRAMA BENEFICIOS",
        item_type: "S",
        measure_unit: "0"
      })
      ::Colppy::Product.new(product_params).save
    end

    def create_invoice!(customer)
        invoice = ::Colppy::SellInvoice.new(client: client, company: company, customer: customer)
        invoice.invoice_number1 = @invoice_number
        invoice.invoice_type_id = 'B'
        invoice.receipt_type_id = '8'
        invoice.payment_condition_id = 'Contado'
        invoice.electronic_bill = 'Factura Electrónica'
        invoice.status_id = 'Cobrada'
        invoice.description = 'Suscripcion tarjeta del deporte boca'
        invoice.add_item(build_service)

        payment = build_payment(invoice)
        invoice.add_payment(payment)
        invoice.save
      end
  end
end

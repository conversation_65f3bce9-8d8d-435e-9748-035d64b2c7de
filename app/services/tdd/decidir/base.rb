require 'rest_client'

module Tdd
  module Decidir
    module Base
      BASE_URL = DECIDIR_ENDPOINT

      private
      def header
        { "apikey": @key, content_type: "application/json", "cache-control": 'no-cache' }
      end

      def request(method, path_info, params = nil, key=DECIDIR_TDD_PRIVATE_KEY)
        @key = key
        response = RestClient::Request.execute( method: method, url: path_info, payload: params.to_json, headers: header, verify_ssl: OpenSSL::SSL::VERIFY_NONE )
        body = JSON.parse(response.body)
        body.deep_transform_keys! { |key| key.to_sym }

      rescue => exception
        store = Mkp::Store.find_by_name("tddboca")
        #Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, store).deliver_later
        { message: exception.message, status: exception.http_code, status_details: { error_type: exception.to_s, status: 'cancelled' }}
      end
    end
  end
end

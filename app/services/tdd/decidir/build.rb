module Tdd
  module Decidir
    module Build

      STATUSES = {
        'pending' => 0,
        'approved' => 1,
        'in_process' => 0,
        'in_mediation' => 0,
        'rejected' => 2,
        'cancelled' => 2,
        'refunded' => 3,
        'charged_back' => 3,
        nil => 2
      }.freeze

      def build_payment_params(payment)
        {
          customer: {
            id: payment.user_id.to_s,
            email: payment.user_email
          },
          site_transaction_id: payment.token,
          token: payment.token,
          payment_method_id: payment.payment_method_id,
          bin: payment.bin,
          amount: parse_amount(payment.amount).to_i,
          currency: "ARS",
          installments: 1,
          description: "",
          payment_type: "single",
          sub_payments: []
        }
      end

      def build_payment_response(response)
        return build_error(response) if response[:status_details].present?
        {
          status: STATUSES[response[:status]],
          amount: amount_to_decimal(response[:amount].to_s),
          collected_at: response[:date],
          gateway: 'Decidir',
          gateway_data: response,
          gateway_object_id: response[:id],
          payment_method: response[:payment_method_id]
        }
      end

      def build_error(response)
        error_code = response[:status_details].try(:code) || response[:code] || 'invalid_card'
        response.merge({
          errors: I18n.t("v5.controllers.checkout.payment.errors.#{error_code}")
        })
      end

      def parse_amount(amount)
        sprintf('%.2f', amount).to_s.split(".").join("")
      end

      def amount_to_decimal(string_amount)
        string_amount.insert(string_amount.size-2, '.').to_f
      end
    end
  end
end

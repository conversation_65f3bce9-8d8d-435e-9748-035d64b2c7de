module Tdd
  module Decidir
    module Client
      include Base
      include Build
      extend self

      def collect(payment)
        payment = build_payment_params(payment)
        response = request(:post, "#{BASE_URL}/payments" , payment)
        build_payment_response(response)
      end

      def card_tokens(user_id)
        response = request(:get, "#{BASE_URL}/usersite/#{user_id}/cardtokens")
        response.try(:[], :tokens).try(:first).try(:[], :token)
      end

      def payment_token(data)
        request(:post, "#{BASE_URL}/tokens" , data, DECIDIR_PUBLIC_KEY)
      end
    end
  end
end

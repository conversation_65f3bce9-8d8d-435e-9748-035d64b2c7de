class Tdd::Registration
  attr_accessor :customer_params, :payment_params, :payment, :customer

    def initialize(customer_params, payment_params, store)
      @current_store = store
      @customer_params = customer_params
      @payment_params = payment_params
    end

    def save
      @customer = @current_store.customers.build(@customer_params)
      set_generated_password
      return false unless @customer.valid?
      @customer.save
      @payment = @customer.subscription.payments.build(@payment_params)
      return false unless @payment.valid?
      @payment.save
      #Tdd::RegisterMailer.notify_subscription(@customer, @password).deliver
      true
    end

    def errors
      [customer&.errors&.full_messages, payment&.errors&.full_messages].compact.flatten
    end

    def set_generated_password
      return if customer_params[:password].present?
      @password = Devise.friendly_token.first(12)
      @customer.assign_attributes(password: @password, password_confirmation: @password)
    end
end

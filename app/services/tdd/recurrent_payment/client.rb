module Tdd
  module RecurrentPayment
    module Client
      include Build
      extend self

      def exec(payment)
        @payment = payment
        return unless card_tokens_permit?
        @token = Decidir::Client.card_tokens(@customer_id)

        return unless payment_token_permit?
        data = build_payment_token(@token, @security_code)
        tokenizable_payment = Decidir::Client.payment_token(data)

        return unless tokenizable_payment
        payment = build_payment(payment.subscription, tokenizable_payment)
        Decidir::Client.collect(payment)
      end

      private
      def card_tokens_permit?
        @payment.gateway_data && (@customer_id ||= @payment.gateway_data.try(:[], :customer).try(:[], :id)).present?
        @customer_id || false
      end

      def payment_token_permit?
        @token && (@security_code ||= @payment.try(:subscription).try(:customer).try(:member).try(:code).try(:decrypt, STRING_SECURITY)).present?
        @security_code || false
      end
    end
  end
end

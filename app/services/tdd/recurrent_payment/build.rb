module Tdd
  module RecurrentPayment
    module Build

      def build_payment_token(token, security_code)
        {
          token: token,
          security_code: security_code
        }
      end

      def build_payment(subscription, tokenizable_payment)
        email = subscription.customer.email || ''
        OpenStruct.new({
          user_id: email,
          user_email:email,
          token: tokenizable_payment[:id],
          payment_method_id:1,
          bin: tokenizable_payment[:bin],
          amount: subscription.amount
        })
      end
    end
  end
end

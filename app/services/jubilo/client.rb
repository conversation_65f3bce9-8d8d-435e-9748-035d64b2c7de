module Jubilo
  class Client
    include Base
    include Build

    def initialize(env)
      @version  = 'v6'
      @base_url = Rails.env.production? ? JUBILO_PRODUCTION_URL : JUBILO_STAGING_URL
      @auth_url = @base_url + '/token'
      @api_url  = @base_url + '/api/' + @version
      @api_url_v4  = @base_url + '/api/v4/'
    end

    def login(params)
      request(:post, @auth_url, { params: params, headers: { content: 'application/x-www-form-urlencoded' } })
    end

    def client(token, cuil)
      request(:get, "#{@api_url}/cliente/#{cuil}", { headers: { token: token } } )
    end

    def search(token, params)
      request(:get, "#{@api_url_v4}/clientes?#{params.to_query}", { headers: { token: token } } )
    end

    def credit(token, id)
      request(:get, "#{@api_url}/credito/#{id}", { headers: { token: token } } )
    end

    def max_credit(token, cuil)
      headers = {"Content-type": "application/json" }.tap do |head|
        head["Authorization"] = token
      end

      response = RestClient::Request.execute(
        method: :get,
        url: "#{@api_url}/cliente/#{cuil}/MaximoMontoParaFinanciar/jubilo",
        payload: {}, headers: headers, verify_ssl: false, proxy: nil
      )

      sprintf('%.2f', response.body.to_f.round(2))
    end

    def sellers(token)
      request(:get, "#{@api_url}/vendedor", { headers: { token: token } } )
    end

    def payment_options(token, creditType, cuil, amount)
      request(:get, "#{@api_url}/credito/#{creditType}/#{cuil}/#{amount}/", { headers: { token: token } } )
    end

    def post_credit(token, creditType, cuil, amount, key_payment_option, purchase_id, seller_id, positive_identification_token)
      credit = build_credit(cuil, amount, key_payment_option, purchase_id, seller_id, positive_identification_token)
      request(:post, "#{@api_url}/Credito/#{creditType}/Generar/", { headers: { token: token }, params: credit })
    end

    def referrer(token, cuil)
      request(:delete, "#{@api_url}/Referido/#{cuil}", { headers: { token: token } } )
    end
  end
end

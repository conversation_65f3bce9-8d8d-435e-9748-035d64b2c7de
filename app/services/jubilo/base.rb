require 'rest_client'

module Jubilo
  module Base

    private
    def header(headers)
      {"Content-type": headers[:content] || "application/json" }.tap do |head|
        head["Authorization"] = headers[:token] if headers[:token].present?
      end
    end

    def request(method, path_info, args = {})
      Rails.logger.info "jubilo request:"
      Rails.logger.info "method: #{method}"
      Rails.logger.info "path_info: #{path_info}"
      Rails.logger.info "args: #{args}"
      response = RestClient::Request.execute( method: method, url: path_info, payload: args[:params], headers: header(args[:headers]) , verify_ssl: false, proxy: nil)
      Rails.logger.info "jubilo response:"
      Rails.logger.info response.body

      res_body = (response.code  == 200 && response.body == '') ? {}.to_json : response.body
      body = JSON.parse(res_body)
      deep_transform(body)
    rescue RestClient::BadRequest, RestClient::ResourceNotFound, RestClient::Unauthorized, RestClient::Conflict => e
      error =
        if e.try(:http_body)
          e.http_body.length > 2 ? JSON.parse(e.http_body) : JSON.parse('{}')
        else
          error_message
        end
      { message: e.message, status: e.http_code, error: error }
    rescue => exception
      store = Mkp::Store.find_by_name("jubilo")
      Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, store).deliver_later
      { message: exception.message, status: exception.http_code }
    end

    def deep_transform(body)
      return body.deep_transform_keys! { |key| key.underscore } if body.is_a?(Hash)
      data = body.map do |element|
        element.deep_transform_keys! { |key| key.underscore }
      end
      { clientes: data }
    end

    def error_message
      OpenStruct.new(error: 'backend_error', error_description: I18n.t('jubilo.error')).to_h
    end
  end
end

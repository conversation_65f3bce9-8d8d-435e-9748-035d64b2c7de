require 'uri'
require 'net/http'

module Gateways
  module Refunds
    class Base
      attr_accessor :status, :order, :valid, :error

      def initialize(order, reason)
        @order = order
        @payments = order.payments
        @valid = false
      end

      def perform
        if @payments.any?
          @payments.each{ |payment| payment.cancel! }
          if @payments.all { |payment| payment.status == 'cancelled' }
            refund_transaction
          else
            set_errors("Error cancelando el pago")
          end
        else
          set_errors("No existe un pago para esta orden")
        end
      rescue StandardError => e
        set_errors("Error procesando la cancelación del pago: #{e.message}")
      rescue Avenida::Payments::VisaPuntosUnexpectedResponse => e
        set_errors("Error procesando la cancelación del pago: #{e.message}")
      end

      private

      def set_errors(message)
        @order.listable.update(status: message)
        Rails.logger.error message
      end
    end
  end
end

require 'uri'
require 'net/http'

module Gateways
  module Refunds
    class Sube < Base
      attr_accessor :status, :order, :valid

      def initialize(order)
        @order = order
        @payment = order.payment
        @valid = false
      end

      private

      def refund_transaction
        url = URI("#{SUBE_URL}/refund")
        http = Net::HTTP.new(url.host, url.port)

        request = Net::HTTP::Post.new(url)
        request["content-type"] = 'application/json'

        body_hash = {
          "purchase_id" => @order.external_transaction_id
        }

        request.body = body_hash.to_json
        response = http.request(request)
        result = JSON.parse(response.body)

        if result['result_success']
          @status = "Visa Puntos: Cancelled - Sube: #{result['result_message']}"
          @order.listable.update(status: @status)
          @valid = true
        else
          @status = "No fue posible completar la transacción - #{result['result_message']}"
          set_errors(result['result_message'])
        end

      end

      def set_errors(message)
        @status = message
        @order.listable.update(status: message)
        Rails.logger.error message
      end
    end
  end
end

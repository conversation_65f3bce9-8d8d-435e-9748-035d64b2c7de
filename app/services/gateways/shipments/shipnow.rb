module Gateways
  module Shipments
    class Shipnow

      STORE_ID = SHIPNOW['store_id'].freeze
      INITIAL_STATE = 'ready_to_pick'.freeze
      # MELI_STORE_ID = SHIPNOW['meli_store_id'].freeze
      MELI_STORE_ID = 114

      WEBHOOKS_TOPICS = [
        'orders/update'
      ].freeze

      STATUSES_MAP = {
        'new' => 'in_process',
        'awaiting_payment' => 'in_process',
        'ready_to_pick' => 'in_process',
        'picking_list' => 'in_process',
        'packing_slip' => 'in_process',
        'ready_to_ship' => 'in_process',
        'shipped' => 'shipped',
        'delivered' => 'delivered',
        'not_delivered' => 'returned',
        'on_hold' => 'in_process',
        'cancelled' => 'cancelled'
      }.freeze

      class << self

        def active?
          client.active?
        end

        def process(shipment)
          response = client.create_order(shipnow_shipment_json(shipment))
          return response unless response["errors"].present?
          handle_errors(response, shipment)
        end

        def update_order(shipment)
          shipnow_json = {
            external_reference: shipment.id.to_s,
            ship_to: ship_to(shipment),
            items: shipnow_items(shipment.items),
            store_id: STORE_ID,
            status: 'on_hold',
            id: shipment.gateway_data[:id]
          }.to_json
          response = client.update_order(shipment.gateway_data[:id],shipnow_json )

        end

        def processed_at(payload_data)
          payload_data[:created_at].to_datetime
        end

        def process_notification(notification)
          topic, resource_id = notification.values_at(:topic, :resource_id)
          case topic
          when 'orders/update'
            payload = client.order(resource_id).deep_symbolize_keys
            shipment = Mkp::Shipment.find_by_gateway_object_id(resource_id)
            if shipment.present? && payload[:external_reference].to_i == shipment.id
              status = get_status(payload[:status])

              shipment.update_attributes!({
                gateway_data: update_gateway_data(shipment, payload)
              })

              process_label(shipment, payload) if payload[:shipment].present?
              Mkp::StatusChange::EntityStatusManage.status_change(shipment, status)
            end
          when 'products/update'
            Mkp::Shipments::ShipnowProductSyncWorker.perform_async(resource_id)
          end
        end

        private

        def client
          @client ||= ShipnowApi
        end

        def get_status(external_status)
          STATUSES_MAP[external_status]
        end

        def shipnow_shipment_json(shipment)
          {
            external_reference: shipment.id.to_s,
            ship_to: ship_to(shipment),
            items: shipnow_items(shipment.items),
            store_id: STORE_ID,
            status: INITIAL_STATE
          }.to_json
        end

        def ship_to(shipment)
          address = shipment.destination_address
          {
            name: address.first_name.titleize,
            last_name: address.last_name.titleize,
            zip_code: address.zip.delete('^0-9').to_i,
            city: address.city.titleize,
            email: shipment.suborders.first.customer.email,
            state: address.state.titleize
          }.tap do |destination|
            address_line = ["#{address.address} #{address.street_number}", address.address_2].select{|t| t.present?}.map(&:titleize).join(', ').strip
            destination[:address_line] = address_line
            destination[:phone] = address.telephone if address.telephone.present?
          end
        end

        def shipnow_items(items)
          items.map do |item|
            {
              external_reference: item.variant.ean_code,
              quantity: item.quantity,
              title: "#{item.variant.title} - #{item.variant.name}"
            }
          end
        end

        def handle_errors(response, shipment)
          error_titles = response["errors"].map{|e| e["title"]}
          return response unless error_titles.include?("items")
          shipment.items.map do |item|
            create_shipnow_product(item.variant)
          end
          unless ( !!@_handle_errors_done )
            @_handle_errors_done = true
            process(shipment)
          else
            response
          end
        end

        def create_shipnow_product(variant)
          client.create_product(shipnow_product_json(variant))
        end

        def shipnow_product_json(variant)
          {
            external_reference: variant.ean_code,
            title: "#{variant.title} - #{variant.name}",
            price: shipnow_price(variant.price),
            currency: "ARS"
          }.to_json
        end
        
        def shipnow_price(price)
          {
            retail: price,
            wholesale: price * 0.7,
            buy: price * 0.7
          }
        end

        def update_gateway_data(shipment, payload)
          payload.deep_dup.tap do |data|
            data[:original_payload] = shipment.gateway_data[:original_payload] || shipment.gateway_data
            data[:notifications_payloads] = shipment.gateway_data[:notifications_payloads] || {}
            data[:notifications_payloads][Time.now.to_s] = payload
          end
        end

        def process_label(shipment, payload)
          label_data = payload[:shipment]

          unless shipment.labels.where(tracking_number: label_data[:tracking_number]).present?
            courier = payload[:shipping_option][:carrier_code]
            label_price = label_data[:shipping_price].to_f + label_data[:insurance_price].to_f
            shipment.labels.create do |object|
              object.tracking_number = label_data[:tracking_number]
              object.courier = courier
              object.price = label_price

              if label_data[:estimated_delivery].present?
                object.estimated_delivery_date = label_data[:estimated_delivery].to_datetime
              end
            end
          end
        end

      end
    end
  end
end

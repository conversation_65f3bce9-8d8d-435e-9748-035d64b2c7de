module Gateways
  module Shipments
    class Pickit

      SHIPPING_PRICE = 79

      STATUSES_MAP = {
        'EnRetailer' => 'unfulfilled',
        'DisponibleParaRetiro' => 'unfulfilled',
        'EnCentroDeDistribucion' => 'shipped',
        'EnPuntoPickit' => 'shipped',
        'Retirado' => 'delivered',
        'Vencido' => 'returned',
      }.freeze

#{"ApiKey":"43EVPYXV7W","Metodo":"ObtenerCotizacionEnvio",
#"Parametros":"{\"tokenId\":\"P4NB49QYE6\",\"direccionCliente\":\"Thames 1961. CABA\",
#\"articulos\":[{\"sku\":\"112233\",\"tipoProducto\":1,\"articulo\":\"Remera Nike\",\"precio\":1,\"pesoKg\":1,\"pesoL\":1,\"pesoPV\":1}]}"}
      class << self
        def get_quotation_number(body)
          response = PickitWS.post("ObtenerCotizacionEnvio", body)
          return response
        end

  #{"ApiKey":"43EVPYXV7W","Metodo":"ObtenerInformacionPuntoSeleccionado",
  #"Parametros":"{\"tokenId\":\"P4NB49QYE6\",\"cotizacionId\":15830}"}
        def get_pickup_point_information(body)
          response = PickitWS.post("ObtenerInformacionPuntoSeleccionado", body)
          return_hash =  {
            amount: SHIPPING_PRICE,
            pickup_point_id: response[:PuntoPickit][:PuntoPickitId],
            pickup_point_name: response[:PuntoPickit][:Nombre],
            pickup_point_business: response[:PuntoPickit][:Cadena],
            pickup_point_person_name: response[:PuntoPickit][:Responsable],
            pickup_point_person_mail: response[:PuntoPickit][:EmailResponsable],
            pickup_point_address: response[:PuntoPickit][:Direccion],
            pickup_point_phone: response[:PuntoPickit][:Telefono],
            pickup_point_zip: response[:PuntoPickit][:CodigoPostal],
            pickup_point_horarios: response[:PuntoPickit][:Horarios],
            pickup_point_state: response[:PuntoPickit][:Provincia],
            carrier: "Pickit"
          }
        end


        def confirm_order(body)
          response = PickitWS.post("ImponerTransaccionEnvio", body)
        end

        def process_notification(transactionId, status)
          shipments = Mkp::Shipment.where(gateway_object_id: transactionId)
          status = STATUSES_MAP[status]
          if shipments.present?
            shipments.each do |shipment|
              Mkp::StatusChange::EntityStatusManage.status_change(shipment, status)
            end
            message = "Order Updated"
          else
            message = "Order not found"
          end
          return message
        end

        def process_label(shipment)
          body = { tokenId: PICKIT_TOKEN_ID,  transaccionId:shipment.gateway_object_id }
          response  = PickitWS.post("ObtenerEtiqueta", body.to_json)

          shipment.labels.create do |label|
            label.gateway = "Pickit"
            label.gateway_data = response
            label.gateway_object_id = shipment.gateway_object_id
            label.price = shipment.charged_amount
            label.tracking_number = response[:NumeroPedido]
            label.courier = "Pickit"
            # if (tracker = payload.tracker).present?
            #   label.courier = tracker.carrier
            #   label.tracking_number = tracker.tracking_code
            #   label.created_at = tracker.created_at
            #   label.updated_at = tracker.updated_at
            #   label.activity_log = tracker.tracking_details.map{|td| td.as_json.deep_symbolize_keys}
            # end
          end
        end

        def confirm_ready_to_pick(shipment)
          body = { tokenId: PICKIT_TOKEN_ID,  transaccionId:shipment.gateway_object_id }
          response = PickitWS.post("DisponibleParaRetiro", body.to_json)
        end

      end

    end
  end
end

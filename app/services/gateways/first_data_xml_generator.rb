module Gateways
  class FirstDataXmlGenerator
    def initialize(checkout_cart = nil, payment_data = nil, gateway_object_id = nil , amount = nil, firstdata_store = nil)
      @checkout_cart = checkout_cart
      @payment_data = payment_data
      @gateway_object_id = gateway_object_id
      @amount = amount
      @firstdata_store = firstdata_store
    end

    def generate_xml(xml_name)
      path = Rails.root.join('app', 'views', 'services', 'gateways', 'firstdata', "#{xml_name}.xml.erb")
      ERB.new(File.read(path), nil, "-").result(binding)
    end
  end
end

module Gateways
  module Labels
    class SmartCarrier
      include Smartcarrier::Const
      include Smartcarrier::Client
      include Smartcarrier::Build

      attr_accessor :carrier

      class << self
        def purchase(shipment, label_id = nil, carrier = nil)
          label = new({ shipment: shipment,
                        label_id: label_id,
                        carrier: carrier }, carrier)
          label.purchase!
        end

        def get_url(gateway_data, format = nil)
          gateway_data[:purchase_info][:label][:label_url]
        end

        def get_extra_docs(gateway_object_id, gateway_data)
          #TODO
        end

        def allow_thermal_formats?
          false
        end

        def get_order_status_by_tracking_number(tracking_number)
          label = Mkp::ShipmentLabel.find_by_tracking_number(tracking_number)
          return unless label.present?

          sm = new({}, label.courier.downcase.to_sym)
          sm.status!(label.gateway_object_id, tracking_number)
        end
      end

      def initialize(config, carrier = nil)
        return unless carrier.present?

        @shipment = config[:shipment]
        @id = config[:label_id]
        default_carrier = Couriers.get_settings_for(carrier).to_h
        carrier_attributes = CARRIER_MAP[carrier.downcase][:delivery].merge(default_carrier)
        @carrier = OpenStruct.new(carrier_attributes)
        set_data if @shipment.present?
      end

      def purchase!
        response = post_shipments(post_data_shipment)
        return false unless response.present?

        set_purchase_data(response)
      end

      def refresh!(label_id)
        label = Mkp::ShipmentLabel.find(label_id)
        response = get_shipments(label.tracking_number, label.courier.downcase.to_sym)
        return unless response.present?

        new_status = response["shipments"].first["status"]
        label.shipment.update_attributes(status: STATUS_MAP[new_status])
      end

      def status!(id, tracking_number)
        response = get_status_shipment_with(id,
                                            tracking_number,
                                            carrier.carrier)
        return unless response.present?

        return { status: response["status"] }
        nil
      end

      def courier
        carrier
      end

      def origin_data;end
    end
  end
end

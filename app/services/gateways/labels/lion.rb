require 'rest_client'
require 'barby'
require 'chunky_png'
require 'barby/barcode/code_128'
require 'barby/outputter/png_outputter'

module Gateways
  module Labels
    class Lion
      attr_reader :id, :shipment, :to, :from

      STATUSES_MAP = {
        :retiro => 'in_process',
        :en_deposito => 'in_process',
        :despachado => 'shipped',
        :entregado => 'delivered',
        :primera_visita => 'shipped',
        :segunda_visita => 'shipped',
        :devolucion => 'returned',
        :recibido => 'cancelled'
      }.freeze

      MANDATORY_KEYS_TO_PURCHASE = [:shipment_id].freeze
      GATEWAY_URL = "http://api.lionxpress.com.ar/api"

      def initialize(config)
        @shipment = config[:shipment]
        @id = config[:label_id]
        set_data if @shipment.present?
      end

      def self.purchase(shipment, label_id=nil)
        label = self.new(shipment: shipment, label_id: label_id)
        return label if label.purchase!
        nil
      end

      def self.get_order_status_by_tracking_number(tracking_number)
        response = JSON(RestClient.get("#{GATEWAY_URL}/OrderStatus/#{LION_KEY}/#{tracking_number}"))
        { status: ParseTrackingResponse.parse_tracking_response(response["id"]), id: response }
        rescue RestClient::ExceptionWithResponse => err
          nil
      end

      def self.get_url(gateway_data, format)
        "/manage/ar/shipments/#{gateway_data[:label_id]}.pdf"
      end

      def self.get_extra_docs(gateway_object_id, gateway_data)
        #TODO
      end

      def self.mandatory_keys
        MANDATORY_KEYS_TO_PURCHASE
      end

      def self.allow_thermal_formats?
        false
      end

      def purchase!
        post_purchase
      end

      def door_to_door_purchase
        @to = @shipment.destination_address
        @from = @shipment.origin_address
      end

      def first_mile_purchase
        address_data = Network['AR'].pickit_warehouse
        @to = OpenStruct.new(
                full_name: address_data["name"],
                zip: address_data["zip_code"],
                city: address_data["city"],
                address: address_data["address"],
                address_2: nil,
                telephone: nil
              )
        @from = @shipment.origin_address
      end

      def tracking_code
        suborder.public_id
      end

      def selected_rate
        OpenStruct.new(carrier: 'Lion', rate: total)
      end

      def total
        0 # TODO
      end

      def courier
        Couriers.get_settings_for('lion')
      end

      def title
        "LION XPRESS"
      end

      def to_hash
        {
          :shipment_id => @shipment.id,
          :label_id => @id
        }
      end

      def operation_id
        0 # No aplica
      end

      def icon

      end

      def origin_data

      end

      def contact_name

      end

      def image_code(data)
        code = Barby::Code128B.new(data).to_png(margin:3, height: 50)
        Base64.encode64(code.to_s).gsub(/\s+/, "")
      end

      def get_package(shipment)
        return {} if shipment.blank?
        Mkp::Shipping::Packager.new(shipment.items).get_package_values(length: :centimeters, mass: :kilograms)
      end


      def suborder
        @shipment.suborders.first
      end

      def shop
        suborder.shop
      end

      def by_pdf label
        OpenStruct.new(
              tracking_number: tracking_code,
              shop: shop.title,
              product: @shipment.items.map(&:product).map(&:title).join(", "),
              weight: @shipment.items.to_a.sum(0, &:total_weight) || "-",
              quantity: @shipment.items.to_a.sum(&:quantity),
              destination: @to.full_name,
              street: "#{@to.address} #{@to.street_number}",
              floor: @to.address_2,
              city: @to.city,
              zip:@to.zip,
              barcode: tracking_code
          )
      end

      private

      def set_data
        @shipment.is_pickup? ? first_mile_purchase : door_to_door_purchase
      end

      def post_purchase
        RestClient.post("#{GATEWAY_URL}/external/orders", post_purchase_data.to_json, {content_type: :json})
      rescue RestClient::ExceptionWithResponse => err
        nil
      end

      def post_purchase_data
        data = {
          'key' => LION_KEY,
          'orders' => [{
                       'externalCode' => tracking_code.to_s,
                       'supplier' => post_purchase_from
                     }]
        }
        data['orders'][0].merge!(post_purchase_to)
        data
      end

      def post_purchase_to
        {
          'fullName' => @to.full_name,
          'email' => suborder.order.customer.email,
          'phone' => @to.telephone || "",
          'address' => post_purchase_address(@to)
        }
      end

      def post_purchase_address(address)
        {
          'street' => address.address,
          'number' => address.street_number,
          'zipCode' => address.zip,
          'locality' => address.city,
          'addressLine1' => address.address_2 || ""
        }
      end

      def post_purchase_from
        {
          'externalId' => shop.id.to_s,
          'businessName' => shop.title,
          'email' => "<EMAIL>",
          'phone' => @from.telephone || "",
          'address' => post_purchase_address(@from),
          'products' => post_purchase_products
        }
      end

      def post_purchase_products
        @shipment.items.map { |item|
          {
            'name' => item.title,
            'description' => "",
            'qty' => item.quantity.to_s,
            'weight' => item.product.weight.to_s.present? ? item.product.weight.to_i : 0 ,
            'externalId' => item.id.to_s
          }
        }
      end

      module ParseTrackingResponse
        extend self
        WORDS = [
            {id:2,motive: "En depósito de operador logístico"},
            {id:4,motive: "Entregado"},
            {id:5,motive: "Primera visita"},
            {id:6,motive: "Segunda visita"},
            {id:7,motive: "Anómalo"},
            {id:8,motive: "En proceso de retiro"},
            {id:10,motive: "Cancelado"},
            {id:11,motive: "Programado para visita en domicilio"}
          ]
        DEFAULT_RULE = {sequence:[8,2,11,4], relative_id:{ 1 => 2, 3 => 11, 9 => 8}}
        RULES = [
            {ids:[5], sequence:[8,2,11,5,4]},
            {ids:[6], sequence:[8,2,11,5,6,4]},
            {ids:[7], sequence:[7]},
            {ids:[10], sequence:[10]}
          ]

        def parse_tracking_response(id)
          rule = RULES.find{|rule| rule[:ids].include? id} || DEFAULT_RULE
          id = rule.try(:[],:relative_id).try(:[],id) || id
          sequence = rule[:sequence]
          sequence.each_with_index.map do |value, index|
            motive = WORDS.find{|e| e[:id].eql?(value)}[:motive]
            active = sequence.index(id) >= index
            { motivo: motive, active: active }
          end
        end
      end
    end
  end
end


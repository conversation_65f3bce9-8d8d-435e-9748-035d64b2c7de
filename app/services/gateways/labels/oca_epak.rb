require 'rest-client'
module Gateways
  module Labels
    class OcaEpak

      COURIER = :oca.freeze
      OPERATION_SERVICE = {:door_to_door => 291671, :local_to_door => 291673, :door_to_local => 291672}.freeze
      MANDATORY_KEYS_TO_PURCHASE = [:orden_retiro, :numero_envio].freeze
      SERVICES_WITH_EXTRA_DOCS = %w(290779 290781 270768 270769 270770 270771 270772 270773 270774)
      LABEL_URL = "http://www5.oca.com.ar/OCAEpakNet/Views/Impresiones/Etiquetas.aspx?IdOrdenRetiro=".freeze
      ADMISION_DOC_URL = "http://www5.oca.com.ar/OCAEpakNet/Views/Impresiones/OrdenDeAdmision.aspx?IdOrdenRetiro=".freeze

      attr_reader :courier

      class << self
        def mandatory_keys
          MANDATORY_KEYS_TO_PURCHASE
        end

        def purchase(shipment, label_id)
          return unless shipment.extra_info[:gateway_info][:oca_epak].present?

          new(shipment.extra_info[:gateway_info][:oca_epak]).purchase(shipment, label_id)
        end

        def cancel(delivery_order_id)
          if cancel_response = client.cancel_delivery_order(delivery_order_id)
            if cancel_response[:id_result] == "100"
              JSON.parse(cancel_response.to_json)
            elsif cancel_response[:id_result] == "130"
              if cancel_response[:mensaje] =~ /Orden Retiro Anulada/
                JSON.parse(cancel_response.to_json)
              end
            end
          end
        end

        def get_url(gateway_data, format)
          if delivery_details = get_delivery_details(gateway_data[:purchase_info])
            label_url(delivery_details)
          else
            order_operation_id = gateway_data[:purchase_info][:resumen][:codigo_operacion]
            order_result = client.get_order_result(order_operation_id)
            if delivery_details = get_delivery_details(order_result)
              label_url(delivery_details)
            end
          end
        end

        def get_extra_docs(delivery_order_id, gateway_data)
          if delivery_details = get_delivery_details(gateway_data[:purchase_info])
            if SERVICES_WITH_EXTRA_DOCS.include?(delivery_details[:operativa])
              [{
                form_type: 'Orden de Admisión',
                form_url: "#{ADMISION_DOC_URL}#{delivery_details[:orden_retiro]}"
              }]
            end
          end
        end

        def get_shipping_rate(shipment)
          return unless (config = shipment.extra_info[:gateway_info][:oca_epak]).present?
          new(config).get_rate(shipment)
        end

        def get_order_status_by_tracking_number(tracking_number)
          response= RestClient.get("webservice.oca.com.ar/epak_tracking/Oep_TrackEPak.asmx/Tracking_Pieza_ConIdEstado?numeroEnvio=#{tracking_number}")
          response = Hash.from_xml(response)
          data = response["DataSet"]["diffgram"]["NewDataSet"]["Table"]
          keys = ["Motivo" ,"Estado" ,"Sucursal","Sucursal_Direccion", "Fecha"]
          data = [data] if data.is_a?(Hash)
          data.map do |delivery_status|
            {}.tap{ |status| keys.each{|k| status[k.snakecase] = delivery_status[k]} }
          end
        end

        def allow_thermal_formats?
          false
        end

        private

        def client
          client = Oca::Epak::Client.new(OCA_EPAK_USER, OCA_EPAK_PASSWORD)
        end

        def retrieve_status(delivery_order_id)
          client.get_delivery_status(delivery_order_id: delivery_order_id)
        end

        def get_delivery_details(data)
          data[:detalle_ingresos]
        end

        def label_url(details)
          "#{LABEL_URL}#{details[:orden_retiro]}"
        end
      end

      class LabelInfo
        attr_reader :tracking_code, :id

        def initialize(purchase_info, rate)
          @rate = rate
          @purchase_info = purchase_info

          ingresos = purchase_info[:detalle_ingresos] || {}
          @id = ingresos[:orden_retiro]
          @tracking_code = ingresos[:numero_envio]
        end

        def selected_rate
          OpenStruct.new(carrier: 'Oca', rate: @rate[:total])
        end

        def to_hash
          {
            purchase_info: JSON.parse(@purchase_info.to_json),
            rate: JSON.parse(@rate.to_json)
          }
        end
      end

      def initialize(config = {})
        @client = Oca::Epak::Client.new(OCA_EPAK_USER, OCA_EPAK_PASSWORD)
        @account_number = OCA_EPAK_ACCOUNT
        @cuit = OCA_EPAK_CUIT
        @operation = config[:operation]
        @origin = config[:origin]
        @courier = Couriers.get_settings_for(config[:courier])
      end

      def valid?
        @client.check_credentials
      rescue
        false
      end

      def title
        "#{courier.name} - #{@operation[:name]}"
      end

      def origin_data
        @origin.except(:contact_name)
      end

      def operation_id
        @operation[:id]
      end

      def operation_is?(service)
        operation_id.to_i == OPERATION_SERVICE[service]
      end

      def contact_name
        @origin[:contact_name]
      end

      def operations
        @operations ||= @client.get_operation_codes
      end

      def get_package(shipment)
        return {} if shipment.blank?
        Mkp::Shipping::Packager.new(shipment.items).get_package_values(length: :centimeters, mass: :kilograms)
      end

      def purchase(shipment, label_id)
        @label_id = label_id
        rate = get_rate(shipment)
        purchase_info = @client.create_multiple_delivery_orders({
          deliveries_data: deliveries_data(shipment),
          confirm_deliveries: confirm_deliveries?.to_s
        })
        if purchase_info.present?
          LabelInfo.new(purchase_info, rate)
        end
      rescue Oca::Errors::BadRequest, Savon::HTTPError, Savon::SOAPFault, HTTPClient::ReceiveTimeoutError
        nil
      end

      # ##### WE MUST CLEAN THIS METHOD AT SOME POINT #####
      #
      # This is a temporary (and UGLY as fuck) method in order to build the configuration
      # hash that need to go to the shop settings, under :delivery_channel_policy
      # in a key called:
      #   on_demand_labels_config: {
      #     oca_epak: ... THE RESPONSE GOES IN HERE ...
      #   }
      def config_hash_for_shop(operation_id, origin, contact_name)
        return unless (operation_hash = operations.find{ |operation| operation[:id_operativa] == operation_id.to_s }).present?

        case origin
        when String # we asume that it's the CODE for the taxation center to drop the package
          return unless (admision_center = admision_centers(full: true).find{ |store| store[:sigla].try(:strip) == origin }).present?

          origin_hash = {
            street: admision_center[:calle].try(:strip).to_s.titleize,
            street_number: admision_center[:numero].try(:strip).to_s,
            zip_code: admision_center[:codigo_postal].try(:strip).to_s,
            city: admision_center[:localidad].try(:strip).to_s.titleize,
            state: admision_center[:provincia].try(:strip).to_s.titleize,
            contact_name: contact_name,
            id_taxation_center: admision_center[:id_centro_imposicion].try(:strip).to_s,
            phone: admision_center[:telefono].try(:strip).to_s,
            code: admision_center[:sigla].try(:strip).to_s
          }
        when Hash # we asume that's a hash with an address data
          origin_hash = origin.dup.merge(contact_name: contact_name)
        end
        return unless origin_hash.present?

        {
          courier: :oca,
          operation: {
            id: operation_hash[:id_operativa].strip.to_s,
            name: operation_hash[:descripcion].strip.to_s
          },
          origin: origin_hash
        }
      end

      def admision_centers(full: false)
        full ? @client.taxation_centers_with_services : @client.taxation_centers
      end

      def needs_extra_docs_to_ship?
        SERVICES_WITH_EXTRA_DOCS.include?(@operation[:id])
      end

      private

      def get_rate(shipment)
        packages = package_hash(shipment)
        opts = {
          total_weight: packages.total_weight,
          total_volume: BigDecimal(packages.volume.to_s),
          origin_zip_code: @origin[:zip_code].to_s,
          destination_zip_code: only_numbers_zip_code(shipment.destination_address[:zip]),
          declared_value: (shipment.items.to_a.sum(0, &:total)/2).round.to_s,
          package_quantity: packages.get_package_values(length: :centimeters, mass: :kilograms).count,
          cuit: @cuit,
          operation_code: @operation[:id].to_s,
        }
        @client.get_shipping_rate(opts)
      end

      def get_multipackages(items)
        product = items.map(&:product).max_by{ |p| p.packages.to_i }
        (product.packages || 1).to_s
      end

      def deliveries_data(shipment)
        Oca::Epak::MultipleDeliveriesData.new(
          account_number: @account_number,
          deliveries: deliveries_data_hash(shipment)
        )
      end

      def deliveries_data_hash(shipment)
        { origin_data_hash => [ delivery_hash(shipment) ] }
      end

      def origin_data_hash
        {
          "cp" => @origin[:zip_code].to_s,
          "contacto" => @origin[:contact_name].to_s,
          "solicitante" => "Avenida.com"
        }.tap do |hash|
          if @origin[:id_taxation_center].present?
            hash["id_centro_de_imposicion"] = @origin[:id_taxation_center].to_s
          else
            hash["fecha"] = (Time.now + 1.day).strftime('%Y%m%d')
            hash["calle"] = @origin[:street].to_s
            hash["numero"] = @origin[:street_number].to_s
            hash["piso"] = @origin[:floor].to_s if @origin[:floor].present?
            hash["departamento"] = @origin[:apartment].to_s if @origin[:apartment].present?
            hash["localidad"] = @origin[:city].to_s
            hash["provincia"] = @origin[:state].to_s
          end
        end
      end

      def delivery_hash(shipment)
        reference = "#{shipment.order.id}/#{shipment.id}/#{@label_id.to_s}"
        {
          "id_operativa" => @operation[:id].to_s,
          "numero_remito" => reference,
          "destinatario" => destination_hash(shipment),
          "paquetes"=> package_hash(shipment).get_package_values(length: :centimeters, mass: :kilograms)
        }
      end

      def destination_hash(shipment)
        destination = shipment.destination_address
        address_2 = destination[:address_2].present? ? destination[:address_2].gsub(/"/, '') : nil
        street = [ set_street_number(destination), address_2 ].compact.join(', ').squish.gsub(/"/, '')
        skus = shipment.items.map(&:variant).map(&:sku).join(' / ')
        {
          "apellido" => destination[:last_name],
          "nombre" => destination[:first_name],
          "calle" => street.truncate(30, separator: ' ', omission: ''),
          "numero" => "",
          "piso" => "",
          "departamento" => "",
          "cp" => only_numbers_zip_code(destination[:zip]),
          "localidad" => destination[:city],
          "provincia" => destination[:state],
          "telefono" => destination[:telephone],
          "celular" => destination[:telephone],
          "observaciones" => skus
        }
      end

      def only_numbers_zip_code(zip_code)
        zip_code.to_s[/(\d{4})/, 1].to_s
      end

      def set_street_number(destination)
        destination[:street_number].present? ? "#{destination[:address]} #{destination[:street_number]}" : destination[:address]
      end

      def full_address(destination)
        address_2 = destination[:address_2].present? ? destination[:address_2] : nil
        address = set_street_number(destination)
        [
          [ address, address_2 ].compact.join(', ').squish,
          "CP: #{destination[:zip]}",
          destination[:city],
          destination[:state]
        ].compact.join(' - ').squish.gsub(/"/, '')
      end

      def package_hash(shipment)
        @package_hash ||= Mkp::Shipping::Packages.new(shipment.items)
      end

      def confirm_deliveries?
        (Rails.env.development? || ON_STAGING) ? false : true
      end
    end
  end
end

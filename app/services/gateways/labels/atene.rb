require 'rest_client'
require 'rqrcode'

module Gateways
  module Labels
    class BadRequestError < StandardError; end

    class Atene
      attr_reader :id, :shipment, :to, :from
      attr_accessor :tracking_code, :purchase_info

      STATUSES_MAP = {
        '1' => 'in_process',#en_preparacion_de_orden
        '2' => 'shipped',#en_camino
        '3' => 'delivered',#entregado
        '4' => 'returned',#devuelto
        '5' => 'shipped',#primera_visita
        '6' => 'delivered'#retirado
      }.freeze

      MANDATORY_KEYS_TO_PURCHASE = [:shipment_id].freeze
      DELIVERY_TYPE = ['48 hs', 'Same Day'].freeze
      GATEWAY_URL = ATENE_URL.freeze
      GATEWAY_URL_API = "#{GATEWAY_URL}/api/v1".freeze

      def initialize(config)
        @shipment = config[:shipment]
        @id = config[:label_id]
        set_data if @shipment.present?
      end

      def self.purchase(shipment, label_id=nil)
        label = self.new(shipment: shipment, label_id: label_id)
        label.purchase!
      end

      def self.get_url(gateway_data, format)
        "/manage/ar/shipments/#{gateway_data[:label_id]}.pdf"
      end

      def self.get_extra_docs(gateway_object_id, gateway_data)
        #TODO
      end

      def self.mandatory_keys
        MANDATORY_KEYS_TO_PURCHASE
      end

      def self.allow_thermal_formats?
        false
      end

      def purchase!
        response = post_purchase
        set_purchase_data(response)
      rescue RestClient::BadRequest => e
        nil
      rescue RestClient::UnprocessableEntity => e
        nil
      rescue RestClient::GatewayTimeout => e
        nil
      end

      def selected_rate(total = nil)
        OpenStruct.new(carrier: 'Atene', rate: total)
      end

      def courier
        Couriers.get_settings_for('atene')
      end

      def title
        "ATENE LOGISTIC"
      end

      def to_hash(data = nil)
        {
          shipment_id: @shipment.id,
          label_id: @id,
          purchase_info: data
        }
      end

      def by_pdf(label)
        suborder = @shipment.suborders.first
        info = label[:gateway_data][:purchase_info]
        product = info[:product]
        to = info[:buyer]
        OpenStruct.new(
              tracking_number: suborder.public_id,
              shop: suborder.shop.title,
              product: product[:name],
              weight: product[:weight],
              quantity: product[:quantity],
              destination: to[:name],
              street: "#{to[:street]} #{to[:number]}",
              floor: to[:floor],
              city: to[:city],
              zip:to[:zip],
              barcode: info[:id].to_s
          )
      end

      def image_code(data)
        code = RQRCode::QRCode.new(data).as_png(
          resize_gte_to: false,
          resize_exactly_to: false,
          fill: 'white',
          color: 'black',
          size: 120,
          border_modules: 4,
          module_px_size: 6,
          file: nil
          )
        Base64.encode64(code.to_s).gsub(/\s+/, "")
      end

      def operation_id
        0 # No aplica
      end

      def icon;end

      def origin_data;end

      def contact_name;end

      def get_package(shipment)
        return {} if shipment.blank?
        Mkp::Shipping::Packager.new(shipment.items).get_package_values(length: :meters, mass: :kilograms)
      end

      def suborder
        @shipment.suborders.first
      end

      def package
        @package ||= get_package(@shipment)
      end

      def shop
        suborder.shop
      end

      def default_delivery_type
        DELIVERY_TYPE.first
      end

      def set_purchase_data(response)
        response.map { | order | label_struct(order) }
      end

      def label_struct(data)
        OpenStruct.new( tracking_code:data['tracking_number'],
                        selected_rate: selected_rate(data['sending_cost']),
                        id:data['id'],
                        to_hash:to_hash(data)
                      )
      end

      def get_order_status(id)
        request_auth(:get, "#{GATEWAY_URL_API}/status/#{id}")
      end

      def self.get_order_status_by_tracking_number(tracking_number)
        label = self.new({})
        label.get_order_by(tracking_number)
      end

      def get_order_by(tracking_number)
        request_auth(:get, "#{GATEWAY_URL_API}/status/code/#{tracking_number}")
      end

      private
      def set_data
        @to = @shipment.destination_address
        @from = @shipment.origin_address
      end

      def token(params)
        @token_type ||= params['token_type']
        @token ||= params['access_token']
      end

      def header
        @header ||= {Authorization: "#{@token_type} #{@token}", content_type: :json} if @token.present?
      end

      def  authorization
        return if @token.present? & @token_type.present?
        oauth
      end

      def oauth
        response = request(:post, "#{GATEWAY_URL}/oauth/token", ATENE_KEYS)
        token(response)
      end

      def get_all_shipping_companies
        request_auth(:get, "#{GATEWAY_URL_API}/shipping_companies")
      end

      def get_all_orders
        request_auth(:get, "#{GATEWAY_URL_API}/orders")
      end

      def get_cancel_order(id)
        request_auth(:get, "#{GATEWAY_URL_API}/cancel_order/#{id}")
      end

      def post_quotient(item)
        request_auth(:post, "#{GATEWAY_URL_API}/quotient", quote_data(item).to_json)
      end

      def post_purchase
        request_auth(:post, "#{GATEWAY_URL_API}/orders", post_purchase_data.to_json)
      end

      def request_auth(method, path_info, params = nil)
        authorization
        request(method, path_info, params)['response']
        rescue RestClient::ResourceNotFound
          nil
      end

      def request(method, path_info, params = nil)
        response = RestClient::Request.execute( method: method, url: path_info, payload: params, headers: header)
        JSON.parse(response.body)
      end

      def sanitize_address_name(address)
        if address.street_number.present?
          [address.address, address.street_number]
        else
          address_parts = address.address.scan(/\w+/)
          street_number = address_parts.pop
          [address_parts.join(' '), street_number]
        end
      end

      def quote_data(item)
        product = item.product
        {
          "quote" => {
            "zip_origin" => @from.zip,
            "zip_destiny" => @to.zip,
            'weight' => package[:weight],
            'height' => package[:height],
            'length' => package[:length],
            'width' => package[:width],
            'price' => item.sale_price,
            "delivery_type" => default_delivery_type
            }
        }
      end

      def post_purchase_data
        {
          'order' => {
            'order_attributes' => order_attributes,
            'buyer_attributes' => buyer_attributes,
            'product_attributes' => product_attributes
          }
        }
      end

      def order_attributes
        warehouse_name = shop.warehouse.first_name if shop.warehouse.present?
        {
          'detail' => '',
          'delivery_type' => default_delivery_type,
          'sale_number' => suborder.order.id,
          'order_status_id' => 1,
          'warehouse_name' => warehouse_name,
          'shipping_company_id' => nil
        }
      end

      def buyer_attributes
        address = @to
        street, street_number = sanitize_address_name(address)
        {
          'name' => address.full_name,
          'email' => '<EMAIL>',
          'street' => street,
          'number' => street_number.to_i,
          'floor' => '1',
          'department' => address.address_2,
          'area_code' => '',
          'phone' => address.telephone,
          'zip' => address.zip,
          'city' => address.city,
          'province' => address.state,
          'between_streets' => ''
        }
      end

      def product_attributes
        @shipment.items.map do |item|
          {
            'name' => item.title,
            'description' => item.product.description.gsub(/[^a-zA-Z0-9\- ]/,""),
            'price' => item.sale_price.to_s,
            'weight' => package[:weight],
            'height' => package[:height],
            'length' => package[:length],
            'width' => package[:width],
            'quantity' => item.quantity.to_i,
            'parts' => 1,
            'image' => ''
          }
        end
      end
    end
  end
end

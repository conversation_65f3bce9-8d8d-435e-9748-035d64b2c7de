module Gateways
  module Labels
    class Easypost

      MANDATORY_KEYS_TO_PURCHASE = [:shipment_id, :rate_id].freeze
      ALLOWED_FORMATS = [:pdf, :epl2, :zpl]
      DOMESTIC_SPEEDS = ['ground', '3days', '2days', '1day'].freeze
      INTERNATIONAL_SPEEDS = ['intl_priority', 'intl_express'].freeze

      STATUSES = {
        'unknown' => 'in_process',
        'pre_transit' => 'in_process',
        'available_for_pickup' => 'in_process',
        'in_transit' => 'shipped',
        'out_for_delivery' => 'shipped',
        'delivered' => 'delivered',
        'return_to_sender' => 'returned',
        'failure' => 'cancelled',
        'cancelled' => 'cancelled',
        'error' => 'cancelled'
      }.freeze

      class << self
        def mandatory_keys
          MANDATORY_KEYS_TO_PURCHASE
        end

        def retrieve(external_shipment_id)
          EasyPost::Shipment.retrieve(external_shipment_id)
        rescue
          Rails.logger.info('Unable to retrieve the Shipment.')
        end

        def purchase(shipment, label_id)
          data = shipment.extra_info
          raise ArgumentError unless has_required_keys?(data)
          shipment_id, rate_id = data[:gateway_info].values_at(*MANDATORY_KEYS_TO_PURCHASE)

          external_shipment = retrieve(shipment_id)

          get_rate(external_shipment, rate_id) if external_shipment.present?
        end

        def cancel(external_shipment_id)
          if external_shipment = retrieve(external_shipment_id)
            refunded_shipment = external_shipment.refund
            refunded_shipment.refund_status !=  "rejected" ? refunded_shipment : nil
          end
        end

        def get_url(gateway_data, format)
          postage_label = gateway_data[:postage_label]

          unless (label_url = postage_label[:"label_#{format}_url"])
            if ALLOWED_FORMATS.include?(format)
              return get_format(gateway_data[:id], format)
            else
              return postage_label[:label_url]
            end
          end

          label_url
        end

        def get_extra_docs(external_shipment_id, gateway_data)
          retrieve(external_shipment_id).forms.map(&:to_hash)
        end

        def get_status(external_status)
          STATUSES[external_status]
        end

        def has_required_keys?(data)
          return false unless (gateway_info = data[:gateway_info]).present?
          MANDATORY_KEYS_TO_PURCHASE.all? { |key| gateway_info.key?(key) }
        end

        def allow_thermal_formats?
          true
        end

        private

        def get_rate(external_shipment, rate_id)
          if external_shipment.selected_rate.present?
            external_shipment
          else
            external_shipment.buy(rate: { id: rate_id })
          end
        end

        def get_format(external_shipment_id, format)
          external_shipment = retrieve(external_shipment_id)
          unless external_shipment.postage_label.send("label_#{format}_url")
            external_shipment.label(file_format: format)
          end
          external_shipment.postage_label.send("label_#{format}_url")
        end

      end

    end
  end
end

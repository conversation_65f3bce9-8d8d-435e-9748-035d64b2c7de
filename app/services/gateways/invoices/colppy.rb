module Gateways
  module Invoices
    class Colppy

      COMPANY_ID = COLPPY_COMPANY_ID.freeze
      SHIPMENT_PRODUCT_CODE = 'GTOCOMLOGISTICA'.freeze

      class << self
        def instance
          @@instance ||= new
        end

        def process(order)
          invoice_order = Gateways::Invoices::OrderMapper.new(order)
          if instance.valid? && (customer = get_or_create_customer(invoice_order)).present?

            items = create_items(invoice_order)

            if (unit_price = invoice_order.shipments_cost) > 0
              items << {
                product: instance.shipment_product,
                unit_price: unit_price,
                quantity: 1
              }
            end

            instance.create_invoice!(items, customer, invoice_order)
          end
        end

        def credit_note_generate(order, suborder = nil)
          object_credit_note = suborder.present? ? Gateways::Invoices::SuborderMapper.new(suborder) : Gateways::Invoices::OrderMapper.new(order)
          if instance.valid? && (customer = get_or_create_customer(object_credit_note)).present?

            items = create_items(object_credit_note)

            if (unit_price = object_credit_note.shipments_cost) > 0
              items << {
                product: instance.shipment_product,
                unit_price: unit_price,
                quantity: 1
              }
            end

            instance.create_credit_note!(items, customer, instance.description_for_invoice(object_credit_note))
          end
        end

        def create_items(order)
          if (products = get_or_create_products(order)).present?
            build_items(order, products)
          else
            []
          end
        end

        def get_invoice_url(order)
          customer = instance.get_customer_by_email(order.customer.email)
          invoice = order.invoices.first
          params = "idEmpresa=#{COLPPY_COMPANY_ID}&idCliente=#{customer.id}&idFactura=#{invoice.gateway_object_id}&idUsuario=#{COLPPY_USER["username"]}&correo=no"
          url = "https://login.colppy.com/resources/php/fe/FE_ImprimirEnviarFactura.php?"+params
        end

        private

        def get_or_create_customer(order)
          customer = instance.get_customer_by_email(order.customer.email)
          return customer if customer.present?

          customer_doc_number = order.payment.get_customer_legal_id
          instance.create_customer(order.customer, customer_doc_number)
        end

        def get_or_create_products(order)
          {}.tap do |result|
            order.items.each do |item|
              product = instance.get_product_by_sku(item.variant.gp_sku)
              result[item.id] = product.present? ? product : instance.create_product(item.variant)
            end
          end
        end

        def build_items(order, products)
          discount = order_discount(order)
          order.items.map do |item|
            {
              product: products[item.id],
              unit_price: item.unit_price_charged,
              discount_percentage: discount,
              quantity: item.quantity,
              tax: item.product.iva
            }
          end
        end

        def order_discount(order)
          shipments_cost = order.shipments_cost
          total = order.total - shipments_cost
          total_without_discount = order.total_without_discount - shipments_cost
          (100 - (total * 100 / total_without_discount)).round(2)
        end

      end

      def initialize
        @mode = Rails.env.production? && !ON_STAGING ? 'live' : 'sandbox'
        @user = ::Colppy::User.new(COLPPY_USER['username'], COLPPY_USER['password'])
        @company = client.company(COMPANY_ID)
      end

      def client
        @client ||= ::Colppy::Client.new(
          COLPPY_CLIENT['username'],
          COLPPY_CLIENT['password'],
          @user,
          @mode
        )
      end

      def valid?
        client.session_key.present?
      end

      def get_customer_by_email(email)
        Array.wrap(@company.customers({filter:[ field: "Email", op: "=", value: email]})[:results]).last
      end

      def get_product_by_sku(gp_sku)
        @company.product_by_code(gp_sku)
      end

      def create_invoice!(items, customer, order)
        invoice = ::Colppy::SellInvoice.new(client: @client, company: @company, customer: customer)
        invoice.invoice_number1 = "0003"
        invoice.invoice_type_id = "B"
        invoice.receipt_type_id = "8"
        invoice.payment_condition_id = "Contado"
        invoice.electronic_bill = "Factura Electrónica"
        invoice.status_id = "Cobrada"
        invoice.description =  description_for_invoice(order)
        #invoice.invoice_date = "28-09-2018" if order.created_at.strftime('%m/%y') == '09/18' #remove after billing
        items.each do |item|
          invoice.add_item(
            product: item[:product],
            unit_price: item[:unit_price],
            discount_percentage: item[:discount_percentage],
            quantity: item[:quantity],
            tax: item[:tax]
          )
        end

        pay = build_payment(invoice, order)
        invoice.add_payment(pay)

        invoice.save
      end

      def build_payment(invoice, order)
        {
          payment_type_id: 'Tarjeta de Credito',
          amount: invoice.total_charged
        }.merge!(data_payment(order.payment))
      end

      def data_payment(payment)
        gateway = payment.gateway.downcase.to_sym
        account = 'MERCADO PAGO' if gateway == :mercadopago
        account = 'TODO PAGO'  if gateway == :todopago
        { payment_account_id: (account || 'DECIDIR') }
      end

      def create_credit_note!(items, customer, description)
        credit_note = ::Colppy::CreditNote.new(client: @client, company: @company, customer: customer)
        credit_note.invoice_number1 = "0003"
        credit_note.invoice_type_id = "B"
        credit_note.receipt_type_id = "NCV"
        credit_note.status_id = "Aprobada"
        credit_note.description = description
        items.each do |item|
          credit_note.add_item(
            product: item[:product],
            unit_price: item[:unit_price],
            discount_percentage: item[:discount_percentage],
            quantity: item[:quantity],
            tax: item[:tax]
          )
        end
        credit_note.save
      end

      def create_customer(gp_customer, doc_number)
        address = gp_customer.addresses.last
        state = parse_state address

        params = default_params.merge({
          name: gp_customer.full_name,
          fantasy_name: gp_customer.full_name,
          doc_number: doc_number,
          email: gp_customer.email,
          address: address.full_address,
          address_city: address.city,
          address_zipcode: address.zip,
          address_state: state,
          legal_address: address.full_address,
          legal_address_city: address.city,
          legal_address_zipcode: address.zip,
          legal_address_state: state,
          tax_condition_id: '3'
        })
        ::Colppy::Customer.new(params).save
      end

      def parse_state(address)
        return "CABA" if address.state == "Capital Federal"
        return "Buenos Aires" if address.state == "GBA" || address.state == "Bs As Interior"
        address.state
      end

      def create_product(variant)
        product_params = default_params.merge({
          name: "#{variant.title}, #{variant.name}, GP_SKU: #{variant.gp_sku}".truncate(100),
          code: variant.gp_sku,
          detail: variant.name,
          sell_price: variant.price,
          inventory_account: "Mercaderias",
          sales_costs_account: "Costo de Mercaderias Vendidas",
          sales_account: "Ventas Locales"
        })

        ::Colppy::Product.new(product_params).save
      end

      # def shipment_product
      #   shipment_product_params = default_params.merge({
      #     id: "109213",
      #     name: "Logística",
      #     code: SHIPMENT_PRODUCT_CODE,
      #     sales_account: "Ingresos Varios"
      #   })
      #   ::Colppy::Product.new(shipment_product_params)
      # end

      def shipment_product
        shipment_product_params = default_params.merge({
          id: "0",
          name: "Logística",
          code: "0",
          # inventory_account: "Fletes",
          sales_account: "Ingresos Varios",
        })
        ::Colppy::Product.new(shipment_product_params)
      end

      def description_for_invoice(order)
        "#{order.id} - #{order.title}".truncate(100)
      end

      private

      def default_params
        {
          client: client,
          company: @company
        }
      end
    end
  end
end

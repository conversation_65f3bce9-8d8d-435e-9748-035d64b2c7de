module Gateways
  module Invoices
    class OrderMapper

      DEFAULT_METHOD = [:id , :title, :customer, :invoices, :payment, :network, :shipments_cost, :created_at ]

      attr_accessor :order, :orders_invoice

      def initialize(order)
        @order = order
        @orders_invoice = order.suborders.reject{|s| s.shop.fc?}
      end

      DEFAULT_METHOD.each do |default_method|
        define_method default_method do
          @order.send(default_method)
        end
      end

      def items
        orders_invoice.map(&:items).flatten.compact
      end

      def total
        total_without_discount - order.bonified_amount_total.to_f - order.promotion_discount.to_f
      end

      def total_without_discount
        orders_invoice.to_a.sum(0, &:total_without_shipment) + shipments_cost
      end
    end
  end
end

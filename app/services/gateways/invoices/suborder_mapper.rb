module Gateways
  module Invoices
    class SuborderMapper

      DEFAULT_METHOD_ORDER = [ :id, :title, :customer, :invoices, :payment, :network ]
      DEFAULT_METHOD = [ :items, :total, :total_without_discount, :shipment_cost ]

      attr_accessor :suborder, :orders_invoice

      def initialize(suborder)
        @suborder = suborder
      end

      DEFAULT_METHOD_ORDER.each do |default_method|
        define_method default_method do
          suborder.order.send(default_method)
        end
      end

      DEFAULT_METHOD.each do |default_method|
        define_method default_method do
          if suborder.send(default_method).kind_of? Numeric
            suborder.shop.fc? ? suborder.send(:shipment_cost) : suborder.send(default_method)
          elsif suborder.send(default_method).is_a? Array
            suborder.shop.fc? ? [] : suborder.send(default_method)
          elsif suborder.send(default_method).is_a? ActiveRecord::Associations::CollectionProxy
            suborder.shop.fc? ? [] : suborder.send(default_method).flatten.compact
          end
        end
      end

      def shipments_cost
        shipment_cost
      end
    end
  end
end

module Gateways
  module Payments

    def self.for(network)
      network = Network[network].thin? ? Network[Network.default] : Network[network]
      network.payment_gateways
    end

    def self.get_instance_of(name, network)
      klass = self.for(network).detect{ |klass| klass.downcase == name }
      return if klass.blank?
      "Gateways::Payments::#{klass}".constantize
    end

    class FailedPaymentAttemptException < StandardError
    end

    FailedPaymentAttemp = Struct.new(:error, :status, :response, :cause) do
      def cancelled?
        true
      end

      def get_error
        {
          status: "gateway_error",
          status_detail: cause
        }
      end
    end

    class Error < ::StandardError; end

    class ValidationError < Error
      attr_reader :params, :errors, :instance, :method_name

      def initialize(result, instance, method_name)
        @params = result.params
        @errors = result.errors
        @instance = instance
        @method_name = method_name
      end
    end
  end
end

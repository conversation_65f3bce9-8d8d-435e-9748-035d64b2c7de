module Gateways
  module Payments
    class SystemPoint
      class << self

        METHODS = [:get_customer_legal_id, :get_cc_brand, :get_installments, :get_cc_bin, :get_cc_bank, :external_id]

        def verify_payment_params(params)
          params.merge(gateway: Gateways::Payments::SystemPoint)
        end

        def collect_payment(checkout_cart, payment_data)
          payment_data = set_params(payment_data)
          sys_customer = SystemPoints::User.new(checkout_cart.customer)
          debit = sys_customer.debit(checkout_cart.total_points)
          return unless debit.present?
          Mkp::Payment.create do |object|
            object.status = payment_data[:payment_status]
            object.collected_amount = debit.value
            object.collected_at = debit.created_at
            object.gateway = payment_data[:gateway]
            object.gateway_data = payment_data
            object.gateway_object_id = debit.transaction_identify
            object.payment_method = "points"
            object.installments = 1
          end
        end

        METHODS.each do |meth|
          define_method(meth){ |param| "" }
        end

        def cancel_payment!(gateway_object_id)
          refund(gateway_object_id)
          nil
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          refund(gateway_object_id, amount)
        end

        private

        def refund(gateway_object_id, amount = nil)
          payment = Mkp::Payment.find_by(gateway_object_id: gateway_object_id, gateway: self.name.demodulize)
          order = payment.order
          sys_customer = SystemPoints::User.new(order.customer)
          sys_customer.accredit(amount || payment.collected_amount.to_i)
          payment.update_column :status, 'refunded'
        end

        def set_params(params)
          params ||= {gateway: '', document_type: '', document_number:''}
          object = params.slice(:gateway, :document_type, :document_number)
          object[:gateway] = self.name.demodulize
          object[:payment_status] = 'collected'
          object
        end
      end
    end
  end
end

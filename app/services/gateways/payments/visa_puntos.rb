require 'uri'
require 'net/http'

module Gateways
  module Payments
    class VisaPuntosUnexpectedResponse < Exception;end
    class VisaPuntos
      ALLOWED_PARAMS = [
        :payment_status,
        :payment_method,
        :document_number,
        :domicilio_completo,
        :codigo_postal,
        :cod_provincia,
        :localidad,
        :telefono,
        :points_amount,
        :money_amount
      ].freeze

      STATUSES = {
        'pending' => 'pending',
        'collected' => 'collected',
        'cancelled' => 'cancelled',
        'refunded' => 'refunded'
      }.freeze

      PAYMENT_METHOD_IDS = {
        0 => "puntos",
        1 => "plata",
        2 => "puntos/plata"
      }

      class << self
        def verify_payment_params(params)
          params.symbolize_keys.select do |key, value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: Gateways::Payments::VisaPuntos)
        end

        def collect_payment(checkout_cart, payment_data, current_user = nil)
          cobis_id = current_user.present? ? current_user.uuid : checkout_cart.customer_uuid

          url = URI("#{VISAPUNTOS_ENDPOINT}/users/points_exchange")
          http = Net::HTTP.new(url.host, url.port)

          request = Net::HTTP::Post.new(url)
          request["api-key"] = checkout_cart.store.visa_puntos_api_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'

          body_hash = {
            "puntos_premio" => checkout_cart.sp_subtotal_points,
            "domicilio_completo" => payment_data[:domicilio_completo],
            "codigo_postal" => payment_data[:codigo_postal],
            "telefono" => payment_data[:telefono],
            "localidad" => payment_data[:localidad],
            "cod_provincia" => payment_data[:cod_provincia],
            "document_number" => cobis_id,
            "cod_premio" => payment_data[:cod_premio]
          }

          if payment_data[:exchange_type] == 'voucher'
            body_hash.merge!(
              "exchange_type" => payment_data[:exchange_type],
              "producto" => payment_data[:producto]
            )
          end

          request.body = body_hash.to_json
          response = http.request(request)
          result = JSON.parse(response.body)

          if result['success']
            Mkp::Payment.create do |object|
              object.status = STATUSES['collected']
              object.collected_amount = checkout_cart.sp_subtotal_points
              object.collected_at = DateTime.now
              object.gateway = self.name.demodulize
              object.gateway_data = result['points_exchange'].merge({
                payer: {
                  identification: {
                    type: payment_data[:document_type],
                    number: payment_data[:document_number],
                  }
                }
              })
              object.gateway_object_id = result['points_exchange']['id_operacion']
              object.payment_method = 'visa_puntos'
            end
          elsif result.key?("error")
            Gateways::Payments::FailedPaymentAttemp.new(
              result["error"]["response_message"],
              response.code,
              result["error"],
              "visa_puntos_#{result['error']['response_code']}"
            )
          end
        end

        def collect_payment_by_items(checkout_cart, cart, payment_data, current_user = nil)
          payments = []
          cobis_id = current_user.present? ? current_user.uuid : checkout_cart.customer_uuid

          url = URI("#{VISAPUNTOS_ENDPOINT}/users/points_exchange")
          http = Net::HTTP.new(url.host, url.port)

          request = Net::HTTP::Post.new(url)
          request["api-key"] = checkout_cart.store.visa_puntos_api_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'

          checkout_cart.checkout_items.each do |item|
            body_hash = {
              "puntos_premio" => item.points,
              "domicilio_completo" => payment_data[:domicilio_completo],
              "codigo_postal" => payment_data[:codigo_postal],
              "telefono" => payment_data[:telefono],
              "localidad" => payment_data[:localidad],
              "cod_provincia" => payment_data[:cod_provincia],
              "document_number" => cobis_id,
              "cod_premio" => payment_data[:cod_premio]
            }

            request.body = body_hash.to_json
            response = http.request(request)
            result = JSON.parse(response.body)

            if result['success']
              payment = Mkp::Payment.create do |object|
                object.status = result['success'] ? STATUSES['collected'] : STATUSES['pending']
                object.collected_amount = item.points
                object.collected_at = DateTime.now
                object.gateway = self.name.demodulize
                object.gateway_data = result['points_exchange'].merge({
                                                                          payer: {
                                                                              identification: {
                                                                                  type: payment_data[:document_type],
                                                                                  number: payment_data[:document_number],
                                                                              }
                                                                          }
                                                                      })
                object.gateway_object_id = result['points_exchange']['id_operacion']
                object.payment_method = 'visa_puntos'
              end

              payments << payment
              item.payment = payment
            else
              if result.key?("error")
                Rails.logger.error 'Error procesando pago en puntos...'
                Rails.logger.error result['error']['response_code']
                Rails.logger.error result["error"]["response_message"]

                payments << Gateways::Payments::FailedPaymentAttemp.new(
                                result["error"]["response_message"],
                                response.code,
                                result["error"],
                                "visa_puntos_#{result['error']['response_code']}"
                            )
              end
            end
          end
          payments
        end

        def cancel_payment!(gateway_object_id, store = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          url = URI("#{VISAPUNTOS_ENDPOINT}/users/points_return")
          http = Net::HTTP.new(url.host, url.port)

          visa_puntos_api_key = store.visa_puntos_api_key if store
          if !visa_puntos_api_key
            visa_puntos_api_key = payment.order.present? ? payment.order.store.visa_puntos_api_key : VISAPUNTOS_DEFAULT_APIKEY
          end
          request = Net::HTTP::Post.new(url)
          request["api-key"] = visa_puntos_api_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'

          body_hash = { "exchange_id" => gateway_object_id }
          request.body = body_hash.to_json

          response = http.request(request)
          result = JSON.parse(response.body)

          if result['success']
            payment.update_attribute(:status, "cancelled")
            {
              id: gateway_object_id,
              status: 'cancelled',
              date_approved: DateTime.now
            }
          elsif result.key?("error")
            raise VisaPuntosUnexpectedResponse, result["error"]
          end
        end

        def get_status(gateway_object_id)
          ' - '
        end

        def get_customer_legal_id(data)
          payer = data[:payer]
          return payer[:identification][:number] if payer.present?
          nil
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          ' - '
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          gateway_object_id
        end
      end
    end
  end
end

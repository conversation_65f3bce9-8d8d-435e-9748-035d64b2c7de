module Gateways
  module Payments
    class Tarjetadigital
      STATUSES = {
        'pending' => 'pending',
        'collected' => 'collected',
        'cancelled' => 'cancelled',
      }.freeze

      class << self
        def verify_payment_params(params)
          params.merge(gateway: self.name.constantize)
        end

        def collect_payment(checkout_cart, payment_data)
          params = build_params(checkout_cart, payment_data)
          response = client.operation(params)
          return unless response.dig("success")
          Mkp::Payment.create do |object|
            object.status = payment_data[:payment_status] || "pending"
            object.collected_amount = checkout_cart.total
            object.collected_at = Date.today
            object.gateway = self.name.demodulize
            object.gateway_data = response.dig("message").merge!(request_id: request_count)
            object.gateway_object_id = response.dig("message", "idOperation")
            object.payment_method = "tarjetadigital"
            object.installments = payment_data[:installments]
          end
        end

        def cancel_payment!(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)

          if payment.created_at < 24.hours.ago
            payment.update_attribute(:status, "cancelled")
            {
              id: gateway_object_id,
              status: 'cancelled',
              date_approved: DateTime.now
            }
          end
        end

        def get_status(gateway_object_id)
          "collected"
        end

        def get_customer_legal_id(data)
          ' - '
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          ' - '
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          gateway_object_id
        end

        def client
          @client ||= Tdigital::Client.new
        end

        private

        def request_count
          @request_count ||= Mkp::Payment.where(gateway: 'Tarjetadigital').count.next
        end

        def build_params(checkout_cart, data)
          domain = "#{checkout_cart.store.hostname}/checkout"
          {
            "idRetailer": TDIGITAL_RETAILER,
            "idBank": 160523,
            "idRequest": request_count,
            "amount": checkout_cart.total,
            "quotas": data[:installments],
            "successUrl": "#{domain}/tarjeta-digital/process/#{checkout_cart.purchase_id}",
            "rejectedUrl": "#{domain}/error",
            "idCardType": 1,
            "idCampaign": 3,
            "status_code": 1
          }
        end
      end
    end
  end
end

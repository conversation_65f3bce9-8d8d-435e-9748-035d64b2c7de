module Gateways
  module Payments
    class DecidirDistributedMacro < DecidirBase
      ALLOWED_PARAMS = [
          :card_number,
          :card_expiration_month,
          :card_expiration_year,
          :security_code,
          :card_holder_name,
          :installments,
          :coef,
          :payment_method_id,
          :issuer_id,
          :document_type,
          :bin,
          :document_number,
          :cardholder_name,
          :card_last_four_digits
      ].freeze

      class << self
        def gateway_class
          Gateways::Payments::DecidirDistributedMacro
        end

        def verify_payment_params(params)
          params.symbolize_keys.select do |key, value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: gateway_class)
        end


        def collect_payment(checkout_cart, payment_data)
          @store = checkout_cart.store
          total_amount = total_amount(payment_data[:installments], payment_data[:coef], checkout_cart)
          payments = {}
          gateway_object_ids = []
          (checkout_cart.checkout_items.group_by { |item| item.shop.decidir_public_key }).each do |public_key, items|
            @shop = items.first.shop
            coef = payment_data[:coef]
            total_amount_for_shop = parse_total_amount((items.sum {|a| a.total_with_points_discount(@store)}) * (coef.to_f > 0 ? coef.to_f : 1))

            url = URI("#{DECIDIR_ENDPOINT}/tokens")

            http = Net::HTTP.new(url.host, url.port)
            http.use_ssl = true
            http.verify_mode = OpenSSL::SSL::VERIFY_NONE

            request = Net::HTTP::Post.new(url)
            request["apikey"] = public_key
            request["content-type"] = 'application/json'
            request["cache-control"] = 'no-cache'

            body_hash =  {
                "card_number" => payment_data[:card_number],
                "card_expiration_month" => payment_data[:card_expiration_month],
                "card_expiration_year" => payment_data[:card_expiration_year],
                "security_code" => payment_data[:security_code],
                "card_holder_name" => payment_data[:card_holder_name],
                "card_holder_identification" => {
                    "type" => payment_data[:document_type],
                    "number" => payment_data[:document_number]
                }
            }

            request.body = body_hash.to_json
            response = http.request(request)

            begin
              result = JSON.parse(response.read_body)
              Rails.logger.info('--- DECIDIR DISTRIBUTED RESPONSE ---\n')
              Rails.logger.info(result)
            rescue
              return Gateways::Payments::FailedPaymentAttemp.new("parse_error", "rejected", result, "parse_error")
            end

            if result.key?("id")
              # set document to store it in payment ahead
              @cardholder_name = result['cardholder']['name']
              @card_last_four_digits = result['last_four_digits']

              body_hash =  {
                  "site_transaction_id" => result["id"],
                  "token" => result["id"],
                  "payment_method_id" => payment_data[:payment_method_id].to_i,
                  "bin" => payment_data[:bin],
                  "amount" => total_amount_for_shop.to_i,
                  "currency" => "ARS",
                  "installments" => payment_data[:installments].to_i,
                  "description" => "",
                  "payment_type" => "distributed",
                  "sub_payments" => sub_payments_generator(checkout_cart, payment_data, total_amount_for_shop, public_key)
              }

              payment = base_collect_payment(checkout_cart, payment_data, body_hash)

              @gateway_data = payment.gateway_data.deep_merge({ payer: { identification: {:cardholder_name=>@cardholder_name, :card_last_four_digits=>@card_last_four_digits}}})

              if payment.cancelled?
                # si falla algun pago tengo que cancelar todos los anteriores que si se pudieron cobrar
                payments.each do |key, value|
                  (Mkp::Payment.find value[:payment_id]).cancel!
                end
                return payment
              else
                payment.update_attributes(installments: payment.gateway_data["sub_payments"].first["installments"], gateway: 'DecidirDistributed')
              end

              gateway_object_ids << result["id"]

              checkout_cart.items_per_shop.map do |shop, items|
                if shop.decidir_public_key == public_key
                  payments[shop.name] = {
                                  shop_id: shop.id,
                                  payment_id: payment.id
                              }
                end
              end
            elsif result.key?("error_type")
              return Gateways::Payments::FailedPaymentAttemp.new(
                  result["error_type"],
                  result["status"],
                  result,
                  result["status_details"].try([],"code") || result["code"] || 'invalid_card'
              )
            else
              return Gateways::Payments::FailedPaymentAttemp.new("token_error", "rejected", result, "token_error")
            end
          end

          if payments.any?
            payment = Mkp::Payment.create do |object|
              object.status = 'collected'
              object.collected_amount = amount_to_decimal(total_amount.to_s)
              object.collected_at = Time.zone.now
              object.gateway = self.name.demodulize
              object.gateway_data = payments.merge(@gateway_data)
              object.gateway_object_id = gateway_object_ids.join('-')
              object.payment_method = payment_data[:payment_method_id]
              object.installments = payment_data[:installments]
              object.document_type = payment_data[:document_type]
              object.document_number = payment_data[:document_number]
            end

            return payment
          end
        end

        def cancel_payment!(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          last_cancelation = nil
          payment.gateway_data.each do |key, value|
            unless key != :payer || value[:payment_id].nil?
              url = URI("#{DECIDIR_ENDPOINT}/payments/#{value[:payment_id]}/refunds")
              shop_payment = Mkp::Payment.find_by_gateway_object_id(value[:payment_id])
              @store = shop_payment.sale_item.store

              http = Net::HTTP.new(url.host, url.port)
              http.use_ssl = true
              http.verify_mode = OpenSSL::SSL::VERIFY_NONE

              request = Net::HTTP::Post.new(url)
              request["apikey"] = private_key
              request["content-type"] = 'application/json'
              request["cache-control"] = 'no-cache'
              request.body = "{}"

              response = http.request(request)
              result = JSON.parse(response.read_body)
              raise DecidirUnexpectedResponse, response.to_s if result["status"] != "approved"
              shop_payment.update_attribute(:status, "refunded")
              payment.update_information!(result)
              last_cancelation = result
            end
            payment.update_attribute(:status, "refunded")
            last_cancelation
          end
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          shop = Mkp::Shop.find aditional_data[:shop_id]
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)

          payment.gateway_data.each do |key, value|
            if shop.name == key
              shop_payment = Mkp::Payment.find_by_gateway_object_id(value[:payment_id])
              shop_gateway_data = shop_payment.gateway_data["sub_payments"].detect {|each| each["site_id"] == shop.decidir_site_id}

              sub_payments =
                  [
                      {
                          "id": shop_gateway_data["subpayment_id"],
                          "amount": shop_gateway_data["amount"]
                      }
                  ]

              body = {
                  "sub_payments" => sub_payments
              }

              base_refund_partial!(gateway_object_id, body)
            end
          end
        end

        def external_id(gateway_object_id, additional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          shop = Mkp::Shop.find additional_data[:shop_id]
          payment.gateway_data.each do |key, value|
            if shop.name == key
              shop_payment = Mkp::Payment.find(value[:payment_id])
              return shop_payment.gateway_data ? shop_payment.gateway_data["site_transaction_id"] : ' - '
            end
          end

          return ' - '
        end

        def get_customer_legal_id(data)
          payer = data[:payer]
          return payer[:identification][:number] if payer.present?
          nil
        end

        private

        def private_key
          @shop.decidir_private_key
        end

        def sub_payments_generator(checkout_cart, payment_data, total_amount, public_key)
          result = []
          total_net = 0
          checkout_cart.items_per_shop.map do |shop, items|
            if shop.decidir_public_key == public_key
              total_net += items.sum {|a| a.total_with_points_discount(@store)}
            end
          end
          checkout_cart.items_per_shop.map do |shop, items|
            if shop.decidir_public_key == public_key
              delivery_options = checkout_cart.choosen_delivery_options[shop.id].first
              delivery_price = delivery_options.charge
              partial_percent = (items.sum {|a| a.total_with_points_discount(@store)}) / total_net
              proportional_amount = amount_to_decimal(total_amount.to_s) * partial_percent
              amount = ((proportional_amount * (1 - (shop.sale_commission.to_f) / 100)) + delivery_price).round(2)

              result << {
                  "site_id": shop.decidir_site_id,
                  "installments": payment_data[:installments].to_i,
                  "amount": parse_total_amount(amount).to_i
              }
            end
          end

          result
        end
      end
    end
  end
end

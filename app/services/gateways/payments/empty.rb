module Gateways
  module Payments
    class Empty
      class << self
        def verify_payment_params(params)
          params.merge(gateway: Gateways::Payments::Empty)
        end

        def collect_payment(checkout_cart, payment_data)
          payment_data["gateway"] = self.name.demodulize if payment_data["gateway"].present?
          Mkp::Payment.create do |object|
            object.status = payment_data[:payment_status] || "pending"
            object.collected_amount = checkout_cart.total
            object.collected_at = Date.today
            object.gateway = self.name.demodulize
            object.gateway_data = payment_data
            object.gateway_object_id = nil
            object.payment_method = "no_payment"
            object.installments = 0
          end
        end

        def get_status(gateway_object_id)
          "collected"
        end

        def get_customer_legal_id(data)
          ' - '
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          ' - '
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          gateway_object_id
        end
      end
    end
  end
end

require 'uri'
require 'net/http'

module Gateways
  module Payments
    class DecidirUnexpectedResponse < Exception;end
    class DecidirBase
      ALLOWED_PARAMS = [
        :token,
        :installments,
        :coef,
        :payment_method_id,
        :issuer_id,
        :document_type,
        :bin,
        :document_number,
        :cardholder_name,
        :card_last_four_digits,
        :cards
      ].freeze

      LOOKUP_URL = ''

      STATUSES = {
        'pending' => 'pending',
        'approved' => 'collected',
        'in_process' => 'pending',
        'in_mediation' => 'pending',
        'rejected' => 'cancelled',
        'cancelled' => 'cancelled',
        'refunded' => 'refunded',
        'charged_back' => 'refunded',
        nil => 'cancelled'
      }.freeze

      PAYMENT_METHOD_IDS = {
        1 =>  "visa",
        6 =>  "amex",
        65 => "amex",
        15 => "mastercard",
        24 => "naranja",
        31 => "visa debito",
        104 => "mastercard",
        106 => "maestro"
      }.freeze

      class << self
        def gateway_class
          Gateways::Payments::Decidir
        end

        def verify_payment_params(params)
          params.symbolize_keys.select do |key, value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: gateway_class)
        end

        def collect_payment(checkout_cart, payment_data)
          @store = checkout_cart.store
          total_amount = total_amount(payment_data[:installments], payment_data[:coef], checkout_cart)

          body_hash =  {
                        "site_transaction_id" => payment_data[:token],
                        "token" => payment_data[:token],
                        "payment_method_id" => payment_data[:payment_method_id].to_i,
                        "bin" => payment_data[:bin],
                        "amount" => total_amount,
                        "currency" => "ARS",
                        "installments" => payment_data[:installments].to_i,
                        "description" => "",
                        "payment_type" => "single",
                        "sub_payments" => []
                        }

          base_collect_payment(checkout_cart, payment_data, body_hash )
        end

        def base_collect_payment(checkout_cart, payment_data, body )
          @store = checkout_cart.store
          url = URI("#{DECIDIR_ENDPOINT}/payments")

          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request["apikey"] = private_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'

          brand_name = PAYMENT_METHOD_IDS.fetch(payment_data[:payment_method_id].to_i, 'visa')

          request.body = body.to_json
          response = http.request(request)
          begin
            result = JSON.parse(response.read_body)
          rescue
            return Gateways::Payments::FailedPaymentAttemp.new("parse_error", "rejected", result, "parse_error")
          end

          error_code = get_error_code(result)
          if result.key?("id") && error_code.blank?
            Mkp::Payment.create do |object|
              object.status = STATUSES[result["status"]]
              object.collected_amount = amount_to_decimal(result["amount"].to_s)
              object.collected_at = result["date"]
              object.gateway = self.name.demodulize
              object.gateway_data = result.merge({
                                                     payer: {
                                                         identification: {
                                                             type: payment_data[:document_type],
                                                             number: payment_data[:document_number],
                                                             name_payment: payment_data[:cardholder_name],
                                                             card_last_four_digits: payment_data[:card_last_four_digits]
                                                         }
                                                     }
                                                 })
              object.gateway_object_id = result["id"]
              object.payment_method = result["card_brand"]
              object.installments = result["installments"]
              object.document_type = payment_data[:document_type]
              object.document_number = payment_data[:document_number]
            end
          elsif result.key?("error_type")
            Gateways::Payments::FailedPaymentAttemp.new(
                result["error_type"],
                result["status"],
                result.merge(body: request.body),
                result["status_details"].try([],"code") || result["code"] || 'invalid_card'
            )
          elsif error_code.present?
            Gateways::Payments::FailedPaymentAttemp.new(
              result["error_type"], result["status"], result.merge(body: request.body), error_code
            )
          end
        end

        def get_error_code(result)
          result.fetch('status_details', {})&.fetch('error', {})&.fetch('reason', {})&.fetch('id', nil)&.to_s.presence
        end

        def cancel_payment!(gateway_object_id)
          url = URI("#{DECIDIR_ENDPOINT}/payments/#{gateway_object_id}/refunds")
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          @store = payment.sale_item.store

          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request["apikey"] = private_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'
          request.body = "{}"

          response = http.request(request)
          result = JSON.parse(response.read_body)
          raise DecidirUnexpectedResponse, result if result["status"] != "approved"
          payment.update_attribute(:status, "refunded")
          result
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          body = {amount: parse_total_amount(amount)}

          base_refund_partial!(gateway_object_id, body)
        end

        def base_refund_partial!(gateway_object_id, body)
          url = URI("#{DECIDIR_ENDPOINT}/payments/#{gateway_object_id}/refunds")
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          @store = payment.sale_item.store

          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request["apikey"] = private_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'

          request.body = body.to_json

          response = http.request(request)
          result = JSON.parse(response.read_body)
          raise DecidirUnexpectedResponse, result if result["status"] != "approved"
          result
        end

        def refund_partial_and_update_payment!(payment, suborder, aditional_data = nil)
          result = refund_partial!(payment.gateway_object_id, suborder.total, aditional_data = nil)

          if result.key?("error_type")
            message = result["message"]
            Rails.logger.error "Error en refund parcial con decidir: #{message}"
            Rails.logger.error "response: #{result}"
            return false
          else
            if result["status"] == "approved"
              if payment.gateway_data[:refunded_suborders].present?
                suborders = "#{payment.gateway_data[:refunded_suborders]},#{suborder.id}"
              else
                suborders = suborder.id.to_s
              end

              data = payment.gateway_data.merge!(refunded_suborders: suborders)
              payment.update(gateway_data: data)
              suborder.update_attribute(:refunded, true)
              return true
            else
              message = "Refund not approved."
              Rails.logger.error "Error en refund parcial con decidir: #{message}"
              Rails.logger.error "response: #{result}"
              return false
            end
          end
        end

        def get_payment(gateway_object_id)
          url = URI("#{DECIDIR_ENDPOINT}/payments/#{gateway_object_id}")
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          @store = payment.sale_item.store

          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Get.new(url)
          request["apikey"] = private_key
          request["content-type"] = 'application/json'
          request["cache-control"] = 'no-cache'
          request.body = "{}"

          response = http.request(request)
          JSON.parse(response.read_body)
        end

        def get_status(gateway_object_id)
          collection = get_payment(gateway_object_id)
          collection["status"] if collection.present?
        end

        def get_error(data)
          {
            status: data["status"],
            status_detail: data["status_details"]["card_reason"]
          }
        end

        def get_history(data)
          history = []
          if (original_payload = data[:original_payload]).present?
            history << history_event(original_payload)
            data[:notifications_payloads].each do |timestamp, payload|
              history << history_event(payload, timestamp)
            end
          else
            history << history_event(data)
          end
          history
        end

        def get_customer_legal_id(data)
          payer = data[:payer]
          return payer[:identification][:number] if payer.present?
          nil
        end

        def get_cc_brand(gateway_object_id)
          Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["card_brand"]
        end

        def get_installments(gateway_object_id)
          Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["installments"]
        end

        # def get_cc_bin(gateway_object_id)
        #   Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["installments"]
        # end

        def get_cc_bin(gateway_object_id)
          Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["bin"]
        end

        def get_cc_bank(gateway_object_id)
          number = get_cc_bin(gateway_object_id)
          bin = Bin.find_by_number(number)
          bin&.bank&.name || ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          payment.gateway_data ? payment.gateway_data["site_transaction_id"] : ' - '
        end

        private

        def private_key
          @store.decidir_credentials.private_key
        end

        def paying_with_ticket?(payment_data)
          TICKET_PAYMENT_METHODS.include?(payment_data[:payment_method_id])
        end

        def parse_total_amount(amount)
          sprintf('%.2f', amount).to_s.split(".").join("").to_i
        end

        def total_amount(installments, coef, checkout_cart)
          (installments.to_i == 1 ) ? amount_one_installment(checkout_cart) : (parse_total_amount(checkout_cart.total * (coef.to_f > 0 ? coef.to_f : 1)))
        end

        def amount_for_checkourt_cart(checkout_cart)
          parse_total_amount(checkout_cart.total)
        end

        def amount_one_installment(checkout_cart)
          parse_total_amount(checkout_cart.total_one_installment)
        end

        def history_event(payload, timestamp = nil)
          {
              status: payload[:status],
              status_detail: payload[:status_detail],
              amount: payload[:transaction_amount]
          }.tap do |event|
            event[:timestamp] = if timestamp.present?
                                  timestamp
                                else
                                  payload[:date_created]
                                end.to_datetime
          end
        end

        def amount_to_decimal(string_amount)
          string_amount.insert(string_amount.size-2, '.').to_f
        end
      end
    end
  end
end

module Gateways
  module Payments
    class FirstDataDistributed < FirstDataBase
      class << self
        def gateway_class
          Gateways::Payments::FirstDataDistributed
        end

        def verify_payment_params(params)
          super.merge(gateway: Gateways::Payments::FirstDataDistributed)
        end


        def collect_payment(checkout_cart, payment_data)
          @store = checkout_cart.store
          total_amount = checkout_cart.total
          payments = {}
          gateway_object_ids = []
          checkout_cart.items_per_shop.map do |shop, items|
            total_amount_for_shop = (items.sum {|a| a.total_with_points_discount(@store)}) * (payment_data[:coef].to_f > 0 ? payment_data[:coef].to_f : 1)
            delivery_price = checkout_cart.choosen_delivery_options[shop.id].first.charge
            total_amount_for_shop = total_amount_for_shop + delivery_price

            credentials = shop.first_data_credential_for(items) || @store.first_data_credential_for_checkout_cart(checkout_cart)

            payment = base_collect_payment(checkout_cart, payment_data, total_amount_for_shop, credentials)

            if payment.cancelled?
              # si falla algun pago tengo que cancelar todos los anteriores que si se pudieron cobrar
              payments.each do |key, value|
                (Mkp::Payment.find value[:payment_id]).cancel!
              end
              return payment
            else
              payment.update_attributes(gateway: 'FirstData')
            end

            gateway_object_ids << payment.gateway_object_id

            payments[shop.name] = {
                shop_id: shop.id,
                payment_id: payment.id
            }
          end

          if payments.any?
            payment = Mkp::Payment.create do |object|
              object.status = status('SALE', 'APPROVED')
              object.collected_amount = total_amount
              object.collected_at = Time.zone.now
              object.gateway = self.name.demodulize
              object.gateway_data = payments.merge({
                                                       payer: {
                                                           identification: {
                                                               type: payment_data[:document_type],
                                                               number: payment_data[:document_number],
                                                           }
                                                       }
                                                   })
              object.gateway_object_id = gateway_object_ids.join('-')
              object.payment_method = payment_data[:payment_method_id]
              object.installments = payment_data[:installment_number]
              object.document_type = payment_data[:document_type]
              object.document_number = payment_data[:document_number]
            end

            return payment
          end
        end

        def cancel_payment!(gateway_object_id)
          payment = (Mkp::Payment.where(gateway_object_id: gateway_object_id, gateway: 'FirstDataDistributed')).last
          last_cancelation = nil

          payment.gateway_data.each do |key, value|
            if key != :payer && !value[:payment_id].nil?
              shop = Mkp::Shop.find value[:shop_id]
              shop_payment = Mkp::Payment.find(value[:payment_id])
              credentials = shop.first_data_credential_for_order(payment.sale_item) || payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
              last_cancelation = basic_cancel_payment(shop_payment.gateway_object_id, credentials, shop_payment)
              shop_payment.update_attribute(:status, "refunded")
            end
            payment.update_attribute(:status, "refunded")
            last_cancelation
          end
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          shop = Mkp::Shop.find aditional_data[:shop_id]
          payment = (Mkp::Payment.where(gateway_object_id: gateway_object_id, gateway: 'FirstDataDistributed')).last

          payment.gateway_data.each do |key, value|
            if shop.name == key
              shop_payment = Mkp::Payment.find(value[:payment_id])
              credentials = shop.first_data_credential_for_order(payment.sale_item) || payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
              basic_cancel_payment(shop_payment.gateway_object_id, credentials, shop_payment)
              shop_payment.update_attribute(:status, "refunded")
            end
          end
        end

        def refund_partial_and_update_payment!(payment, suborder, aditional_data = nil)
          shop = suborder.shop
          suborders = ""
          payment.gateway_data.each do |key, value|
            if shop.name == key
              shop_payment = Mkp::Payment.find(value[:payment_id])
              credentials = shop.first_data_credential_for_order(payment.sale_item) || payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
              result = base_refund_partial_and_update_payment!(credentials, shop_payment, suborder, aditional_data = nil)
              if result
                if payment.gateway_data[:refunded_suborders].present?
                  suborders = "#{payment.gateway_data[:refunded_suborders]},#{suborder.id}"
                else
                  suborders = suborder.id.to_s
                end
              end
            end
          end
          data = payment.gateway_data.merge!(refunded_suborders: suborders)
          payment.update(gateway_data: data)
        end
      end
    end
  end
end

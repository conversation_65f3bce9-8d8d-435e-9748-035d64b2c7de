# frozen_string_literal: true

module Gateways
  module Payments
    # Payment gateway for decidir distributed
    class DecidirDistributed < DecidirBase
      class << self
        def gateway_class
          Gateways::Payments::DecidirDistributed
        end

        def collect_payment(checkout_cart, payment_data)
          @store = checkout_cart.store
          total_amount = total_amount(payment_data[:installments],
                                      payment_data[:coef],
                                      checkout_cart)

          body_hash =  {
            'site_transaction_id' => payment_data[:token],
            'token' => payment_data[:token],
            'payment_method_id' => payment_data[:payment_method_id].to_i,
            'bin' => payment_data[:bin],
            'amount' => total_amount.to_i,
            'currency' => 'ARS',
            'installments' => payment_data[:installments].to_i,
            'description' => '',
            'payment_type' => 'distributed',
            'sub_payments' => sub_payments_generator(checkout_cart,
                                                     payment_data,
                                                     total_amount)
          }

          payment = base_collect_payment(checkout_cart, payment_data, body_hash)

          payment.update(installments: payment_data[:installments]) unless payment.cancelled?

          payment
        end

        def refund_partial!(gateway_object_id, amount, aditional_data)
          shop = Mkp::Shop.find aditional_data[:shop_id]
          payment = Mkp::Payment.find_by(gateway_object_id: gateway_object_id)
          sub_payments = payment.gateway_data['sub_payments']
          shop_gateway_data = sub_payments.detect do |each|
            each['site_id'] == shop.decidir_site_id
          end

          sub_payments =
            [
              {
                "id": shop_gateway_data['subpayment_id'],
                "amount": shop_gateway_data['amount']
              }
            ]

          if AVENIDA_SITE_ID.present?
            avenida_gateway_data = sub_payments.detect do |each|
              each['site_id'] == AVENIDA_SITE_ID
            end

            sub_payments << {
              "id": avenida_gateway_data['subpayment_id'],
              "amount": parse_total_amount(amount).to_i -
                        shop_gateway_data['amount']
            }
          end

          body = { 'sub_payments' => sub_payments }

          base_refund_partial!(gateway_object_id, body)
        end

        def refund_partial_and_update_payment!(payment,
                                               suborder,
                                               aditional_data = nil)
          result = refund_partial!(payment.gateway_object_id,
                                   suborder.total,
                                   aditional_data)

          if result.key?('error_type')
            message = result['message']
            Rails.logger.error "Error en refund parcial con decidir: #{message}"
            Rails.logger.error "response: #{result}"
            false
          elsif result['status'] == 'approved'
            refunded_suborders = payment.gateway_data[:refunded_suborders]
            suborders = if refunded_suborders.present?
                          "#{refunded_suborders},#{suborder.id}"
                        else
                          suborder.id.to_s
                        end

            data = payment.gateway_data.merge!(refunded_suborders: suborders)
            payment.update(gateway_data: data)
            suborder.update(refunded: true)
            true
          else
            message = 'Refund not approved.'
            Rails.logger.error "Error en refund parcial con decidir: #{message}"
            Rails.logger.error "response: #{result}"
            false
          end
        end

        def get_installments(gateway_object_id)
          Mkp::Payment.find_by(gateway_object_id: gateway_object_id)
                      .gateway_data['sub_payments']
                      .first['installments']
        end

        private

        def sub_payments_generator(checkout_cart, payment_data, total_amount)
          result = []
          sale_commision = 0
          partial_amount = 0
          total_net = checkout_cart.checkout_items.sum(&:total)
          checkout_cart.items_per_shop.map do |shop, items|
            delivery_options = checkout_cart.choosen_delivery_options[shop.id].first
            delivery_price = delivery_options.charge

            partial_percent = items.sum(&:total) / total_net
            proportional_amount = amount_to_decimal(total_net.to_s) *
                                  partial_percent
            proportional_discount = get_proportional_discount(checkout_cart,
                                                              shop,
                                                              partial_percent)
            # If it hasn't configured a decidir_site_id then
            # it's going by our site_id
            # in that case we do not need to separate commissions
            commission = shop.decidir_site_id.present? ? (1 - shop.sale_commission.to_f / 100) : 1
            amount = ((proportional_amount * commission) +
                     delivery_price - proportional_discount).round(2)

            result << {
              "site_id": shop.get_decidir_site_id(@store),
              "installments": payment_data[:installments].to_i,
              "amount": parse_total_amount(amount).to_i
            }

            partial_amount += parse_total_amount(amount).to_i
            sale_commision += (items.sum(&:total) *
                              (shop.sale_commission.to_f / 100)).round(2)
          end

          if AVENIDA_SITE_ID.present?
            result << {
              "site_id": AVENIDA_SITE_ID,
              "installments": payment_data[:installments].to_i,
              "amount": parse_total_amount(sale_commision).to_i +
                        (total_amount.to_i -
                        partial_amount -
                        parse_total_amount(sale_commision).to_i)
            }
          end

          total_distributed = (result.map { |each| each[:amount] }).inject(:+)

          delta = total_amount - total_distributed
          result.each { |hash| hash[:amount] += delta * (hash[:amount] / total_distributed) }

          result
        end

        def get_proportional_discount(checkout_cart, shop, partial_percent)
          return 0.0 unless checkout_cart.coupon

          coupon_total_discount = checkout_cart.coupon_total_discount.to_s

          Rails.logger.info("Checkout Cart: #{checkout_cart}")
          Rails.logger.info("Checkout Cart Items: #{checkout_cart.items}")

          items_that_apply = checkout_cart.items.reject do |item|
            product, coupon = begin
                        [item.product, checkout_cart.coupon]
                              rescue StandardError
                                [Mkp::Product.find(item[:product][:id]),
                                 Mkp::Coupon.find(checkout_cart.coupon[:id])]
                      end
            Rails.logger.info("Checkout Coupon: #{checkout_cart.coupon}")

            coupon.what_does_it_apply_to?(product) == 'not_apply'
          end
          if items_that_apply.blank?
            return amount_to_decimal(coupon_total_discount) * partial_percent
          end

          grouped_by_shop = items_that_apply.group_by(&:shop)
          if grouped_by_shop[shop]
            partial_shop_percent = grouped_by_shop[shop].sum(&:total) /
                                   items_that_apply.sum(&:total)
            amount_to_decimal(coupon_total_discount) * partial_shop_percent
          end

          0.0
        end
      end
    end
  end
end

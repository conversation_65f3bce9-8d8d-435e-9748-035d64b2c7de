module Gateways
  module Payments
    class Braintree
      attr_reader :result, :payment_method_type

      ENABLED_METHODS_TYPES = [
        'CreditCard',
        'PayPalAccount'
      ].freeze

      LOOKUP_URL = "https://www.braintreegateway.com/merchants/#{BRAINTREE_MERCHANT_ID}/transactions/".freeze

      STATUSES = {
        'authorization_expired' => 'cancelled',
        'authorized' => 'pending',
        'authorizing' => 'pending',
        'failed' => 'cancelled',
        'gateway_rejected' => 'cancelled',
        'processor_declined' => 'cancelled',
        'settled' => 'collected',
        'settlement_confirmed' => 'collected',
        'settlement_declined' => 'cancelled',
        'settlement_pending' => 'pending',
        'settling' => 'collected',
        'submitted_for_settlement' => 'collected',
        'voided' => 'cancelled'
      }.freeze


      class << self
        def verify_payment_method(nonce, payment_method_type)
          unless ENABLED_METHODS_TYPES.include?(payment_method_type)
            raise ArgumentError.new("The payment type: #{payment_method_type} doesn't look valid!")
          end

          options = set_options(nonce, payment_method_type)

          new(create_customer_with_payment_method_verification(options), payment_method_type)
        end

        def collect_payment(checkout_cart, payment_information)
          total_amount = checkout_cart.total.round(2)

          result = ::Braintree::Transaction.sale(
            amount: total_amount,
            customer_id: payment_information[:customer_id],
            payment_method_token: payment_information[:token],
            options: {
              submit_for_settlement: true
            }
          )

          if result.success?
            transaction = result.transaction

            payment = Mkp::Payment.create do |object|
              object.status = self::STATUSES[transaction.status]
              object.collected_amount = transaction.amount
              object.collected_at = transaction.created_at
              object.gateway = self.name.demodulize
              object.gateway_data = {
                transaction: HashWithIndifferentAccess.new(result.transaction.as_json)
              }
              object.gateway_object_id = transaction.id
              object.payment_method = transaction.payment_instrument_type
            end

            if paid_with_paypal?(payment) && !payment.collected?
              ::Mkp::Payments::PaypalTransactionStatusUpdater.perform_in(30.minutes, payment.id)
            end

            payment
          end
        rescue
          nil
        end

        def get_error(data)
          warn "[NOT IMPLEMENTED] The method #get_error in Braintree is not implemented"
          {}
        end

        def get_history(data)
          return [] unless (trasaction = data[:trasaction]).present?
          trasaction[:status_history]
        end

        def get_customer_legal_id(data)
          ' - '
        end

        private

        def create_customer_with_payment_method_verification(options)
          ::Braintree::Customer.create(options)
        end

        def set_options(nonce, payment_method_type)
          { payment_method_nonce: nonce }.merge(extra_options_for_method(payment_method_type))
        end

        def extra_options_for_method(payment_method_type)
          return {} unless payment_method_type == 'CreditCard'
          {
            credit_card: {
              options: {
                verify_card: true
              }
            }
          }
        end

        def paid_with_paypal?(payment)
          payment.payment_method == 'paypal_account' && payment.pending?
        end
      end

      def initialize(result, payment_method_type)
        @result = result
        @payment_method_type = payment_method_type
      end

      def method_missing(method_name, *args, &block)
        @result.send(method_name) if success?
      end

      def customer
        @result.customer
      end

      def success?
        @result.success?
      end

      def errors
        error_list = {
          validation: @result.errors
        }

        error_list = {
          verification: @result.credit_card_verification
        } if @result.credit_card_verification.present?

        error_list
      end

      def get_data_for_session
        if success?
          {
            customer_id: customer.id,
            payment_method_token: customer.payment_methods[0].token,
            payment_method_type: @payment_method_type
          }.merge(gateway: Gateways::Payments::Braintree)
        else
          {}
        end
      end

    end
  end
end

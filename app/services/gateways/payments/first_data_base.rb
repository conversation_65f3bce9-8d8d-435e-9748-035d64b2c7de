module Gateways
  module Payments
    class FirstDataBase
      ALLOWED_PARAMS = [
          :gateway,
          :card_number,
          :card_expiration_month,
          :card_expiration_year,
          :security_code,
          :document_type,
          :document_number,
          :installment_number,
          :installment_cft,
          :installment_tea,
          :installment_coef
      ].freeze

      STATUSES = {
          'SALE' => 'collected',
          'VOID' => 'cancelled',
          'RETURN' => 'refunded'
      }.freeze

      class << self
        def verify_payment_params(params)
          params.symbolize_keys.select do |key,value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: Gateways::Payments::FirstData)
        end

        def collect_payment(checkout_cart, payment_data)
          credentials = checkout_cart.store.first_data_credential_for_checkout_cart(checkout_cart)
          total_amount = checkout_cart.total
          base_collect_payment(checkout_cart,payment_data, total_amount, credentials)
        end

        def base_collect_payment(checkout_cart,payment_data, total_amount, credentials)
          response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('basic_sale', checkout_cart, payment_data, nil, total_amount, credentials.firstdata_store))

          result = response.body.dig(:ipg_api_order_response)

          if result[:order_id]
            Mkp::Payment.create do |object|
              object.status = status('SALE', result[:transaction_result].to_s)
              object.collected_amount = total_amount
              object.collected_at = Time.at(result[:t_date].to_i)
              object.gateway = self.name.demodulize
              object.gateway_data = result.merge({ 'bin' => payment_data[:card_number].first(6)})
              object.gateway_object_id = result[:order_id]
              object.payment_method = result[:payment_type]
              object.installments = payment_data[:installment_number]
              object.document_type = payment_data[:document_type]
              object.document_number = payment_data[:document_number]
            end
          end
        rescue Savon::Error => error
          if error.respond_to? :http
            if error.http.respond_to? :body
              error_hash = Hash.from_xml(error.http.body)
              if error_hash.respond_to? :dig
                envelop_hash = error_hash.dig('Envelope')
                if envelop_hash.respond_to? :dig
                  body_hash = envelop_hash.dig('Body')
                  if body_hash.respond_to? :dig
                    fault_hash = body_hash.dig('Fault')
                    if fault_hash.respond_to? :dig
                      detail_hash = fault_hash.dig('detail')
                      if detail_hash.respond_to? :dig
                        order_response_hash = detail_hash.dig('IPGApiOrderResponse')
                        if order_response_hash .respond_to? :dig
                          return Gateways::Payments::FailedPaymentAttemp.new(
                              order_response_hash.dig( 'ProcessorResponseMessage') || order_response_hash.dig('ErrorMessage'),
                              order_response_hash.dig('TransactionResult'),
                              result,
                              "first_data_#{order_response_hash.dig('ProcessorResponseCode') || ('11101' if order_response_hash.dig('ApprovalCode').include? '11101')}" # send here
                          )
                        end
                      end
                    end
                  end
                end
              end
            end
          end
          Gateways::Payments::FailedPaymentAttemp.new(
              "La respuesta del gateway no se puede procesar",
              "Error",
              result,
              "first_data_standard_error" # send here
          )
        end


        def collect_payment_with_pre_post_auth(checkout_cart, payment_data)
          credentials = checkout_cart.store.first_data_credential_for_checkout_cart(checkout_cart)
          total_amount = checkout_cart.total
          response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('basic_sale_preauth', checkout_cart, payment_data, nil, total_amount, credentials.firstdata_store))

          pre_result = response.body.dig(:ipg_api_order_response)

          payment_data[:order_id] = pre_result[:order_id]
          post_response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('basic_sale_postauth', checkout_cart, payment_data, nil, total_amount, credentials.firstdata_store))

          result = post_response.body.dig(:ipg_api_order_response)
          if result[:order_id]
            Mkp::Payment.create do |object|
              object.status = status('SALE', result[:transaction_result].to_s)
              object.collected_amount = total_amount
              object.collected_at = Time.at(result[:t_date].to_i)
              object.gateway = self.name.demodulize
              object.gateway_data = result
              object.gateway_object_id = result[:order_id]
              object.payment_method = result[:payment_type]
              object.installments = payment_data[:installment_number]
            end
          end
        rescue Savon::Error => error
          Gateways::Payments::FailedPaymentAttemp.new(
              (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ProcessorResponseMessage') || (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ErrorMessage'),
              (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'TransactionResult'),
              result,
              "first_data_#{(Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ProcessorResponseCode') || ('11101' if (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ApprovalCode').include? '11101')}" # send here
          )
        end

        def cancel_payment!(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          credentials = payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
          basic_cancel_payment(gateway_object_id, credentials, payment)
        end

        def basic_cancel_payment(gateway_object_id, credentials, payment)
          #response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('void',  payment.order, nil, payment[:gateway_data][:ipg_transaction_id], nil))
          response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('return',  payment.sale_item, nil, gateway_object_id, payment.collected_amount, credentials.firstdata_store))
          result = (Hash.from_xml(response.http.body)).dig('Envelope', 'Body', 'IPGApiOrderResponse')
          result[:id] = result['OrderId']
          result[:status] = 'VOID'
          result[:date_approved] = Time.at(result['TDate'].to_i)
          result
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          credentials = payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
          self.client(credentials).call(:ipg_api_order, xml: generate_xml('return',  payment.sale_item, nil, gateway_object_id, amount, credentials.firstdata_store))
        end

        def refund_partial_and_update_payment!(payment, suborder, aditional_data = nil)
          credentials = payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
          base_refund_partial_and_update_payment!(credentials, payment, suborder, aditional_data = nil)
        end


        def base_refund_partial_and_update_payment!(credentials, payment, suborder, aditional_data = nil)
          response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('return',  payment.sale_item, nil, payment.gateway_object_id, suborder.total, credentials.firstdata_store))

          if response.http.code == 200
            if payment.gateway_data[:refunded_suborders].present?
              suborders = "#{payment.gateway_data[:refunded_suborders]},#{suborder.id}"
            else
              suborders = suborder.id.to_s
            end

            data = payment.gateway_data.merge!(refunded_suborders: suborders)
            payment.update(gateway_data: data)
            suborder.update_attribute(:refunded, true)
            return true
          else
            message = (Hash.from_xml(response.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ProcessorResponseMessage') || (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ErrorMessage')
            Rails.logger.error "Error en refund parcial con firstdata: #{message}"
            Rails.logger.error "response: #{response.http.body}"
            return false
          end
        rescue Savon::Error => error
          message = (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ProcessorResponseMessage') || (Hash.from_xml(error.http.body)).dig('Envelope', 'Body', 'Fault', 'detail', 'IPGApiOrderResponse', 'ErrorMessage')
          Rails.logger.error "Error en refund parcial con firstdata: #{message}"
          Rails.logger.error "response: #{error.http.body}"
          return false
        end

        def get_payment(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          credentials = payment.sale_item.store.first_data_credential_for_order(payment.sale_item)
          response = self.client(credentials).call(:ipg_api_order, xml: generate_xml('inquiry_order', payment.sale_item, nil, gateway_object_id, nil, credentials.firstdata_store))
          response.dig('Body', 'TransactionValues').last
          response.body.to_json
        end

        def get_status(gateway_object_id)
          collection = get_payment(gateway_object_id)
          status(collection["TransactionType"], ["TransactionState"]) if collection.present?
        end

        def status(transaction_type, transaction_state)
          if transaction_state == 'CAPTURED' || transaction_state == 'APPROVED'
            return STATUSES[transaction_type]
          end
          'Undefined'
        end

        def client(credentials)
          adapter_cert = Paperclip.io_adapters.for(credentials.ssl_cert)
          adapter_cert_key = Paperclip.io_adapters.for(credentials.ssl_cert_key)

          savonConfig = {
              :wsdl => FIRSTDATA_DOMAIN,
              basic_auth: [credentials.user, credentials.password],
              :log_level => :debug,
              :log => true,
              :ssl_verify_mode => :none,
              :ssl_cert_file => adapter_cert.path,
              :ssl_cert_key_file => adapter_cert_key.path,
              :ssl_cert_key_password => credentials.certificate_password,
              :open_timeout => 600,
              :read_timeout => 600
          }

          Savon.client savonConfig
        end

        def generate_xml(xml_name, checkout_cart, payment_data, gateway_object_id, amount, firstdata_store)
          generator = Gateways::FirstDataXmlGenerator.new(checkout_cart, payment_data, gateway_object_id, amount, firstdata_store)
          generator.generate_xml(xml_name)
        end

        def get_customer_legal_id(data)
          ' - '
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data[:bin]
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          Mkp::Payment.find_by_gateway_object_id(gateway_object_id).installments
        end

        def external_id(gateway_object_id, additional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          payment.gateway_data ? payment.gateway_data[:order_id] : ' - '
        end
      end
    end
  end
end

module Gateways
  module Payments
    class Todopago
      class TodopagoUnprocessedCollectException < StandardError;end
      class TodopagoUnexpectedResponse < StandardError;end

      ALLOWED_PARAMS = [
        :public_request_key,
        :request_key,
        :operation_id,
        :doc_number,
        :doc_type,
        :payment_method
      ].freeze

      LOOKUP_URL = ''

      class << self
        def verify_payment_params(params)
          params.symbolize_keys.select do |key, value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: Gateways::Payments::Todopago)
        end

        def collect_payment(checkout_cart, payment_data)
          payment = Mkp::Payment.create do |object|
            object.status = 'pending'
            object.gateway = 'Todopago'
            object.gateway_data = {
              request_key: payment_data[:request_key],
              public_key: payment_data[:public_request_key],
              payer: {
                identification: {
                  type: payment_data[:doc_type],
                  number: payment_data[:doc_number],
                }
              }
            }
            object.gateway_object_id = payment_data[:operation_id]
            object.payment_method = payment_data[:payment_method] || "creditcard"
            object.document_type = payment_data[:doc_type]
            object.document_number = payment_data[:doc_number]
          end

          process_payment!(payment_data[:operation_id], payment_data[:request_key])
          payment.reload
        end

        def process_payment!(operation_id, request_key)
          response = self.client.getAuthorizeAnswer({
              Security: TODOPAGO_SECURITY,
              MERCHANT: TODOPAGO_MERCHANT,
              RequestKey: request_key,
              AnswerKey: operation_id,
          })

          response = JSON.parse(response)['envelope']['body']['get_authorize_answer_response']

          if response['status_code'].to_i == -1
            payment = Mkp::Payment.find_by_gateway_object_id(operation_id)
            payment = Mkp::Payment.create({gateway_object_id: operation_id}) if payment.nil?
            payment.status = 'collected'
            payment.collected_amount = response['payload']['request']['amount']
            payment.collected_at = response['payload']['answer']['datetime']
            payment.gateway = 'Todopago'
            payment.gateway_data = response.merge({
              request_key: request_key,
              operation_id: operation_id,
              payer: payment.gateway_data.try(:[], :payer),
            })

            payment.installments = response['payload']['answer']['installmentpayments'].to_i
            payment.save!
          else
            ExceptionNotifier.notify_exception(TodopagoUnprocessedCollectException.new, data: {
              response: response,
              payment: self,
              problem: "we're not processing this status_code on todopago collect_payment"
            })
          end
        rescue => error #Sometimes TodoPago Fails
          ExceptionNotifier.notify_exception(error, data: {
            response: response,
            payment: self
          })
          nil
        end

        def cancel_payment!(gateway_object_id)
          pay_refund(:voidRequest, gateway_object_id)
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          pay_refund(:returnRequest, gateway_object_id, {AMOUNT: '%.2f' % amount})
        end

        def pay_refund(method, id, opts = {})
          @payment = Mkp::Payment.find_by_gateway_object_id(id)
          options = refund_options.merge!(opts)
          response = client.send(method, options)
          return raise_with(response) unless response.present?
          response = JSON.parse(response)['envelope']['body']
          return raise_with(response) unless response.dig('void_response', 'status_code') == '2011'

          @payment.update_attribute(:status, 'refunded')
          response
        end

        def get_payment(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          client.getOperations({MERCHANT: TODOPAGO_MERCHANT, OPERATIONID: payment.order.purchase_id})
        end

        def get_status(gateway_object_id)
          client.getOperations({MERCHANT: TODOPAGO_MERCHANT, OPERATIONID: gateway_object_id})
        end

        def get_error(data)
        end

        def get_history(data)
        end

        def get_customer_legal_id(data)
          payer = data[:payer]
          return payer[:identification][:number] if payer.present?
          nil
        end

        def get_cc_brand(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          if payment.gateway_data["payload"].nil?
            ' - '
          else
            payment.gateway_data["payload"]["answer"]["paymentmethodname"]
          end
        end

        def get_cc_bin(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          if payment.gateway_data["payload"].nil?
            ' - '
          else
            payment.gateway_data["payload"]["answer"]["cardnumbervisible"]
          end
        end

        def external_id(gateway_object_id, additional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          return ' - ' if payment.gateway_data.try(:[], "payload").try(:[], "answer").try(:[], "operationnumber").nil?
          payment.gateway_data["payload"]["answer"]["operationnumber"]
        end

        def get_installments(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          if payment.gateway_data["payload"].nil?
            ' - '
          else
            payment.gateway_data["payload"]["answer"]["installmentpayments"]
          end
        end

        def refund_options
          {
            Security: TODOPAGO_SECURITY,
            Merchant: TODOPAGO_MERCHANT,
            RequestKey: @payment.gateway_data[:request_key],
          }
        end

        def raise_with(response)
          raise TodopagoUnexpectedResponse, response.to_s
        end

        def client
          header = { 'Authorization' => TODOPAGO_APP_KEY }
          TodoPago::TodoPagoConector.new(header, TODOPAGO_ENDPOINT)
        end
      end
    end
  end
end

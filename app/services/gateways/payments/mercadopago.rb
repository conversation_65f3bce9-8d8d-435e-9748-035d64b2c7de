module Gateways
  module Payments
    class Mercadopago
      ALLOWED_PARAMS = [
        :token,
        :installments,
        :payment_method_id,
        :issuer_id,
        :doc_type,
        :doc_number
      ].freeze

      LOOKUP_URL = 'https://www.mercadopago.com.ar/activities?q='.freeze

      STATUSES = {
        'pending' => 'pending',
        'approved' => 'collected',
        'in_process' => 'pending',
        'in_mediation' => 'pending',
        'rejected' => 'cancelled',
        'cancelled' => 'cancelled',
        'refunded' => 'refunded',
        'charged_back' => 'refunded',
        nil => 'cancelled'
      }.freeze

      TICKET_PAYMENT_METHODS = %w(bapropagos cargavirtual pagofacil rapipago).freeze

      class << self
        def verify_payment_params(params)
          params.symbolize_keys.select do |key,value|
            ALLOWED_PARAMS.include?(key)
          end.merge(gateway: Gateways::Payments::Mercadopago)
        end

        def collect_payment(checkout_cart, payment_data)
          total_amount = total_amount_for_checkourt_cart(checkout_cart)
          load_mercadopago_credentials(checkout_cart.store)
          data = if paying_with_ticket?(payment_data)
            {
              description: checkout_cart.title,
              transaction_amount: total_amount,
              payment_method_id: payment_data[:payment_method_id],
              payer: {
                email: checkout_cart.customer.email,
                identification: {
                  type: payment_data[:doc_type],
                  number: payment_data[:doc_number]
                }
              }
            }
          else
            total_amount = total_amount_one_installment(checkout_cart) if payment_data[:installments].to_i == 1
            {
              description: checkout_cart.title,
              transaction_amount: total_amount,
              token: payment_data[:token],
              installments: payment_data[:installments].to_i,
              payment_method_id: payment_data[:payment_method_id],
              statement_descriptor: 'GOODPEOPLE.COM',
              payer: {
                email: checkout_cart.customer.email
              }
            }.tap do |options|
              if payment_data[:installments].to_i > 1
                options[:issuer_id] = payment_data[:issuer_id].to_i
              end
            end
          end.merge(external_reference: checkout_cart.purchase_id)

          result = HashWithIndifferentAccess.new(client.create_payment(data))

          if result.key?(:id)
            Mkp::Payment.create do |object|
              object.status = STATUSES[result[:status]]
              object.collected_amount = result[:transaction_amount]
              object.collected_at = result[:date_approved]
              object.gateway = self.name.demodulize
              object.gateway_data = result
              object.gateway_object_id = result[:id]
              object.payment_method = result[:payment_type_id]
              object.installments = result[:installments]
              object.document_type = payment_data[:doc_type]
              object.document_number = payment_data[:doc_number]
            end
          elsif result.key?(:error)
            Rails.logger.error '#### MERCADOPAGO ERROR'
            Rails.logger.error result
            Rails.logger.error result[:error]

            Gateways::Payments::FailedPaymentAttemp.new(
              result[:error],
              result[:status],
              result,
              result[:cause].first[:code].to_s # send here
            )
          end
        end

        def cancel_payment!(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          if payment.status == 'pending' || payment.status == 'in process'
            #la cancelación se utiliza para pagos en efectivo.
            response = client.update_payment(gateway_object_id, status: 'cancelled')
          else
            response = client.refund(gateway_object_id)
          end
          if response.present?
            response[:id] = response['id']
            response[:status] = 'cancelled'
            response.deep_symbolize_keys
          end
        end

        def refund_partial!(gateway_object_id, amount, aditional_data = nil)
          payload = {amount: amount}
          response = client.partial_refund(gateway_object_id, payload)
          response.deep_symbolize_keys if response.present?
        end

        def refund_partial_and_update_payment!(payment, suborder, aditional_data = nil)
          result = refund_partial!(payment.gateway_object_id, suborder.total, aditional_data = nil)

          if result.key?(:error)
            message = result[:cause]
            Rails.logger.error "Error en refund parcial con decidir: #{message}"
            Rails.logger.error "response: #{result}"
            return false
          else
            if result[:status] == "approved"
              if payment.gateway_data[:refunded_suborders].present?
                suborders = "#{payment.gateway_data[:refunded_suborders]},#{suborder.id}"
              else
                suborders = suborder.id.to_s
              end

              data = payment.gateway_data.merge!(refunded_suborders: suborders)
              payment.update(gateway_data: data)
              suborder.update_attribute(:refunded, true)
              return true
            else
              message = "Refund not approved."
              Rails.logger.error "Error en refund parcial con decidir: #{message}"
              Rails.logger.error "response: #{result}"
              return false
            end
          end
        end

        def get_payment(gateway_object_id)
          collection = client.notification(gateway_object_id)['collection']
          collection.deep_symbolize_keys if collection.present?
        end

        def get_status(gateway_object_id)
          collection = get_payment(gateway_object_id)
          collection[:status] if collection.present?
        end

        def get_error(data)
          data.select{|k, v| k.to_sym == :status || k.to_sym == :status_detail}.symbolize_keys
        end

        def get_history(data)
          history = []
          if (original_payload = data[:original_payload]).present?
            history << history_event(original_payload)
            data[:notifications_payloads].each do |timestamp, payload|
              history << history_event(payload, timestamp)
            end
          else
            history << history_event(data)
          end
          history
        end

        def get_customer_legal_id(data)
          legal_id = if data[:payment_type_id] == 'credit_card'
            number = data[:cardholder][:identification][:number] rescue nil
            number ||= data[:card][:cardholder][:identification][:number]
          else
            data[:payer][:identification][:number]
          end

          legal_id
        end

        def get_cc_brand(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["payment_method_id"]
        end

        def get_installments(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id).gateway_data["installments"]
        end

        def get_cc_bin(gateway_object_id)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          if payment.gateway_data["card"].nil?
            ' - '
          else
            payment.gateway_data["card"]["first_six_digits"]
          end
        end

        def external_id(gateway_object_id, additional_data = nil)
          payment = Mkp::Payment.find_by_gateway_object_id(gateway_object_id)
          payment.gateway_data ? payment.gateway_data[:external_reference] : ' - '
        end

        def load_mercadopago_credentials(store)
          #Hay que rehacer esto, y cargar los datos de forma mas estandarizada
          #que sirva para todos los medios de pago que se deseen agregar
          credentials = store.gateway_credentials
          if credentials.count == 4
            @client_id = credentials.find_by(name: "client_id").value
            @client_secret = credentials.find_by(name: "client_secret").value
            @public_key = credentials.find_by(name: "public_key").value
            @access_token = credentials.find_by(name: "access_token").value
          end
        end

        private

        def client
          MercadoPago::Client.new(
              @client_id || MERCADOPAGO_CLIENT_ID,
              @client_secret || MERCADOPAGO_CLIENT_SECRET,
              @access_token || MERCADOPAGO_ACCESS_TOKEN
          )
        end

        def paying_with_ticket?(payment_data)
          TICKET_PAYMENT_METHODS.include?(payment_data[:payment_method_id])
        end

        def total_amount_for_checkourt_cart(checkout_cart)
          parse_total_amount(checkout_cart.total)
        end

        def total_amount_one_installment(checkout_cart)
          parse_total_amount(checkout_cart.total_one_installment)
        end

        def parse_total_amount(amount)
          if (amount % 1).zero?
            amount.to_i
          else
            amount.to_f
          end
        end

        def history_event(payload, timestamp = nil)
          {
            status: payload[:status],
            status_detail: payload[:status_detail],
            amount: payload[:transaction_amount]
          }.tap do |event|
            event[:timestamp] = if timestamp.present?
              timestamp
            else
              payload[:date_created]
            end.to_datetime
          end
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'uri'
require 'net/http'

module Gateways
  module Purchases
    # It manage comunication with puntos y premios to redempt a recharge
    class RecargaCelular < Base
      def initialize(params, user)
        @params = params
        @phone_code = params.delete(:phone_code)
        @phone_number = params.delete(:phone_number)
        @phone_company = params.delete(:phone_company)
        @full_name = params.delete(:full_name)
        @amount = params[:amount].to_f
        @cod_premio = VISAPUNTOS_COD_PREMIO
        @valid = false
        initialize_variant
        create_order(user)
      end

      private

      def create_transaction
        @integration = puntos_y_premios_service
        response = @integration.recharge(
          @order.payment.gateway_object_id,
          @params[:amount],
          @phone_company,
          @order.customer.uuid,
          @full_name,
          @phone_code,
          @phone_number
        )

        status = "Recarga Celular: #{response[:response_code]} "\
                  "- #{response[:response_message]}"

        if response[:response_code] == '1'
          @status = 'Visa Puntos: Collected. ' + status
          Mkp::PurchaseMailer.delay_for(5.seconds)
                             .recarga_celular_purchase_success(@order,
                                                               @phone_code,
                                                               @phone_number,
                                                               @phone_company,
                                                               @params[:email])
          create_and_associate_shipments
          run_invoice_item_creator(order)
          run_update_supplier_stock(order)
          @valid = true
        else
          @status = "No fue posible completar la transacción - #{status}"
          @order.payments.each { |payment| payment.cancel!(@order.store) }
          @order.destroy
        end
      rescue StandardError => e
        @status = "Error procesando el pago: #{e.message}"
        @order.payments.each { |payment| payment.cancel!(@order.store) }
        @order.destroy
      end

      def puntos_y_premios_service
        Mkp::Integration::PuntosYPremios.new
      end

      def calculate_points_amount
        equivalence = @order.store.visa_puntos_recargas_equivalence
        return 0 unless equivalence.present? && equivalence.positive?

        (@amount / equivalence).ceil
      end

      def initialize_variant
        title = "Recarga #{@phone_company} $#{@params[:amount]}"
        @variant = (Mkp::Product.find_by_title title).variants.first
      end

      def create_order(customer)
        ActiveRecord::Base.transaction do
          @order = Mkp::Order.create({
                                       customer: customer,
                                       store_id: @params[:store_id],
                                       purchase_id: "recarga-celular-#{customer.id}-#{Time.now.to_i}",
                                       title: @variant.product.title,
                                       id_cobis: customer.uuid
                                     })

          suborder = @order.suborders.create({
                                               shop: @variant.product.shop,
                                               title: @variant.product.title
                                             })

          suborder.items.create({
                                  product: @variant.product,
                                  variant: @variant,
                                  quantity: 1,
                                  price: @amount,
                                  points: calculate_points_amount,
                                  point_equivalent: @order.store.visa_puntos_recargas_equivalence
                                })
        end
      end

      def create_and_associate_shipments
        suborder = @order.suborders.first
        manager = Mkp::ShipmentsManager.new(@order, suborder, suborder.items,
                                            nil,
                                            OpenStruct.new({ email: @params[:email] }))
        manager.create_shipment('virtual')
        @order.shipments.each do |shipment|
          Mkp::StatusChange::EntityStatusManage.status_change(shipment, 'delivered')
        end
      end
    end
  end
end

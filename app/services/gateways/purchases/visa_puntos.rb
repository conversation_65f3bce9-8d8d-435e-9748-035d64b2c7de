# frozen_string_literal: true

require 'uri'
require 'net/http'

module Gateways
  module Purchases
    #  Class to manage visa puntos order creation
    class VisaPuntos
      attr_accessor :status, :order, :valid, :payment_data

      def initialize(params, user)
        @params = params
        @customer = user
        @variant = Mkp::Variant.find(params[:variant_id])
        @cod_premio = VISAPUNTOS_COD_PREMIO
        @valid = false
      end

      def perform
        create_order

        @payment_data = {}
        @payment_data[:domicilio_completo] = 'Av. Eduardo Madero 1180'
        @payment_data[:codigo_postal] = '1106'
        @payment_data[:telefono] = '01152226500'
        @payment_data[:localidad] = 'CABA'
        @payment_data[:cod_provincia] = '1'
        @payment_data[:exchange_type] = 'points'
        @payment_data[:cod_premio] = @cod_premio

        payment = Gateways::Payments::VisaPuntos.collect_payment(@order,
                                                                 @payment_data)
        error = payment.get_error

        if error.present?
          @status = "No fue posible completar la transacción "\
          "- #{I18n.t("v5.controllers.checkout.payment.errors.#{error[:status_detail]}")}"
          @order.destroy
        else
          if @order.store.name == 'bancomacro'
            Mkp::Integration::PuntosYPremios.new.redemption(@order, @params[:email])
          end
          complete_order(payment)
        end
      rescue StandardError => e
        revert_order "Error procesando el pago: #{e.message}"
      end

      private

      def create_order
        ActiveRecord::Base.transaction do
          @order = Mkp::Order.create({
                                         customer: @customer,
                                         store_id: @params[:store_id],
                                         purchase_id: "macro-points-#{@customer.id}-#{Time.now.to_i}",
                                         title: @variant.product.title
                                     })

          suborder = @order.suborders.create({
                                                shop: @variant.product.shop,
                                                title: @variant.product.title
                                             })

          suborder.items.create({
                                  product: @variant.product,
                                  variant: @variant,
                                  quantity: 1,
                                  price: @variant.product.price.present? ? @variant.product.price : 0,
                                  points: @variant.points_price,
                                  point_equivalent: @order.store.visa_puntos_equivalence
                                })

        end
      end

      def revert_order(error)
        @status = error
        @order.payments.each(&:cancel!)
        @order.destroy
      end

      def complete_order(payment)
        @status = 'Visa Puntos: Collected.'
        @order.payments << payment
        @variant.decrease_quantity(1)
        Mkp::PurchaseMailer.delay_for(5.seconds).points_exchange_success(@order,
                                                                         @variant,
                                                                         @params[:email])
        create_and_associate_shipments
        run_invoice_item_creator(order)
        run_update_supplier_stock(order)
        @valid = true
      end

      def run_invoice_item_creator(order)
        InvoiceItem.create_from(order)
      end

      def run_update_supplier_stock(order)
        SupplierStock.create_from(order)
      end

      def create_and_associate_shipments
        suborder = @order.suborders.first
        manager = Mkp::ShipmentsManager.new(@order, suborder, suborder.items,
                                            nil,
                                            OpenStruct.new({ email: @params[:email] }))
        manager.create_shipment('virtual')
        @order.shipments.each do |shipment|
          Mkp::StatusChange::EntityStatusManage.status_change(shipment, 'delivered')
        end
      end
    end
  end
end

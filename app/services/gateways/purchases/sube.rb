require 'uri'
require 'net/http'

module Gateways
  module Purchases
    class Sube < Base
      def initialize(params, user)
        @card_number = params.delete(:card_number)
        @params = params
        @amount = params[:amount].to_f
        @order = Purchase.create(params.merge!(gateway: 'Sube', target_item: @card_number))
        @order.update(points: calculate_points_amount)
        @order.customer = user
        @cod_premio = VISAPUNTOS_COD_PREMIO
        @valid = false
      end

      private

      def create_transaction
        url = URI("#{SUBE_URL}/buy")
        http = Net::HTTP.new(url.host, url.port)

        request = Net::HTTP::Post.new(url)
        request["content-type"] = 'application/json'

        body_hash = {
          "purchase" => {
            "card_number" => @card_number,
            "charge_amount" => @params[:amount].to_i
          }
        }

        request.body = body_hash.to_json
        response = http.request(request)
        result = JSON.parse(response.body)

        Rails.logger.debug "SUBE transaction result:"
        Rails.logger.debug result['result_message']

        if result['result_success']
          @status = "Visa Puntos: Collected - Sube: #{result['result_message']}"
          @order.update(
            status: @status,
            external_transaction_id: result['logs']['sub_etransaction_id'] || '0999999999',
            point_equivalent: @order.store.visa_puntos_sube_equivalence
          )
          Mkp::PurchaseMailer.delay_for(5.seconds).sube_purchase_success(@order, result['channel'], @card_number)
          run_invoice_item_creator(order)
          run_update_supplier_stock(order)
          @valid = true
        else
          @status = result['result_message']
          @order.payments.each{ |payment| payment.cancel! }
          @order.destroy
        end
      end

      def calculate_points_amount
        result = 0
        equivalence = @order.store.visa_puntos_sube_equivalence
        result = (@amount / equivalence).ceil if equivalence.present? && equivalence > 0
        result
      end
    end
  end
end

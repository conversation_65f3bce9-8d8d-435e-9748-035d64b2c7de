# frozen_string_literal: true

require 'uri'
require 'net/http'

module Gateways
  module Purchases
    # Class to manage macro voucher order creation
    class MacroVoucher
      attr_accessor :status, :order, :valid, :payment_data

      def initialize(params, user)
        @params = params
        @customer = user
        @variant = Mkp::Variant.find(params[:variant_id])
        @payment_method = Mkp::Product::PAYMENT_METHODS.find { |m| m[:value] == @variant.properties[:payment_method] }
        @valid = false
      end

      def perform
        create_order

        @payment_data = {}
        @payment_data[:domicilio_completo] = 'Av. Eduardo Madero 1180'
        @payment_data[:codigo_postal] = '1106'
        @payment_data[:telefono] = '01152226500'
        @payment_data[:localidad] = 'CABA'
        @payment_data[:cod_provincia] = '1'
        @payment_data[:exchange_type] = 'voucher'
        @payment_data[:producto] = @payment_method[:key]
        @payment_data[:cod_premio] = @variant.sku

        payment = Avenida::Payments::VisaPuntos.collect_payment(@order,
                                                                 @payment_data)
        error = payment.get_error

        if error.present?
          @status = "No fue posible completar la transacción"\
                    " - #{I18n.t("v5.controllers.checkout.payment.errors.#{error[:status_detail]}")}"
          @order.destroy
        else
          @status = 'Visa Puntos: Collected.'
          order.payments << payment
          @variant.decrease_quantity(1)
          Mkp::PurchaseMailer.delay_for(5.seconds)
                             .voucher_exchange_success(@order,
                                                       @variant,
                                                       @params[:email])
          create_and_associate_shipments
          run_invoice_item_creator(order)
          run_update_supplier_stock(order)
          @valid = true
        end
      rescue StandardError => e
        @status = "Error procesando el pago: #{e.message}"
        @order.payments.each(&:cancel!)
        @order.destroy
      end

      private

      def create_order
        ActiveRecord::Base.transaction do
          @order = Mkp::Order.create({
                                                customer: @customer,
                                                store_id: @params[:store_id],
                                                purchase_id: "macro-voucher-#{@customer.id}-#{Time.now.to_i}",
                                                title: @variant.product.title,
                                                id_cobis: @customer.uuid
                                     })

          suborder = @order.suborders.create({
                                               shop: @variant.product.shop,
                                               title: @variant.product.title
                                             })

          suborder.items.create({
                                  product: @variant.product,
                                  variant: @variant,
                                  quantity: 1,
                                  price: @variant.product.price.present? ? @variant.product.price : 0,
                                  points: @variant.points_price,
                                  point_equivalent: @order.store.visa_puntos_equivalence
                                })
        end
      end

      def run_invoice_item_creator(order)
        InvoiceItem.create_from(order)
      end

      def run_update_supplier_stock(order)
        SupplierStock.create_from(order)
      end

      def create_and_associate_shipments
        suborder = @order.suborders.first
        manager = Mkp::ShipmentsManager.new(@order, suborder, suborder.items,
                                            nil,
                                            OpenStruct.new({ email: @params[:email] }))
        manager.create_shipment('virtual')
        @order.shipments.each do |shipment|
          Mkp::StatusChange::EntityStatusManage.status_change(shipment, 'delivered')
        end
      end
    end
  end
end

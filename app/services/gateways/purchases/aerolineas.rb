require 'uri'
require 'net/http'

module Gateways
  module Purchases
    class Aerolineas < Base
      def initialize(params, user)
        @params = params
        @order = Purchase.create(params.merge!(gateway: 'Aerolineas'))
        @order.customer = user
        @cod_premio = VISAPUNTOS_COD_PREMIO
        @valid = false
      end

      private

      def create_transaction
        mile_accreditation_params = @params
        mile_accreditation_params[:order_id] = @order.id
        mile_accreditation_params[:miles] = @order.miles

        @integration = aerolineas_argentinas_services
        response = @integration.mile_accreditation(mile_accreditation_params)

        status = "Aerolineas Argentinas: #{response["codigo_status"].strip} - #{response["descri_status"].strip}"

        if response["codigo_status"].strip == '000'
          @status = "Visa Puntos: Collected. " + status
          @order.update(status: @status, point_equivalent: @order.store.aerolineas_argentinas_credential.points_equivalent, target_item: response[:membership_number])
          Mkp::PurchaseMailer.delay_for(5.seconds).aerolineas_purchase_success(@order)
          run_invoice_item_creator(order)
          run_update_supplier_stock(order)
          @valid = true
        else
          @status = "No fue posible completar la transacción - #{status}"
          @order.payments.each{ |payment| payment.cancel! }
          @order.destroy
        end
      end

      def aerolineas_argentinas_services
        AerolineasArgentinas::WebServices.new
      end
    end
  end
end

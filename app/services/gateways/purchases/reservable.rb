require 'uri'
require 'net/http'

module Gateways
  module Purchases
    class Reservable
      BNA_MIMOTO_ID = 43

      attr_accessor :order, :valid, :user, :error

      def initialize(params, store, bna_params = nil)
        @params = params
        @store = store
        @variant = Mkp::Variant.find(params[:variant_id])
        @valid = false
        @bna_params = bna_params
      end

      def perform
        return unless validate_whitelist
        return unless validate_customer_multiple_purchases
        return unless validate_product_availability

        return if @store.id == BNA_MIMOTO_ID && !validate_date_mimoto
        return unless create_customer

        create_order
      end

      private

      def create_customer
        @user = @store.customers.find_by(doc_type: 'DNI', doc_number: @params[:dni], provider: @store.name)

        if @user.present?
          true
        else
          fullname = @params[:full_name].split(' ', 2)

          # password = Devise.friendly_token[0, 7]

          # @user = @store.customers.new({
          #                                email: @params[:email],
          #                                password: password,
          #                                password_confirmation: password,
          #                                first_name: fullname[0].strip,
          #                                last_name: fullname[1].strip,
          #                                provider: @store.name,
          #                                doc_type: 'DNI',
          #                                doc_number: @params[:dni]
          #                              })

          @user = Mkp::Guest.create({
                                      email: @params[:email],
                                      first_name: fullname[0].strip,
                                      last_name: fullname[1].strip,
                                      network: 'AR',
                                      doc_number: @params[:dni],
                                      telephone: @params[:phone_number]
                                      # provider: @store.name
                                    })

          if @user.save
            true
          else
            @error = 'Ha ocurrido un error'
            handle_error(@user.errors)
            false
          end
        end
      end

      def create_order
        ActiveRecord::Base.transaction do
          @order = Mkp::Order.create({
                                       customer: @user,
                                       store_id: @store.id,
                                       purchase_id: "reservable-#{@user.id}-#{Time.now.to_i}",
                                       title: @variant.product.title,
                                       gross_total: 0
                                     })

          suborder = @order.suborders.create({
                                               shop: @variant.product.shop,
                                               title: @variant.product.title
                                             })

          suborder.items.create({
            product: @variant.product,
            variant: @variant,
            quantity: 1,
            points: 0,
            currency: Mkp::Currency.find_by(network: 'AR')
          }.merge(item_extra_attrs(@store.id)))

          CustomerReservationPurchases.create(customer_dni: @params[:dni].strip,
                                              store_id: @store.id,
                                              product_id: @variant.product.id)

          update_reservation_code if @variant.product.third_party_code_required

          if @store.id == BNA_MIMOTO_ID
            @variant.decrease_quantity(1)
            assign_office_to_suborder(suborder)
            package = Mkp::Bna::InfoPackage.new(
              info_package_json.tap do |ipack_json|
                # Para mantener formato ya guardado en mkp_bna_info_package (varchar)
                ipack_json[:order_date] = ipack_json[:order_date].to_time.utc.iso8601(3)
              end
            )
            Mkp::Bna::MailerWorker.perform(@order, @params[:token], 'booked') if package.save!
          else
            Mkp::VoucherMailer.delay_for(5.seconds).customer_reservation(@order)
          end
        end

        run_invoice_item_creator(order)
        run_update_supplier_stock(order)
        @valid = true
      rescue ActiveRecord::RecordInvalid => e
        @error = e.message
      end

      def item_extra_attrs(store_id)
        case store_id
        when BNA_MIMOTO_ID then { status: 'booked', price: @variant.price }
        else { status: 'available', price: 0 }
        end
      end

      def update_reservation_code
        reservation_code = ThirdPartyCode.where(store: @store,
                                                manufacturer: @variant.product.manufacturer,
                                                available: true).first
        @order.reservation_code = reservation_code.code
        @order.reservation_status = 'active'
        reservation_code.update(available: false)
      end

      def update_reservation_code
        reservation_code = ThirdPartyCode.where(store: @store,
                                                manufacturer: @variant.product.manufacturer,
                                                available: true).first
        @order.reservation_code = reservation_code.code
        @order.reservation_status = 'active'
        reservation_code.update(available: false)
      end

      def validate_configuration?
        @store.whitelist_configuration.user_strategy_class.exists?(@params[:dni], @store)
      end

      def validate_dni?
        @store.whitelist_configuration.validation_strategy_class.authorized?(@params[:dni], @store, @variant.product.id)
      end

      def validate_product_availability
        items = [{ variant_id: @variant.id }]
        service = Checkout::ValidateProductAvailability.new(items, @store)
        service.perform

        if service.valid
          true
        else
          handle_error(service.error)
          false
        end
      end

      def validate_whitelist
        if @store.whitelist_configuration.present? && @store.whitelist_configuration.active
          if validate_configuration?
            if validate_dni?
              true
            else
              handle_error("Usted ya adquirió este producto: #{@variant.product.title}")
              false
            end
          else
            handle_error(@store.whitelist_configuration.unauthorized_user_message)
            false
          end
        else
          # handle_error('Error procesando la operación')
          true
        end
      end

      def validate_customer_multiple_purchases
        return true unless @store.id == BNA_MIMOTO_ID

        valid = CustomerReservationPurchases
                .where(customer_dni: @params[:dni], store_id: @store.id).exists?
        handle_error('Ya hay una reserva en curso') if valid
        !valid
      end

      def handle_error(error)
        @error = error
        ExceptionNotifier.notify_exception('Error procesando reserva', data: {
                                             error: error,
                                             service: 'Gateways::Purchases::Reservable',
                                             params: @params.dup,
                                             store: @store.dup
                                           })
      end

      def run_invoice_item_creator(order)
        InvoiceItem.create_from(order)
      end

      def run_update_supplier_stock(order)
        SupplierStock.create_from(order)
      end

      def info_package_json
        {
          solicitude_id: @bna_params[:solicitude_id],
          program_type: @bna_params[:program_type],
          client_profile: @bna_params[:profile],
          order_date: @bna_params[:date],
          limit_amount: @bna_params[:limit_amount],
          order_id: @order.id,
          office_token: @params[:token]
        }
      end

      def assign_office_to_suborder(suborder)
        office = Mkp::Bna::Office.find_by(token: @params[:token])
        suborder.update!(office: office)
      end

      def validate_date_mimoto
        if HashValidatorService.expired_date?(@bna_params[:date])
          handle_error('Fecha expirada')
          false
        else
          true
        end
      end
    end
  end
end

require 'uri'
require 'net/http'

module Gateways
  module Purchases
    class Base
      attr_accessor :status, :order, :valid, :payment_data, :cod_premio

      def perform
        @payment_data = {}
        @payment_data[:domicilio_completo] = "Av. <PERSON> 1180"
        @payment_data[:codigo_postal] = "C1106ACY"
        @payment_data[:telefono] = "01152226500"
        @payment_data[:localidad] = "CABA"
        @payment_data[:cod_provincia] = "1"
        @payment_data[:document_type] = ''
        @payment_data[:document_number] = @params[:document_number] || ''
        @payment_data[:cod_premio] = @cod_premio

        if @order.sp_subtotal_points > 0
          payment = Avenida::Payments::VisaPuntos.collect_payment(@order, @payment_data)
          error = payment.get_error

          if error.present?
            @status = "No fue posible completar la transacción"
            @order.destroy
            Rails.logger.error "#{@status} - #{I18n.t("v5.controllers.checkout.payment.errors.#{error[:status_detail]}")}"
          else
            @order.payments << payment
            create_transaction
          end
        else
          @status = "El monto en puntos debe ser mayor a 0"
          @order.destroy
        end
      rescue StandardError => e
        @status = "Error procesando el pago"

        @order.payment.cancel! if @order.payment.present?
        @order.destroy
      end

      def run_invoice_item_creator(order)
        InvoiceItem.create_from(order)
      end

      def run_update_supplier_stock(order)
        SupplierStock.create_from(order)
      end
    end
  end
end

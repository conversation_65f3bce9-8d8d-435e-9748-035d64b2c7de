module UrlMapperService
  extend self

  PRODUCT_HABTM_ASSOCIATIONS = %w(genders sports).freeze
  RELEVANT_ATTRIBUTES = {
    'Product' => %w[slug manufacturer_id category_id genders sports],
    'Category' => %w[slug]
  }.freeze

  ASSOCIATIONS = {
    'Product' => [
      :genders,
      :manufacturer,
      :sports,
      :category
    ],
    'Category' => [
      :genders,
      :manufacturers,
      :sports
    ]
  }.freeze

  def generate_urls_for(model)
    UrlMapperServiceWorker.perform_in(30.minutes, :generate, model.class.name, model.id)
    UrlMapper.map_out(model, model.network)
  end

  def destroy_urls_for(model)
    destroy_urls(model)
    UrlMapper.purge_map(model, model.network)
  end

  def update_urls_for(model)
    network = model.network
    update_urls(model, network)

    previous_model = setup_previous_model(model)

    UrlMapper.purge_map(previous_model, network)
    UrlMapper.map_out(model, network)
  end

  # Publicly accessible utilities (e.g. from workers).
  def generate_urls(model)
    traverse_urls(model, UrlMapper.method(:map_out))
  end

  def update_urls(model, network)
    return unless RELEVANT_ATTRIBUTES[class_name(model)].any? { |k| model.changes.key?(k) }

    previous_model = setup_previous_model(model)

    # Purge stale URLs.
    traverse_urls(previous_model, UrlMapperHelper.method(:mapper_path)).reject do |url|
      url_has_results?(url, network)
    end.each { |url| UrlMapper.purge_path(url, network)  }

    # And now we generate the URLs.
    generate_urls(model)
  end

  def changes_are_relevant?(model)
    (RELEVANT_ATTRIBUTES[class_name(model)] & model.changes.keys).any?
  end

  private

  def destroy_urls(model)
    traverse_urls(model, UrlMapperHelper.method(:mapper_path)).reject do |url|
      url_has_results?(url, model.network)
    end.each { |url| UrlMapper.purge_path(url, model.network) }
  end

  def traverse_urls(model, operation)
    models = if model.class.name == 'Mkp::Category'
      Array.wrap(model) + model.children
    else
      Array.wrap(model)
    end

    # We need the network only if we're writing URLs.
    network = model.network if operation.name == :map_out

    paths = models.each_with_object([]) do |updated_model, paths|
      associations = ASSOCIATIONS[class_name(updated_model)].map do |association_name|
        Array.wrap(updated_model.send(association_name))
      end

      genders, manufacturers, sports, categories = associations

      catalog_updated_model = UrlMapperHelper::CATALOG_MODELS_ORDER.include?(updated_model.class.name) ? updated_model : nil

      associations.each do |collection|
        collection.each do |model|
          models = [model] + Array.wrap(catalog_updated_model)

          paths << operation.call(models, network)

          case model.class.name
          when 'Mkp::Gender'
            paths << traverse_manufacturers_urls(operation, models, network, manufacturers, sports, categories)
            paths << traverse_sports_urls(operation, models, network, sports, categories)
            paths << traverse_categories_urls(operation, models, network, categories)
          when 'Mkp::Manufacturer'
            paths << traverse_sports_urls(operation, models, network, sports, categories)
            paths << traverse_categories_urls(operation, models, network, categories)
          end
        end
      end
    end

    paths.flatten
  end

  def traverse_manufacturers_urls(operation, models, network, manufacturers, sports, categories)
    manufacturers, sports, categories = Array.wrap(manufacturers), Array.wrap(sports), Array.wrap(categories)

    manufacturers.each_with_object([]) do |manufacturer, paths|
      models_combination = models + [manufacturer]
      paths << operation.call(models_combination, network)

      sports.each do |sport|
        models_combination = models + [manufacturer, sport]
        paths << operation.call(models_combination, network)

        categories.each do |category|
          models_combination = models + [manufacturer, sport, category]
          paths << operation.call(models_combination, network)
        end
      end
    end
  end

  def traverse_sports_urls(operation, models, network, sports, categories)
    sports, categories = Array.wrap(sports), Array.wrap(categories)

    sports.each_with_object([]) do |sport, paths|
      models_combination = models + [sport]
      paths << operation.call(models_combination, network)

      categories.each do |category|
        models_combination = models + [sport, category]
        paths << operation.call(models_combination, network)
      end
    end
  end

  def traverse_categories_urls(operation, models, network, categories)
    categories = Array.wrap(categories)

    categories.each_with_object([]) do |category, paths|
      models_combination = models + [category]
      paths << operation.call(models_combination, network)
    end
  end

  def url_has_results?(url, network)
    stored_data = UrlMapper.send(:retrieve, url, network)

    params = JSON.parse(stored_data)['params'] if stored_data.present?

    return unless params.present?

    params.merge!(network: network)

    Mkp::Catalog::Finder.find(params.deep_symbolize_keys).hits.count > 0
  end

  def setup_previous_model(model)
    other = model.dup

    relevant_changes = model.changes.slice(*RELEVANT_ATTRIBUTES[class_name(model)])

    if model.is_a?(Mkp::Product)
      PRODUCT_HABTM_ASSOCIATIONS.each do |key|
        unless relevant_changes.keys.include?(key)
          other.public_send("#{key}=", model.public_send("#{key}"))
        end
      end
    end

    relevant_changes.each do |(key, (previous, following))|
      other.public_send("#{key}=", previous)
    end

    other
  end

  def class_name(model)
    model.class.name.demodulize
  end
end

module CancelService
  extend self

  def cancel(ids, motive = "")
    orders = Mkp::Order.where(id: ids)
    orders.each do |order|
      begin
        order.payment.update_columns(status: "cancelled")
        order.update_cancel_with!(motive, false)
        order.suborders.each do |suborder|
          suborder.shipments.each{|s| Mkp::StatusChange::EntityStatusManage.status_change(s, 'cancelled')}
          suborder.update_cancel_with!(motive, false)
        end
      rescue => e
        message = "order #{order.id} - description #{e.message}"
        ExceptionNotifier.notify_exception(e, data: {message: message})
      end
    end
  end

  def refund(ids, motive = "")
    orders = Mkp::Order.where(id: ids)
    orders.each do |order|
      begin
        order.payment.update_columns(status: "refunded")
        order.update_refund_with!(motive, false)
        order.suborders.each do |suborder|
          suborder.shipments.each{|s| Mkp::StatusChange::EntityStatusManage.status_change(s, 'cancelled')}
          suborder.update_refund_with!(motive, false)
        end
      rescue => e
        message = "order #{order.id} - description #{e.message}"
        ExceptionNotifier.notify_exception(e, data: {message: message})
      end
    end
  end
end

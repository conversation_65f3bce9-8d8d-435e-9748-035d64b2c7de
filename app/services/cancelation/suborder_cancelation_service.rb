# frozen_string_literal: true

module Cancelation
  # class to manage suborder cancelations
  class SuborderCancelationService < CancelationService
    private

    def object_for_cancel
      suborder
    end

    def objects_to_update
      [suborder]
    end

    def validate_payments
      return true if cupon?

      return false unless payment.present?
      return false unless payment.gateway_data[:refunded_suborders].present?

      suborder_ids = payment.gateway_data[:refunded_suborders].split(',')
      suborder_ids.include?(@suborder.id.to_s)
    end

    def credit_note_generation!
      return unless Rails.env.production?

      return unless can_credit_note

      credit_note = order.credit_notes.create(gateway: 'Colppy',
                                              suborder: suborder)
      credit_note&.generate!(order, suborder)
    end

    def cancel_payments!
      return unless can_refund_payment?

      payment.refund_partial_and_update_payment!(@suborder,
                                                 { shop_id: @suborder.shop.id })

      @order.reload
    end

    def can_refund_payment?
      !cupon? &&
        (suborder.in_process? ||
            suborder.fulfilled? ||
            suborder.unfulfilled?) &&
        order.payments.all?(&:collected?)
    end

    def payment
      @payment ||= @order.payment
    end
  end
end

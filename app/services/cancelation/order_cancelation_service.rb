# frozen_string_literal: true

module Cancelation
  # Class to manage order cancelations
  class OrderCancelationService < CancelationService
    private

    def validate_payments
      if @order.store.cancel_payments
        return @order.payments.all?(&:returned?) || cupon?
      end

      true
    end

    def object_for_cancel
      order
    end

    def objects_to_update
      order.suborders
    end

    def credit_note_generation!
      return unless Rails.env.production?

      order.credit_notes.create(gateway: 'Colppy')
      order.credit_notes.last.generate!(order, nil) if order.credit_notes.any?
    end

    def cancel_payments!
      return if cupon?

      @order.payments.where.not(status: %w[cancelled refunded]).each(&:cancel!)
    end
  end
end

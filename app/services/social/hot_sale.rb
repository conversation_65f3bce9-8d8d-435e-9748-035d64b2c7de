module Social
  class HotSale
    class << self
      def isToday?
        (Date.today - 3.hours).between? Date.new(2017, 5, 15), Date.new(2017, 5, 17)
      end
      def isExtendedWeekToday?
        (Date.today - 3.hours).between? Date.new(2017, 5, 15), Date.new(2017, 5, 21)
      end
      def logoPath
        self.isExtendedWeekToday? ? 'logos/logo-full-hot-week.svg' : 'logos/logo-full.png'
      end
    end
  end
end

module Social
  module PostRecommender
    SAME_AUTHOR_SIZE = 2
    MONTH_TIME_FRAME = 2
    MIN_SAME_SPORTS_SIZE = 2
    MIN_SAME_TYPE_SIZE = 2

    def self.recommend(post, network, limit)
      posts = []
      posts += same_author_posts(post)

      same_sport_posts_size = MIN_SAME_SPORTS_SIZE + 2 - posts.count
      posts += same_sport_posts(post,
                                same_sport_posts_size,
                                network,
                                posts.select{|post|post.is_a?(Whatsup)}.map(&:id),
                                posts.select{|post|post.is_a?(Album)}.map(&:id))

      same_type_posts_size = MIN_SAME_TYPE_SIZE + 4 - posts.count
      posts += same_type_posts(post,
                               same_type_posts_size,
                               network,
                               posts.select{|post|post.is_a?(Whatsup)}.map(&:id),
                               posts.select{|post|post.is_a?(Album)}.map(&:id))

      posts.sort_by(&:created_at).reverse
    end

    private

    def self.same_author_posts(post)
      whatsups = post.author.sent_whatsups
                     .where('whatsups.created_at > ?', MONTH_TIME_FRAME.months.ago)
                     .limit(SAME_AUTHOR_SIZE)
                     .order('RAND()')

      whatsups = whatsups.where('whatsups.id <> ?', post.id) if post.is_a? Whatsup

      albums = post.author.albums
                   .where('albums.created_at > ?', MONTH_TIME_FRAME.months.ago)
                   .limit(SAME_AUTHOR_SIZE)
                   .order('RAND()')

      albums = albums.where('albums.id <> ?', post.id) if post.is_a? Album

      (whatsups + albums).sample(SAME_AUTHOR_SIZE)
    end

    def self.same_sport_posts(post, limit, network, avoid_whatsups_id, avoid_albums_ids)
      return [] if post.sports.empty?
      whatsups = Whatsup.includes(:sports)
                        .joins(:author)
                        .where('sports.id IN(?)', post.sport_ids)
                        .where('whatsups.created_at > ?', MONTH_TIME_FRAME.months.ago)
                        .where('users.network = ?', network)
                        .limit(limit)
                        .order('RAND()')

      whatsups = whatsups.where('whatsups.id NOT IN (?)', avoid_whatsups_id) if avoid_whatsups_id.any?
      whatsups = whatsups.where('whatsups.id <> ?', post.id) if post.is_a? Whatsup

      albums = Album.joins(:author)
                    .includes(:sports)
                    .where('sports.id IN(?)', post.sport_ids)
                    .where('albums.created_at > ?', MONTH_TIME_FRAME.months.ago)
                    .where('users.network = ?', network)
                    .limit(limit)
                    .order('RAND()')

      albums = albums.where('albums.id NOT IN (?)', avoid_albums_ids) if avoid_albums_ids.any?
      albums = albums.where('albums.id <> ?', post.id) if post.is_a? Album

      (whatsups + albums).sample(limit)
    end

    def self.same_type_posts(post, limit, network, avoid_whatsups_id, avoid_albums_ids)
      if post.is_a? Album
        albums = Album.joins(:author)
                      .where('albums.created_at > ?', MONTH_TIME_FRAME.months.ago)
                      .where('users.network = ?', network)
                      .where('albums.id <> ?', post.id)
                      .limit(limit)
                      .order('RAND()')

        albums = albums.where('albums.id NOT IN (?)', avoid_albums_ids) if avoid_albums_ids.any?
        albums
      else post.is_a? Whatsup
        whatsups = Whatsup.joins(:author)
                          .where('whatsups.created_at > ?', MONTH_TIME_FRAME.months.ago)
                          .where('users.network = ?', network)
                          .where('whatsups.id <> ?', post.id)
                          .limit(limit)
                          .order('RAND()')

        whatsups = whatsups.where('whatsups.id NOT IN (?)', avoid_whatsups_id) if avoid_whatsups_id.any?

        if (type_filter = get_post_type(post.metadata)).presence
          whatsups.where("metadata LIKE '%#{type_filter}%'")
        else
          whatsups = whatsups.where('metadata = ?', nil.to_yaml)
          if post.picture.present?
            whatsups.joins(:picture)
          else
            whatsups.where('whatsups.id NOT IN (?)', Attachment::WhatsupPicture.pluck(:owner_id))
          end
        end
      end
    end

    def self.get_post_type(metadata)
      return nil if metadata.blank?
      metadata[:type]
    end
  end
end

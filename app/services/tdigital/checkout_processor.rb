class Tdigital::CheckoutProcessor

  #  Tarjeta Digital Status
  #  0 - <PERSON><PERSON><PERSON> (Estado de transición, raramente lo verás)
  #  1 - OK
  #  2 - En Curso
  #  3 - Rechazado

  STATUS = %i[pending collected pending cancelled].freeze

  BODY = {
    not_payment: {
      body: {success: false, message: 'Not payment for purchase_id'},
      code: 422
    },
    not_collected:{
      body: {success: false, message: 'Payment pending'},
      code: 422
    },
  }

  attr_reader :purchase_id

  def initialize(purchase_id:)
    @purchase_id = purchase_id
  end

  def done
    return not_payment if payment.blank?
    #@response = client.verify(opts)
    #return not_collected unless @response.dig('success')
    collected
  end

  private
  def opts
    {
      idRetailer: TDIGITAL_RETAILER,
      idRequest: payment.gateway_data.dig(:request_id),
      idOperation: payment.gateway_data.dig('idOperation'),
      token: payment.gateway_data.dig('token')
    }
  end

  def payment
    @payment ||= Mkp::Payment.joins(:order)
                             .find_by( gateway: "Tarjetadigital", mkp_orders: { purchase_id: @purchase_id })
  end

  def not_payment
    OpenStruct.new(BODY[:not_payment])
  end

  def not_collected
    OpenStruct.new(unproccesable_entity || BODY[:not_collected])
  end

  def collected
    payment.update_column(:status, 'collected')
    body = { order_id: payment.order.id }
    OpenStruct.new(body: body, code: 200)
  end

  def unproccesable_entity
    @response &&
    { body: {success: false, message: @response.dig('message', 'desc')}, code: 422 }
  end

  def client
    @client ||= Client.new
  end
end

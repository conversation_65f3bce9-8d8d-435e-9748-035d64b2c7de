module Tdigital
  module Base

    AUTH_CREDENTIALS = {
      grant_type: "client_credentials",
      client_id: TDIGITAL_USER,
      client_secret: TDIGITAL_PASSEWORD,
    }

    REST_EXPEPTIONS = [ RestClient::Forbidden, RestClient::BadRequest]

    private
    attr_reader :token

    def header
      { authorization: "Bearer #{token}" } if token.present?
    end

    def authorization
      response = request(:post, @auth_url, params: AUTH_CREDENTIALS)
      @token ||= response["access_token"]
    end

    def request_auth(method, path_info, params = {})
      authorization if token.blank?
      request(method, path_info, params)
    end

    def request(method, path_info, args = {})
      response = RestClient::Request.execute(method: method,
                                             url: path_info,
                                             headers: header,
                                             payload: args[:params],
                                             verify_ssl: false)
      JSON.parse(response)
    rescue *REST_EXPEPTIONS => e
      JSON.parse(e.http_body)
    rescue => exception
      store = Mkp::Store.find(20)
      Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, store).deliver_later
      { message: exception.message, status: exception.http_code }
    end
  end
end

module Tdigital
  class Client
    include Base

    def initialize
      @version  = 'v1'
      @domain = TDIGITAL_DOMAIN
      @auth_url = @domain + '/authorize/token'
      @api_url  = @domain + '/api/' + @version
    end

    def operation(params)
      request_auth(:post, "#{@api_url}/request", params: params)
    end

    def verify(params)
      request(:get, "#{@api_url}/verify?#{params.to_query}")
    end
  end
end

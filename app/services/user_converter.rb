module UserConverter
  extend self

  def to_brand(user)
    user_profile = user.profile
    brand_profile = Social::Profile::Brand.new(
      name: user_profile.full_name,
      user_id: user.id,
      created_at: user_profile.created_at,
      updated_at: user_profile.updated_at
    )

    if brand_profile.save!
      brand = Social::Profile::Brand.find_by_user_id(user.id)

      unless user_profile.avatar.new_record?
        brand_avatar = Social::Attachment::AvatarPicture.find(user_profile.avatar.id)
        brand_avatar[:user_id] = user.id
        brand_avatar[:owner_type] = 'Social::Profile::Brand'
        brand_avatar.owner_id = brand.id
        brand_avatar.save!
      end

      unless user_profile.cover.new_record?
        brand_cover = Social::Attachment::CoverPicture.find(user_profile.cover.id)
        brand_cover.user_id = user.id
        brand_cover.owner_type = 'Social::Profile::Brand'
        brand_cover.owner_id = brand.id
        brand_cover.save!
      end

      if user.update_attribute(:type, 'Brand')
        user_profile = Social::Profile::User.find_by_user_id(user.id)
        user_profile.destroy
      end

    end

    clear_cache_of_recommendations
  end

  def clear_cache_of_recommendations
    if recommended_keys = $redis.keys('*follow_recommendations_*').presence
      $redis.del(recommended_keys)
    end
  end
end

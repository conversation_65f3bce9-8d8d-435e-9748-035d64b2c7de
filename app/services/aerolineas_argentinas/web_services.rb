require 'uri'
require 'net/http'
module AerolineasArgentinas
  class WebServices
    def initialize
      @gateway_url_api = "#{AEROLINEAS_ARGENTINAS_URL}/api/"
    end

    def frequent_traveler_information(data)
      if data[:document_type] == 'DNI' || data[:document_type] == 'PAS'
        frequent_traveler_by_document_number(data[:document_type], data[:document_number], data[:store_id])
      elsif data[:document_type] == 'SOC'
        frequent_traveler_by_membership_number(data[:document_number], data[:store_id])
      else
        {"error": "Debe utilizar DNI, PAS o SOC" }.to_json
      end
    end

    def frequent_traveler_by_document_number(document_type, document_number, store_id)
      credentials = credentials_for(store_id)

      return {} if !credentials.present?

      uri = URI("#{@gateway_url_api}modARPlusServices/wsCorpValidaSocio?vl_id_socio_ffp=#{document_type}:#{document_number}")

      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE

      request = Net::HTTP::Get.new(uri.request_uri)
      request.basic_auth credentials.user, credentials.password

      response = http.request(request)
      response = JSON.parse(response.body)
      response.first["membership_number"] = response.first['lv_nume_doc'].strip
      response
    end

    def frequent_traveler_by_membership_number(membership_number, store_id)
      credentials = credentials_for(store_id)

      return {} if !credentials.present?

      uri = URI("#{@gateway_url_api}modARPlusServices/wsCorpValidaSocio?vl_id_socio_ffp=#{membership_number}")

      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE

      request = Net::HTTP::Get.new(uri.request_uri)
      request.basic_auth credentials.user, credentials.password

      response = http.request(request)
      response = JSON.parse(response.body)
      response.first["membership_number"] = membership_number
      response
    end

    def mile_accreditation(params)
      credentials = credentials_for(params[:store_id])

      return {} if !credentials.present?

      user_info = frequent_traveler_information(params).first

      uri = URI("#{@gateway_url_api}modARPlusServices/wsPartnersAcreditarMillasService")

      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE

      request = Net::HTTP::Post.new(uri.request_uri)
      request.basic_auth credentials.user, credentials.password
      request.set_form_data(mile_accreditation_data(credentials, user_info, params[:miles], params[:order_id]))

      response = http.request(request)
      response = JSON.parse(response.body)
      response.merge!({membership_number: user_info["membership_number"]})
    end

    def mile_accreditation_data(credentials, user_info, miles, order_id)
      transaction_date = Time.now.strftime("%d/%m/%Y")
      {
        "Partner_code" => credentials.partner_code,
        "Partner_id" => credentials.partner_id,
        "Partner_nbr" => credentials.partner_nbr,
        "Socio" => user_info["membership_number"],
        "Nume_fact" => order_id < 10 ? "0#{order_id}" : order_id,
        "Millas" => miles,
        "Millas_Promo" => "0",
        "Lugar" => "BUE",
        "Cod_Promo" => "",
        "Apellido" => user_info["lv_apellido"].strip,
        "Fecha_Desde" => transaction_date,
        "Fecha_Hasta" => transaction_date,
        "Codidocu" => user_info["lv_tipo_doc"].strip,
        "Numedocu" => user_info["lv_nume_doc"].strip,
        "Aux" => "",
        "Int1" => "0 ",
        "Int2" => "0",
        "Ldate" => ""
      }
    end

    def credentials_for(store_id)
      Mkp::Store.find(store_id).aerolineas_argentinas_credential
    end
  end
end

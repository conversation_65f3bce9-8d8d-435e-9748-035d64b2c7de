module OmniauthService
  extend self

  VALID_PROVIDERS = [:google, :facebook, :twitter]

  def call!(provider, auth_token, current_store)
    return invalid_object unless valid_provider?(provider)
    oauth = client(provider).authenticate(auth_token)
    user_info = oauth['user_info']
    return invalid_object unless user_info.try(:[], 'id').present?
    identity = Identity.find_or_create_by(uuid: user_info['id'], provider: provider)
    user = identity.user || build_user(identity, user_info, current_store)

    { success: true, user: user }
  rescue Omniauth::Errors::ResponseError
    invalid_object
  end

  def fetch_token!(provider, code)
    return invalid_object unless valid_provider?(provider)

    client(provider).new.fetch_token!(code)
  rescue Omniauth::Errors::ResponseError
    false
  end

  def valid_provider?(provider)
    VALID_PROVIDERS.include?(provider.to_sym)
  end

  def client(provider)
    "Omniauth::#{provider.camelize}::Client".constantize
  end

  def invalid_object
    { success: false, user: nil }
  end

  def build_user(identity, user_info, current_store)
    identity.user = current_store.customers.find_or_create_by(email: user_info.try(:[], 'email').to_s) do |u|
      u.first_name, u.last_name = user_info.try(:[], 'name').split if user_info.try(:[], 'name').present?
      u.email = user_info.try(:[], 'email') if user_info.try(:[], 'email').present?
      u.password = Devise.friendly_token[0, 7]
      u.store = current_store
    end
    identity.save
    identity.user
  end
end

module Tdd
  class BuildInvoiceWorker
    include Sidekiq::Worker
    sidekiq_options retry: 3

    def perform(ids)
      payments = Payment.where(id: ids) || return
      store = Mkp::Store.find_by_name("tddboca")

      payments.each do |payment|
        begin
          next unless payment.can_invoice?
          invoice = payment.build_invoice
          invoice.generate!
        rescue => exception
          Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, store).deliver_later
        end
      end
    end
  end
end

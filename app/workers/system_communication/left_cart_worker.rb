class SystemCommunication::LeftCartWorker
  include Sidekiq::Worker

  def perform(communication_id)
    communication = SystemCommunication::LeftCart.find(communication_id)

    return unless communication.valid_customer? && communication.valid_cart?

    SystemCommunication::LeftCartMailer.with_items_only(communication).deliver
    communication.update_column(:notified_at, Time.now)
    reset_cart_status(communication.cart_id)
  end

  private

  def reset_cart_status(cart_id)
    cart = Mkp::Cart.find(cart_id)
    cart.reset_status!
  end
end

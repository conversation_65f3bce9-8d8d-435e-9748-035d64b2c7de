class PictureUploadWorker
  include Sidekiq::Worker
  sidekiq_options queue: `hostname`.strip

  def perform(picture_class, picture_id)
    Rails.logger.info("Reprocessing images for a #{picture_class} with id #{picture_id}")
    picture = picture_class.constantize.find(picture_id)

    return PictureUploadWorker.perform_in(5.minutes, picture_class, picture_id) unless File.file?(picture.unpublished_photo(:original, :path))

    if picture.processing
      picture.photo = File.open(picture.unpublished_photo(:original, :path))
      picture.processing = false
      picture.save!
      picture.touch_picture_owner
      picture.refresh_feed
    end
  rescue ActiveRecord::RecordNotFound => e
    # A picture does not exist with that ID, do nothing but log the error
    logger.info("ERROR: We could not find a #{picture_class} with id=#{picture_id}")
  end

  protected

  # def touch_picture_owner(picture)
  #   return if not picture.respond_to?(:owner)
  #   picture.owner.respond_to?(:final_touch) ? picture.owner.final_touch : picture.owner.touch
  # end
  #
  # def refresh_feed(picture)
  #   return if not picture.respond_to?(:owner)
  #   return if not picture.owner.respond_to?(:author)
  #
  #   logger.info("Refreshing feed of user [#{picture.owner.author.id}]")
  #   Social::HomeFeed.of(picture.owner.author).refresh
  # end
end

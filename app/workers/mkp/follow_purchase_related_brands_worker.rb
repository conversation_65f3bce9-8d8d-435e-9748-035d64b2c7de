module Mkp
  class FollowPurchaseRelatedBrandsWorker
    include Sidekiq::Worker

    def perform(order_id)
      order = Mkp::Order.includes(order_includes).find(order_id)

      return if order.customer.is_a? Mkp::Guest

      customer = order.customer

      related_brands_for(order).each do |brand|
        customer.follow(brand) unless customer.follows?(brand)
      end
    end

    private

    def order_includes
      { suborders: { items: { product: :manufacturer } } }
    end

    def related_brands_for(order)
      manufacturers = order.items.map(&:product).map(&:manufacturer).uniq
      manufacturers.map do |manufacturer|
        manufacturer.related_brand(order.network)
      end.compact
    end
  end
end

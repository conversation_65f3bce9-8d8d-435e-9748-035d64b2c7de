module Mkp
  module Shipments
    class ShipnowOrderProcessWorker
      include Sidekiq::Worker

      def perform(shipment_id)
        shipment = Mkp::Shipment.find(shipment_id)
        if should_process?(shipment)
          shipment.gateway = 'Shipnow'

          shipnow_data = shipment.process_with_gateway

          if (errors = shipnow_data['errors']).blank?
            shipment.gateway_data = shipnow_data.deep_symbolize_keys
            shipment.gateway_object_id = shipnow_data['id']
            shipment.status = 'in_process'
            shipment.save!
          else
            raise ShipnowApiError.new, errors
          end
        end
      end

      private

      def should_process?(shipment)
        !shipment.processed_with_gateway? && shipment.fulfilled_by_gp? && shipment.order.store.production
      end

    end
  end
end

module Mkp
  module Shipments
    class ShipnowProductSyncWorker
      include Sidekiq::Worker

      ENDPOINT_URL = "https://api.mercadolibre.com"

      def perform(sn_product_id)
        return unless (sn_product = ShipnowApi.product(sn_product_id)).present?
        return unless (variant = Mkp::Variant.find_by_gp_sku(sn_product['external_reference'])).present?

        variant.update_attributes(quantity: sn_product['stock']['real'])
        variant.update_attributes(reserved_quantity: reserved_quantity(variant, sn_product['stock']['committed']))

        update_meli_stock(variant, sn_product) unless sn_product['extra_data']['meli_publications'].blank?
      end

      private

      # Adding the internal reserved quantity to ShipNow stock
      def reserved_quantity(variant, reserved_stock)
        Mkp::OrderItem.available.where(variant_id: variant.id).reject do |item|
          item.order.is_paid? || item.shipments.pending.all { |s| s.in_process? }
        end.reduce(reserved_stock) { |sum, order| sum + order.quantity }
      end

      def update_meli_stock(variant, sn_product)
        meli_item_id = sn_product['extra_data']['meli_publications'][0]['item_id']
        meli_variant_id = sn_product['extra_data']['meli_publications'][0]['variation_id']
        return unless (external_product = get_meli_item(meli_item_id)).present?
        put_request(meli_item_id, remote_update_params(variant, external_product, meli_variant_id))
      end

      def remote_update_params(variant, external_product, meli_variant_id)
        product = variant.product
        if product.variants.sum(:quantity) > 0
          if external_product.variations.present?
            variants_stock_hash(external_product, variant, meli_variant_id)
          else
            { available_quantity: variant.quantity }
          end
        else
          { status: 'paused' }
        end
      end

      def variants_stock_hash(external_product, variant, meli_variant_id)
        {
          variations: external_product.variations.map do |external_variant|
            {
              id: external_variant.id
            }.tap do |hash|
              hash[:available_quantity] = if external_variant.id == meli_variant_id
                variant.quantity
              else
                external_variant.available_quantity
              end
            end
          end
        }
      end

      def get_meli_item(meli_id)
        action = "/items/#{meli_id}"
        response = RestClient.get("#{ENDPOINT_URL}#{action}", headers)
        OpenStruct.new(JSON.parse(response))
      rescue
        nil
      end

      def put_request(external_id, params)
        sn_meli_store = ::ShipnowApi.store(Gateways::Shipments::Shipnow::MELI_STORE_ID)
        access_token = sn_meli_store['credentials']['access_token']
        action = "/items/#{external_id}?access_token=#{access_token}"
        response = RestClient.put("#{ENDPOINT_URL}#{action}", params.to_json, headers)
      end

      def headers
        { content_type: :json, accept: :json }
      end

    end
  end
end

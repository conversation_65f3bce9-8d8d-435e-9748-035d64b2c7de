module Mkp
  class CopyMatrixWorker
    include Sidekiq::Worker

    def perform(copy_store_id, paste_store_id)
      copy_store = Mkp::Store.find(copy_store_id)
      paste_store = Mkp::Store.find(paste_store_id)
      if paste_store.present? && copy_store.present?
        paste_store.carriers.delete_all
        dup_carriers = copy_store.carriers
        dup_carriers.each do |carrier|
          dup_carrier = carrier.dup
          dup_carrier.id = nil
          dup_carrier.store_id = paste_store.id
          Mkp::CarrierStore.create(dup_carrier.attributes)
        end
      end
    end
  end
end

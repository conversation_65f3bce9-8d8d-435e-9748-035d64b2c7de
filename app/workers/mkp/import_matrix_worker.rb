require 'activerecord-import/base'
ActiveRecord::Import.require_adapter('mysql2')

module Mkp
  class ImportMatrixWorker
    include Sidekiq::Worker
    include Csv::Helpers
    sidekiq_options queue: `hostname`.strip, retry: false

    def perform(store_id, filename)
      return unless File.exist?(filename)

      @store = Mkp::Store.find_by(id: store_id)
      carriers = []
      delimiter = find_delimiter(filename) || DEFAULT_DELIMITER
      CSV.foreach(filename, headers: true, col_sep: delimiter,
                            encoding: 'utf-8', header_converters: :symbol) do |row|
        attributes = row.to_hash
        carriers << Mkp::CarrierStore.new(attributes)
      end
      @store.carriers.import(carriers)
    end
  end
end

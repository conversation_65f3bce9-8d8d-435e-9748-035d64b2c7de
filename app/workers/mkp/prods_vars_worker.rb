module Mkp
  class ProdsVarsWorker
    include Sidekiq::Worker
    sidekiq_options queue: :solr
  
    def reindexes_logger
      @@reindexes_logger ||= Logger.new("#{Rails.root}/log/smart_reindex.log")
    end
  
    def perform(shop_id)
      products = Mkp::Product.where(shop_id: shop_id)
      products_without_variants = products.select { |p| p.variants.empty? }
      products_ids_with_variants_nil = products_without_variants.map(&:id).uniq
      variants = products.flat_map(&:variants)
  
      search_products = Mkp::Product.search { with :shop_id, shop_id }
      search_variants = Mkp::Variant.search { with :shop_id, shop_id }

      if products_ids_with_variants_nil.any?
        product_info_str = products_without_variants.map { |p| "(ID: #{p.id}, Nombre: #{p.title})" }.join(', ')
        shop = Mkp::Shop.find(shop_id)
        message = <<~MSG
          Hay productos activos en la Tienda sin variantes asociadas.

          Productos: #{product_info_str}
          Shop Title: #{shop.title}
          Shop ID: #{shop.id}
        MSG

        RocketChatNotifier.notify(
          message,
          event: 'Productos sin variantes',
          emoji: ':warning:',
          webhook_dest: :products_actives_variants
        )
      end
  
      if products.size != search_products.total
        reindexes_logger.info("Reindexing products from shop_id: #{shop_id}")
        Sunspot.remove(Mkp::Product) { with :shop_id, shop_id }
        Sunspot.index products
        Sunspot.commit
      end
  
      if variants.size != search_variants.total
        reindexes_logger.info("Reindexing variants from shop_id: #{shop_id}")
        Sunspot.remove(Mkp::Variant) { with :shop_id, shop_id }
        Sunspot.index variants
        Sunspot.commit
      end
    end
  end
end

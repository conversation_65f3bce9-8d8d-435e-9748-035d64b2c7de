module Mkp
  class ReportNotifierWorker
    include Sidekiq::Worker

    def perform(review_id)
      if review = Mkp::Review.find(review_id)
        if review.reported_by.present?
          Pioneer::Admin.where(network: review.network).each do |admin|
            ::Mkp::ReportMailer.network_admin_notification(review, admin).deliver
          end
          shop_admins(review).each do |shop_admin|
            ::Mkp::ReportMailer.shop_admin_notification(review, shop_admin).deliver
          end
        end
      end
    end

    private

    def shop_admins(review)
      review.product.shop.admins
    end
  end
end

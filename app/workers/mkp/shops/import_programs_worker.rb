module	Mkp
  module Shops
    class ImportProgramsWorker
      include Sidekiq::Worker
      sidekiq_options queue: `hostname`.strip, retry: false
      def perform(row)
        shop = Mkp::Shop.find(row['seller_id'].to_i)
        shop.shop_stores.destroy_all
        shop_store =  Mkp::ShopStore.find_or_create_by(shop_id: shop.id, store_id: row['store_id'])
        shop_store.update!(start_date: row['start_date'], end_date: row['end_date'], payment_program_id: row['program_id'])
      end
    end 
  end
end

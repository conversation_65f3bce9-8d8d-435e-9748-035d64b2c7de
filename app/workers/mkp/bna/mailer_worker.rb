module Mkp
  module Bna
    class MailerWorker
      class << self
        def perform(order, bna_hash, status) # rubocop:disable Metrics/CyclomaticComplexity
          status_aux = status === 'pending_cancelation' ? 'pending_cancellation' : status
          case status_aux
          when 'booked'
            handle_booked_status(order, status_aux, bna_hash)
          when 'approved'
            handle_approved_status(order, status_aux)
          when 'declined'
            handle_provider_status(order, status_aux)
          when 'posted'
            handle_provider_status(order, status_aux)
          when 'cancelled'
            handle_provider_status(order, status_aux)
          when 'billed'
            handle_store_status(order, status_aux, bna_hash)
          when 'pending_cancellation'
            handle_store_status(order, status_aux, bna_hash)
          when 'pending_cancelation'
            handle_store_status(order, status_aux, bna_hash)
          else
            handle_delivered_status(order, status_aux, bna_hash)
          end
        end

        private

        def handle_booked_status(order, status, id_store)
          Mkp::Bna::StoreMailerWorker.perform_async(order.id, status, id_store)
          Mkp::Bna::CustomerMailerWorker.perform_async(order.id, status)
          Mkp::Bna::ProviderMailerWorker.perform_async(order.id, status)
        end

        def handle_approved_status(order, status)
          Mkp::Bna::ProviderMailerWorker.perform_in(1.minute, order.id, status)
          Mkp::Bna::CustomerMailerWorker.perform_in(1.minute, order.id, status)
        end

        def handle_provider_status(order, status)
          Mkp::Bna::ProviderMailerWorker.perform_in(1.minute, order.id, status)
        end

        def handle_store_status(order, status, id_store)
          Mkp::Bna::StoreMailerWorker.perform_in(3.minutes, order.id, status, id_store)
        end

        def handle_delivered_status(order, status, id_store)
          Mkp::Bna::CustomerMailerWorker.perform_in(1.minute, order.id, status)
          Mkp::Bna::StoreMailerWorker.perform_in(1.minute, order.id, status, id_store)
        end
      end
    end
  end
end

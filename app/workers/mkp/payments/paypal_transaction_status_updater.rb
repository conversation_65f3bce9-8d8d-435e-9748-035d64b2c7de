module Mkp
  module Payments
    class PaypalTransactionStatusUpdater
      include Sidekiq::Worker

      def perform(payment_id)
        payment = ::Mkp::Payment.find(payment_id)

        transaction = Braintree::Transaction.find(payment.gateway_object_id)

        status = Avenida::Payments::Braintree::STATUSES[transaction.status]

        if (status_changed = payment.status != status)
          update_payment_data(payment, transaction, status)
        else
          self.class.perform_in(30.minutes, payment_id)
        end
      end

      private

      def update_payment_data(payment, transaction, status)
        original_gateway_data = HashWithIndifferentAccess.new(payment.gateway_data)
        new_gateway_data = { transaction: transaction.as_json }

        attributes = {
          status: status,
          gateway_data: update_gateway_data(original_gateway_data, new_gateway_data)
        }.tap do |attributes|
          attributes[:collected_at] = Time.now if status == 'collected'
        end

        payment.update_attributes(attributes)

        if payment.collected?
          trigger_paid_order_actions(payment.order)
        else
          self.class.perform_in(30.minutes, payment.id)
        end
      end

      def update_gateway_data(original_gateway_data, gateway_data)
        gateway_data.deep_dup.tap do |data|
          data[:original_payload] = original_gateway_data[:original_payload] || original_gateway_data
          data[:notifications_payloads] = original_gateway_data[:notifications_payloads] || {}
          data[:notifications_payloads][Time.now.to_s] = gateway_data
        end
      end

      def trigger_paid_order_actions(order)
        ::Mkp::PurchaseProcessor.send(:track_shipments, order)

        %w(customer sellers network_admins).each do |receiver|
          ::Mkp::PurchaseProcessor.send(:"notify_paid_to_#{receiver}", order)
        end

        order.solr_index
      end
    end
  end
end

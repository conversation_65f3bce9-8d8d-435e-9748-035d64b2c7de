module Mkp
  class OrderReminderMailerWorker
    include Sidekiq::Worker
    sidekiq_options retry: false

    def perform(suborder_id, timeframe)
      suborder = Mkp::Suborder.find(suborder_id)

      send_reminders_for(suborder, timeframe)
    end

    private

    def send_reminders_for(suborder, timeframe)
      suborder.shop.merchants.each do |merchant|
        Mkp::OrderMailer.shop_reminder_notification(suborder, merchant, timeframe).deliver
      end
    end
  end
end

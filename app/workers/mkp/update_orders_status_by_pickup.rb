require 'activerecord-import/base'
ActiveRecord::Import.require_adapter('mysql2')

module Mkp
  class UpdateOrdersStatusByPickup
    include Sidekiq::Worker
    include Csv::Helpers
    sidekiq_options queue: `hostname`.strip, retry: false

    def perform(params= {})
      file_path = params['file_path'] || ''
      shop_ids = params['shop_ids'] || []

      if !File.exist?(file_path)
        Rails.logger.info "No se encontro el archivo #{file_path}"
        return
      end

      row_errors = []
      Rails.logger.info "Actualizando estado a entregado en: #{file_path}"
      delimiter = find_delimiter(file_path) || DEFAULT_DELIMITER
      CSV.foreach(file_path, headers: true, col_sep: delimiter,
                             encoding: 'utf-8', header_converters: :symbol) do |row|
        attributes = row.to_hash
        public_id_parts = attributes[:public_id].split('-')

        next unless valid_delivered_numbers.include?(attributes[:delivered])
        next unless shop_ids.include?(public_id_parts[2]&.to_i)
        next unless public_id_parts.length == 3

        shop = Mkp::Shop.find(public_id_parts[2])
        suborder = shop&.suborders&.find(public_id_parts[1])
        Mkp::UpdateOrderShipmentStatus.perform_async(suborder.shipment.id, attributes[:delivered]) if suborder.present?
      end
    end

    private

    def valid_delivered_numbers
      %w[1 2]
    end
  end
end
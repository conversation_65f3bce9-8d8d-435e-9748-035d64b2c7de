require "activerecord-import/base"
ActiveRecord::Import.require_adapter('mysql2')

module Mkp
  class ImportProductsPoints
    include Sidekiq::Worker
    sidekiq_options retry: false

    def perform(filename)
      return unless File.exist?(filename)
      product_stores = []
      CSV.foreach(filename, headers: true, encoding:'utf-8', header_converters: :symbol ) do |row|
        attributes = row.to_hash.slice(:product_id, :store_id, :points)

        product_id = Mkp::Variant.find_by_gp_sku(row[:sku].strip).try(:product_id) if row[:sku] != nil
        attributes.merge!({product_id: product_id}) if product_id != nil

        product_stores << Mkp::ProductStore.new(attributes)

      end
      Mkp::ProductStore.import(product_stores)
    end
  end
end

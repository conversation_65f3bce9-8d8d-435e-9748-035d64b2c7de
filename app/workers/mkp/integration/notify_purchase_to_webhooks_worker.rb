module Mkp
  module Integration
    class NotifyPurchaseToWebhooksWorker
      include Sidekiq::Worker

      COUNTER_LIMIT = 25.freeze
      MIME_JSON = "application/json".freeze
      SANDBOX_URL = "http://requestb.in/17z8hok1".freeze

      sidekiq_options retry: false

      def perform(suborder_id, webhook, counter = 1)
        counter = counter + 1
        suborder = Mkp::Suborder.find(suborder_id)

        response = connection(webhook).post do |call|
          call.headers['Content-Type'] = MIME_JSON
          call.body = body(suborder)
        end

        case response.status.to_s
        when /^4[0-9]{2}/
          ExceptionNotifier.notify_exception("Webhook not available", data: {
            suborder_id: suborder.id,
            shop: suborder.shop.title,
            webhook: webhook
          })
          re_enqueue_job(24.hours, suborder_id, webhook, counter)
        when /^5[0-9]{2}/
          re_enqueue_job(5.minutes, suborder_id, webhook, counter)
        end
      end


      private

      def connection(webhook)
        Faraday.new(webhook_url(webhook)) do |faraday|
          faraday.adapter(Faraday.default_adapter)
        end
      end

      def body(suborder)
        {
          topic: "order/create",
          shop_id: suborder.shop.id,
          order_id: suborder.id
        }.to_json
      end

      def re_enqueue_job(run_in, id, webhook, counter)
        return if counter >= COUNTER_LIMIT
        Mkp::Integration::NotifyPurchaseToWebhooksWorker.perform_in(run_in, id, webhook, counter)
      end

      def webhook_url(webhook)
        (Rails.env.development? || ON_STAGING) ? SANDBOX_URL : webhook
      end

    end
  end
end

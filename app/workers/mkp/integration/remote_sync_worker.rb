module Mkp
  module Integration
    class RemoteSyncWorker
      include Sidekiq::Worker

      def perform(product_id, variant_ids)
        product = Mkp::Product.find(product_id)

        product.external_objects.each do |external_object|
          case external_object.integration_name
          when 'mercadolibre', 'gpmercadolibre'
            external_object.integration.remote_sync(product, external_object)
          when 'shopify'
            external_object.integration.remote_sync(variant_ids)
          end
        end
      end

    end
  end
end

require 'activerecord-import/base'
ActiveRecord::Import.require_adapter('mysql2')

module Mkp
  class ImportLabelsSubordersWorker
    include Sidekiq::Worker
    include Csv::Helpers
    sidekiq_options queue: `hostname`.strip, retry: false

    def perform(filename, current_shop_id = nil)
      if !File.exist?(filename)
        Rails.logger.info "No se encontro el archivo #{filename}"
        return
      end

      row_errors = []
      Rails.logger.info "Procesando etiquetas en: #{filename}"
      delimiter = find_delimiter(filename) || DEFAULT_DELIMITER
      CSV.foreach(filename, headers: true, col_sep: delimiter,
                            encoding: 'utf-8', header_converters: :symbol) do |row|
        attributes = row.to_hash
        begin
          new_label = ProcessNewLabel.new(attributes.merge(:current_shop_id => current_shop_id)).call
        rescue => e
          new_label = nil
        end
        if new_label.nil?
          row_errors.push(attributes[:public_id] || attributes[:tracking_number])
        end
      end
      if current_shop_id.present?
        unless row_errors.empty?
          ImportLabelMailer.notify_errors(Mkp::Shop.find(current_shop_id.to_i), row_errors).deliver
        else
          ImportLabelMailer.notify_success(Mkp::Shop.find(current_shop_id.to_i)).deliver
        end
      end
    end
  end
end

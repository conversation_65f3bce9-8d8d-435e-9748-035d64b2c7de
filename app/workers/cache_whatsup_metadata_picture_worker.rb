class CacheWhatsupMetadataPictureWorker
  include Sidekiq::Worker

  def perform(whatsup_id)
    whatsup = Social::Whatsup.find(whatsup_id)

    if whatsup.metadata.blank?
      raise "Cant do the cache of Social::Whatsup with id #{whatsup_id}, it doesnt have metadata"
    end

    if whatsup.metadata[:image].blank?
      raise "Cant do the cache of Social::Whatsup with id #{whatsup_id}, it doesnt have metadata[:image]"
    end

    url = whatsup.metadata[:image]
    name = "metadata-cache/#{whatsup.metadata[:type]}/whatsup-#{whatsup.id}/#{File.basename(url)}"
    bucket = "a#{(Rails.env.production? ? '01' : '00' )}.gpstatic.com"

    if url.include? bucket
      raise "metadata[:image] of Social::Whatsup with id #{whatsup_id} already cached on S3"
    end

    return unless url_exist?(url)

    #
    # I added this if case because the open() method if the file is less than 10kb
    # returns a StringIO and not Tempfile that's why the unlink, path and close!
    # methods does not work.
    # Source: http://stackoverflow.com/questions/6235048/undefined-method-path-for-stringio-in-ruby#answer-23666898
    #
    if defined?(OpenURI) && OpenURI::Buffer.const_defined?('StringMax')
      OpenURI::Buffer.send('remove_const', 'StringMax')
      OpenURI::Buffer.send('const_set', 'StringMax', 0)
    end
    file = open(URI.parse(url))

    begin
      s3 = Aws::S3.new
      s3_pic = s3.buckets[bucket].objects[name]

      if !s3_pic.exists?
        s3_pic.write(file: file.path, acl: :public_read, cache_control: 'max-age=315576000')
      end

      whatsup.metadata[:image] = s3_pic.public_url.to_s
      whatsup.save
    ensure
      file.unlink
      file.close!
    end
  end

  private

  def url_exist?(url_string)
    require 'net/http'
    url = URI.parse(url_string)
    req = Net::HTTP.new(url.host, url.port)
    req.use_ssl = (url.scheme == 'https')
    path = url.path if url.path.present?
    res = req.request_head(path || '/')
    if res.kind_of?(Net::HTTPRedirection)
      url_exist?(res['location']) # Go after any redirect and make sure you can access the redirected URL
    else
      res.code[0] != "4" #false if http code starts with 4 - error on your side.
    end
  rescue Errno::ENOENT
    false #false if can't find the server
  end
end

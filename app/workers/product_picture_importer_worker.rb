require 'net/http'

class ProductPictureImporterWorker
  include Sidekiq::Worker

  def perform(integration_id, external_picture_hash, product_id, view_order)
    set_open_uri_buffer_string_max_to_zero!

    integration = Mkp::Integration::Base.find(integration_id)
    product = Mkp::Product.find(product_id)
    external_picture_hash = external_picture_hash.deep_symbolize_keys
    external_picture = OpenStruct.new(external_picture_hash)
    job_queued_key = integration.job_queued_key('picture', 'import', external_picture.external_id)

    return if picture_has_been_processed?(product, external_picture)

    ActiveRecord::Base.transaction do
      product_picture = upload_picture(external_picture.styles, view_order)

      return unless product_picture.present?

      integration.create_integration_object(product_picture, external_picture)

      if (variants_ids = external_picture.variant_ids).present?
        associated_variants = get_associated_variants(integration_id, variants_ids)

        if associated_variants.present?
          associated_variants.each do |variant|
            variant.update_attributes(picture_id: product_picture.id)
          end
        end
      end

      product.pictures << product_picture
    end
  rescue OpenURI::HTTPError => e
    Rails.logger.info("There was an error (#{e.message}) when trying to upload the remote picture with this data:")
    Rails.logger.info("integration_id: #{integration_id}")
    Rails.logger.info("external_picture_hash: #{external_picture_hash.as_json}")
    Rails.logger.info("product_id: #{product_id}")
    Rails.logger.info("view_order: #{view_order}")
  ensure
    $redis.del(job_queued_key)
  end

  private

  def set_open_uri_buffer_string_max_to_zero!
    if defined?(OpenURI) && OpenURI::Buffer.const_defined?('StringMax')
      OpenURI::Buffer.send('remove_const', 'StringMax')
      OpenURI::Buffer.send('const_set', 'StringMax', 0)
    end
  end

  def picture_has_been_processed?(product, external_picture)
    product.pictures.detect do |picture|
      next unless picture.external_object.present?

      picture.external_object.external_id == external_picture.external_id
    end
  end

  def get_associated_variants(integration_id, external_variant_ids)
    conditions = {
      integration_id: integration_id,
      integrable_type: Mkp::Variant.name,
      external_id: external_variant_ids
    }

    Mkp::Integration::Object.where(conditions).map(&:integrable)
  end

  def upload_picture(external_picture_styles, view_order)
    valid_styles = styles.select do |style|
      picture_url = external_picture_styles[style]
      picture_url_is_processable?(picture_url)
    end

    return unless valid_styles.present?

    picture = Mkp::Attachment::ProductPicture.new(view_order: view_order)

    valid_styles.each do |style|
      external_picture_styles = HashWithIndifferentAccess.new(external_picture_styles)

      picture_url = external_picture_styles[style]

      image_file = open(URI.parse(picture_url))

      full_path = picture_full_path(picture, picture_url, style)

      if style == :original
        picture_attributes = {
          photo_content_type: image_file.content_type,
          photo_file_name: picture.token + File.extname(picture_url),
          photo_file_size: image_file.size,
          processing: false
        }

        picture.attributes = picture_attributes
      end

      store_image(image_file, full_path)
    end

    picture.save!

    picture
  end

  def styles
    styles = Mkp::Attachment::Picture.styles.keys
    styles << :original # 'original size' key
  end

  def picture_url_is_processable?(picture_url)
    picture_url.present? && image_exists?(picture_url)
  end

  def image_exists?(image_url)
    parsed_image_url = URI.parse(image_url)

    request = Net::HTTP.new(parsed_image_url.host, parsed_image_url.port)
    request.use_ssl = (parsed_image_url.scheme == 'https')

    response = request.request_head(parsed_image_url.path.presence || '/')

    if response.kind_of?(Net::HTTPRedirection)
      image_exists?(response['location'])
    else
      response.code.first != '4'
    end
  rescue Errno::ENOENT, OpenURI::HTTPError
    false
  end

  def store_image(image_file, full_path)
    begin
      store = AWS::S3.new
      bucket = default_options[:bucket]
      avenida_bucket = "avenida"
      cache_control = default_options[:cache_control]
      full_path = "#{avenida_bucket}/#{full_path}"

      probably_stored_picture = store.buckets[bucket].objects[full_path]

      probably_stored_picture.write(
        file: image_file.path,
        acl: :public_read,
        cache_control: cache_control
      )
    ensure
      image_file.delete
      image_file.close!
    end
  end

  def picture_full_path(picture, picture_url, style)
    extension = File.extname(picture_url)

    base_name = picture.token
    base_name += "_#{style}"

    attachment = picture.photo.name.to_s.pluralize

    public_folder = public_folder_path(picture)

    "products/#{attachment}/#{public_folder}/#{base_name}#{extension}"
  end

  def default_options
    Paperclip::Attachment.default_options
  end

  def public_folder_path(picture)
    Paperclip::Interpolations.public_folder(picture.photo, '')
  end
end

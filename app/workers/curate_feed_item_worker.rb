class CurateFeedItemWorker
  include Sidekiq::Worker

  def perform(feed_item_id, feed_item_class)
    feed_item = feed_item_class.constantize.where(id: feed_item_id).first
    return if feed_item.blank?
    author = feed_item.author
    ["AR", "US"].each do |network|
      if Social::CuratedUser.where('user_id = ? AND network = ?', author.id, network).any?
        Social::CuratedFeedItem.create!(item_id: feed_item_id,
                                        item_type: feed_item_class,
                                        network: network)
      end
    end
  end
end

class AvenidaProductPictureWorker
  include Sidekiq::Worker

  def perform(product_id, url_image, index)
    styles = {
                st: '100x100#', # small-thumb (cropped)
                t:  '130x130>', # thumbnail (cropped if bigger than specified)
                m:  '300x300#', # medium (cropped)
                ml: '450x450>', # medium-large (cropped if bigger than specified)
                l:  '720x720>', # large (cropped if bigger than specified)
              }

    picture = Mkp::Attachment::ProductPicture.new(product_id: product_id,
                                                  photo: open(url_image),
                                                  view_order: index)

    picture.styles = styles
    picture.processing = false
    picture.save
  end
end


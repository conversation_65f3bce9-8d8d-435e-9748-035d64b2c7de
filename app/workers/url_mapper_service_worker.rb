class UrlMapperServiceWorker
  include Sidekiq::Worker

  ASSOCIATIONS = {
    'Product' => [
      :genders,
      :manufacturer,
      :sports,
      :category
    ],
    'Category' => [
      :genders,
      :sports
    ]
  }.freeze

  def perform(action, class_name, model_id)
    model = get_model(class_name, model_id)

    return if model.blank?

    case action
    when :generate
      UrlMapperService.generate_urls(model)
    when :update
      UrlMapperService.update_urls(model)
    end
  end

  private

  def get_model(class_name, model_id)
    klass = class_name.constantize
    klass = klass.includes ASSOCIATIONS[class_name.demodulize]

    klass.where(id: model_id).take
  end
end

class NotifyUserWorker
  include Sidekiq::Worker

  def perform(event_type, options)
    log_notification_sent(event_type, options)
    notifier = instantiate_notifier(event_type, options)
    notifier.notify!
  end

  private

  def log_notification_sent(event_type, options)
    Rails.logger.info("Sending notification for event[#{event_type}] and options[#{options.inspect}].")
  end

  def instantiate_notifier(event_type, options)
    notifier_class = choose_notifier_class(event_type)
    notifier_class.new(HashWithIndifferentAccess.new(options))
  end

  def choose_notifier_class(event_type)
    "#{event_type.to_s.camelize}Notifier".constantize
  end
end

# Notifies users about a new comment. Those who receive the notifications are
# the author of the post being commented, and users who also commented that
# post.
class CommentedNotifier
  def initialize(options)
    @comment = Social::Comment.find(options[:id])
  end

  def notify!
    notify_author_of_commented_post
    notify_commenters_of_commented_post
  end

  private

  def commented_own_post?
    @comment.user_id == @comment.target.author_id
  end

  def notify_author_of_commented_post
    unless commented_own_post?
      notification = Notification::CommentedPost.send!(@comment)
    end
  end

  def notify_commenters_of_commented_post
    users_to_notify = other_commenters_of(@comment.target)
    users_to_notify.each do |user|
      notification = Notification::CommentedSamePost.send!(@comment, user)
    end
  end

  def author_of_this_post_or_comment?(user)
    user == @comment.target.author || user == @comment.user
  end

  def other_commenters_of(post)
    all_comments = @comment.target.comments
    all_commenters = all_comments.map(&:user).uniq
    all_commenters.select do |commenter|
      !author_of_this_post_or_comment?(commenter)
    end
  end
end

# Notifies a user that a post he created was favorited.
class FavoritedNotifier
  def initialize(options)
    @favorite = Favorite.find(options[:id])
  end

  def favorited_product?
    @favorite.favoritable_type == 'Mkp::Product'
  end

  def favorited_own_post?
    @favorite.user_id == @favorite.favoritable.author_id
  end

  def previously_notified?
    Notification::Favorited.previously_notified?(@favorite)
  end

  def notify!
    if !favorited_product? && !favorited_own_post? && !previously_notified?
      notification = Notification::Favorited.send!(@favorite)
    end
  end
end

# Notifies a user who starts to be followed by another one.
class FollowedNotifier
  def initialize(options)
    @follow = Social::Follow.find(options[:id])
  end

  def notify!
    unless Notification::Followed.previously_notified?(@follow)
      notification = Notification::Followed.send!(@follow)
    end
  end
end

# Notifies a user that a question he made about a product was answered.
class AnsweredNotifier
  def initialize(options)
    @answer = Mkp::Answer.find(options[:id])
  end

  def notify!
    Notification::Answered.send!(@answer)
  end
end

class MentionedNotifier
  def initialize(options)
    @object = options[:klass].constantize.find(options[:id])
  end

  def mentions_author
    case @object
      when Social::Whatsup then @object.author
      when Social::Comment then @object.user
    end
  end

  def mentions_container
    @object.body
  end

  def notify_mentions
    mentioned_users = Social::Mention.scan_mentioned_users(mentions_container)
    mentioned_users.each do |mentioned_user|
      mention = Social::Mention.new(on: @object, from: mentions_author, to: mentioned_user)
      notify_mention(mention)
    end
  end

  def notify_mention(mention)
    unless mention.to_self?
      notification = Notification::Mentioned.send!(mention)
    end
  end

  def notify!
    notify_mentions
  end
end

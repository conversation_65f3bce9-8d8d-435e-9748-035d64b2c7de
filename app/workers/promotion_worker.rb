class PromotionWorker
  include Sidekiq::Worker

  # Date format must be as follows 'DD-MM-YYYY HH:MM:SS'
  def perform(id, date)
    today = DateTime.now.in_time_zone('Pacific Time (US & Canada)')
    end_date = ActiveSupport::TimeZone['Pacific Time (US & Canada)'].parse(date)

    if today < end_date
      if !(today.hour >= 2 && today.hour <= 7)
        brand = User.find(id)
        followers = [brand.id] + brand.followers.pluck(:id)
        user = User.where('id NOT IN (?)', followers)
                   .where('login_count > ? AND last_login_at < ?', 0, Date.today.prev_month(7))
                   .where('network = ? AND type = ?', 'US', 'SocialUser')
                   .sample()

        if user.present?
          user.follow(brand)
          PromotionMailer.entered_contest(brand, user).deliver
        end
      end

      PromotionWorker.perform_in(rand(45..90).minutes, id, date)
    end
  end
end

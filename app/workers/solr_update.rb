class SolrUpdate
  include Sidekiq::Worker
  sidekiq_options queue: :solr

  def perform(classname, id)
    begin
      classname.constantize.unscoped.find(id).solr_index
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.info("================ START - ERROR: #{e.class} ================")
      Rails.logger.info(e.message)
      Rails.logger.info(e.backtrace)
      Rails.logger.info("================ END - ERROR: #{e.class} ================")
    ensure
      Rails.logger.info("Solr Update for: #{classname} and #{id}")
    end
  end
end

class NewsletterSubscriptionWorker
  include Sidekiq::Worker
  sidekiq_options retry: 3

  def perform(recipient_data,list_name, store_name="avenida")
    @store_name = store_name
    recipient_data["email_address"] = recipient_data["email_address"].strip if recipient_data["email_address"].present?
    return unless recipient_data.present? && list_name.present? && validate_email(recipient_data["email_address"])
    recipient_data.symbolize_keys!
    list_id = MAILCHIMP['lists'][@store_name]["#{list_name}_id"]
    Rails.logger.info "Subcribing #{recipient_data[:email_address]} on the #{list_name} list."
    add_recipient_to_list(recipient_data, list_id)
    Rails.logger.info "Everything done."
  end

  private

  def validate_email(email)
    email.match(/\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i).present?
  end

  def add_recipient_to_list(recipient_data, list_id)
    mailchimp_client.add_recipient_to_list(recipient_data, list_id)
  end

  def mailchimp_client
    @client ||= MailChimpClient.new(@store_name)
  end
end

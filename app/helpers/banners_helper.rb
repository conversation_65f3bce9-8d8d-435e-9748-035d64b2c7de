module BannersHelper
  def banner_link(banner, options = {})
    return '' if banner.link.blank?

    url_link = banner.link
    options[:title] = banner.title

    if banner.has_external_link?
      options[:rel] = 'nofollow'
      options[:target] = '_blank'
    else
      url_link = url_link + "#{(url_link =~ /\?/) ? '&' : '?'}track=clicks-#{banner.id}"
    end

    link_to('', url_link, options)
  end
end

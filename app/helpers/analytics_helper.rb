module AnalyticsHelper
  extend self
  include ActionView::Helpers::TagHelper
  include ActionView::Helpers::JavaScriptHelper

  ACTIVE_SERVICES  = ANALYTICS_SETUP['active_services'].deep_symbolize_keys.freeze
  AVAILABLE_EVENTS = ANALYTICS_SETUP['available_events'].freeze

  def analytics?
    INCLUDE_ANALYTICS
  end

  def load_analytics_libraries
    return unless analytics?
    ACTIVE_SERVICES.map do |service_name, service_setup|
      render partial: "partials/v5/javascripts/services/#{service_setup[:partial_name]}"
    end.join('').html_safe
  end

  AVAILABLE_EVENTS.each do |event|
    define_method :"#{event}" do |*arguments|
      return unless analytics?
      script = ACTIVE_SERVICES.map do |service_name, service_setup|
        if name = service_setup[:tracker_name].presence
          tracker = name.constantize
          tracker.send(:"#{event}", *arguments) if tracker.respond_to?(:"#{event}")
        end
      end.compact
      javascript_tag script.join('').html_safe if script.present?
    end
  end

  def track_product_list_clicks(list_name, variants, version = 'v4')
    script = store_product_list(list_name, variants)
    script << "gp.analytics.initListClick('#{list_name}', '#{version}');"
    javascript_tag "#{script.join('')}"
  end

  def track_add_remove_from_cart
    script = ["gp.analytics.initCartWatcher();"]
    javascript_tag "#{script.join('')}"
  end

  def track_checkout_steps(checkout_items)
    script = [store_checkout_cart(checkout_items)]
    script << "gp.analytics.initCheckoutSteps();"
    javascript_tag "#{script.join('')}"
  end

  private

  def store_product_list(list_name, variants)
    [
        "gp.lists = gp.lists || {};",
        add_variants_data_list(list_name, variants)
    ]
  end

  def add_variants_data_list(list_name, variants)
    data = {}
    variants.map.with_index do |variant, index|
      product = variant.product
      data[variant.id] = {
        id: product.id,
        name: product.title,
        variant: variant.name,
        price: product.price,
        brand: product.manufacturer.name,
        category: product.category.try(:full_path_name),
        position: index,
        dimension2: variant.id,
        dimension5: "#{product.on_sale? ? 'On Sale' : 'No'}",
        dimension6: product.shop.title
      }
    end
    "gp.lists['#{list_name}'] = #{data.to_json};"
  end

  def store_checkout_cart(items)
    data = {}
    items.each do |item|
      product = OpenStruct.new(item.product)
      data[item.variant_id] = {
        id: product.id,
        name: product.title,
        brand: product.manufacturer[:name],
        category: product.category[:full_path_name],
        price: item.price,
        variant: item.variant_name,
        sku: item.gp_sku,
        weight: product.weight,
        quantity: item.quantity,
        dimension2: item.variant_id,
        dimension5: "#{item.on_sale? ? 'On Sale' : 'No'}",
        dimension6: product.shop[:title]
      }
    end
    "gp.checkout_items = #{data.to_json};"
  end

end

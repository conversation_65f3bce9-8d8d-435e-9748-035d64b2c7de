module LandingsHelper
  extend self

  def component_partial_name(component)
    component.type.demodulize.underscore
  end

  def render_components(components)
    chunks = []
    components.chunk do |component|
      component.display_in_main_container?
    end.each do |in_main_container, components|
      chunks << if in_main_container
        content_tag :div, class: 'main-container' do
          print_components(components)
        end
      else
        print_components(components)
      end
    end
    chunks.join.html_safe
  rescue ActiveRecord::SubclassNotFound
    render_components(components.where("components.type IN (?)", available_component_types))
  end

  private

  def available_component_types
    ::Pages::Components::Base::AVAILABLE_COMPONENTS.map{ |component| component[:type] }
  end

  def print_components(components)
    components.map do |component|
      render partial: "landings/v5/components/#{component_partial_name(component)}", locals: { component: component }
    end.join.html_safe
  end
end

module Mailers
  module ShipmentsHelper
    def get_carrier_and_service_options(shipment)
      shipment_options = if shipment.gateway_data.present? && shipment.gateway_data[:shipping_option].present?
        option = HashWithIndifferentAccess.new(shipment.gateway_data[:shipping_option])
        [option[:carrier_code].try(:upcase), option[:service_code]]
      else
        zip = shipment.destination_address.zip
        suborder = shipment.suborders.first
        carrier = shipment.order.store.get_carrier(zip, suborder.get_suborder_weight, suborder.shop_id)
        if carrier.present?
          [carrier.gateway, carrier.delivery_message]
        else
          ["OcaEpak", "15 a 20 días"]
        end
      end

      shipment_options
    end
  end
end

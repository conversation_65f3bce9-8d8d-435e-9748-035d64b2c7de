module Social
  module Profiles<PERSON><PERSON><PERSON>
    def show_edit_profile_button?(profile_owner)
      current_user.present? && @user == current_user
    end

    def profile_edit_fields_for(profile, form)
      if profile.instance_of?(Social::Profile::Brand)
        render(partial: 'brand_edit_fields', locals: { form: form })
      elsif profile.instance_of?(Social::Profile::User)
        render(partial: 'user_edit_fields', locals: { form: form })
      end
    end

    def get_most_frequent_manufacturer_in(shop)
      manufacturers_ids = shop.products.map(&:manufacturer_id)
      return manufacturers_ids.first if manufacturers_ids.compact.uniq.length == 1

      manufacturers_ids.compact.uniq.last
      # manufacturers_ids.each_with_object({}) do |ids, counts|
      #   counts[ids] += 1
      # end.invert.sort
    end
  end
end

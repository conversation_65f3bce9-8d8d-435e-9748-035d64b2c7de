module Social
  module Feed<PERSON><PERSON><PERSON>
    def render_feed_item(feed_item, locals = {})
      if feed_item.respond_to?(:type)
        class_name = feed_item.type.to_s
      else
        class_name = feed_item.class.name
      end

      base_path = class_name.pluralize.underscore

      render partial: "#{base_path}/feed_item", locals: { feed_item: feed_item }.merge(locals)
    end

    def show_follow_button?(user_to_follow)
      return true unless current_user.present?
      user_to_follow != current_user
    end

    def show_follow_button_by_id?(player_id, player_type)
      return true unless current_user.present?
      return true if current_user.id != player_id
      return true if current_user.class != player_type
      false
    end

    def current_user_favorited?(post)
      post.favorited_by?(current_user)
    end

    def current_user_following?(player)
      current_user.present? && current_user.follows?(player)
    end

    def current_user_following_by_id?(player_id, player_type)
      return false unless current_user.present?
      current_user.follows_by_id?(player_id, player_type)
    end

    def show_delete_button?(author)
      current_user.present? && current_user == author
    end

    def show_delete_button_by_id?(author_id, author_type)
      return false unless current_user.present?
      return false if current_user.id != author_id
      return false if current_user.class != author_type
      return true
    end

    def feed_item_url(item, options = {})
      if item.is_a?(Social::Whatsup)
        social_whatsup_url(item, options)
      else
        social_album_url(item, options)
      end
    end

    def feed_item_title(item)
      class_name = item.class.name.gsub('Social::', '')

      if class_name == 'Whatsup'
        if item.metadata.present? && item.metadata[:title].present?
          item.metadata[:title].truncate(50)
        elsif item.body.present?
          item.body.truncate(50)
        end
      elsif class_name == 'Album'
        if item.title.present?
          item.title.truncate(50)
        elsif item.description.present?
          item.description.truncate(50)
        end
      else
        nil
      end
    end
  end
end

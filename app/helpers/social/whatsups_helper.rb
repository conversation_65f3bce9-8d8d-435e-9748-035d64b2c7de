module Social
  module Whatsups<PERSON>elper

    def whatsup_post_title(whatsup)
      get_data(whatsup).post_title
    end

    def whatsup_post_description(whatsup)
      get_data(whatsup).post_description
    end

    def whatsup_post_image(whatsup)
      get_data(whatsup).post_image
    end

    private

    def get_data(whatsup)
      @data = {} if @data.nil?
      @data[whatsup] = WhatsupData.new(whatsup) if @data[whatsup].nil?
      @data[whatsup]
    end

    class WhatsupData
      def initialize(whatsup)
        @whatsup = whatsup
      end

      def author
        <AUTHOR> <AUTHOR> @whatsup.author.full_name
      end

      def by
        @by || @by = "#{I18n.t('social.partials.feed_item_author.by')} #{author}"
      end

      def date
        @date || @date = @whatsup.created_at.to_date.to_s(:long)
      end

      def title
        return @title unless @title.nil?
        @title = @whatsup.metadata.presence && @whatsup.metadata[:title] || ''
      end

      def description
        return @description unless @description.nil?
        @description = @whatsup.body ? @whatsup.body.strip.gsub(/\s+/, ' ') : ''
      end

      def post_title
        return @post_title unless @post_title.nil?
        title = (title || description || '').truncate(50)
        title += ' | ' if title.present?
        @post_title = "#{title}Post #{by} - #{date}"
      end

      def sports_names
        return @sports_names unless @sports_names.nil?
        sports = @whatsup.try(:sports).presence
        @sports_names = sports.present? ? sports.map(&:name).join(', ') : ''
      end

      def post_description
        return @post_description unless @post_description.nil?
        desc = (description || title || '').truncate(120)
        desc += ' | ' if desc.present?
        desc = [desc, 'Post', by]
        desc.push('tagged on', sports_names) if sports_names.present?
        desc.push('-', date)
        @post_description = desc.join(' ')
      end

      def post_image
        return @post_image unless @post_image.nil?
        image = ''
        if @whatsup.picture.present?
          image = @whatsup.picture.url(:m)
        elsif @whatsup.metadata.present? && @whatsup.metadata[:image].present?
          image = @whatsup.metadata[:image]
        end
        @post_image = image
      end
    end

  end
end

module Social
  module <PERSON><PERSON><PERSON><PERSON><PERSON>
    def favorites_pretty_count(item)
      count = item.favorites_count
      return if count == 0

      if (count == 1) && (item.users_favoriting.first == current_user.presence)
        _t = 'social.partials.item_body.favorites_pretty_count_me_only'
        return I18n.t(_t, login: current_user.login)
      end

      names = item.users_favoriting.limit(3).to_ary

      if current_user.present? && item.favorited_by?(current_user)
        c_user_index = names.index{ |u| u.id == current_user.id }
        if c_user_index.is_a? Numeric
          names.delete_at c_user_index
        else
          names.pop
        end
        names.unshift current_user
      end

      names.map! do |u|
        if u == current_user
          _t = 'social.partials.item_body.favorites_pretty_count_me'
        else
          _t = 'social.partials.item_body.favorites_pretty_count_name'
        end
        I18n.t(_t, login: u.login, full_name: u.full_name)
      end

      if count > 3
        _t = 'social.partials.item_body.favorites_pretty_count_others'
        names << I18n.t(_t, count: count - 3)
      end

      _t = 'social.partials.item_body.favorites_pretty_count'
      _sep = I18n.t('social.partials.item_body.favorites_pretty_count_last_separator')
      names_length = names.length
      names = names.join(', ').reverse.sub(', '.reverse, _sep.reverse).reverse
      I18n.t(_t, names: names, count: names_length)
    end
  end
end
class ProcessOrderItemStatus
  STATUS_DELIVERED = 'delivered'.freeze
  STATUS_RETURNED = 'returned'.freeze
  SHIPMENT_KIND_REFUND = 'refund'.freeze

  def initialize(item, status)
    @item = item
    @status = status
    @current_item_shipment = @item.current_shipment
  end

  def perform
    return process_item_exchange_status if exchange?

    return process_item_refund_status if refund?

    @status
  end

  private

  def process_item_exchange_status
    delivered_item = @item.shipments.select do |item|
      item.status == STATUS_DELIVERED
    end

    return STATUS_DELIVERED if delivered_item.any?
    @current_item_shipment.status
  end

  def exchange?
    %w[exchange_refund exchange_change].include? @current_item_shipment.shipment_kind
  end

  def process_item_refund_status
    return STATUS_RETURNED if
      @current_item_shipment.status == STATUS_DELIVERED
    STATUS_DELIVERED
  end

  def refund?
    @current_item_shipment.shipment_kind == SHIPMENT_KIND_REFUND
  end
end

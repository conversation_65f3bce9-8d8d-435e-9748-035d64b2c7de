module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def show_content_divider
    case @network
      when 'AR' then not current_page_is_on_lux?
      when 'US' then logged_in? and not current_page_is_on_lux?
    end
  end

  def hide_content_divider
    @network == 'AR' and logged_in?
  end

  def show_marketing_communications
    @network == 'AR' and not current_page_is_on_lux?
  end

  def network_selector_item_url(networks, network)
    if current_page_is_on_shop?
      mkp_root_url(network: network.downcase)
    else
      request.original_url.gsub(/\/(#{networks.join('|')})\//i, "/#{network.downcase}/")
    end
  end

  def network_currency(currencies, network)
    "(#{currencies[network].symbol}#{currencies[network].identifier})"
  end

  def managed_users
    if @managed_users_cache.nil?
      if !logged_in?
        @managed_users_cache = false
      else
        managed_users = User.where(id: current_logged_in_user.managed_users).includes(:profile)
        @managed_users_cache = (managed_users - [current_user])
        @managed_users_cache.compact!
        if @managed_users_cache.present?
          @managed_users_cache.sort! { |x,y| x.login <=> y.login }
        else
          @managed_users_cache = false
        end
      end
    end
    @managed_users_cache || []
  end
end

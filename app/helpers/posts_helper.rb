module <PERSON>s<PERSON><PERSON><PERSON>
  def post_presenter
    PostPresenter
  end

  private

  class PostPresenter
    def initialize(post)
      @subject = post
    end

    def subject
      @subject
    end

    def path
      u = ActiveModel::Naming.param_key @subject.class
      url_helpers.send("#{u}_path".to_sym, @subject)
    end

    def url
      u = ActiveModel::Naming.param_key @subject.class
      url_helpers.send("#{u}_url".to_sym, @subject)
    end

    def created_at
      @subject.created_at.strftime('%Y-%m-%dT%H:%M:%S%z')
    end

    def pictures
      return @pictures unless @pictures.nil?
      @pictures = if @subject.is_a?(Social::Whatsup)
        whatsup_pictures
      elsif @subject.is_a?(Social::Album)
        album_pictures
      end
    end

    def title
      @subject.is_a?(Social::Album) ? @subject.title_html : nil
    end

    def description
      if @subject.is_a?(Social::Whatsup)
        @subject.body_html
      elsif @subject.is_a?(Social::Album)
        @subject.description_html
      end
    end

    def metadata
      @subject.metadata if @subject.respond_to?(:metadata)
    end

    def embedded
      return @embedded unless @embedded.nil?
      if metadata.present? && %w(url youtube vimeo).include?(metadata[:type])
        @embedded = metadata
      end
    end

    def embedded_is_video?
      return @embedded_is_video unless @embedded_is_video.nil?
      @embedded_is_video = metadata.present? &&
                           %w(youtube vimeo).include?(metadata[:type])
    end

    def favorites_path
      @favorites_path ||= if subject.is_a?(Social::Whatsup)
        url_helpers.social_whatsup_favorites_path(subject)
      else
        url_helpers.social_album_favorites_path(subject)
      end
    end

    private

    def url_helpers
      Rails.application.routes.url_helpers
    end

    def method_missing(method, *args, &block)
      @subject.send(method, *args, &block)
    end

    def whatsup_pictures
      picture = if pic = @subject.picture.presence
        pic.url(:m)
      elsif metadata.present? && metadata[:type] == 'instagram'
        metadata[:image]
      end
      picture.blank? ? [] : [picture]
    end

    def album_pictures
      case @subject.pictures.length
        when 0
          []
        when 1
          @subject.pictures[0..0]
        when 2..5
          @subject.pictures[0..3]
        else
          @subject.pictures[0..5]
      end.map { |p| p.url(:m) }
    end
  end

end

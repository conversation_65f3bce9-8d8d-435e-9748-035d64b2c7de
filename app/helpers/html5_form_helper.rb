##
# Helper to Extract validations from models and be used on html5 forms.
#
# This is a bad way to do it, here is a better idea that would be nice to implement:
# http://slainer68.wordpress.com/2010/09/02/rails-3-html-5-and-client-side-forms-validations-using-validator/
#
# Example:
#   = f.text_area :description, maxlength: html5_maxlength_for(Mkp::Question, :description)
##
module Html5FormHelper
  def html5_required(model_class, attribute)
    model_class.validators_on(attribute).find do |v|
      v.is_a?(::ActiveModel::Validations::PresenceValidator)
    end.present?
  end

  def html5_maxlength(model_class, attribute)
    model_class.validators_on(attribute).find do |v|
      v.is_a?(::ActiveModel::Validations::LengthValidator) && v.options[:maximum].present?
    end.options[:maximum]
  end
end
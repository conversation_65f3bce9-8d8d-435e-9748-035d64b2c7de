module UrlMapperHelper
  extend self

  WILDCARD_MODELS = UrlMapper::WILDCARD_MODELS
  CATALOG_MODELS_ORDER = UrlMapper::CATALOG_MODELS_ORDER.keys.freeze
  ALLOWED_GENDERS_PATHS = %w(men women kids hombres mujeres ninos).freeze
  FILTERS_KEYS = %i(sh sz clr pr o query d).freeze

  def mapper_path(models = [], network = nil, options = {})
    models = Array.wrap(models)

    return if (valid_models_classes = WILDCARD_MODELS & models.map { |m| m.class.name }).blank?

    network = network.downcase if network.present?

    if models.length == 1
      return generate_path(models, options, network)
    end

    catalog_models_classes = CATALOG_MODELS_ORDER & valid_models_classes

    if catalog_models_classes.length == valid_models_classes.length
      models = order_models_for_catalog(models)
    else
      models = [models.detect { |model| model.class.name == "Pages::Landing" }]
    end

    generate_path(models, options, network)
  end

  def absolute_mapper_path(models = [], network = nil, options = {})
    "/#{mapper_path(models, network, options)}"
  end

  # Be carefull using this method
  # depends on the precense of the * request * object
  def absolute_mapper_url(models = [], network = nil, options = {})
    "#{protocol_and_host}/#{mapper_path(models, network, options)}"
  end


  def should_be_indexed?(catalog_url)
    uri = URI.parse(catalog_url.encode("ASCII", invalid: :replace, undef: :replace, replace: ''))
    query = uri.query
    guessed_network = uri.path.split('/').second

    return true if query.blank?
    return false if catalog_url.end_with?(base_catalog_path(guessed_network))

    query_keys = Rack::Utils.parse_query(query).keys
    unallowed_keys = query_keys - ['query', 's']

    return true if unallowed_keys.blank?
    false
  end

  private

  def generate_path(models, options, network = nil)
    multiple_models, single_models = models.partition { |model| model.is_a?(Array) }

    parts = [network]

    if single_models.present?
      parts << single_models.map do |model|
        path_for_model(model)
      end.join('-')
    else
      parts << 'products'
    end

    path = parts.compact.join('/')

    if multiple_models.present? || options.present?
      params = multiple_params(multiple_models).merge(options)
      path += "?#{params.to_query}"
    end

    path
  end

  def path_for_model(model)
    case model.class.name
    when "Mkp::Gender"
      path_for_gender(model)
    when "Mkp::Category"
      model.full_path_slug
    when "Mkp::Product", "Lux::Product"
      path_for_product(model)
    else
      model.slug
    end
  end

  def order_models_for_catalog(models)
    CATALOG_MODELS_ORDER.map do |class_name|
      next unless (results = models.select { |model| model.class.name == class_name }).present?
      results.size == 1 ? results.first : results
    end.compact
  end

  def path_for_gender(model)
    ALLOWED_GENDERS_PATHS.include?(model.slug) ? model.slug : model.root.slug
  end

  def path_for_product(model)
    "#{model.shop.slug}-#{model.slug}"
  end

  def multiple_params(multiple_models)
    multiple_models.each_with_object(Hash.new { |h, k| h[k] = [] }) do |instances, hash|
      instances.each do |instance|
        hash[UrlMapper::CATALOG_MODELS_ORDER[instance.class.name]] << instance.slug
      end
    end
  end

  def protocol_and_host
    "#{request.protocol}#{request.host_with_port}"
  rescue
    ''
  end

  def base_catalog_path(network)
    @base_catalog_path ||= Rails.application.routes.url_helpers.mkp_catalog_root_path(network: network)
  end
end

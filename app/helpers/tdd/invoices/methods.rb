module Tdd::Invoices::Methods

  def valid?
    client.session_key.present?
  end

  def parse_state(address)
    address.state = address.state.titleize unless address.state == "CABA" || address.state == "GBA"
    return "CABA" if address.state == "Capital Federal"
    return "Buenos Aires" if address.state == "GBA" || address.state == "Bs As Interior"
    address.state
  end

  def default_params
    { client: client, company: company }
  end

  def build_payment(invoice)
    {
      payment_type_id: "Tarjeta de Credito",
      payment_account_id: "TARJETA VISA A COBRAR",
      bank: "TC VISA",
      amount: invoice.total_charged
    }
  end
end

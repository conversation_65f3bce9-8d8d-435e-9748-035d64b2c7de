module Mkp
  module OrderDetailsHelper
    #--- Address & Customer Stuff ---#
    def address_line(address)
      [address.address, address.address_2].select{|t| t.present?}.map(&:titleize).join(', ').strip
    end
    #--- END | Address & Customer Stuff ---#

    ##### OLD STUFF BELOW #######
    # This probably should go elsewhere, maybe asking the gateway or something like that.
    # But because it's only being used by the view, I place it in here for now
    def payment_details(payment)
      PaymentDetails.new({ name: payment.gateway, data: payment.gateway_data }, payment.order.network)
    end

    class PaymentDetails
      VALID_GATEWAYS = ['Braintree', 'Mercadopago']

      def initialize(gateway, network)
        @gateway = gateway[:name]
        @data = gateway[:data]
        @details = if VALID_GATEWAYS.include?(@gateway)
          self.send("#{network.downcase}_details")
        else
          { title: @gateway }
        end
      end

      def to_s
        return '' unless @details.present?
        output = data[:img].present? ? "<img src=#{data[:img]} class='payment-img'/>" : ""
        output += "<span class='payment-title'>#{data[:title]}</span>"
        output.html_safe
      end

      def data
        @details
      end

      private

      def us_details
        operation = @data[:transaction].deep_symbolize_keys
        if operation[:payment_instrument_type] == 'credit_card'
          details = operation[:credit_card_details]
          {
            title: "#{details.card_type} (#{details.cardholder_name}: **** ... #{details.last_4})",
            img: details.image_url
          }
        elsif operation[:payment_instrument_type] == 'paypal_account'
          details = operation[:paypal_details]
          {
            title: "#{details.payer_first_name} #{details.payer_last_name} (#{details.payer_email})",
            img: details.image_url
          }
        else
          {}
        end
      end

      def ar_details
        operation = @data[:original_payload] || @data
        if operation[:payment_type_id].present?
          operation = operation.deep_symbolize_keys
          if operation[:payment_type_id] == 'credit_card'
            card = operation[:card]
            {
              title: "#{operation[:payment_method_id].titleize} (#{card[:cardholder][:name]}: **** ... #{card[:last_four_digits]})",
              img: "https://www.mercadopago.com/org-img/MP3/API/logos/#{operation[:payment_method_id]}.gif",
              installments: operation[:installments]
            }
          elsif operation[:payment_type_id] == 'ticket'
            {
              title: "#{operation[:payment_method_id].titleize}",
              img: "https://www.mercadopago.com/org-img/MP3/API/logos/#{operation[:payment_method_id]}.gif"
            }
          else
            {}
          end
        else
          operation = @data
          operation[:id] ? { title: "Referencia de MercadoPago: #{operation[:id]}" } : {}
        end
      end
    end

  end
end

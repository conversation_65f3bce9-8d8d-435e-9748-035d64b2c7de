# encoding: utf-8
module Mkp::CheckoutHelper

  CartItemStruct = Struct.new(:variant_id,
                              :quantity,
                              :properties,
                              :sku,
                              :gp_sku,
                              :variant_name,
                              :ean_code,
                              :picture_id,
                              :picture_thumb,
                              :url,
                              :product) do

    def name
      [product[:title], product[:manufacturer][:name]].join(' - ')
    end

    def price
      (product[:sale_price] || product[:regular_price]).to_f
    end

    def on_sale?
      product[:sale_price].present?
    end

    def has_no_property?
      properties.include?(:noproperty)
    end
  end

  def country_select_options(network)
    GeoConstants::Countries.get_list(network).invert.to_a
  end

  def states_select_options(country)
    GeoConstants::States.get_list_for(country).invert.to_a
  end

  def create_custom_options_for_select(options, default)
    if options.blank?
      options = [ [ default, default ] ]
    end
    options_for_select(options, default)
  end

  def full_location(address)
    location = [address.city.titleize]
    location << (address.state.length <= 4 ? address.state.upcase : address.state.titleize)
    location << address.zip.upcase
    location.select{|v| v.present?}.join(', ')
  end

  def full_country(address)
    GeoConstants::Countries.name_for_code(address.country)
  end

  def customer_full_name
    return '' unless @current_customer.present?
    @current_customer.full_name.strip!
  end

  def is_not_user?
    !is_user?
  end

  def is_user?
    return false unless @current_customer.present?
    @current_customer.kind_of?(User)
  end

  # Payments Stuff
  def get_braintree_client_token
    braintree_customer_id = @current_customer.braintree_customer_id if @current_customer.present?
    begin
      Braintree::ClientToken.generate({ customer_id: braintree_customer_id })
    rescue ArgumentError => e
      Rails.logger.info("CHECKOUT TRACE: Braintree client token issue begin -->")
      Rails.logger.info( e.message )
      Rails.logger.info("braintree_customer_id: "+ braintree_customer_id.to_s)
      Rails.logger.info("Current Customer ID: "+ @current_customer.id.to_s) if @current_customer.present?
      Rails.logger.info("<-- CHECKOUT TRACE: Braintree client token issue end" )
      Braintree::ClientToken.generate
    end
  end

  def ar_identification_types_options
    [
      ["DNI", "DNI"],
      ["Cédula", "CI"],
      ["L.C.", "LC"],
      ["L.E.", "LE"],
      ["Otro", "Otro"]
    ]
  end

  def should_display_taxes?
    return true if @network == 'US'
    false
  end

  def should_track_google_analytics_conversion?
    session.delete(:track_conversion)
  end

  def find_hash_custom_properties(available_properties, property)
    # example available["color",{"slug":"helmet_size","name":"talle del casco"}]
    # example properties {"color":{"hex":"#000000","name":"Negro","slug_name":"negro"},"helmet_size":"S"}
    return nil unless available_properties.present?
    hash = {}
    available_properties.each do |key|
      if key.is_a?(Hash) && key[:slug] == property.to_s
        hash = key
      end
    end
    hash
  end

  def get_name_for_property(product, k)
    name = if (product[:available_properties_names].present? && product[:available_properties_names].key?(k)\
      && product[:available_properties_names][k].present?)
      "#{product[:available_properties_names][k]}: "
    elsif (property = get_property(product[:available_properties], k)).present?
      "#{property.capitalize}: "
    else
      t('.properties.' + k.to_s)
    end
  end

  def get_property(available_properties, k)
    available_properties.each do |property|
      if property.is_a?(Hash) &&  property[:slug] == k.to_s
         return property[:name]
      elsif property == k
        return property
      end
    end
    nil
  end

end

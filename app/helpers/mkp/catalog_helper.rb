module Mkp::CatalogHelper

  # New V5 Stuff
  def order_options
    [
      [I18n.t('mkp.catalog.v5.order.popular_desc'), "popular-desc"],
      [I18n.t('mkp.catalog.v5.order.available_on_desc'), "available_on-desc"],
      [I18n.t('mkp.catalog.v5.order.price_desc'), "price-desc"],
      [I18n.t('mkp.catalog.v5.order.price_asc'), "price-asc"]
    ]
  end

  def get_property_name(property)
    if property.is_a?(Hash)
      property[:name]
    else
      t(".#{property}")
    end
  end

  def default_order_option
    params[:o] ? params[:o] : "popular-desc"
  end

  def fullpath_with_order(fullpath, value)
    path, querystring = fullpath.split('?')
    querystring = querystring ? "#{querystring}&o=#{value}" : "o=#{value}"
    "#{path}?#{querystring}"
  end
  # End - New V5 Stuff

  def variants_pagination(type)
    _params = params.dup
    _params[:s] = @seed if @seed.present?
    options = {
      param_name: 'p',
      params: _params,
      renderer: VariantsPagination::LinkRenderer
    }.tap do |_options|
      _options[:class] = "pagination-#{type}"
      case type
      when :simple
        _options[:page_links] = false
        _options[:previous_label] = t("will_paginate.#{type}_previous_label")
        _options[:next_label] = t("will_paginate.#{type}_next_label")
      end
    end

    will_paginate(@variants, options)
  end

  def catalog_main_model
    return @catalog_main_model if @catalog_main_model.present?
    # TODO: Priority: Brands, categories, sports and genders.
    collections = [@shop, @manufacturers, @categories, @sports, @genders].reject(&:blank?)
    main_model = collections.select { |collection| collection.size == 1 }.flatten
    full_network = Network[@network].thin? ? Network.default : @network
    @catalog_main_model = main_model.present? && CatalogModel.new(main_model.first, full_network)
  end

  def header_object
    return @header_object if @header_object

    @header_object = catalog_main_model || DefaultCatalogModel.new(params)
  end

  def catalog_title(show_avenida = true)
    ts = build_title
    gp = show_avenida ? " | Avenida.com" : ""
    if ts.length > 0
      title = ts.join(' ').truncate(70, separator: ' ', omission:'')
      title << gp if title.length <= (70 - gp.length)
    else
      title = "#{I18n.t('mkp.variants.index.title')}#{gp}"
    end
    title
  end

  def meta_description
    description = build_description.join(' ')
    get_meta_description_catalog_ar(description)
  end

  def open_graph_locals(canonical_url, description, title, catalog_main_model)
    {
      og_url: canonical_url,
      og_description: description,
      og_title: title
    }.tap do |locals|
      if catalog_main_model && catalog_main_model.logo(:xl)
        locals[:og_image_url] = catalog_main_model.logo(:xl)
      end
    end
  end

  def catalog_list_name
    unless (name = @manufacturers&.map(&:slug).compact).present?
      if @genders&.map(&:slug).present?
        name = (@genders&.map(&:slug) + @categories&.map(&:full_path_slug)).compact
      else
        name = (@categories&.map(&:full_path_slug)).compact
      end
    end
    name << 'sale' if params[:d].present?
    name << 'search' if params[:query].present?
    name ||= ['product']
    name << 'catalog'
    "#{name.join('-')}"
  end

  def catalog_url_rel(catalog_url)
    should_be_indexed?(catalog_url) ? nil : 'nofollow'
  end

  def get_section_cover(catalog_main_model)
    return unless @network == 'US'

    cover_url = case catalog_main_model.name
    when 'Men' then '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_men_NEW2-compressed.jpg'
    when 'Women' then '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_women_NEW2-compressed.jpg'
    when 'Clothing'
      if request[:slug_1] == 'men'
        '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_men_clothing.jpg'
      elsif request[:slug_1] == 'women'
        '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_women_clothing.jpg'
      end
    when 'Footwear'
      if request[:slug_1] == 'men'
        '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_men_footwear.jpg'
      elsif request[:slug_1] == 'women'
        '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_women_footwear.jpg'
      end
    end

    cover_url
  end

  private

  def descriptionize(string)
    string.gsub(/\s+/, ' ').strip.truncate(250) if string.present?
  end

  def get_meta_description_catalog_ar(description_parameters)
    # extra_text = " Pago en cuotas y envío gratis a partir de $#{Network[@network].free_shipping_from}"
    extra_text = " Pago en cuotas y envío gratis a CABA y GBA"
    desc = "Compra online #{description_parameters}."

    if (desc.length > 156)
      desc.truncate(153)
    elsif desc.length <= 156 - extra_text.length
      desc << extra_text
    elsif desc.length <= 156 - (extra_text.length + 1)
      desc << "#{extra_text}."
    end

    desc
  end

  def build_title
    ts = []
    ts << I18n.t('mkp.partials.subheader.on_sale') if params[:d].present?
    ts << get_gender_title_chunk(@genders) if get_gender_title_chunk(@genders).present?
    ts << @manufacturers.map(&:name) if @manufacturers.present?
    ts << @sports.map(&:name) if @sports.present?
    ts << @categories.map(&:full_path_slug) if @categories.present?
    title_array = ts.flatten.map {|t| t.to_s.gsub('-', ' ').titleize}
    title_array[0] = "#{title_array[0]} - " if ts.length > 1
    title_array.map do |element|
      element.gsub(/\s\s[0-9]/, '')
    end
  end

  def get_gender_title_chunk(incoming_genders)
    return unless incoming_genders.present?
    @gender_title_chunk ||= if incoming_genders.all?(&:is_kid?)
      I18n.t('mkp.catalog.v5.kids')
    elsif incoming_genders.length == 1
      incoming_genders.first.name
    end
  end

  def build_description
    ts = []
    ts << @categories.map(&:full_path_slug) if @categories.present?
    if (@manufacturers.present? && brands = @manufacturers.map(&:name)).present?
      ts << brands
    end
    if (@genders.present?)
      gender_name = @genders.map(&:name).join(', ')
      ts << "#{I18n.t('mkp.catalog.v5.by')} #{gender_name}"
    end
    ts << @sports.map(&:name) if @sports.present?
    description_array = ts.flatten.map {|t| t.to_s.gsub('-', ' ')}
    description_array.map do |element|
      element.gsub(/\s\s[0-9]/, '')
    end
  end

  class CatalogModel
    attr_reader :model, :network

    def initialize(model, network)
      @model = model
      @network = network
      @logos = {}
      @covers = {}
    end

    def name
      @model.name || false
    end

    def is_catalog_main_model?
      true
    end

    def description
      return @description unless @description.nil?
      @description = if @model.respond_to?(:get_description)
        @model.get_description(@network)
      else
        if @model.respond_to?(:description) && @model.description.present?
          @model.description
        else
          @model.brand.manufacturer.get_description(@network) rescue nil
        end
      end || false
    end

    def logo(style = :t)
      return @logos[style] unless @logos[style].nil?

      if @model.class == Mkp::Manufacturer
        @logos[style] = @model.logo(:lt) || false
      end
      @logos[style]
    end

    def cover(style = :cover)
      return @covers[style] unless @covers[style].nil?

      case @model
      when Mkp::Manufacturer
        brand = @model.brands.where(network: @network).all.sample
        @covers[style] = brand.present? ? brand.profile.cover.url(style) : false
      when Mkp::Shop
        if brand = @model.brand
          @covers[style] = brand.present? ? brand.profile.cover.url(style) : false
        end
      when Mkp::Category
        if @model.cover
          @covers[style] = @model.cover.url(style)
        end
      end
      @covers[style]
    end
  end

  class DefaultCatalogModel
    attr_reader :cover, :name, :description, :model

    def initialize(params)
      @name = if params[:d].present?
        I18n.t("mkp.partials.subheader#{'.query' if params[:query]}.on_sale")
      else
        I18n.t("mkp.partials.subheader#{'.query' if params[:query]}.products")
      end

      if params[:network] =~ /us/i && @name == 'Sale'
        @cover = '//s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/CB_sale_NEW3-compressed.jpg'
      end
    end

    def is_catalog_main_model?
      false
    end

    def logo
      nil
    end
  end
end

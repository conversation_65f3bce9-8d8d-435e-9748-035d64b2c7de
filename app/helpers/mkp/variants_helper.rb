module Mkp::VariantsHelper

  # New V5 Stuff
  def product_head_title(product, selected_color = nil)
    [
      product.title,
      (selected_color && selected_color[:name]),
      '|',
      product.manufacturer_name
    ].compact.join(' ')
  end

  def product_sale_detector(product)
    @product_sale_detector ||= Mkp::ProductSaleDetector.new(product)
  end

  def price_metas(product)
    on_sale = product_sale_detector(product).is_on_sale?
    price = number_with_precision(product.price, precision: 2)
    title = I18n.t("mkp.variants.v5.partials.head.#{ on_sale ? 'sale' : 'price'}", locale: locale_to_use(product.network))
    [price, title]
  end

  def keywords_for(product)
    category_path = product.category.path
    manufacturer = product.manufacturer
    keywords = [ product.title, product.slug ]
    keywords.concat(category_path.map(&:name))
    keywords.concat(category_path.map(&:slug))
    keywords.concat(product.sports.map(&:name))
    keywords.concat(product.genders.map(&:name))
    keywords.concat([manufacturer.name, manufacturer.slug]) if manufacturer.present?
    keywords.join(',')
  end

  def product_open_graph(canonical_url, product, selected_color)
    {
      og_type: 'product',
      og_url: canonical_url,
      og_description: get_meta_description,
      og_title: "#{product.title} - #{product.manufacturer_name}"
    }.tap do |_hash|
      if (picture = product.picture(color: selected_color))
        _hash[:og_image_url] = picture.url(:l)
      end
    end
  end

  def product_twitter_card(canonical_url, product, selected_color, price, price_title, currency)
    {
      tw_card: 'product',
      tw_username: Network[@network].twitter_account_username,
      tw_title: "#{product.title} - #{product.manufacturer_name}",
      tw_description: get_meta_description,
      tw_label1: "#{price_title} #{I18n.t('mkp.variants.v5.partials.head.twitter.in')} #{currency.identifier}",
      tw_data1: "#{currency.symbol} #{price}",
      tw_label2: I18n.t('mkp.variants.v5.partials.head.twitter.brand')
    }.tap do |_hash|
      if (picture = product.picture(color: selected_color))
        _hash[:tw_image_url] = picture.url(:l)
      end
      _hash[:tw_data2] = if same_brand?(product)
        product.manufacturer_name
      else
        "#{product.manufacturer_name} - #{t('mkp.variants.v5.partials.head.twitter.sold_by')} #{product.shop.title}"
      end
    end
  end

  def properties_matrix_for(product)
    properties = product.available_properties
    return [] if properties.include?(:noproperty)

    properties_values = properties.each_with_object([]) do |property_name, result|
      values = product.available_values_for(property_name)
      result << (property_name == :color ? values.map{|h| h[:slug_name]} : values)
    end
    properties_matrix = properties_values[0].product(*properties_values[1..-1])
    properties_matrix.to_json.html_safe
  end

  def bootstrap_variant(product, selected_color = nil)
    return '' if product.available_properties.include?(:noproperty)

    if selected_color
      variant = product.variants.with_stock.detect do |variant|
        variant.properties[:color][:name] == selected_color[:name]
      end
    else
      variant = product.variants.with_stock.first
    end

    variant && variant.id
  end
  # End - New V5 Stuff

  def variants_show_link_renderer
    VariantsShowLinkRenderer
  end

  def same_brand?(product)
    return false if product.shop.brand.blank? || product.manufacturer.brands.blank?
    product.manufacturer.brands.include?(product.shop.brand)
  end

  def google_tag_params(product)
    tag_params = {
      ecomm_prodid: product.id,
      ecomm_pagetype: "product",
      ecomm_totalvalue: product.price,
      ecomm_gender: tag_product_gender(product)
    }
    tag_params[:ecomm_currency] = product.currency.identifier if product.currency.present?
    tag_params[:ecomm_network] = product.network if product.network.present?
    tag_params[:ecomm_category] = product.category.name.html_safe if product.category.present?
    tag_params[:ecomm_categoryroot] = product.category.root.name.html_safe if product.category.present? && product.category.root
    tag_params[:ecomm_sport] = product.sports.first.name.html_safe if product.sports.present?
    tag_params[:ecomm_sportroot] = product.sports.first.root.name.html_safe if product.sports.present? && product.sports.first.root
    tag_params.to_json
  end

  def tag_product_gender(product)
    return 'unisex' unless product.genders.where(ancestry: '1').count == 1
    gender = product.genders.where(ancestry: '1').first
    gender = if gender.slug == 'men' || gender.slug == 'hombres'
      'male'
    else
      'female'
    end
  end

  def size_chart_settings(product)
    genders = product.genders.map(&:slug)
    category = get_size_chart_category(product)
    return nil if category.blank? || %w(girls boys).any? { |gender| genders.include?(gender) }
    {
      category: category,
      genders: genders
    }
  end

  def get_meta_description
    return @meta_description if @meta_description

    product_title = params[:product_slug].gsub('-',' ').titleize
    manufacturer = params[:manufacturer_id].gsub('-',' ').titleize
    description_parameters = [ manufacturer, product_title ].join(' ')
    @meta_description = case @network
      when 'AR' then get_meta_description_product_ar(description_parameters)
      when 'US' then get_meta_description_product_us(description_parameters)
    end
  end

  def get_meta_description_product_ar(description_parameters)
    append1 = " Ofertas, Pago en Cuotas"
    # append2 = " y Envío Gratis a partir $#{Network[@network].free_shipping_from} ."
    append2 = " y Envío Gratis a CABA y GBA"
    desc = "#{description_parameters}."
    if(desc.length > 156)
      desc.truncate(153)
    elsif desc.length <= 156 - (append1.length + append2.length)
      desc << (append1 << append2)
    elsif desc.length <= 156 - (append1.length + 1)
      desc << "#{append1}."
    end
      desc
  end

  def get_meta_description_product_us(description_parameters)
    append = " Free Shipping on all domestic orders."
    desc = "Shop #{description_parameters}."
    if(desc.length > 156)
      desc.truncate(153)
    elsif desc.length <= 156 - append.length
      desc << append
    end
    desc
  end

  def get_size_chart_category(product)
    categories = product.category_group_title
    if (categories & %w(Clothing Indumentaria)).any?
      "clothing"
    elsif (categories & %w(Footwear Calzado)).any?
      "shoes"
    end
  end

  def free_shipping?(product, free_shipping_from)
    product.price.to_f >= free_shipping_from
  end

  def parse_hash_value(value)
    return t('mkp.variants.v5.partials.does') if value == "1"
    return t('mkp.variants.v5.partials.doesnt') if value == "0"
    value
  end

  class VariantsShowLinkRenderer < ::WillPaginate::ActionView::LinkRenderer
    protected

    def page_number(page)
      if page == current_page
        "<span class=\"page current\" data-page=\"#{page}\">#{page}</span>"
      else
        "<span class=\"page\" data-page=\"#{page}\">#{page}</span>"
      end
    end

    def previous_page
      nil
    end

    def next_page
      nil
    end
  end
end

# /////////////////////////////////////////////
# Google Analytics Custom Dimensions & Metrics:
# == DIMENSIONS ==
# 1 = Network (Hit Scope)
# 2 = Variant ID (Product Scope)
# 3 = Customer/Buyer [Guest | User]-[ID] (Hit Scope)
# 5 = In Sale? (Product Scope)
# 6 = Shop Name (Product Scope)
# 7 = Order Name (Hit Scope)
# 11 = Payment Method (Hit Scope)
#
# == METRICS ==
# 1 = Coupons Discount (Hit Scope - Currency(Decimal) Formatting)
# 2 = Comission (Product Scope - Currency(Decimal) Formatting)
# 3 = Total Paid (Hit Scope - Currency(Decimal) Formatting)
# 4 = Order Comission (Hit Scope - Currency(Decimal) Formatting)
# 5 = Sale Discount (Product Scope - Currency(Decimal) Formatting)
#
# /////////////////////////////////////////////

module Analytics

  module GoogleAnalyticsTracker
    extend self

    def track_pageview(*arguments)
      "ga('send', 'pageview');"
    end

    def track_product_list(*arguments)
      return unless ( list_name = arguments[0] ).is_a?(String) && ( variants = arguments[1] ).is_a?(Array)
      [
        set_currency(variants.first.product.currency_code),
        add_products_impressions(list_name, variants)
      ].join('')
    end

    def track_product_detail(*arguments)
      return unless ( product = valid_arguments(arguments, ['Mkp::Product']).first ).present?
      [
        set_currency(product.currency_code),
        add_product(product),
        set_action('detail')
      ].join('')
    end

    def track_entering_checkout(*arguments)
      return unless ( checkout_items = arguments[0] ).is_a?(Array)
      currency_code = checkout_items.first.product[:currency_code].upcase
      [
        set_currency(currency_code),
        add_products(checkout_items),
        set_action('checkout', { step: 1 })
      ].join('')
    end

    def track_conversion(*arguments)
      return unless ( order = valid_arguments(arguments, ['Mkp::Order']).first ).present?
      [
        set_currency(order.currency_code),
        set_hit_level_dimensions(order),
        set_hit_level_metrics(order),
        add_products(order.items),
        set_purchase_action(order)
      ].join('')
    end

    private

    def valid_arguments(arguments, class_names)
      arguments.select{ |a| class_names.include?(a.class.name) }
    end

    def set_currency(currency_code)
      "ga('set', '&cu', '#{currency_code}');"
    end

    def add_products_impressions(list_name, variants)
      index = 0
      variants.in_groups_of(30, false).flat_map do |chunks_of_variants|
        chunks_of_variants.map do |variant|
          product = variant.product
          impression = {
            id: product.id,
            name: sanitize_string(product.title),
            variant: variant.name,
            price: product.price,
            brand: sanitize_string(product.manufacturer.name),
            category: product.category.try(:full_path_name),
            position: index,
            list: list_name
          }
          index += 1
          "ga('ec:addImpression', #{impression.to_json});"
        end.concat(["ga('send', 'event', 'Product List', 'impression', {'nonInteraction': true});"])
      end
    end

    def add_product(product)
      data = {
        id:         product.id,
        name:       sanitize_string(product.title),
        category:   product.category.try(:full_path_name),
        brand:      sanitize_string(product.manufacturer.name),
        price:      product.price,
        dimension5: "#{product.on_sale? ? 'On Sale' : 'No'}",
        dimension6: sanitize_string(product.shop[:title])
      }
      "ga('ec:addProduct', #{data.to_json});"
    end

    def set_action(name, data = nil)
      if data.present?
        "ga('ec:setAction', '#{name}', #{data.to_json});"
      else
        "ga('ec:setAction', '#{name}');"
      end
    end

    def set_hit_level_dimensions(order)
      {
        dimension1:  order.network,
        dimension3:  customer_dimension(order),
        dimension7:  sanitize_string(order.title),
        dimension11: payment_method(order),
      }.inject('') do |code, (k, v)|
        "#{code}ga('set', '#{k}', '#{v}');"
      end
    end

    def set_hit_level_metrics(order)
      {
        metric3:     order.total,
        metric4:     order_commission_metric(order)
      }.tap do |data|
        data[:metric1] = coupon_discount_metric(order) if order.coupon_code.present?
      end.inject('') do |code, (k, v)|
        "#{code}ga('set', '#{k}', #{v.to_f});"
      end
    end

    def add_products(items)
      items.map.with_index do |item, index|
        product = if item.is_a?(Mkp::OrderItem)
          data_from_order_item(item, index)
        else
          data_from_simple_item(item, index)
        end
        "ga('ec:addProduct', #{product.to_json});"
      end
    end

    def set_purchase_action(order)
      action_fields = {
        id:          order.purchase_id,
        affiliation: 'Avenida Site',
        revenue:     (order.subtotal.to_f - order.coupons_discount.to_f),
        tax:         order.taxes.to_f,
        shipping:    order.shipments_cost.to_f
      }.tap do |data|
        data[:coupon] = order.coupon.code if order.has_coupon?
      end
      set_action('purchase', action_fields)
    end

    def customer_dimension(order)
      "#{order.customer_type == 'User' ? 'User' : 'Guest'}-#{order.customer_id}"
    end

    def payment_method(order)
      return "Avenida Coupon" unless order.payment.present?
      order.payment.payment_method
    end

    def coupon_discount_metric(order)
      order.coupons_discount
    end

    def order_commission_metric(order)
      order.items.map { |item| item.quantity * item.commission }.sum
    end

    def data_from_order_item(item, index)
      {
        id:         item.product.id,
        name:       sanitize_string(item.title),
        category:   item.product.category.name,
        brand:      sanitize_string(item.product.manufacturer.name),
        price:      item.unit_price_charged,
        variant:    item.variant.name,
        quantity:   item.quantity,
        position:   (index + 1),
        dimension2: item.variant_id,
        dimension5: "#{item.on_sale? ? 'On Sale' : 'No'}",
        dimension6: sanitize_string(item.product.shop.title),
        metric2:    item.commission,
        metric5:    item.sale_discount
      }
    end

    def data_from_simple_item(item, index)
      product = OpenStruct.new(item.product)
      {
        id:         product.id,
        name:       sanitize_string(product.title),
        category:   product.category[:full_path_name],
        brand:      sanitize_string(product.manufacturer[:name]),
        price:      item.price,
        variant:    item.variant_name,
        quantity:   item.quantity,
        position:   (index + 1),
        dimension2: item.variant_id,
        dimension5: "#{item.on_sale? ? 'On Sale' : 'No'}",
        dimension6: sanitize_string(product.shop[:title])
      }
    end

    def sanitize_string(name)
      name.gsub("'", "")
    end

  end
end

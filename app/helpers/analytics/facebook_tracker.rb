# Following the documentation written in https://developers.facebook.com/docs/facebook-pixel/api-reference
# we know that Facebook Pixel could track 9 Standard events, and any Custom Event that we may want.
#
# Events can be reported using fbq('track', 'eventName', {customData}); where customData is optional.
# The first value, track, should not be altered.
#
# The the 9 standard events are:
#   1 - ViewContent           : When a key page is viewed such as a product page, e.g. landing on a product detail page
#   2 - Search                : When a search is made, e.g. when a product search query is made
#   3 - AddToCart             : When a product is added to the shopping cart, e.g. click on add to cart button
#   4 - AddToWishlist         : When a product is added to a wishlist, e.g. click on add to wishlist button
#   5 - InitiateCheckout      : When a person enters the checkout flow prior to completing the checkout flow, e.g. click on checkout button
#   6 - AddPaymentInfo        : When a payment information is added in the checkout flow, e.g. click / LP on save billing info button
#   7 - Purchase              : When a purchase is made or checkout flow is completed, e.g. landing on thank you/confirmation page
#   8 - Lead                  : When a sign up is completed, e.g. click on pricing, signup for trial
#   9 - CompleteRegistration  : When a registration form is completed, e.g. complete subscription/signup for a service
#
# And in each event the optional {customData} are:
#   1 - ViewContent           : {value, currency, content_category, content_name, content_type, content_ids}
#   2 - Search                : {value, currency, content_category, content_ids, search_string}
#   3 - AddToCart             : {value, currency, content_name, content_type, content_ids}
#   4 - AddToWishlist         : {value, currency, content_name, content_category}
#   5 - InitiateCheckout      : {value, currency, content_name, content_category, content_ids, num_items}
#   6 - AddPaymentInfo        : {value, currency, content_category, content_ids}
#   7 - Purchase              : {value, currency, content_name, content_type, content_ids, num_items, order_id}
#   8 - Lead                  : {value, currency, content_name, content_category}
#   9 - CompleteRegistration  : {value, currency, content_name, status}
#
# And this parameters means:
#   value:             value of a user performing this event to the business
#   currency:          currency for the value specified.
#   content_name:      Name of the page/product
#   content_category:  Category of the page/product
#   content_ids:       Product ids associated with the event. e.g. SKUs of products for AddToCart event: ['ABC123', 'XYZ789']
#   content_type:      Either 'product' or 'product_group' based on the content_ids being passed. If the ids being passed in content_ids parameter are ids of products then the value should be 'product'. If product group ids are being passed, then the value should be 'product_group'.
#   num_items:         Used with InitiateCheckout event. The number of items that checkout was initiated for.
#   search_string:     Used with the Search event. The string entered by the user for the search
#   order_id:          Used with Purchase event. The unique order id of the successful purchase
#   status:            Used with the CompleteRegistration event, to show the status of the registration.

module Analytics

  module FacebookTracker
    extend self

    AVAILABLE_FACEBOOK_EVENTS = [
      'ViewContent',
      'Search',
      'AddToCart',
      'AddToWishlist',
      'InitiateCheckout',
      'AddPaymentInfo',
      'Purchase',
      'Lead',
      'CompleteRegistration'
    ]

    def track_pageview(*arguments)
      "fbq('track', 'PageView');"
    end

    def track_product_detail(*arguments)
      return unless ( valid_models = valid_arguments(arguments, ['Mkp::Product']) ).present?
      track_product_view(valid_models.first) if valid_models.length == 1
    end

    def track_conversion(*arguments)
      return unless ( valid_models = valid_arguments(arguments, ['Mkp::Order']) ).present?
      track_purchase(valid_models.first) if valid_models.length == 1
    end

    private

    def valid_arguments(arguments, class_names)
      arguments.select{ |a| class_names.include?(a.class.name) }
    end

    def track_product_view(product)
      custom_data = {
        value:            product.price.to_f,
        currency:         product.currency.identifier,
        content_category: "#{product.category.path.map(&:name).join(' > ') if product.category.present?}",
        content_name:     "#{product.title} - #{product.manufacturer.name}",
        content_type:     'product_group',
        content_ids:      [product.id.to_s]
      }
      track_code('ViewContent', custom_data)
    end

    def track_purchase(order)
      custom_data = {
        value:        (order.net_total - order.coupon_discount.to_f).to_f,
        currency:     order.currency_code,
        content_name: order.title,
        content_type: 'product',
        content_ids:  order.items.map{|order_item| order_item.variant_id.to_s },
        num_items:    order.total_products,
        order_id:     order.purchase_id
      }.tap do |data|
        if order.total_products == 1
          product = order.items.last.product
          data[:content_category] = product.category.path.map(&:name).join(' > ')
        end
      end
      track_code('Purchase', custom_data)
    end

    def track_code(event_name, custom_data)
      return unless AVAILABLE_FACEBOOK_EVENTS.include?(event_name)
      "fbq('track', '#{event_name}', #{custom_data.to_json});"
    end

  end

end

module ApplicationHelper
  require 'digest/md5'

  # Displays an icon for each network with an auth link
  def connect_networks
    if current_user
      output = ''
      (['facebook', 'twitter', 'google_oauth2'] - current_user.authentications.collect {|auth| auth.provider}).each do |provider|
        output += link_to(image_tag("providers/32px/#{provider}.png", :title => provider), "/auth/#{provider}")
      end
      output
    end
  end

  # Display connected network badges
  def connected_networks
    if current_user
      output = ''
      current_user.authentications.each do |auth|
        title = "#{auth.provider} - #{auth.uid}"
        output += link_to(image_tag("providers/24px/#{auth.provider}.png", :title => title), auth, :confirm => "Are you sure you want to remove #{auth.provider} authentication option?", :method => :delete)
      end
      output
    end
  end

  def random_products
    return_set = Set.new
    products = Product.find(:all)
    unless products.empty?
      if products.size <= 4
        return_set = products
      else
        while (return_set.size < 4) do
          return_set << products[rand(products.size)]
        end
      end
    end
    return_set
  end

  def random_users
    return_set = Set.new
    users = User.find(:all)
    unless users.empty?
      if users.size <= 4
        return_set = users
      else
        while (return_set.size < 4) do
          return_set << users[rand(users.size)]
        end
      end
    end
    return_set
  end

  def like_link(content)
    if content.user_likes.include?(current_user)
      link_to 'UnLike', like_path(:content_type => content.class.to_s, :content_id => content.id),  :method => :delete
    else
      link_to 'Like', like_path(:content_type => content.class.to_s, :content_id => content.id),  :method => :post
    end
  end

  def report_contents_link (content)
    unless content.user_report_contents.include?(current_user)
      link_to 'Report Content', report_contents_path(:content_type => content.class.to_s, :content_id => content.id),  :method => :post
    end
  end

  def locale_to_use(network)
    Network[network].locale || I18n.default_locale
  end

  def url_for(options = nil)
    # In order to write the https protocol on specify routes
    # Discussed in: http://stackoverflow.com/questions/3993651/rails-3-ssl-routing-redirects-from-https-to-http
    if options.is_a?(Hash)
      options[:protocol] ||= 'http'
    end

    if options.is_a?(Hash) && options.key?(:controller) && @network.present?
      if options[:network].nil? && options[:controller].match(/^mkp\//).present?
        options[:network] = @network.downcase
      end
    end

    super options
  end

  def current_page_is_on_shop?
    !(params[:controller] =~ /^mkp\//).nil?
  end

  def current_page_is_on_lux?
    request.fullpath.to_s.scan("manage").present?
  end

  def merge_params_on_url_string(uri, params)
    uri = URI uri

    query = Rack::Utils.parse_query uri.query
    query.merge! params
    query = Rack::Utils.build_query query

    uri.query = query

    uri.to_s
  end

  def alt_to_product_images(product)
    alt = "#{product.category.try(:name)} - #{product.manufacturer.try(:name)} #{product.title}"
    if alt.length > 70
      alt = "#{product.manufacturer.try(:name)} #{product.title}"
    end
    alt
  end

  def content_for_once(location, widget_id, &block)
    @included_widgets ||= []
    unless @included_widgets.include?(widget_id)
      @included_widgets << widget_id
      content_for(location, &block)
    end
  end

  def svg_fallback
    @svg_fallback ||= 'this.onerror=null;this.src=this.src.replace(/.svg$/, ".png")'
  end
end

class AuthenticationsController < ApplicationController
  skip_before_filter :store_return_to_location, only: [:authorized, :check]
  after_filter :enable_followers_module, only: [:authorized]

  def create
    auth_class = get_authentication_class
    authentication = auth_class.of(current_user)
    if authentication
      authentication.update_attributes(params[:authentication])
    else
      unless auth_class.is_uid_taken?(params[:authentication][:uid])
        auth_class.create(params[:authentication]) { |a| a.user = current_user }
      else
        flash[:error] = t('controllers.authentications.auth_taken')
      end
    end

    respond_to do |format|
      format.html { redirect_to :back }
      format.json { render json: true }
    end
  end

  def destroy
    Authentication.of_provider(params[:provider]).deauthorize(current_user)
    redirect_to :back
  end

  def authorized
    env_auth = request.env['omniauth.auth']

    callback_url = provider_callback_url(env_auth)
    if callback_url.present?
      session['omniauth.auth'] = env_auth
      callback_url = [callback_url, request.query_string].join('?') unless request.query_string.empty?
      redirect_to callback_url and return
    end

    if twitter_provider_and_popup?
      if logged_in?
        authentication = Authentication.new_from_auth_hash(auth_hash)
      else
        authentication = Authentication.find_or_create_by_auth_hash(auth_hash)
        if authentication.has_user?
          authentication.login_user
        else
          user = create_account(twitter_identifier)
          authentication.user = user
          authentication.save
          authentication.login_user
        end
      end
      close_twitter_popup
    else
      if logged_in?
        notify_authorization
      else
        login_or_signup_with_authentication
      end
    end
  end

  def check
    render json: { logged_in: logged_in?, return_url: session[:return_to] }, status: :ok
  end

  def failure
    redirect_to :root
  end

  private

  def facebook_identifier
    request.env['omniauth.auth']['info']['email']
  end

  def twitter_identifier
    request.env['omniauth.auth']['info']['nickname']
  end

  def create_account(identifier)
    UserCreator.perform(identifier,
                        request.env['omniauth.auth']['provider'],
                        {first_name: first_name(identifier),
                         last_name: last_name,
                         network: @network})
  end

  def first_name(identifier)
    fn = request.env['omniauth.auth']['info']['name'].split[0]
    fn.to_s.present? ? fn.to_s : identifier
  end

  def last_name
    ln = request.env['omniauth.auth']['info']['name'].split[1]
    ln.to_s.present? ? ln.to_s : "  "
  end

  def provider_callback_url(env_auth)
    return nil if session[env_auth[:provider]].blank?
    session[env_auth[:provider]][:callback_url]
  end

  def twitter_provider_and_popup?
    return false if request.env['omniauth.auth']['provider'] != 'twitter'
    return false if not cookies[:twitter_oauth_popup]
    true
  end

  def close_twitter_popup
    cookies[:twitter_oauth_popup] = nil
    # this session variable will be used later on, when we implement the check method
    session[:twitter_omniauth_success] = true
    return render 'twitter_popup_close', layout: false
  end

  def notify_authorization
    @authentication = Authentication.new_from_auth_hash(auth_hash)
    return render 'authorized', layout: false
  end

  def login_or_signup_with_authentication
    authentication = Authentication.find_or_create_by_auth_hash(auth_hash)
    if authentication.has_user?
      authentication.login_user
      redirect_to_previous_location
    elsif authentication.has_user_with_google_auth?(auth_hash) # This is for back compatibility with users migrated from old site
      authentication.user_id = User.find_by_email(auth_hash.info.email).id
      authentication.save
      authentication.login_user
      redirect_to_previous_location
    else
      if cookies[:facebook_oauth_popup]
        cookies[:facebook_oauth_popup] = nil
        user = create_account(facebook_identifier)
        authentication.user = user
        authentication.save
        authentication.login_user
        redirect_to_previous_location
      else
        redirect_to sign_up_path(user_info_from_oauth(authentication))
      end
    end
  end

  def auth_hash
    env_auth = request.env['omniauth.auth']
    @auth_hash ||= Authentication.normalize_auth_hash!(env_auth)
  end

  def get_authentication_class
    case params[:authentication][:type]
    when "Authentication::Facebook" then Authentication::Facebook
    when "Authentication::Google"   then Authentication::Google
    when "Authentication::Twitter"  then Authentication::Twitter
    end
  end

  def user_info_from_oauth(auth)
    auth_id = auth.id
    email = auth_hash.info.email
    profile = { first_name: auth_hash.info.first_name,
                last_name: auth_hash.info.last_name }
    avatar_url = auth.avatar_url

    { auth_id: auth_id, email: email, profile: profile, avatar_url: avatar_url }
  end
end

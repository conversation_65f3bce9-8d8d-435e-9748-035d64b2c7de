module Mkp
  class ReportsController < ApplicationController
    skip_before_filter :new_whatsup, :store_return_to_location,:bootstrap_cart

    def create
      review = Mkp::Review.find(params[:review_id])
      if review.report(current_user.id)
        notify_report_to_admins(review)

        render json: { message: 'ok' }, status: :ok
      else
        render json: { errors: review.errors.full_messages.to_json },
               status: :unprocessable_entity
      end
    end

    def destroy
      review = Mkp::Review.find(params[:review_id])
      if review.revert_report(current_user.id)
        render json: { message: 'ok' }, status: :ok
      else
        render json: { errors: review.errors.full_messages.to_json },
               status: :unprocessable_entity
      end
    end

    protected

    def notify_report_to_admins(review)
      return if review.report_notified

      Mkp::ReportNotifierWorker.perform_in(10.minutes, review.id)
      review.report_notifications_sent!
    end
  end
end

module Mkp
  class VariantsController < ApplicationController
    include CatalogLegacy
    before_filter :set_reviewed_session
    ID_REGEX = /^[0-9]+$/

    def show
      @store = Mkp::Store.find_by_id params[:store_id]
      @current_store  = !(@store.nil?)? @store : Mkp::Store.first
      product_slug = params[:product_slug]
      preview = params[:preview] == 'true'
      @product = find_product(product_slug, preview)

      if @product.blank?
        @product, color_slug = get_product_and_color(product_slug, preview)

        if @product.blank?
          fake_params = slugged_fake_params
          if Mkp::Catalog::UrlHandler.parse_slugs(fake_params)
            handle_redirect!(custom_params: fake_params) and return
          else
            not_found
          end
        end

        not_found unless (@selected_color = get_color(color_slug)) || @product.present?
      end

      if @selected_color.blank?
        if @product.variants.any? && @product.variants.first.properties[:color].present? && @product.variants.first.properties[:color].is_a?(Hash)
          @selected_color = @product.available_values_for(:color).first
        end
      end

      # dont allow reduntant products urls
      url_options = {}
      url_options[:color] = @selected_color[:name] if @selected_color.present? && @selected_color.is_a?(Hash)
      product_url = @product.get_url(url_options.merge(network: params[:network]))
      if (request.path != product_url) && request.path
        redirect_to product_url + "?store_id=#{params[:store_id]}", status: :moved_permanently
        return
      end

      @favorites_count = @product.users_favoriting.count
      @breadcrumb = Mkp::Breadcrumb.new(@product).adapt
      @last_questions = last_replied_questions
      @last_reviews = last_reviews
      @product_json = Rabl.render(@product, 'mkp/products/show', format: 'hash')

      if (@unavailable = @product.total_stock_cached == 0 || @product.unavailable?)
        @other_options = @product.recommended_products(4, true)
        @recommended_products = @product.recommended_products(12)
      else
        @recommended_products = @product.recommended_products(4)
      end

      render_properly :show
    end

    private

    # We have to use .find for friendly_id gem usage.
    # Slug history (Doesnt work with find_by_slug)
    def find_product(id, preview = false)
      if preview
        Mkp::Product.by_network(@network).find(id)
      elsif Network[@network].thin?
        Mkp::Product.active.find(id)
      else
        Mkp::Product.active.by_network(@network).find(id)
      end
    rescue ActiveRecord::RecordNotFound
      # We're dealing with an invisible product here.
      Mkp::Product.by_network(@network).find_by_slug(id)
    end

    def get_product_and_color(full_product_slug, preview = false)
      slug_parts = full_product_slug.split('-')

      (slug_parts.length - 1).times.each_with_object({product: nil, color_slug: []}) do |_, result|
        result[:color_slug].unshift(slug_parts.pop)
        product = find_product(slug_parts.join('-'), preview)

        if product.present?
          result[:product] = product
          result[:color_slug] = result[:color_slug].join('-')
          return result.values
        end
      end.values
    end

    def slugged_fake_params
      {
        network: params[:network],
        slug_1: params[:manufacturer_id],
        slug_2: params[:product_slug]
      }
    end

    def get_color(color_slug)
      # Ability to point the color using the variant id
      if ID_REGEX =~ color_slug
        variant = @product.variants.find_by_id(color_slug)
        if variant.present? && variant.properties[:color] && variant.properties[:color].is_a?(Hash)
          return variant.properties[:color]
        end
      end

      @product.available_values_for(:color).detect do |color|
        color[:slug_name] == color_slug if color.is_a?(Hash)
      end
    end

    def last_replied_questions(how_many = 20)
      @product.questions.joins(:answer).limit(how_many)
    end

    def last_reviews(how_many = 20)
      @product.reviews.curated.appropriate.not_reported_except_by(nil).limit(how_many)
    end

    def set_reviewed_session
      session[:already_reviewed] ||= []
    end
  end
end

module Mkp
  class CartsController < ApplicationController
    skip_before_filter :check_user_has_network,
                       :new_whatsup,
                       :store_return_to_location,
                       :bootstrap_cart

    def show
      begin
        decode_items
      rescue ArgumentError;end
      cookiefy_items
      set_left_cart_cookie

      redirect_to mkp_checkout_url(@network.downcase)
    end

    def items
     @items = load_cart.items
      respond_to do |format|
        format.json {
          render json: Rabl.render(@items, 'mkp/cart/items', :view_path => 'app/views')
        }
      end
    end

    def sync
      extracted_data = extract_relevant_data(params[:cart] || [])
      if current_user
        load_cart.update_items(extracted_data)
        load_cart.reset_status!
      end
      respond_to do |format|
        format.json { render json: { ok: true } }
      end
    end

    private

    def decode_items
      tentative_cart_items = parse_items(params[:id])

      @items = sanitize_items(tentative_cart_items)
    end
    def parse_items(tentative_cart_items = '')
      Base64.decode64(tentative_cart_items).split('/').map do |item|
        item.split('+').map(&:to_i)
      end.to_h
    end
    def sanitize_items(cart_items)
      existing_variants = Mkp::Variant.active.with_stock.where(id: cart_items.keys)

      existing_variants.each_with_object([]) do |variant, list|
        quantity = if cart_items[variant.id] > variant.quantity
          variant.quantity
        else
          cart_items[variant.id]
        end

        list << { variant_id: variant.id, quantity: quantity }
      end
    end

    def cookiefy_items
      cookies[:qcart] = @items.to_json
    end

    def set_left_cart_cookie
      cookies[:left_cart] = { value: true,   expires: 1.days.from_now }
    end

    def load_cart
      current_user.get_or_create_regular_cart(@network)
    end

    def extract_relevant_data(raw_data)
      raw_data.map{|raw_data_item| { variant_id: raw_data_item[1]["variant_id"],
                                     quantity:   raw_data_item[1]["quantity"] }}
    end
  end
end

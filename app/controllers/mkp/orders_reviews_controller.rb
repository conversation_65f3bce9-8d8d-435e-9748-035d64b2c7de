module Mkp
  class OrdersReviewsController < ApplicationController
    skip_before_filter :store_return_to_location,
                       :clean_guest_info

    skip_before_filter :new_whatsup,
                       :bootstrap_cart, only: [:create]

    def new
      load_order
      load_communication
      load_variants
    end

    def create
      review = build_review

      respond_to do |format|
        format.json do
          if review.save
            send_thanks_email
            render json: { status: :ok }
          else
            render json: { errors: review.errors.full_messages.to_json },
                   status: :unprocessable_entity
          end
        end
      end
    end

    private

    def define_layout
      'mkp/application'
    end

    def build_review
      if communication.customer_type == 'User'
        review = ::Mkp::Review::User.new review_params
        review.user = communication.customer
      else
        review = ::Mkp::Review::Guest.new review_params
        review.guest = communication.customer
      end

      review
    end

    def communication
      @communication ||= SystemCommunication::Review.find(params[:communication_id])
    end

    def load_order
      @order = Mkp::Order.find_by_purchase_id(params[:order_id])
    end

    def load_communication
      @communication = SystemCommunication::Review.find(params[:cid])

      shipment = Mkp::Shipment.find(@communication.shipment_id)

      raise ActiveRecord::NotFound unless @communication.token == params[:token]
      raise ActiveRecord::NotFound unless shipment.order.id == @order.id
    end

    def load_variants
      @variants = Mkp::Variant.unscoped.find(@communication.variants_ids)
    end

    def review_params
      params.require(:mkp_review).permit(:curated, :description, :product_id, :stars, :title)
    end

    def send_thanks_email
      return if communication.thanks_communication_id.present?

      coupon = CouponGenerator.perform_for(communication.network).last
      thanks_communication = SystemCommunication::ReviewThanks.create(communication, coupon)

      communication.assign_thanks_communication(thanks_communication.id)

      SystemCommunication::ReviewMailer.delay_for(20.seconds).thanks(thanks_communication)
    end
  end
end

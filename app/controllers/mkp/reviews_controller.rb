module Mkp
  class ReviewsController < ActionController::Base
    layout false

    protect_from_forgery with: :exception
    
    # Concerns need it by the before_filter
    # explicitly set by this controller
    #
    # Ordered by the calls on the before filter
    include NetworkExposure
    include SetLocale
    include UserSessionator

    # Filters provided by the included concerns
    prepend_before_filter :set_network
    before_filter :set_locale
    before_filter :set_reviewed_session

    def create
      # render_not_allowed and return unless current_user.present?

      product = Mkp::Product.find(params[:product_id])

      if current_user.present?
        review = Mkp::Review::User.new(review_params) do |review|
          review.user = current_user
          review.product = product
        end
      else
        review = Mkp::Review.new(review_params) {|review| review.product = product}
      end

      if review.save
        session[:already_reviewed].push(product.id)

        render json: {
          message: I18n.t('v5.controllers.reviews.create.done'),
          content: render_to_string('partials/v5/_review', layout: false, locals: { review: review })
        }, status: :created
      else
        render json: {
          message: I18n.t('v5.controllers.reviews.create.fail'),
          errors: review.errors.full_messages.to_json
        }, status: :unprocessable_entity
      end
    rescue ActiveRecord::RecordNotFound
      render json: {
        errors: 'The product could not be found'
      }, status: :not_found
    end

    protected

    def render_not_allowed
      render json: { errors: { status: 'not allowed' } }, status: :forbidden
    end

    def review_params
      params.require(:mkp_review).permit(:title, :description, :stars, :email)
    end

    def set_reviewed_session
      session[:already_reviewed] ||= []
    end
  end
end

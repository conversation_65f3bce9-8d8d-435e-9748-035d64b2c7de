module Mkp
  class ApplicationController < ::ApplicationController
    layout :define_layout

    include UserNetworkEnsure
    before_filter :check_user_has_network

    private

    def url_for(options = nil)
      # In order to write the https protocol on specify routes
      # Discussed in: http://stackoverflow.com/questions/3993651/rails-3-ssl-routing-redirects-from-https-to-http

      options[:protocol] ||= 'http' if options.is_a?(Hash)

      if options.is_a?(Hash) && options.key?(:controller) && @network.present?
        if options[:network].nil? && options[:controller].match(/^mkp\//).present?
          options[:network] = @network.downcase
        end
      end

      super options
    end

    def define_layout
      'application'
    end
  end
end

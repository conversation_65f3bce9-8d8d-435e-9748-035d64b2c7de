module Mkp
  class QuestionsController < ActionController::Base
    layout false

    protect_from_forgery with: :exception
    
    # Concerns need it by the before_filter
    # explicitly set by this controller
    #
    # Ordered by the calls on the before filter
    include NetworkExposure
    include SetLocale
    include UserSessionator

    # Filters provided by the included concerns
    prepend_before_filter :set_network
    before_filter :set_locale

    def create
      render_not_allowed and return unless current_user.present?

      product = Mkp::Product.find(params[:product_id])
      question = product.questions.new(params[:mkp_question]) do |question|
        question.user = current_user
      end
      if question.save
        render json: {
          message: I18n.t('v5.controllers.questions.create.done', name: current_user.first_name),
          content: render_to_string('partials/v5/_question', layout: false, locals: { question: question })
        }, status: :created
      else
        render json: {
          message: I18n.t('v5.controllers.questions.create.fail'),
          errors: question.errors.full_messages.to_json
        }, status: :unprocessable_entity
      end
    rescue ActiveRecord::RecordNotFound
      render json: {
        errors: 'The product could not be found'
      }, status: :not_found
    end

    def render_not_allowed
      render json: { errors: { status: 'not allowed' } }, status: :forbidden
    end
  end
end

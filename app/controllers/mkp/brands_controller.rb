module Mkp
  class BrandsController < ApplicationController
    def index
      # Sunspot params
      network          = obtain_full_network
      is_thin_network  = Network[@network].thin?
      thin_network     = params[:network].upcase if is_thin_network

      search = Mkp::Variant.search(include: [product: [:manufacturer]]) do
        with :network, network
        with :deleted, false
        with :shop_visible, true
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now

        with_category = with :categories_friendly_ids, params[:c] if params[:c].present?
        with_sport = with :sports_friendly_ids, params[:sp] if params[:sp].present?

        if is_thin_network
          any_of do
            with :available_countries, thin_network
            with :available_countries, 'all'
          end
        end

        facet :manufacturer_id, limit: -1, sort: :count
        facet :categories_ids, exclude: with_category
        facet :sports_ids, exclude: with_sport
      end

      manufacturer_ids = search.facet(:manufacturer_id).rows.map(&:value)
      @breadcrumbs = build_breadcrumbs
      @brands = Mkp::BrandsFilter.brands(manufacturer_ids)
      @filter = Mkp::BrandsFilter.filter(search)
      @active_filter = Mkp::BrandsFilter.active_filter(params)

      render_properly :index
    end

    private

    def build_breadcrumbs
      b = []
      p = { network: @network.downcase }

      b << { name: 'Shop',
             href: Rails.application.routes.url_helpers.mkp_root_path(p) }
      b << { name: I18n.t('mkp.brands.index.title'),
             href: Rails.application.routes.url_helpers.mkp_brands_path(p) }

      b
    end

    def obtain_full_network
      Network[@network].thin? ? Network.default : @network.upcase
    end
  end
end

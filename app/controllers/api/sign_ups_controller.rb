module Api
  class SignUpsController < ::ActionController::Base
    def social_user
      signup_params = extract_social_user_params
      signup = PartialSignup.new_for_user(signup_params, params[:avatar])

      if signup.save
        UserMailer.delay.partial_signup_email(signup)
        render_success('user')
      else
        render_error('user', signup.errors)
      end
    end

    def brand
      sign_up_params = extract_brand_params
      sign_up = PartialSignup.new(sign_up_params)

      if sign_up.save
        UserMailer.delay.brand_partial_signup_email(sign_up)
        render_success('brand')
      else
        render_error('brand', sign_up.errors)
      end
    end

    private

    def render_success(model_name)
      message = "#{model_name.capitalize} was signed up successfully."
      render json: { message: message }
    end

    def render_error(model_name, errors)
      message = "There were errors signing up the #{model_name}."
      response = { message: message, errors: errors.full_messages}
      render json: response, status: 422
    end

    def extract_social_user_params
      params.slice(:email, :first_name, :last_name)
    end

    def extract_brand_params
      result = {}
      result[:brand_name] = params[:name]
      result[:email] = params[:email]
      result[:brand_contact_name] = params[:contact_name]
      result[:brand_contact_title] = params[:contact_title]
      result[:brand_kind] = params[:kind]
      result
    end
  end
end
module Api
  class SuggestionsController < ::ApplicationController
    skip_before_filter :load_new_session,
                       :new_whatsup,
                       :store_return_to_location,
                       :bootstrap_cart,
                       :clean_guest_info,
                       :set_mkp_menu,
                       :track_network_affiliate

    before_filter :parse_params

    # TODO: We should fine-tune this values.
    CATEGORIES_TO_SUGGEST = 2
    USERS_TO_SUGGEST = 4
    PRODUCTS_TO_SUGGEST = 5
    CATEGORIES_FACETS_TO_SUGGEST = 5
    MANUFACTURER_FACETS_TO_SUGGEST = 3

    def index
      @category_ids = @current_store.categories.pluck(:id)
      @shop_ids = @current_store.shops.pluck(:id)
      response = {}
      if categories.present?
        response[:categories] = categories
        response[:variants] = manufacturer_variants_facets
        response[:manufacturer_facets] = manufacturer_facets
      else
        response[:brands] = users('Brand') if load_users?('Brand')
        response[:users] = users('SocialUser') if load_users?('SocialUser')

        response[:variants] = variants
        response[:categories_facets] = categories_facets
      end
      response[:suggester_search] = suggester_search
      response = response.reject {|k, v| v.blank?}

      render json: response
    end

    private

    def variants_search
      return @variants_search unless @variants_search.nil?

      network = @network
      query = @query
      shop_ids = @shop_ids
      category_ids = @category_ids
      @variants_search = ::Mkp::Variant.search(include: { product: :pictures }) do
        fulltext query do
          phrase_slop 1
          boost_fields :category_name => 8.0,
                       :manufacturer_name => 3.0,
                       :title => 10.0,
                       :description => 1.0
        end

        with :shop_id, shop_ids
        with :categories_ids, category_ids
        with :deleted, false
        with :shop_visible, true
        with :display_variant, true
        with :network, network
        with(:quantity).greater_than_or_equal_to 1
        with(:available_on).less_than Time.now

        paginate page: 1, per_page: PRODUCTS_TO_SUGGEST

        facet :categories_ids

        facet :on_sale do
          row(:on_sale) do
            any_of do
              all_of do
                with(:sale_on).less_than_or_equal_to Time.now
                with(:sale_until).greater_than_or_equal_to Time.now
              end
              all_of do
                with(:shop_massive_discount_on).less_than_or_equal_to Date.today
                with(:shop_massive_discount_until).greater_than_or_equal_to Date.today
              end
            end
          end
        end
      end
    end

    def categories_search
      return @categories_search unless @categories_search.nil?
      category_ids = @category_ids
      exact_query = %Q("#{params[:query]}")
      @categories_search = ::Mkp::Category.search do
        fulltext exact_query
        with :active, true
        with :network, params[:network]
        with :id, category_ids
        facet :manufacturers_ids, sort: :count
        facet :variants_ids, sort: :count
      end
    end

    def categories
      return @categories unless @categories.nil?
      @categories = []

      search = categories_search

      return @categories if search.total.zero?

      top_categories = search.results.take(CATEGORIES_TO_SUGGEST)

      @categories = top_categories.each_with_object([]) do |category, result|
        result << build_top_category_suggestion(category)
      end
    end

    def suggester_search
      @suggester_search = []
      exact_query = %Q("#{params[:query]}")
      network = params[:network].downcase
      search = Suggestion.search(exact_query, network)
      search.each do  |result|
        @suggester_search << build_suggester(result)
      end
      return @suggester_search unless @suggester_search.nil?
    end


    def manufacturer_facets
      return @manufacturer_facets unless @manufacturer_facets.nil?

      @manufacturer_facets = []

      search = categories_search

      return @manufacturer_facets if search.total.zero?

      top_category = search.results.first

      manufacturers_ids = manufacturers_on_search(search)
      manufacturers_to_suggest(manufacturers_ids).each do |manufacturer|
        @manufacturer_facets << build_manufacturer_suggestion(manufacturer, top_category)
      end

      @manufacturer_facets
    end

    def categories_facets
      return @categories_facets unless @categories_facets.nil?

      @categories_facets = []

      search = variants_search

      return @categories_facets if search.total.zero?

      @categories_facets << build_category_generic_suggestion

      categories_ids = categories_on_search(search)
      categories_to_suggest(categories_ids).each do |category|
        @categories_facets << build_category_suggestion(category)
      end

      @categories_facets << build_category_sale_suggestion if categories_on_sale?(search)

      @categories_facets
    end

    def manufacturer_variants_facets
      return @manufacturer_variants_facets unless @manufacturer_variants_facets.nil?

      @manufacturer_variants_facets = []

      search = categories_search

      return @manufacturer_variants_facets if search.total.zero?

      variants_ids = variants_on_search(search)
      variants = variants_to_suggest(variants_ids)
      @manufacturer_variants_facets = variants.map(&method(:variant_suggestion))

      @manufacturer_variants_facets
    end

    def categories_on_search(search)
      search.facet(:categories_ids).rows.map(&:value)
    end

    def manufacturers_on_search(search)
      search.facet(:manufacturers_ids).rows.map(&:value)
    end

    def variants_on_search(search)
      search.facet(:variants_ids).rows.map(&:value)
    end

    def categories_to_suggest(categories_ids)
      ::Mkp::Category.where(id: categories_ids, ancestry: nil)
                     .limit(CATEGORIES_FACETS_TO_SUGGEST)
    end

    def manufacturers_to_suggest(manufacturers_ids)
      ::Mkp::Manufacturer.where(id: manufacturers_ids)
                       .limit(MANUFACTURER_FACETS_TO_SUGGEST)
    end

    def variants_to_suggest(variants_ids)
      shop_ids = @shop_ids
      category_ids = @category_ids
      ::Mkp::Variant.search(include: { product: :pictures }) do
        with :categories_ids, category_ids
        with :deleted, false
        with :shop_visible, true
        with :shop_deleted, false
        with :display_variant, true
        with :categories_ids, category_ids
        with :shop_id, shop_ids
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now
        with :id, variants_ids
        paginate page: 1, per_page: PRODUCTS_TO_SUGGEST
      end.results
    end

    def build_category_generic_suggestion
      model({
        text: I18n.t('suggestions.text.categories', query: @query),
        url: mkp_catalog_root_path({ network: @network.downcase, query: @query })
      })
    end

    def build_category_suggestion(category)
      model({
        text: I18n.t('suggestions.text.category', query: @query, name: category.name),
        url: UrlMapperHelper.absolute_mapper_path(category, @network.downcase, {query: @query})
      })
    end

    def build_top_category_suggestion(category)
      model({
        text: category.name,
        url: UrlMapperHelper.absolute_mapper_path(category, @network.downcase)
      })
    end

    def build_suggester(suggestion)
      model({
        type: 'suggestion',
        text: suggestion[:value],
        url:  suggestion[:url]
        })
    end


    def build_manufacturer_suggestion(manufacturer, category)
      model({
        text: manufacturer.name,
        url: UrlMapperHelper.absolute_mapper_path([manufacturer,category], @network.downcase)
      })
    end

    def categories_on_sale?(search)
      on_sale = search.facet(:on_sale).rows.presence
      on_sale && on_sale.first.count > 0
    end

    def build_category_sale_suggestion
      model({
        text: I18n.t('suggestions.text.on_sale', query: @query),
        url: mkp_catalog_root_path({ network: @network.downcase, d: 1, query: @query })
      })
    end

    def users(type)
      @users = {} if @users.nil?
      return @users[type] unless @users[type].nil?

      @users[type] = []

      search = User.search(include: [profile: :avatar]) do
        fulltext(params[:query])

        order_by :network, :asc

        with(:type, type)
        paginate page: 1, per_page: USERS_TO_SUGGEST
      end

      @users[type] = search.results.sort do |a, b|
        if a.network == b.network
          0
        elsif a.network == @network && a.network != @network
          -1
        else
          1
        end
      end.map(&method(:users_suggestion))
    end

    def users_suggestion(user)
      model({
        full_name: user.full_name,
        login: "@#{user.login}",
        avatar: users_avatar(user),
        bio: user.profile.bio.present? ? user.profile.bio : "@#{user.login}",
        url: url_helpers.profiles_path(user.login),
        type: user.type.parameterize,
      })
    end

    def users_avatar(user)
      locals = { user: user }

      locals[:style] = :hexagonal if user.type == 'SocialUser'

      render_to_string(partial: 'partials/v5/avatar', locals: locals)
    end

    def variants
      return @variants unless @variants.nil?

      search = variants_search

      return @variants = [] if search.total.zero?

      @variants = search.results.map(&method(:variant_suggestion)).compact
    end

    def variant_suggestion(variant)
      return nil if variant.picture.nil?
      p = variant.product
      on_sale = ::Mkp::ProductSaleDetector.is_on_sale?(p)
      regular_price = '%.2f' % p.regular_price
      sale_price = if on_sale
        '%.2f' % ::Mkp::ProductSaleDetector.calculate(p)
      else
        regular_price
      end
      model({
        on_sale: on_sale,
        regular_price: regular_price,
        sale_price: sale_price,
        title: variant.title,
        picture: variant.picture.url(:t),
        url: variant.get_url,
      })
    end

    def load_users?(type)
      categories.blank? && users(type).present?
    end

    def load_categories_facets?
      categories.blank? && categories_facets.present?
    end

    def load_manufacturer_facets?
      categories.present? && manufacturer_facets.present?
    end

    def parse_params
      @query = params[:query] if params[:query].present?
    end

    def model(attrs = {})
      { value: @query }.merge attrs
    end

    def url_helpers
      Rails.application.routes.url_helpers
    end

  end
end

class Api::V2::OrdersController < Api::V2::ApplicationController
  before_filter :find_order, only: [ :show, :update, :update_liquided, :cancel ]
  before_filter :filter_orders, only: [ :index ]

  def index ; end

  def create
    @order_builder = ::Api::OrderBuilder.new(order_params)

    if @order_builder.save
      render json: {success: true, order_id: @order_builder.order.id}, status: 200
    else
      render json: {success: false, errors: @order_builder.errors.full_messages}, status: 401
    end
  end

  def show
    raise ActiveRecord::RecordNotFound if @order.nil? || @order.store.id != @current_store.id
  end

  def update
    if @order.store.name == "jubilo"
      render json: {success: true, order_id: @order.id, purchase_id: @order.purchase_id}, status: 200 if ::Mkp::Payment::STATUS.include?(update_params[:status]) && @order.payment.update_attributes(
        status: update_params[:status],
        gateway_object_id: update_params[:token]
      )
    else
      render json: { success: false }, status: 401
    end
  end

  def cancel
    if %w(jubilo jubilostaging).any?{ |name| name == @order.store.name }
      #esto podria ser un worker??
      CancelService.cancel(@order.id)
      data = {
        success: true,
        order_id: @order.id,
        purchase_id: @order.purchase_id,
        status: @order&.payment&.status || 'algo salio mal'
      }
      render json: data, status: 200
    else
      render json: { success: false }, status: 401
    end
  end

  def refund
    if %w(jubilo jubilostaging).any?{ |name| name == @order.store.name }
      #esto podria ser un worker??
      CancelService.refund(@order.id)
      data = {
        success: true,
        order_id: @order.id,
        purchase_id: @order.purchase_id,
        status: @order&.payment&.status || 'algo salio mal'
      }
      render json: data, status: 200
    else
      render json: { success: false }, status: 401
    end
  end

  def update_liquided
    if @order.store.name == "jubilo"
      @order.update_columns(jubilo_liquided: update_params[:status] )
      render json: { success: true, order_id: @order.id }, status: :ok
    else
      render json: { success: false }, status: 401
    end
  end

  private
  def order_params
    params.permit(
      :first_name, :last_name, :email, :phone, :address,
      :depth, :street_number, :city, :state, :postal_code, :collected_amount, :items => [:variant_id, :quantity]
    ).merge({
      real_remote_ip: real_remote_ip,
      store_id: @current_store.id
    })
  end

  def update_params
    params.permit(:status, :token)
  end

  def find_order
    id = params[:id]
    search = id.match(/^[0-9]+$/).nil? ? :find_by_purchase_id : :find
    @order = @current_store.orders.send(search, id)
  end

  def filter_orders
    page = params[:page] || 1
    store_id = @current_store.id

    from = params[:from] || (Date.today - 1.months)
    to = params[:to] || Date.today

    payment_status = params[:payment_status]
    shipment_status = params[:shipment_status]

    @orders = ::Mkp::Order.search(include: [:payments, suborders: [:items, :shop]]) do
      with(:store_id, store_id)
      with(:created_at).greater_than_or_equal_to(from) if from
      with(:created_at).less_than_or_equal_to(to) if to

      with :payment_status, payment_status if payment_status
      with :shipment_status, shipment_status if shipment_status
      order_by(:created_at, :desc)
      paginate(page: page, per_page: 75)
    end.results
  end

  def order_edit
    @order
  end
end

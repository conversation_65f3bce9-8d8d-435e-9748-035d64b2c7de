class Api::V2::ProductsController < Api::V2::ApplicationController
  def index
    search = Mkp::Product.search do
      with :deleted, false
      with :visible_shop, true
      with :network, params[:network].upcase
      with :with_stock, true
      with(:available_on).less_than Time.zone.now
      paginate page: params[:p] || 1, per_page: 30
    end
    @products = search.results
  end

  def show
    @product = Mkp::Product.find(params[:id])
  end
end

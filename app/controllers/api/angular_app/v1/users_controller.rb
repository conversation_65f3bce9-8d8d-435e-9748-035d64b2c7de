class Api::AngularApp::V1::UsersController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:me]

  def create
    @user = @current_store.customers.new(user_register_params)
    if @user.save
      render :show, status: 200
    else
      render json: {error: "Un error ha ocurrido", errors: @user.errors.full_messages}, status: 422
    end
  end

  def omniauth
    oauth = OmniauthService.call!(params[:provider], params[:token], @current_store)

    if oauth[:success]
      @user = oauth[:user]
      render json: {success: true, token: JsonWebToken.encode({user_id: @user.id, store_id: @current_store.id})}, status: 200
    else
      render json: {error: 'Something went wrong, please try again'}, status: 401
    end
  end

  def me; end

  def logout
    @current_user = nil
  end

  private

  def user_register_params
    params.require(:user).permit(:email, :password, :password_confirmation)
  end
end

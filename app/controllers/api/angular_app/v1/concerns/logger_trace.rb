module Api::AngularApp::V1::Concerns::LoggerTrace
  extend ActiveSupport::Concern

  def checkout_log
    @checkout_log ||= Logger.new("#{Rails.root}/log/#{@type}.log")
  end

  def logger_trace(type, response = nil, operation = nil)
    @type = type
    checkout_log.info("TRACE_BULLET : ")
    checkout_log.info("TRACE_BULLET : @========================================================================================================@")
    checkout_log.info("TRACE_BULLET : @____________________ TRACING: #{ @checkout_cart.class } ________________________________________________@")
    checkout_log.info("TRACE_BULLET : @========================================================================================================@")
    checkout_log.info("TRACE_BULLET : ")
    checkout_log.info(@checkout_cart.inspect);
    checkout_log.info("TRACE_BULLET : @____________________ TRACING: #{ @cart.class } ___________________@")
    checkout_log.info(@cart.inspect);
    checkout_log.info("TRACE_BULLET : @____________________ TRACING: Items ______________________________@")
    checkout_log.info(@cart.items.inspect);
    if @type == 'checkout'
      checkout_log.info("TRACE_BULLET : @____________________ TRACING: Payment ____________________________@")
      checkout_log.info(@payment.inspect);
    else
      checkout_log.info("TRACE_BULLET : @____________________ TRACING: Commerce ____________________________@")
      checkout_log.info(@commerce.inspect);
      checkout_log.info("TRACE_BULLET : @____________________ TRACING: Response ____________________________@")
      checkout_log.info(response.inspect);
      checkout_log.info("TRACE_BULLET : @____________________ TRACING: operation ____________________________@")
      checkout_log.info(operation.inspect);
    end
  end
end

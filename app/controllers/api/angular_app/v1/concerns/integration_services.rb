require 'json_web_token'

module Api::AngularApp::V1::Concerns::IntegrationServices
  extend ActiveSupport::Concern

  def run_integration_services(action)
    if @current_store.name == "chequeregalo"
      Irsa::Worker.perform_async(@order.id, params[:payment][:document_type])
    end

    if action === :create
      @order.suborders.each do |suborder|
        Lux::IntegrationSaleNotificationWorker.perform_async(suborder.shop.id,
                                                             suborder.id,
                                                             "Mercadolibre")
      end
    end
  end
end

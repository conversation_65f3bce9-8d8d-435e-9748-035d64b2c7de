require 'json_web_token'

module Api::AngularApp::V1::Concerns::Authenticable
  extend ActiveSupport::Concern

  # Validates the token and user and sets the @current_user scope
  def authenticate_user!
    if !payload || !JsonWebToken.valid_payload(payload.first)
      return invalid_authentication
    end

    load_current_user!
    invalid_authentication unless @current_user
  end

  # Returns 401 response. To handle malformed / invalid requests.
  def invalid_authentication
    render json: {error: 'Invalid Request'}, status: 401
  end

  private
  # Deconstructs the Authorization header and decodes the JWT token.
  def payload
    auth_header = request.headers['auth-token']
    token = auth_header.split(' ').last
    JsonWebToken.decode(token)
  rescue
    nil
  end

  # Sets the @current_user with the user_id from payload
  def load_current_user!
    @current_user = @current_store.customers.find_by_id(payload[0]['user_id'])
  end
end

module Api::AngularApp::V1::Concerns::TodopagoProcessable
  extend ActiveSupport::Concern

  def build_todopago_vars
    header = { 'Authorization' => TODOPAGO_APP_KEY }
    @connector = TodoPago::TodoPagoConector.new( header, TODOPAGO_ENDPOINT )
    @commerce = {
      Security: TODOPAGO_SECURITY,
      Merchant: TODOPAGO_MERCHANT,
      EncodingMethod: 'XML',
      URL_OK: Rails.application.routes.url_helpers.mkp_checkout_successful_authorization_url(host: HOSTNAME, network: 'ar')
    }
  end

  def build_todopago_operation(cart, checkout_cart, store = nil)
    {
      MERCHANT: TODOPAGO_MERCHANT,
      OPERATIONID: cart.purchase_id,
      CURRENCYCODE: '032',
      AMOUNT: checkout_cart.parsed_total,
      EMAILCLIENTE: cart.customer.email,
      CSBTCITY: cart.address.city,
      CSBTCOUNTRY: cart.address.country,
      CSBTCUSTOMERID: cart.customer.id.to_s,
      CSBTIPADDRESS: request.remote_ip,
      CSBTEMAIL: cart.customer.email,
      CSBTFIRSTNAME: cart.address.first_name,
      CSBTLASTNAME: cart.address.last_name,
      CSBTPHONENUMBER: cart.address.telephone.to_s,
      CSBTPOSTALCODE: cart.address.zip.to_s,
      CSBTSTATE: build_state_from_cart(cart),
      CSBTSTREET1: [cart.address.address, cart.address.street_number].reject(&:blank?).join(" "),
      CSBTSTREET2: cart.address.address_2 || "",
      CSPTCURRENCY: 'ARS',
      CSPTGRANDTOTALAMOUNT: checkout_cart.parsed_total,
      CSMDD6: 'Web',
      CSSTCITY: cart.address.city,
      CSSTCOUNTRY: cart.address.country,
      CSSTEMAIL: cart.customer.email,
      CSSTFIRSTNAME: cart.address.first_name,
      CSSTLASTNAME: cart.address.last_name,
      CSSTPHONENUMBER: cart.address.telephone.to_s,
      CSSTPOSTALCODE: cart.address.zip.to_s,
      CSSTSTATE: build_state_from_cart(cart),
      CSSTSTREET1: [cart.address.address, cart.address.street_number].reject(&:blank?).join(" "),
      CSITPRODUCTCODE: "default", # TODO: ???
      CSITPRODUCTDESCRIPTION: 'Description', # TODO: ???
      CSITPRODUCTNAME: checkout_cart.title.present? ? checkout_cart.title.gsub('&', '') : 'Compra',
      CSITPRODUCTSKU: 'SKU1234', # TODO: ???
      CSITUNITPRICE: checkout_cart.parsed_total,
      CSITQUANTITY: (checkout_cart.items.try(:size) || 1).to_s,
      CSITTOTALAMOUNT: checkout_cart.parsed_total
    }.tap do |hash|
      if cart.is_pickup
        hash[:CSMDD13] = 'retiro en sucursal'
        hash[:CSBTCITY] = 'Mountain View'
        hash[:CSBTCOUNTRY] = 'US'
        hash[:CSSTPOSTALCODE] = '94043'
        hash[:CSSTSTATE] = 'CA'
        hash[:CSSTSTREET1] = '1295 Charleston Road'
      end
    end
  end

  def build_state_from_cart(cart)
    state_map = {
      'Capital Federal': 'C', 'Bs As Interior': 'B', 'GBA': 'B', 'Catamarca': 'K', 'Chaco': 'H',
      'Chubut': 'U', 'Córdoba': 'X', 'Corrientes': 'W', 'Entre Ríos': 'E',
      'Formosa': 'P', 'Jujuy': 'Y', 'La Pampa': 'L', 'La Rioja': 'F',
      'Mendoza': 'M', 'Misiones': 'N', 'Neuquén': 'Q', 'Río Negro': 'R',
      'Salta': 'A', 'San Juan': 'J', 'San Luis': 'D', 'Santa Cruz': 'Z',
      'Santa Fe': 'S', 'Santiago del Estero': 'G', 'Tierra del Fuego': 'V', 'Tucumán': 'T'
    }

    state_map[:"#{cart.address.state}"] || "C"
  end
end

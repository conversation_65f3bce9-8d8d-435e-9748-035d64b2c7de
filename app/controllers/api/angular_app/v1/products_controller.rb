class Api::AngularApp::V1::ProductsController < Api::AngularApp::V1::ApplicationController
  def show
    @product = Mkp::Product.find_by_slug(params[:id]) || Mkp::Product.find(params[:id])
    raise ActiveRecord::RecordNotFound unless @current_store.categories.include?(@product.category)
    raise ActiveRecord::RecordNotFound unless @current_store.active_shops.pluck(:shop_id).include?(@product.shop.id)
    raise ActiveRecord::RecordNotFound unless @product.is_approved_for_store?(@current_store)
  end

  def calculate_shipping
    product = Mkp::Product.find(params[:product_id])
    if product.present?
      if product.shop.delivery_by_matrix
        response = get_matrix_delivery_options(product)
      else
        service = Avenida::Krabpack::GetDeliveryOptionsService.new(zip_code: params[:postal_code], product: product)
        service.perform

        response = service.valid ? service.response.first : get_matrix_delivery_options(product)
      end
    else
      response = { message: 'El producto no existe' }
    end
    render json: response
  end

  def stock_by_shopping
    stocks = params[:coupon_ids].map do |value|
      Irsa::Stock.get(value)
    end
    stocks = stocks.flatten.group_by{ |s| s["shopping"] }.map{ |s| { "shopping" => s[0], "stock" => s[1].sum{|u| u["stock"]} } if s[1].any?{ |m| m["stock"] > 0 } }.compact
    render json: stocks
    rescue
      render json: { error: "No tenemos el stock disponible", status: 401 }
  end

  private

  def get_matrix_delivery_options(product)
    @current_store.get_delivery_options(params[:postal_code], [product.final_weight], [product.shop_id]).first
  end
end

module Api
  module AngularApp
    module V1
      module Integration
        class PurchasesController < ApplicationController
          before_filter :authenticate_user!, except: :reservable_purchase

          def points_exchange
            gateway = "Gateways::Purchases::#{gateway_params[:name].camelize}".constantize.new(exchange_params, @current_user)
            gateway.perform

            render layout: false, json: { status: 200, message: gateway.status, success: gateway.valid, order_id: gateway.order.id }
          end

          def points_refund
            purchase = Purchase.find params[:order_id]

            service = Gateways::Refunds::Sube.new(purchase)
            service.perform

            render layout: false, json: { status: 200, message: service.status, success: service.valid }
          end

          def voucher_points_exchange
            gateway = Gateways::Purchases::MacroVoucher.new(voucher_params, @current_user)
            gateway.perform

            status = gateway.valid ? 200 : 406
            order_id = gateway.valid ? gateway.order.id : nil

            render layout: false, json: { status: status, message: gateway.status, success: gateway.valid, order_id: order_id }
          end

          def reservable_purchase
            service = Gateways::Purchases::Reservable.new(reservable_params, @current_store, bna_params)
            service.perform

            if service.valid
              render json: { order: service.order }, status: :ok
            else
              render json: { error: service.error }, status: 406
            end
          end

          private

          def exchange_params
            params.require(:exchange).permit(:store_id, :points, :amount, :email, :card_number,
              :document_number, :document_type, :full_name, :phone_number, :phone_code, :phone_company, :variant_id)
          end

          def gateway_params
            params.require(:gateway).permit(:name)
          end

          def reservable_params
            params.require(:purchase).permit(:dni, :email, :full_name, :phone_number, :variant_id, :token)
          end

          def bna_params
            params.require(:bna).permit(:date, :solicitude_id, :limit_amount, :profile, :program_type)
          end

          def voucher_params
            params.require(:exchange).permit(:store_id, :variant_id, :email)
          end
        end
      end
    end
  end
end
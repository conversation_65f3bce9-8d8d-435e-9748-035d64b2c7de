module Api
  module AngularApp
    module V1
      module Integration
        class AerolineasArgentinasController < Api::AngularApp::V1::ApplicationController

          def frequent_traveler_information
            @integration = aerolineas_argentinas_services
            response = @integration.frequent_traveler_information(frequent_traveler_information_params)
            render layout: false, json: response
          end

          def mile_accreditation
            @integration = aerolineas_argentinas_services
            response = @integration.mile_accreditation(mile_accreditation_params)
            render layout: false, json: response
          end

          private

          def frequent_traveler_information_params
            params.require(:aerolineas_argentina).permit(:document_type, :document_number, :store_id)
          end

          def mile_accreditation_params
            params.require(:aerolineas_argentina).permit(:document_type, :document_number, :store_id, :miles, :email)
          end

          def aerolineas_argentinas_services
            AerolineasArgentinas::WebServices.new
          end
        end
      end
    end
  end
end

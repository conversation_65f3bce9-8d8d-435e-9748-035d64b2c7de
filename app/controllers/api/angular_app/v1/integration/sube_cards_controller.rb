module Api
  module Ang<PERSON>App
    module V1
      module Integration
        class SubeCardsController < Api::AngularApp::V1::ApplicationController
          before_filter :authenticate_user!
          before_filter :find_sube_card, only: [:show, :update, :destroy]

          def index
            @sube_cards = @current_user.sube_cards
          end

          def show
          end

          def create
            @sube_card = @current_user.sube_cards.create(sube_card_params)

            if @sube_card.valid?
              render :show
            else
              render json: {errors: @sube_card.errors}, status: 422
            end
          end

          def update
            @sube_card.update_attributes(sube_card_params)

            if @sube_card.valid?
              render :show
            else
              render json: {errors: @sube_card.errors}, status: 422
            end
          end

          def destroy
            if @sube_card.destroy
              render json: true, status: 200
            else
              render json: false, status: 422
            end
          end

          def verify
            service = Macro::VerifySubeCard.new(verify_params)
            service.perform

            render json: { success: service.valid, message: service.message }
          end

          private

          def sube_card_params
            params.require(:sube_card).permit(:number, :alias)
          end

          def verify_params
            params.require(:card_number)
          end

          def find_sube_card
            @sube_card = @current_user.sube_cards.find(params[:id])
          end
        end
      end
    end
  end
end

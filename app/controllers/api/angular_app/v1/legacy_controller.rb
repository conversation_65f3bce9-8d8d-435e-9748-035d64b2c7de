class Api::AngularApp::V1::LegacyController < Api::AngularApp::V1::ApplicationController
  def create
    url_params = params[:path].split('/')
    if url_params.count > 1
      slug = url_params[1]
      if ((product = Mkp::Product.find_by_slug(slug)).present? \
          || (product = get_product_without_property(slug)).present?)
        render json: { controller: "mkp/products", slug: product.slug} and return if product.present?
      end
    else
      mapper_response = UrlMapper.match(params.merge(network: @network))

      if mapper_response.present?
        render json: { controller: mapper_response[:controller], slug: mapper_response[:path] } and return
      end
    end

    render json: { controller: "mkp/catalog", slug: 'products' }
  end

  private

  def get_product_without_property(full_product_slug)
    slug_parts = full_product_slug.split('-')
    slug = slug_parts[0..slug_parts.length-2].join("-")
    Mkp::Product.find_by_slug(slug) || Mkp::Product.find_by_slug(slug[0..(slug.length - 2)])
  end
end

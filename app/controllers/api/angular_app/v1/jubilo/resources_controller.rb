class Api::AngularApp::V1::Jubilo::ResourcesController < Api::AngularApp::V1::ApplicationController
  before_action :auth_header

  def client
    response = client.client(@token, params[:cuil])
    render json: response, status: (response[:status] || :ok)
  end

  def search
    parameters = params.slice(:cuit, :dni, :nombres, :apellidos)
    response = client.search(@token, parameters)

    # este código es para completar el monto maximo a financiar con la v6
    # if response['results'].present?
    #   response['results'].each do |result|
    #     result.merge!({monto_maximo_para_financiar: client.max_credit(@token, result['cuil'])})
    #   end
    # end

    render json: response, status: (response[:status] || :ok)
  end

  def credit
    response = client.credit(@token, params[:credit_id])
    render json: response, status: (response[:status] || :ok)
  end

  def sellers
    response = client.sellers(@token)
    render json: response, status: (response[:status] || :ok)
  end

  def payment_options
    response = client.payment_options( @token, params[:creditType], params[:cuil], params[:amount])
    render json: response, status: (response[:status] || :ok)
  end

  def grant_credit
    response = client.post_credit( @token, params[:creditType], params[:cuil], params[:amount], params[:key_payment_option], params[:purchase_id], params[:seller_id], params[:positive_identification_token])
    render json: response, status: (response[:status] || :ok)
  end

  def referrer
    response = client.referrer( @token, params[:cuil] )
    render json: response, status: (response[:status] || :ok)
  end

  private
  def auth_header
    @token = request.headers['auth-token']
    invalid_authentication unless @token
  end

  def client
    @client ||= ::Jubilo::Client.new(request.headers['mode'])
  end
end

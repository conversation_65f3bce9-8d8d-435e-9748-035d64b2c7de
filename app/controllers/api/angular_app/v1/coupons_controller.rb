class Api::AngularApp::V1::CouponsController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index]

  def index
    @coupons = @current_user.coupons
  end

  def create
    @coupon = Mkp::Coupon::Network.create do |coupon|
      coupon.code = Mkp::Coupon.random_coupon
      coupon.amount = (params[:amount] || 100).to_i
      coupon.minimum_value = 0
      coupon.percent = 0
      coupon.description = ''
      coupon.starts_at = 1.day.ago
      coupon.expires_at = (params[:days_to_expire] || 3).to_i.days.since
      coupon.total_available = 1
      coupon.restrictions = {}
      coupon.network = @network
      coupon.policy = 'value'
      coupon.apply_on_sale = true
      coupon.discount_limit = 0
      coupon.store_id = @current_store.id
    end

    render :show
  end
end

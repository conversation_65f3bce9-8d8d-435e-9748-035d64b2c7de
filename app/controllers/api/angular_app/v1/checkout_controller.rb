# frozen_string_literal: true

module Api
  module AngularApp
    module V1
      # Controller for checkout
      class InvalidGatewayException < StandardError; end

      class CheckoutController < ApplicationController
        class CreateOrderException < StandardError; end
        module CheckoutCart
          class Error < StandardError; end
        end

        class ProductAvailabilityException < StandardError; end

        rescue_from Avenida::Payments::InvalidAmount, InvalidGatewayException, 
          CreateOrderException, CheckoutCart::Error, ProductAvailabilityException do |exception|
          render_error(exception.message)
        end

        include CheckoutFlow
        include Api::AngularApp::V1::Concerns::PaymentBuildParams
        include Api::AngularApp::V1::Concerns::CheckoutBuildable
        include Api::AngularApp::V1::Concerns::TodopagoProcessable
        include Api::AngularApp::V1::Concerns::LoggerTrace
        include Api::AngularApp::V1::Concerns::CheckoutAuthenticable
        include Api::AngularApp::V1::Concerns::IntegrationServices
        include CheckoutProcessable

        before_filter :authenticate_user!, :find_cart, only: %i[
          init cart_quantity coupon authorize_todopago withdrawal_branch
          remove_coupon address delivery_options payment
          subscription_newsletter first_purchase installments
          installments_no_bines jubilo_credit_request
          payment_intent
        ]

        before_filter :ensure_cart_is_there, except: %i[
          init cart_quantity done first_purchase 
          payment_intent
        ]
        before_filter :validate_variant_params, only: [:init]

        def init
          service = Checkout::ValidateProductAvailability.new(params[:items],
                                                              @current_store)
          service.perform

          if service.valid
            build_cart
            build_checkout_cart
            render :cart
          else
            render json: { error: service.error }, status: 406
          end
        end

        def cart_quantity
          if @cart.nil?
            render json: { quantity: 0 }, status: 200
          else
            render json: { quantity: @cart.items.count }, status: 200
          end
        end

        def authorize_todopago
          build_checkout_cart
          build_todopago_vars
          operation = build_todopago_operation(@cart,
                                               @checkout_cart,
                                               @current_store)

          response = @connector.sendAuthorizeRequest(@commerce, operation)
          logger_trace('todopago', response, operation)
          response = JSON.parse(response)['envelope']['body']['send_authorize_request_response']

          if response['status_code'].to_i == -1
            @cart.update_attribute(:data, { todopago: response })
            render json: response.to_json
          else
            render json: { error: 'Something went wrong, please try again' }
          end
        end

        def coupon
          build_checkout_cart
          build_coupon
        rescue Mkp::Coupon::CouponExpiredError => e
          render json: { error: 'El cupón ha expirado' }
        rescue Mkp::Coupon::CouponNotActiveError => e
          render json: { error: 'Este cupón no se encuentra activo' }
        rescue Mkp::Coupon::UserLimitedReachedError => e
          render json: { error: 'Has alcanzado el máximo de usos para este cupón' }
        rescue Mkp::Coupon::BelowAmountLimitError => e
          render json: { error: t('mkp.checkout.coupons_error.below_amount_limit') }
        rescue Mkp::Coupon::InvalidCategoryError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_category', name: e.data[:name]) }
        rescue Mkp::Coupon::CouponNotOnsaleError => e
          render json: { error: t('mkp.checkout.coupons_error.on_sale') }
        rescue Mkp::Coupon::InvalidManufacturerError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_manufacturer', name: e.data[:name]) }
        rescue Mkp::Coupon::InvalidShopError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_shop') }
        rescue Mkp::Coupon::InvalidProductError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_product', name: e.data[:name]) }
        end

        def remove_coupon
          @cart.update_attribute(:coupon_id, nil)
          build_checkout_cart

          render :cart
        end

        def address
          @cart.update_attribute(:is_pickup, params[:is_pickup])
          build_address(params[:form], params[:address_id], params[:billing_address])
          build_checkout_cart
          build_delivery_options

          render :cart
        end

        def delivery_options
          @cart.delivery_options.each do |option|
            option.mark_unselected! if option.selected
          end
          if params[:delivery_option_ids].present?
            params[:delivery_option_ids].each do |selected_option|
              cart_option = @cart.delivery_options.find(selected_option['cart_delivery_option_id'])
              @cart.update!(is_pickup: cart_option.pickup)
              cart_option.mark_selected! if cart_option.present?
            end
          end

          @cart.delivery_options.reload
          build_checkout_cart

          render :cart
        end

        def installments
          @installments = @current_store.installments_for(@cart, params[:bin], params[:doc_type], params[:doc_number],  @current_store.use_bines)
          @installments = installments_array_details(@installments)
          render_installments_error('Tarjeta no válida') unless @installments.any?
        end

        def installments_array_details(installments)
          installments_details = installments.map { |i| [ i.number, i.coef.to_f, sprintf('%.2f', i.total_by_installment(@cart.total)), i.government_program] }
          payment_program_count = installments.map(&:payment_program_id).uniq.count
          installments_details  = installments_details.group_by(&:itself).map { |k,v| [k, v.count] }.to_h.map{ |key,value| key if value == payment_program_count}.compact
          filtered_installments = []
          installments_details.each do |ins|
            installments.each do |i|
              if i.number == ins[0] && i.coef.to_f == ins[1] && sprintf('%.2f', i.total_by_installment(@cart.total)) == ins[2] && i.government_program == ins[3]
                filtered_installments << i
                break
              end
            end
          end
          filtered_installments
        end

        def subscription_newsletter
          @cart.update_attribute(:newsletter, params[:newsletter])
          build_checkout_cart
          render :cart
        end

        def withdrawal_branch
          zip = @cart.address.try(:zip) || '1614'
          @branch_offices = OcaService.branch_offices(zip.to_i)
        end

        def payment_intent
          if params[:gateway] == 'modo'
            (payment_intent_status || payment_modo) and return
          end

          head :not_found
        end

        def payment_modo
          service = Checkout::ValidateProductAvailability.new(@cart.items_to_checkout, @current_store)
          service.perform
          raise ProductAvailabilityException, service.error, [] unless service.valid

          build_checkout_cart
          verify_checkout_cart!
          Rails.logger.info("<Checkout Controller> Items to Checkout: #{@cart.items_to_checkout}")

          params[:payment] = mododistributed
          process_payment_angular if params[:payment].present?
          begin
            ActiveRecord::Base.transaction do
              Rails.logger.info("<Checkout Controller> Checkout Cart Items: #{@checkout_cart.items}")
              collect_payment
              if (@checkout_cart.balance_due? && ( @payment.blank? || @payment.any? { |payment| payment.cancelled? }))
                exit_payment_process and return true
              end
              create_order(@current_store.id)
              create_and_associate_shipments
              suborders_coupon_generator if @order.coupon.present?
              remove_current_cart(params[:gateway]) if @cart.present?
              set_gross_total
              render json: response_pending_payment
            end
          rescue Avenida::Payments::InvalidAmount
            raise
          rescue => e
            log_payment_fail(@payment, @checkout_cart.items, e) if @payment.present?
            raise CreateOrderException, 'La transacción no pudo ser completada, cancelando pagos.', []
          end
        end

        def payment
          set_tdd_boca_address
          build_checkout_cart

          service = Checkout::ValidateProductAvailability.new(@cart.items_to_checkout, @current_store)
          service.perform

          Rails.logger.info("<Checkout Controller> Items to Checkout: #{@cart.items_to_checkout}")
          if service.valid
            begin
              ActiveRecord::Base.transaction do
                if proccess_points_payment
                  if @checkout_cart.total >= 0 && @checkout_cart.choosen_delivery_options.present?
                    build_gateway_params
                    process_payment_angular if params[:payment].present?
                    Rails.logger.info("<Checkout Controller> Checkout Cart Items: #{@checkout_cart.items}")
                    if @checkout_cart.items.count > 0 && ::Mkp::Order.find_by_purchase_id(params['purchase_id']).blank?
                      collect_payment
                    end
                  end

                  logger_trace('checkout')
                  if (@checkout_cart.balance_due? && ( @payment.blank? || @payment.any? { |payment| payment.cancelled? })) || @checkout_cart.choosen_delivery_options.blank?
                    if @points_payments.present?
                      @points_payments.each { |payment| payment.cancel!(@current_store) }
                    end

                    cancelled_payment = @payment&.detect(&:cancelled?)
                    cancel_existing_payments

                    # error = @payment&.cancelled? ? get_payment_error_message : missing_payment_error_message
                    error = cancelled_payment.present? ? get_payment_error_message(cancelled_payment) : missing_payment_error_message
                    if @checkout_cart.choosen_delivery_options.blank?
                      error = 'Debes elegir opciones de envío'
                    end

                    if @payment.present? && @payment.any? { |payment| payment.get_error[:status] == 'gateway_error' }
                      ExceptionNotifier.notify_exception(StandardError.new, data: {
                          error: error,
                          payment: @payment || '',
                          checkout_cart: @checkout_cart,
                          params: params.dup,
                          session: session.dup
                      })
                    end

                    render_error(error)
                  else
                    create_order(@current_store.id)
                    create_and_associate_shipments
                    run_integration_services(:create)
                    if @current_store.name == 'bancomacro'
                      ::Mkp::Integration::PuntosYPremios.new.redemption(@order, nil)
                    end
                    suborders_coupon_generator if @order.coupon.present?
                    # if params[:is_pickit] == '1'
                    #   confirm_pickit_order(params[:pickit_quotation_id])
                    # end
                    remove_current_cart(params[:gateway]) if @cart.present?
                    set_gross_total
                    render json: response_payment
                  end
                else
                  render_error('No se pudo procesar el pago con puntos')
                end
              end
            rescue Avenida::Payments::InvalidAmount
              raise
            rescue => e
              if @payment.present?
                @payment.each do |payment|
                  unless payment.cancelled?
                    "Avenida::Payments::#{payment.gateway}".constantize.cancel_payment_by_store!(payment.gateway_object_id, @current_store, payment, @checkout_cart)
                  end
                end
                log_payment_fail(@payment, @checkout_cart.items, e)
              end
              raise CreateOrderException.new('La transaccion no pudo ser completada, cancelando pagos')
            end
          else
            render_error(service.error)
          end
        end

        def revert_order
          # Borra una orden que acaba de ser creada
          motives = {
              type: 'Dinero en TC',
              reason: 'Producto faltante'
          }
          service = Cancelation::OrderCancelationService.new(order: @order, items: @order.items.map(&:id), motive: motives)
          @order.destroy if service.cancel
        end

        def done
          response = Tdigital::CheckoutProcessor.new(purchase_id: params[:purchase_id]).done
          render json: response.body, status: response.code
        end

        def first_purchase
          # validate first purchase + whitelist
          # esto hace lo mismo que validate whitelist, hay que arreglarlo
          result = false
          if @current_store.whitelist_configuration.present? && @current_store.whitelist_configuration.active
            if params[:document_type].present? && params[:document_number].present?
              if @current_store.whitelist_configuration.user_strategy_class.exists?(params[:document_number], @current_store)
                result = @current_store.whitelist_configuration.validation_strategy_class.authorized?(params[:document_number], @current_store, @cart)
              end
            end
          end

          render json: { result: result }
        end

        def jubilo_credit_request
          if @current_store.name == 'jubilostaging' || @current_store.name == 'jubilo'
            Mkp::AbandonedCartMailer.jubilo_credit_request(jubilo_credit_params, @cart).deliver

            render json: { success: true }, status: :ok
          else
            render json: { success: false, message: 'Servicio no disponible para esta tienda' }, status: 403
          end
        end

        private

        def cancel_existing_payments
          if @payment.present?
            @payment.each do |payment|
              payment.cancel_by_store!(@current_store) if (payment.respond_to?(:cancel_by_store!) && !payment.cancelled?)
            end
          end
        end

        def log_payment_fail(payment, checkout_items, exception)
          @import_log ||= Logger.new("#{Rails.root}/log/fail_create_order.log")
          @import_log.info("--------------------------------------------------------")
          payment.each.with_index(1)  do |pay, index|
            @import_log.info("PAYMENT_DECIDIR PAGO #{index}: #{payment}")
          end
          @import_log.info("CHECKOUT ITEMS: #{checkout_items}")
          @import_log.info("EXCEPTION: #{exception.full_message}")
          @import_log.info("REQUEST: #{request.session&.id} - #{request.path}: #{ActionDispatch::Http::ParameterFilter.new(Rails.application.config.filter_parameters).filter(request.params)}")
        end

        def validate_whitelist
          unless @current_store.whitelist_configuration.present? && @current_store.whitelist_configuration.active
            return
          end
          return if params[:form].nil?

          if dni_active?
            return if dni_authorized?

            data = unauthorized_information

            render json: {
                error: data[:message],
                products: data[:products].map(&:title)
            }, status: 406
          else
            render json: { error: @current_store.whitelist_configuration.unauthorized_user_message }, status: 406
          end
        end

        def dni_active?
          @current_store.whitelist_configuration.user_strategy_class.exists?(params[:form][:doc_number], @current_store)
        end

        def dni_authorized?
          @current_store.whitelist_configuration.validation_strategy_class.authorized?(params[:form][:doc_number], @current_store, @cart)
        end

        def unauthorized_information
          @current_store.whitelist_configuration.validation_strategy_class.build_unauthorized_data(params[:form][:doc_number], @current_store, @cart)
        end

        def response_payment
          {
              success: true,
              order: @order.suborders.flat_map(&:public_id).join(', #'),
              vouchers: @order.items.map { |item| item.coupon&.code }.compact,
              reservation_code: @order.reservation_code.presence
          }.tap do |resp|
            payment_gateway_data = []
            @payment.each do |pay|
              payment_gateway_data << pay&.gateway_data
            end
            resp.merge!(payment: payment_gateway_data) if @payment.any? { |payment| payment.present? }
            if @payment_points.present?
              resp.merge!({ points_payment: @payment_points&.gateway_data })
            end
          end
        end

        def payment_intent_status
          @order = ::Mkp::Order.find_by_purchase_id(params['purchase_id'])
          return false unless @order

          @payment = [@order.payment]
          render json: response_pending_payment
          true
        end

        def response_pending_payment
          payment = @payment.first
          {
              success: true,
              order: @order.suborders.flat_map(&:public_id).join(', #'),
              vouchers: @order.items.map { |item| item.coupon&.code }.compact,
              reservation_code: @order.reservation_code.presence
          }.tap do |resp|
            payment_last_info = payment.last_gateway_data.except(:request)
            payment_last_info[:gateway] = payment_last_info.delete(:response) || payment_last_info["gateway"] || payment.gateway_data["gateway"]
            payment_last_info[:callbacks] = payment_last_info["callbacks"] || payment.gateway_data.dig(:original_payload, :callbacks) || payment.gateway_data["callbacks"]
            payment_last_info.except!(:notifications_payloads, :original_payload)
            resp.merge!(payment: payment_last_info) if payment_last_info.present?
          end
        end

        def validate_variant_params
          if params[:items].nil? && @cart.try(:items).blank?
            render json: { error: 'No variants provided' }, status: 406
          end
        end

        # rubocop:disable Metrics/AbcSize
        def find_cart
          return @cart = nil if !@current_user.present? && params[:purchase_id].nil?
          return @cart = ::Mkp::Cart.find_by_purchase_id(params[:purchase_id]) if params[:purchase_id].present?

          @cart = if @current_user.present? && params[:purchase_id].nil?
            cart = @current_user.carts.last
            ::Mkp::Order.find_by_purchase_id(cart.try(:purchase_id)).present? ? nil : cart
          end
        end
        # rubocop:enable Metrics/AbcSize

        def ensure_cart_is_there
          cart_not_found if @cart.nil?
        end

        def cart_not_found
          render json: { error: 'You need to build your own cart' }, status: 404
        end

        def remove_current_cart(gateway)
          if gateway == 'todopago'
            Mkp::Payments::RemoveCart.perform_in(5.minutes, @cart.purchase_id)
          else
            @cart.destroy
          end
        end

        def proccess_points_payment
          Rails.logger.info("--------------------------------------------LITLE JESUS ---------------------------\n")
          Rails.logger.info(@checkout_cart)
          Rails.logger.info(@checkout_cart.store.has_visa_puntos?)
          if @checkout_cart.store.has_visa_puntos?
            @points_payments = Avenida::Payments::VisaPuntos.collect_payment_by_items(@checkout_cart, @cart, visa_puntos_params, @current_user)
            if @points_payments.any?(&:cancelled?)
              ExceptionNotifier.notify_exception(StandardError.new, data: {
                  error: 'Error procesando pago con puntos',
                  payment: @points_payments,
                  checkout_cart: @checkout_cart,
                  params: params.dup,
                  session: session.dup
              })
              return false
            end
          end

          return true
        end

        def set_tdd_boca_address
          if @current_store.name == 'tddboca'
            build_address(nil, @current_user.addresses.last.id)
            @cart.update_attribute(:delivery_option_id, 'matrix')
          end
        end

        def jubilo_credit_params
          params.permit(:name, :email, :cuil, :phone_number)
        end

        def render_error(error)
          flash[:alert] = error
          render json: { error: 'An error has ocurred trying to process your payment', errors: [error] }, status: 200
        end

        def render_installments_error(error)
          flash[:alert] = error
          render json: { error: error }, status: 406
        end

        def verify_checkout_cart!
          raise CheckoutCart::Error, 'Carrito duplicado', [] unless ::Mkp::Order.find_by_purchase_id(params['purchase_id']).blank?
          raise CheckoutCart::Error, 'Carrito vacío', [] unless @checkout_cart.items.count > 0
          raise CheckoutCart::Error, 'Monto incorrecto en carrito', [] unless @checkout_cart.total >= 0
          raise CheckoutCart::Error, 'Debes elegir opciones de envío', [] unless @checkout_cart.choosen_delivery_options.present?
        end
      end
    end
  end
end

class Api::AngularApp::V1::LandingsController < Api::AngularApp::V1::ApplicationController
  before_filter :set_landing, only: :show
  before_filter :set_home, only: :home

  def show
    raise ActiveRecord::RecordNotFound unless @landing.active?
  end

  def home
    raise ActiveRecord::RecordNotFound unless @landing.home?

    render :show
  end

  private

  def set_home
    @landing ||= @current_store.landings.find_by!(home: true)
  end

  def set_landing
    @landing ||= @current_store.landings.find_by!(slug: params[:id])
  end
end

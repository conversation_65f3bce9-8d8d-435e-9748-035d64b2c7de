class Api::AngularApp::V1::OrdersController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index, :show]
  before_filter :find_order, only: [:show]
  before_filter :filter_orders, only: [:index]

  def index
  end

  def show
  end

  def tracking
    label = Mkp::ShipmentLabel.find_by(tracking_number: params[:id])

    if label.present? && label.shipment.sale_item.store == @current_store
      service = Gateways::Shipments::GetTrackingDetails.new(label: label)
      service.perform

      if service.valid
        render json: service.result, status: 200
      else
        render json: {error: service.error}, status: 406
      end
    else
      render json: {error: 'Envío no encontrado'}, status: 404
    end
  end

  private

  def filter_orders
    page = params[:page] || 1
    @orders = SaleItem.where(store_id: @current_store.id, customer_id: @current_user.id, customer_type: "Mkp::Customer")
                  .order('created_at desc')
                  .paginate(page: page, per_page: 7)
  end

  def find_order
    @order = Mkp::Order.find(params[:id])
  end
end

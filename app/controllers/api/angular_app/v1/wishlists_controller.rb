class Api::AngularApp::V1::WishlistsController < Api::AngularApp::V1::ApplicationController

  before_filter :authenticate_user!, only: [:index, :show, :update, :destroy ]
  before_filter :find_wishlist
  before_filter :find_variant, only: [:update]
  before_filter :find_wishlist_variant, only: [:show, :destroy]

  def index
  end

  def show
  end

  def update
    if @wishlist.variants.push(@variant)
      render json: true, status: 200
    else
      render json: {errors: @wishlist.errors }, status: 401
    end
  end

  def destroy
    @wishlist.variants.delete(@variant)
    render json: true, status: 200
  end

  private
  def find_variant
    @variant = Mkp::Variant.find(params[:id])
  end

  def find_wishlist
    @wishlist = @current_user.wishlist
  end

  def find_wishlist_variant
    @variant = @current_user.wishlist.variants.find(params[:id])
  end
end

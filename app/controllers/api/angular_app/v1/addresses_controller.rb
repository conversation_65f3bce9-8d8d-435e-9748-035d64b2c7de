class Api::AngularApp::V1::AddressesController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index, :show, :create, :update, :destroy]
  before_filter :find_address, only: [:show, :update, :destroy]

  def index
    @addresses = @current_user.addresses
  end

  def show
  end

  def create
    @address = @current_user.addresses.create(address_params)

    if @address.valid?
      render :show
    else
      render json: {errors: @address.errors}, status: 401
    end
  end

  def update
    @address.update_attributes(address_params)

    if @address.valid?
      render :show
    else
      render json: {errors: @address.errors}, status: 401
    end
  end

  def destroy
    if @address.destroy
      render json: true, status: 200
    else
      render json: false, status: 404
    end
  end

  private
  def find_address
    @address = @current_user.addresses.find(params[:id])
  end

  def address_params
    params.require(:address).permit(:first_name, :last_name, :telephone, :address, :address_2, :city, :state, :zip, :country, :pickup, :open_hours, :retardment, :priority, :street_number, :doc_type, :doc_number, :email)
  end
end

class Api::AngularApp::V1::TransactionsPointController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index, :show]

  def index
    @transactions = user.transactions(transactions_params)
  end

  def show
    @transaction = user.transaction(params[:id])
    not_found unless @transaction.present?
  end

  private

  def user
    @user ||= SystemPoints::User.new(@current_user)
  end

  def transactions_params
    params.permit(:from, :to, :type, :limit)
  end
end

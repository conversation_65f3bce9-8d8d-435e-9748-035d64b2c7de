module Api
  module Mkp
    class TrackingController < ActionController::Base

      protect_from_forgery with: :exception

      def create
        if params[:result].present? && params[:description] == "tracker.updated"
          tracker = EasyPost::Event.receive(params[:result].to_json)

          if label = ::Mkp::ShipmentLabel.find_by_gateway_object_id(tracker.shipment_id)
            if label.update_status(tracker.status)
              label.activity_log = tracker.tracking_details.map(&:as_json)

              if tracker.est_delivery_date.present?
                label.estimated_delivery_date = tracker.est_delivery_date.to_datetime
              end

              add_notification_payload(label, params.deep_symbolize_keys)

              label.save!
            end
          end
        end

        render layout: false, json: { status: :received }
      end

      def show
        return render nothing: true, status: 404 unless /^\d+-\d+-\d+$/.match(params[:id])
        order = ::Mkp::Order.find(params[:id].split('-')[0])
        @suborders = order.suborders
        return render nothing: true, status: 404 unless @suborders.any?{|s| s.public_id == params[:id]}
        render
      end

      private

      def add_notification_payload(label, payload)
        label.gateway_data[:notifications_payloads] ||= {}
        label.gateway_data[:notifications_payloads][Time.now.to_s] = payload
      end
    end
  end
end

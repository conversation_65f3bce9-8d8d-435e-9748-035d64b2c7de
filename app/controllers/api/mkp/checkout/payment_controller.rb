module Api
  module Mkp
    module Checkout
      class PaymentController < BaseController
        before_filter :load_payment_gateway_class

        # POST /api/mkp/checkout/payments/verify
        def verify
          payment = @payment_gateway.verify_payment_method(params[:nonce], params[:payment_method_type])
          set_checkout_session_payment(payment.get_data_for_session)

          if payment.success?
            render json: {
              response: {
               verified: true
              }
            }, status: 200
          else
            notify_error({ payment: payment, params: params.dup, session: session.dup})
            render json: { errors: payment.errors }, status: 422
          end
        end

        private

        def load_payment_gateway_class
          unless (klass = available_payment_gateway(params[:gateway])).present?
            raise CheckoutFlowError.new(self, __method__)
          end

          @payment_gateway = "Avenida::Payments::#{klass}".constantize
        end

        def available_payment_gateway(gateway_name)
            Avenida::Payments.for(@network).detect do |klass|
            klass.downcase == gateway_name
          end
        end

      end
    end
  end
end

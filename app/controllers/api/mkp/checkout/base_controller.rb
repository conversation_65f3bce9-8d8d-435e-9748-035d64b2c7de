module Api
  module Mkp
    module Checkout
      class BaseController < ActionController::Base
        force_ssl if SSL_ENABLED

        protect_from_forgery with: :exception

        layout false

        # Concerns need it by the before_filter
        # explicitly set by this controller
        include CurrencyExposure
        include NetworkExposure

        # Concerns that are need it by dependencies
        # or inside some actions, views or helpers
        include UserSessionator
        include GuestInfo
        include CheckoutFlow

        # Filters provided by the included concerns
        prepend_before_filter :set_network

        before_filter :verify_flow_id

        def notify_error(payload)
          ExceptionNotifier.notify_exception(StandardError.new, data: payload)
        end

      end
    end
  end
end

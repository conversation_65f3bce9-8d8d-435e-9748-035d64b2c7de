module Api
  module Mkp
    module Checkout
      class AddressesController < BaseController
        include ::Mkp::CheckoutHelper
        before_filter :set_current_checkout_session_and_cart, except: :index

        # GET /api/mkp/checkout/addresses
        def index
          user = User.find_by_id(params[:user_id])

          addresses = user.presence && user.addresses

          if addresses.present?
            render json: {
              response: addresses
            }, status: 200
          else
            render json: {
              head: :no_content
            }, status: 404
          end
        end

        # POST /api/mkp/checkout/addresses
        def create
          customer = current_customer.presence || guest_customer

          new_address = customer.addresses.build(address_params)

          if new_address.save(validate: false) #We don't need some methods
            render json: {
              response: new_address
            }, status: 201
          else
            render json: {
              errors: [
                address: new_address.errors
              ]
            }, status: 422
          end
        end

        # PUT api/mkp/checkout/addresses
        def update
          customer = current_customer

          address = customer.addresses.find_by_id(params[:id])

          address.assign_attributes(address_params)

          if address.save(validate: false)
            render json: {
              response: address_json(address)
            }, status: 200
          else
            render json: {
              errors: [
                address: address.errors
              ]
            }, status: 422
          end
        end

        private

        def address_json(address)
          address.as_json.merge!(
            full_name: address.full_name,
            full_location: full_location(address),
            full_country: full_country(address)
          )
        end

        def guest_customer
          guest = ::Mkp::Guest.create!(guest_attributes)
          save_guest_session(guest)
          persist_cart(guest)
          guest
        end

        def guest_attributes
          full_name = params[:address][:full_name].split(' ')
          first_name = full_name.shift
          last_name = full_name.join(' ')

          {
            first_name: first_name,
            last_name: last_name,
            email: params[:address][:email].downcase.strip.gsub(/\s+/, ""),
            network: @network
          }
        end

        def address_params
          params.require(:address).permit(*permitted_fields)
        end

        def permitted_fields
          %w(telephone address street_number address_2 city country doc_type doc_number full_name state zip email)
        end
      end
    end
  end
end

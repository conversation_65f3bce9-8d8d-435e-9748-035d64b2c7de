module Api
  module Mkp
    module Checkout
      class PickupController < BaseController

        # POST /api/mkp/checkout/pickup/quotation
        def get_pickup_quotation
          begin
            response = Gateways::Shipments::Pickit.get_quotation_number(get_quotation_params(params))
            render json: response
          rescue RestClient::GatewayTimeout => e
            render json: {
                data: e.to_s,
              }, :status => 400
          end
        end

        def get_selected_pickup_point
          request_body = {
            cotizacionId: params[:quotationId],
            tokenId: PICKIT_TOKEN_ID
          }
          begin
            response = Gateways::Shipments::Pickit.get_pickup_point_information(request_body.to_json)
            render json: response
          rescue PickitUnexpectedResponse => e
            render json: {
                data: e.to_s,
              }, :status => 400
          end
        end

        private

        def get_quotation_params(params)
          params = {
            direccionCliente: params[:location],
            articulos: get_articles(params[:items]),
            tokenId: PICKIT_TOKEN_ID
          }.to_json
        end

#\"articulos\":[{\"sku\":\"112233\",\"tipoProducto\":1,\"articulo\":\"Remera Nike\",
# \"precio\":1,\"pesoKg\":1,\"pesoL\":1,\"pesoPV\":1}]}"}

        def get_articles(items)
          items.each_with_object([]) do |item, articles|
            article = {}
            article['sku'] = item[1][:sku]
            article['tipoProducto'] = 1
            article['articulo'] = item[1][:name]
            article['pesoKg'] = 1
            article['pesoPV'] = ""
            article['pesoL'] = ""
            article['precio'] = item[1][:price]
            articles << article
          end
        end
      end
    end
  end
end

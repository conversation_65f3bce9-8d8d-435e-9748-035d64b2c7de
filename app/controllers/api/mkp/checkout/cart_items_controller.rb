module Api
  module Mkp
    module Checkout
      class CartItemsController < BaseController

        # POST /api/mkp/checkout/cart_items/remove
        def remove
          item_id = params[:variant_id].to_i

          # TODO: Remove this comment when we refactor the CheckoutFlow module
          # This method below runs set_current_checkout_session_and_cart that's why
          # we have access to @checkout_cart instance variable
          #remove_item_from_checkout_session(item_id)

          render json: {
            response: {
              subtotal: @checkout_cart.subtotal,
              promotion: @checkout_cart.get_promotion
            }
          }, status: 200
        end
      end
    end
  end
end

module Api
  module Mkp
    module Checkout
      class CouponsController < BaseController
        before_filter :set_current_checkout_session_and_cart, only: :apply

        # POST /api/mkp/checkout/coupons/apply
        def apply
          get_network_coupon
          if @coupon.present?
            apply_coupon(@coupon)

            if @coupon.errors[:base].present?
              #remove_checkout_session_coupon

              render json: {
                response: {
                  errors: @coupon.errors[:base]
                }
              }, status: 422
            else
              render json: {
                response: {
                  coupon: coupon_hash
                }
              }, status: 201
            end
          else
            render json: {
              response: {
                errors: [ t('mkp.checkout.coupons_error.invalid_coupon') ]
              }
            }, status: 404
          end
        end

        # DELETE /api/mkp/checkout/coupons/clear
        def clear
          #remove_checkout_session_coupon

          render json: {
            response: {
              success: true
            }
          }, status: 200
        end

        private

        def get_network_coupon
          @current_store = ::Mkp::Store.find_by_name("avenida")
          @coupon = ::Mkp::Coupon::Network.where(network: @network)
            .where(store_id: @current_store.id)
            .find_by_code(params[:coupon_code])
        end

        def coupon_hash
          {
            discount: @discount
          }.tap do |coupon|
            if @coupon.percent_policy?
              coupon[:percent] = @coupon.percent
            else
              coupon[:amount] = @coupon.amount
            end
          end
        end

        # TODO: Please, kill this with purifying fire.
        def apply_coupon(coupon)
          if (success = ::Mkp::DiscountHandler.perform!(:apply, @checkout_cart, @coupon))
            #set_checkout_session_coupon(@coupon)
            @discount = ::Mkp::DiscountHandler.determine_amount_for(@checkout_cart)
            #set_current_checkout_session_coupon_discount(@discount)
          else
            raise
          end
        rescue => exception
          case exception
            when ::Mkp::Coupon::InvalidCouponError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.not_found')
            when ::Mkp::Coupon::InvalidNetworkError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.invalid_network')
            when ::Mkp::Coupon::OutOfStockError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.out_of_stock')
            when ::Mkp::Coupon::CouponNotActiveError
              date = @coupon.starts_at.strftime('%d/%m/%Y')
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.not_active', date: date)
            when ::Mkp::Coupon::CouponExpiredError
              date = @coupon.expires_at.strftime('%d/%m/%Y')
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.expired', date: date)
            when ::Mkp::Coupon::UserLimitedReachedError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.used_limit_reached')
            when ::Mkp::Coupon::InvalidShopError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.invalid_shop')
            when ::Mkp::Coupon::InvalidCategoryError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.invalid_category', name: exception.data[:name])
            when ::Mkp::Coupon::InvalidManufacturerError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.invalid_manufacturer', name: exception.data[:name])
            when ::Mkp::Coupon::BelowAmountLimitError
              value = "#{current_currency_format} #{"%.2f" % coupon.minimum_value}"
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.below_limit', value: value)
            when ::Mkp::Coupon::UserExcludedError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.user_not_included')
            when ::Mkp::Coupon::CouponNotOnsaleError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.on_sale')
            when ::Mkp::Coupon::PromotionNotAllowCouponError
              @coupon.errors[:base] << t('mkp.checkout.coupons_error.promotion_not_allow_coupon')
          end
        end
      end
    end
  end
end

module Api
  module Mkp
    module Checkout
      class DeliveryOptionsController < BaseController
        before_filter :set_current_checkout_session_and_cart, only: :create

        # POST /api/mkp/checkout/delivery_options
        def create
          delivery_options = session_delivery_options
          current_taxes = get_total_taxes
          if delivery_options.present?
            render json: {
              response: {
                delivery_options: delivery_options.except(:context, :title),
                taxes: current_taxes,
                title: delivery_options[:title]
              }
            }, status: 200
          else
            render json: {
              head: :no_content
            }, status: 404
          end
        end

        # POST /api/mkp/checkout/delivery_options/set
        def set
          if set_selected_delivery
            render json: {
              response: {
                success: true
              }
            }, status: 200
          else
            render json: {
              errors: [
                "There was an error choosing that delivery option."
              ]
            }, status: 422
          end
        end

        private

        def session_delivery_options
          if (delivery_options = get_checkout_session_delivery_options(params[:address_id])).present? &&
            same_delivery_options_context?(delivery_options)
            delivery_options
          else
            retrieve_grouped_delivery_options
          end
        end

        def same_delivery_options_context?(delivery_options)
          context = delivery_options[:context]
          context[:items] == get_session_items_ids &&
          context[:coupon] == get_session_coupon_code &&
          context[:state] == current_address.state &&
          context[:zip] == current_address.zip
        end

        def current_address
          @current_address ||= current_customer.addresses.find(params[:address_id])
          #set_checkout_session_address(@current_address) unless @current_address.blank?
          @current_address
        end

        def retrieve_grouped_delivery_options
          processor = ::Mkp::Shipping::DeliveryProcessor.new(current_address, @checkout_cart)
          delivery_options = processor.find_delivery_options
          grouped_delivery_options = processor.group_delivery_options(delivery_options)
          if grouped_delivery_options.present?
            grouped_delivery_options.merge!({
              context: {
                items: get_session_items_ids,
                coupon: get_session_coupon_code,
                state: current_address.state,
                zip: current_address.zip
              }
            })
          end

          set_checkout_session_delivery_options(grouped_delivery_options)
        end

        def get_total_taxes
          return 0 unless current_address && current_address.state.present?
          taxes = @checkout_cart.taxes(current_address && current_address.state)
          set_checkout_session_taxes(taxes)
        end

        def set_selected_delivery
          return false unless (delivery_option_id = params[:delivery_id]).present?
          set_checkout_session_delivery_option_selected(delivery_option_id).present?
        end
      end
    end
  end
end

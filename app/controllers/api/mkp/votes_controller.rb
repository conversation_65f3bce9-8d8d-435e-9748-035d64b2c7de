module Api
  module Mkp
    class VotesController < ::Mkp::ApplicationController
      skip_before_filter :new_whatsup,
                         :store_return_to_location,
                         :bootstrap_cart

      before_filter :login_required

      def create
        vote = build_vote
        if vote.save
          render nothing: true, status: :ok
        else
          render nothing: true, status: :unprocessable_entity
        end
      end

      def destroy
        if find_current_user_vote.destroy
          render nothing: true, status: :ok
        else
          render nothing: true, status: :unprocessable_entity
        end
      end

      protected

      def build_vote
        vote = ::Mkp::Vote.new vote_params
        vote.user = current_user
        vote
      end

      def find_current_user_vote
        p = params.permit(:review_id, :product_id)
        p.merge(user_id: current_user.id)
        ::Mkp::Vote.where(p).first
      end

      def vote_params
        params.permit(:review_id, :product_id)
      end

      def login_required
        render nothing: true, status: :unauthorized unless logged_in?
      end
    end
  end
end

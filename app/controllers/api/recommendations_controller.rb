module Api
  class RecommendationsController < ::ApplicationController
    skip_before_filter :load_new_session,
                       :new_whatsup,
                       :store_return_to_location,
                       :bootstrap_cart

    def all
      post = get_post

      limit = params[:limit].to_i
      limit = 6 if limit > 25

      if post.is_a?(::Mkp::Product)
        posts_recommender = ::Social::Feed.recommended_for_product(post, params[:network], limit)
      else
        posts_recommender = ::Social::PostRecommender.recommend(post, params[:network], limit)
      end

      render partial: 'social/partials/feed_item',
             collection: posts_recommender,
             locals: { network: params[:network] },
             as: :feed_item, layout: false
    end

    private

    def get_post
      case params[:type]
        when 'Social::Whatsup' then ::Social::Whatsup.find(params[:id])
        when 'Social::Album'   then ::Social::Album.find(params[:id])
        when 'Mkp::Product'    then ::Mkp::Product.find(params[:id])
        else
          not_found
      end
    end
  end
end

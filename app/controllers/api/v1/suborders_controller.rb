module Api
  module V1
    class SubordersController < EntitiesController
      private

      def entity_class
        ::Mkp::Suborder
      end

      def available_entities
        # from format 'dd/mm/yyyy'
        from = Time.zone.parse(params[:date_from]) if params[:date_from]
        to = Time.zone.parse(params[:date_to]) if params[:date_to]
        @entities = entity_class.where(shop: @current_user.shops)
        @entities = @entities.where('created_at >= ?', from) if from
        @entities = @entities.where('created_at <= ?', to) if to

        @entities
      end

      def initialize_entity
        @entity = available_entities.where(id: params[:id])
        @entity = @entity.last
        raise ActiveRecord::RecordNotFound unless @entity
      end
    end
  end
end
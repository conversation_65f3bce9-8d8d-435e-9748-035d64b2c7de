module Api
  module V1
    class ProductsController < EntitiesController
      skip_before_action :verify_authenticity_token # NOTE: add to skip verify token CSRF

      include ApiProductActions

      def create
        begin
          # Chequeo que los paquetes sean validos
          unless product_params.key?(:packages)
            return render json: { error: "El producto debe contener un paquete" }, status: :bad_request
          end
          unless check_packages_validity()
            return render json: { error: "Los valores del paquete no pueden ser menores o iguales a 0" }, status: :bad_request
          end
          
          # Inicializamos el servicio
          service = AttributesBuilder.new(@current_user, product_params)
          service.perform
      
          # Verificamos si ya existe el producto con el mismo SKU y shop_id
          existing_product = ::Mkp::Variant.where(
            sku: service.result[:variants_attributes].map { |v| v[:sku] },
            shop_id: service.result[:shop_id]
          ).exists?
      
          if existing_product
            render json: { error: "Producto ya existe" }, status: :unprocessable_entity
            return
          end
      
          # Verificamos si hay algún error en el servicio
          if service.error.present?
            render json: { error: service.error }, status: :bad_request
            return
          end
      
          # Iniciamos la transacción
          #PaperTrail.request.whodunnit = @current_user.present? ? @current_user.id : 'system'
          ActiveRecord::Base.transaction do
            # Creamos el producto con create!, esto lanzará una excepción si falla la validación
            @entity = ::Mkp::Product.create!(service.result)
            
            # Realizamos las acciones adicionales post creación
            post_create_actions(service)
          end
      
          # Si todo va bien, devolvemos éxito
          render json: { success: "Producto creado exitosamente", id: @entity.id }, status: :ok
      
        rescue ActiveRecord::RecordInvalid => e
          # Si ocurre algún error de validación, lo capturamos y devolvemos un error
          render json: { error: e.record.errors.full_messages.to_sentence }, status: :unprocessable_entity
        rescue OpenURI::HTTPError => e
          # Si ocurre un error con la descarga de imágenes
          render json: { error: "Failed to fetch image: #{e.message}" }, status: :unprocessable_entity
        rescue StandardError => e
          # Cualquier otro error se maneja aquí
          render json: { error: "Unexpected error: #{e.message}" }, status: :unprocessable_entity
        end

        if product_attr_builder.error.present?
          render json: { error: product_attr_builder.error }, status: :bad_request
          return
        end

        @entity = ::Mkp::Product.create(product_attr_builder.result)

        ActiveRecord::Base.transaction do
          @entity.save!
          post_create_actions(product_attr_builder)
        end

        render json: { success: "Producto creado exitosamente", id: @entity.id }, status: :ok
        rescue ActiveRecord::RecordInvalid => e
          # Si ocurre algún error de validación, lo capturamos y devolvemos un error
          render json: { error: e.record.errors.full_messages.to_sentence }, status: :unprocessable_entity
        rescue OpenURI::HTTPError => e
          # Si ocurre un error con la descarga de imágenes
          render json: { error: "Failed to fetch image: #{e.message}" }, status: :unprocessable_entity
        rescue StandardError => e
          # Cualquier otro error se maneja aquí
          render json: { error: "Unexpected error: #{e.message}" }, status: :unprocessable_entity
        end
      end

      def update
        # Chequeo que los paquetes sean validos
        if product_params.key?(:packages)
          unless check_packages_validity()
            return render json: { error: "Los valores del paquete no pueden ser menores o iguales a 0" }, status: :bad_request
          end
        end

        @entity = ::Mkp::Product.find params[:id]
        shop = @current_user.shops.where(id: @entity.shop_id).first
        if shop
          if @entity
            product_attr_builder = AttributesBuilder.new(@current_user, product_params.merge(shop_id: @entity.shop_id))
            product_attr_builder.perform
            if product_attr_builder.error.present?
              render json: product_attr_builder.error, adapter: :json, status: '400'
            else
              initialize_variants_to_delete(product_attr_builder)
              initialize_package_to_delete(product_attr_builder)
              @entity.update_attributes(product_attr_builder.result)
              if @entity.errors.present?
                render json: @entity.errors.messages.to_s, adapter: :json, status: :bad_request
              else
                post_update_actions(product_attr_builder)
                render json: "Product updated", adapter: :json, status: :ok    
              end
            end
          else
            render json: "Product not found", adapter: :json, status: '404'
          end
        else
          render json: "The product doesn't belong to the user", adapter: :json, status: :bad_request
        end
      end

      def update_async
        @entity = ::Mkp::Product.find params[:id]
        shop = @current_user.shops.where(id: @entity.shop_id).first
        if shop
          if @entity
            job = AsyncRequest::Job.create_and_enqueue(UpdateApiProductsWorker, @current_user.id, params[:id], product_params)
            job.instance_variable_set('@token', AsyncRequest::JsonWebToken.encode(job.id))
            render json: { token: job.token, url: async_request.job_url }, status: :accepted
          else
            render json: "Product not found", adapter: :json, status: '404'
          end
        else
          render json: "The product doesn't belong to the user", adapter: :json, status: :bad_request
        end
      end

      private

      def entity_class
        ::Mkp::Product
      end

      def check_packages_validity
        return false unless product_params[:packages].is_a?(Array)
        return false unless product_params[:packages].size.positive?
        product_params[:packages].each do |p|
          return false if p[:length] <= 0 || p[:width] <= 0 || p[:height] <= 0 || p[:weight] <= 0
        end
        true
      end

      def available_entities
        entity_class.where(shop: @current_user.shops)
      end

      def initialize_entity
        @entity = available_entities.find params[:id]
      end

      def product_params
        params.require(:product).permit( :title, :description, :category_id, :manufacturer_id, :regular_price,:regular_price_without_taxes,
                                         :iva, :available_on, :sale_on, :sale_until, :sale_price,:sale_price_without_taxes, :transaction_type,
                                         :shop_id,
                                         images: [:url],
                                         variants: [ :sku, :quantity,:ean_code, :points_price, :discount_top,
                                                     properties: [:color, :size, :dimensions, :percentage, :payment_method]],
                                         packages: [:width, :height, :length, :weight, :length_unit, :mass_unit])
      end
    end
  end
end

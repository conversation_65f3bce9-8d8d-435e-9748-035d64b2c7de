module Api
  module V1
    class ApiController < ActionController::Base
      protect_from_forgery with: :null_session

      before_action :authenticate_request
      attr_reader :current_user

      private

      def authenticate_request
        command = AuthorizeApiRequest.new(request.headers)
        @current_user = command.call
        render json: { error: 'Not Authorized' }, status: 401 unless @current_user
      end
    end
  end
end
module Api
  module V1
    class EntitiesController < ApiController
      # GET	/api/v1/categories
      def index
        page = params[:page] || 1
        per_page = params[:per_page] || 20
        @entities = available_entities.paginate(:page => page, :per_page => per_page)
      end

      def show
        begin
          initialize_entity
        rescue ActiveRecord::RecordNotFound
          render json: "Record not found", adapter: :json, status: '404'
        end
      end

      private

      def entity_class
        raise "Subclasses must implement this method"
      end

      def available_entities
        entity_class.all
      end

      def initialize_entity
        @entity = entity_class.find params[:id]
      end
    end
  end
end
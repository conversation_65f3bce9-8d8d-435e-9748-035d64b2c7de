module Api
  class UsersController < ::ApplicationController

    protect_from_forgery with: :exception
  
    def all
      login = ERB::Util.html_escape(params[:login])
      klass = params[:klass].present? ? ERB::Util.html_escape(params[:klass]) : 'User'
      users = klass.constantize.select([:id, :login]).where('login LIKE ?', "%#{login}%")

      render json: users.map { |u| { id: u.id, text: u.login } }
    end
  end
end

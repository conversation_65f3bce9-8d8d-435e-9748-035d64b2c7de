# encoding: utf-8
class UserSessionsController < ApplicationController
  force_ssl only: [:new, :create] if SSL_ENABLED

  before_filter :not_logged_in_required, only: [:new, :create]
  after_filter :enable_followers_module, only: [:create]
  skip_before_filter :store_return_to_location, only: [:new]
  before_filter :allow_cors, only: [:new]

  def new
    load_new_session
    if request.xhr? || params[:xhr].present?
      I18n.locale = Network[params[:network]].locale if params[:network].present?

      set_return_to_location("https://api.avenida.com.ar/manage")
      render_properly partial: "partials/login_form",
        locals: {
          msg: params[:msg].presence,
          return_to: "https://api.avenida.com.ar/manage",
          with_guest: params[:with_guest].present?,
          for_review: params[:for_review].present?
        }
    end
  end

  def create
    @user_session = UserSession.new(user_params.to_h)

    if @user_session.save
      redirect_to('/manage')
    else
      render 'new'
    end
  end

  def new_login
    create
  end

  def destroy
    @user_session = UserSession.find
    @user_session.destroy if @user_session

    clean_return_to_location
    clean_current_actor_id

    redirect_to lux_path, notice: 'You have been logged out.'
  end

  private

  def set_return_to_location(url)
    session[:return_to] = url
  end

  def allow_cors
  #  headers['Access-Control-Allow-Origin'] = 'https://avenida.com.ar'
    headers['Access-Control-Allow-Origin'] = '*'
    headers['Access-Control-Allow-Methods'] = %w{GET}.join(',')
    headers['Access-Control-Allow-Headers'] =
      %w{Origin Accept Content-Type X-Requested-With X-CSRF-Token}.join(',')

    head(:ok) if request.request_method == 'OPTIONS'
  end

  def user_params
    params.require(:user_session).permit(:login, :password)
  end
end

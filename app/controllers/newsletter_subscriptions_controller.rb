class NewsletterSubscriptionsController < ApplicationController
  def create
    network = if Network[@network].thin? then Network.default else @network end
    #NewsletterSubscriptionWorker.perform_async({ email_address: params[:email], status: 'subscribed' }, 'subscribers')
    if request.xhr?
      render nothing: true and return
    else
      flash[:success] = t('social.partials.footer.right.subscription_request_sent')
      redirect_to_previous_location
    end
  end
end

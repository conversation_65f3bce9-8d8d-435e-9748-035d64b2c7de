require 'digest/md5'
# encoding: utf-8
class Attachment::PicturesController < ApplicationController

  protect_from_forgery with: :exception
  
  skip_before_filter :authenticate, only: :create
  def show
    @picture = Attachment::Picture.find(params[:id])
  end

  def new
    if params[:contest]
      @contest_id = params[:contest].to_i
    end
    @picture = Attachment::Picture.new
  end

  def create
    picture = Attachment::TempPicture.new
    params[:Filedata].content_type = MIME::Types.type_for(params[:Filedata].original_filename)[0].to_s
    picture.photo = params[:Filedata]
    picture.token = Digest::MD5.hexdigest(current_user.to_s + '-' + picture.photo_file_name.to_s + '-' + rand.to_s + Time.now.to_i.to_s)
    picture.ip = real_remote_ip
    if picture.valid?
      picture.save
      render :text => ['OK', picture.id, ERB::Util.html_escape(picture.photo.url(:st, false))].join("-")
      Attachment::TempPicture.queue_move_to_s3(picture.id, destination_class_name)
    else
      Rails.logger.info "Errores de la picture a guardar:" + picture.errors.inspect
      render :text => ['ERROR', picture.photo_file_name, picture.photo_file_size, picture.processing].join(" - ")
    end
    rescue Exception => e
      render :text => e.message || 'ERROR'
  end

  private

  def destination_class_name
    "Attachment::#{controller_name.classify}"
  end

end

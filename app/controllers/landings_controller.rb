class LandingsController < ApplicationController
  before_filter :force_request_format_to_html,:load_landing

  def show
    redirect_to @landing.path
  end

  private

  def force_request_format_to_html
    request.format = :html
  end

  def load_landing
    network = params[:network] || @network
    @landing = if params[:id].present?
      ::Pages::Landing.avenidable.active_for_network(@network).where(id: params[:id]).first
    else
      ::Pages::Landing.avenidable.home_for_network(network).last
    end

    if @landing.nil?
      redirect_to root_url(purged_params) and return
    end
  end

  def purged_params
    params.reject{ |key, _value| %(network path controller action id).include?(key.to_s) }
  end
end

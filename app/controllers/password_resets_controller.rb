class PasswordResetsController < ApplicationController
  before_filter :not_logged_in_required
  before_filter :load_user_using_perishable_token, only: [:edit, :update]

  def create
    unless @user = User.find_by_email(params[:identifier]).presence
      @user = User.find_by_login(params[:identifier]).presence
    end

    if @user
      @user.reset_perishable_token!
      UserMailer.password_reset_email(@user).deliver!
      redirect_to :done_password_resets
    else
      flash[:error] = t('controllers.password_resets.user_not_found')
      render action: 'new'
    end
  end

  def update
    @user.password = params[:user][:password]
    @user.password_confirmation = params[:user][:password_confirmation]

    if @user.save
      flash[:success] = t('controllers.password_resets.password_updated')
      redirect_to lux_path
    else
      render action: 'edit'
    end
  end

  private

  def load_user_using_perishable_token
    @user = User.find_using_perishable_token(params[:id], 1.day)

    unless @user
      flash[:error] = t('controllers.password_resets.token_not_found')
      redirect_to :new_password_reset
    end
  end
end
class GatewayCredentialController < ApplicationController
    def create
      @gateway_credentials = GatewayCredential.new(gateway_credentials_params)
      @gateway_credentials.save
    end

    def update
      @gateway_credentials.update_attributes(gateway_credentials_params)
    end

    def destroy
   		@gateway_credentials.destroy
    end

    private

    def gateway_credentials_params
      params.require(:gateway_credentials).permit(:id, :store_id, :firstdata_store, :user, :password, :certificate_password, :categories)
    end
end

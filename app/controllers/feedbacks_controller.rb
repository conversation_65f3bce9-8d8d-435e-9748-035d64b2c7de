class FeedbacksController < ActionController::Base

  protect_from_forgery with: :exception

  def create
    items = Mkp::CartItem.find params[:ids].split(',')
    checkout_feedback = Feedback::Checkout.generate(customer, items, params[:did])
    FeedbackNotifierWorker.perform_in(15.seconds, checkout_feedback.id)

    render nothing: true, status: :ok
  end

  protected

  def customer
    ERB::Util.html_escape(params[:ctype]).constantize.find(params[:cid])
  end
end

class PartialSignupsController < ApplicationController

  def create
    sign_up = PartialSignup.new(params[:partial_signup])
    if sign_up.save
      redirect_to sign_up_path(email: sign_up.email)
    else
      sign_up = PartialSignup.find_by_email(params[:partial_signup][:email])
      if sign_up.present?
        redirect_to sign_up_path(email: sign_up.email)
      else
        redirect_to sign_up_path
      end
    end
  end
end

class SettingsController < ApplicationController
  before_filter :login_required
  before_filter :require_logged_in_user_can_manage_admins,
                only: [:add_admin, :admins, :remove_admin]

  def account
    @user = current_user
  end

  def add_admin
    user = User.where('id = ?', params[:id]).first

    if user
      if current_logged_in_user.owns_a_brand?
        assign_as_brand_admin(user)
        assign_as_shop_admin(user)
      end
    end

    redirect_to settings_admins_path
  end

  def admins
    @admins = []
    @admins +=  current_user.owned_brand.managers if current_user.owns_a_brand?
    @admins += current_user.owned_shop.managers if current_user.owns_a_shop?
    @admins.sort! { |a, b| a.login.downcase <=> b.login.downcase }.uniq!

    @available_roles = []
    @available_roles << 'community' if current_user.owns_a_brand?
    @available_roles << 'shop' if current_user.owns_a_shop?
    @available_roles << 'all' if @available_roles.many?
  end

  def remove_admin
    user = User.find(params[:id])

    if current_logged_in_user.owns_a_brand?
      remove_as_brand_admin(user)
      remove_as_shop_admin(user)
    end
    redirect_to settings_admins_path
  end

  def update_admin
    role = params[:role]
    admin = User.find(params[:id])

    if role == 'all'
      assign_as_brand_admin(admin) if current_logged_in_user.owns_a_brand?
      assign_as_shop_admin(admin)
    elsif role == 'community'
      assign_as_brand_admin(admin) if current_logged_in_user.owns_a_brand?
    elsif role == 'shop'
      assign_as_shop_admin(admin)
      remove_as_brand_admin(admin) if current_logged_in_user.owns_a_brand?
    end

    redirect_to settings_admins_path
  end

  def update_account
    @user = current_user

    if @user.update_attributes(params[:user])
      flash_update_success('account')
      redirect_to :back
    else
      render 'account'
    end
  end

  def password
    @user = current_user
  end

  def update_password
    @user = current_user
    current_password = params[:user].delete(:current_password)

    if @user.valid_password?(current_password)
      if @user.update_attributes(params[:user])
        flash_update_success('password')
        redirect_to :back
      else
        render 'password'
      end
    else
      @user.errors.add(:current_password)
      render 'password'
    end
  end

  def orders
    @user = current_user
    @orders = @user.orders.where('created_at > ?', 2.years.ago).order('created_at DESC')
  end

  def apps
    @user = current_user
    @twitter_auth = Authentication::Twitter.of(current_user)
    @facebook_auth = Authentication::Facebook.of(current_user)
  end

  def widgets
    @user = current_user
    @shop = @user.owned_shop if @user.owns_a_shop?
  end

  private

  def remove_as_shop_admin(user)
    shop = current_logged_in_user.owned_shop
    shop.admins.delete(user) if shop
  end

  def remove_as_brand_admin(user)
    brand = current_logged_in_user.owned_brand
    brand.admins.delete(user) if brand
  end

  def assign_as_shop_admin(user)
    shop = current_logged_in_user.owned_shop
    shop.assign_merchant(user) if shop
  end

  def assign_as_brand_admin(user)
    brand = current_logged_in_user.owned_brand

    if brand && !brand.can_be_managed_by?(user)
      brand.admins << user
      brand.save!
    end
  end

  def flash_update_success(attr)
    flash[:success] = t("settings.#{attr}.update_success")
  end

  def require_logged_in_user_can_manage_admins
    unless current_user.owns_a_brand? or current_user.owns_a_shop?
      not_found
    end
  end
end

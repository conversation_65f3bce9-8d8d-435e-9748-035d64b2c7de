# Statics pages controller
# Pages to be served defined on locales files /config/locales/pages/en.yml

class PagesController < ApplicationController
  layout 'pages'

  before_filter :check_user_has_network
  before_filter :set_page_name
  before_filter :serve_pushstate

  # Renders a network-specific view, if there exists a version of the view
  # suffixed with the network.
  #
  # For example, if the current network is 'ar', the view 'contact_ar' will be
  # rendered. If no localized view is found, the normal render behavior takes
  # place.
  def render(*args)
    network = (Network[@network].thin? ? Network.default : @network).downcase

    network_template = "pages/#{@page_name}_#{network}"
    if template_exists?(network_template)
      super(network_template, *args)
    elsif Network[@network].thin? && template_exists?("pages/#{@page_name}_ar")
      network_template = "pages/#{@page_name}_ar"
      super(network_template, *args)
    elsif ( Network[@network].default? || Network[@network].thin? ) && template_exists?("pages/#{@page_name}")
      super(*args)
    end
  end

  def stores; end

  private

  def serve_pushstate
    if request.xhr?
      render layout: nil and return
    end
  end

  def check_user_has_network
    if params[:network].blank?
      params[:network] = @network.downcase
      redirect_to url_for(params)
    end
  end

  def set_page_name
    @page_name = action_name
  end
end

class ApplicationController < ActionController::Base
  layout 'application'

  protect_from_forgery with: :exception
  
  # Concerns need it by the before_filter
  # explicitly set by this controller
  #
  # Ordered by the calls on the before filter
  include NetworkExposure
  include SetLocale
  include UserSessionator
  include ReturnLocationStorer
  include CartHandler
  include NetworkAffiliates

  # Concerns that are need it by inherit controllers
  # or inside some actions, views or helpers
  include CurrencyExposure # CAVI ->  To give support to some controller that depends on che currency
  include RenderProperly   # CAVI ->  To give the ability to properly render on devices and v5 style
  include ErrorsHandler    # CAVI ->  To give support to #not_found call

  # Filters provided by the included concerns
  prepend_before_filter :set_network
  before_filter :set_country_cookie,
                :set_locale,
                :load_new_session,
                :store_return_to_location,
                :bootstrap_cart,
                :track_network_affiliate,
                :load_avenida_store

  def login_required(page_params = {})
    redirect_to login_url(page_params) unless logged_in?
  end

  def not_logged_in_required
    redirect_to :lux if logged_in?
  end

  protected

  ##
  # Used only in this controller:
  # - AuthenticationsController
  # - UserSessionsController
  # so maybe is nice to move this method somewhere else, not sure.
  def enable_followers_module
    session[:followers_module_disabled] = false
  end

  def load_avenida_store
    @current_store = ::Mkp::Store.find_by_name("avenida")
  end
end

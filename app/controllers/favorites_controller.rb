class FavoritesController < ApplicationController

  before_filter :set_favoritable_model

  def create
    @model.favorited_by(current_user)
    render text: 'Item was favorited'
  end

  def destroy
    @model.unfavorited_by(current_user)
    render text: 'Item was unfavorited'
  end

  private

  def set_favoritable_model
    klass = get_favoritable_class
    if klass.nil?
      render nothing: true, status: :unprocessable_entity
      return
    end
    id = params[params[:id_param]]
    @model = klass.find(id)
  rescue ActiveRecord::RecordNotFound
    render nothing: true, status: :unprocessable_entity
  end

  def get_favoritable_class
    case params[:favoritable_type]
    when 'Mkp::Product'    then Mkp::Product
    when 'Social::Whatsup' then Social::Whatsup
    when 'Social::Album'   then Social::Album
    else nil
    end
  end
end

class WidgetsController < ActionController::Base
  include ErrorsHandler
  include CurrencyExposure
  include NetworkExposure
  include SetLocale

  layout 'blank'

  protect_from_forgery with: :exception

  before_filter :simplified_set_network,
                :set_locale

  def shop_products
    @shop = Mkp::Shop.find(params[:shop_id], conditions: { network: @network })

    @orientation = params[:orientation] == 'horizontal' ? 'horizontal' : 'vertical'

    limit = params[:limit].present? ? params[:limit].to_i : 4
    limit = 4 if limit > 50
    @variants = @shop.variants.limit(limit)

    if params[:show] == 'on_sale'
      @variants = @variants.on_sale
    elsif params[:show] == 'newest'
      @variants = @variants.order('mkp_variants.created_at DESC')
    end

    if @variants.size < limit
      diff = limit - @variants.size
      @variants = @variants + @shop.variants.limit(diff)
    end
  end

  def olark_chat
    not_found if not request.xhr?
    render layout: false
  end
end

# Currency exposure based on full/thin networks
module CurrencyExposure
  extend ActiveSupport::Concern

  included do
    helper_method :current_currency
    helper_method :current_currency_format
  end

  protected

  # Get current currency instance
  #
  # Examples
  #
  #  current_currency_display_format #=> Currency
  #
  def current_currency
    @current_currency ||= set_current_currency
  end

  def set_current_currency
    Mkp::Currency.find current_full_network.currency_id
  end

  # Get current currency display format
  #
  # Examples
  #
  #  current_currency_display_format #=> '$', 'USD', 'ARS'
  #
  def current_currency_format
    @current_currency_format ||= set_current_currency_format
  end

  def set_current_currency_format
    if current_network.thin?
      current_currency.identifier
    else
      current_currency.symbol
    end
  end
end

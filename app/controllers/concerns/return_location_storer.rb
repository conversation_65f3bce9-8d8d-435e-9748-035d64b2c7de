# Internal: Store return to location on session
module ReturnLocationStorer
  extend ActiveSupport::Concern

  protected

  def redirect_to_previous_location
    if session[:return_to].present?
      redirect_to session[:return_to]
    elsif request.env['HTTP_REFERER'].present?
      redirect_to :back
    else
      redirect_to root_url
    end
  end

  def store_return_to_location
    if !logged_in? && request.get? && !request.xhr?
      session[:return_to] = request.url
    end
  end

  def clean_return_to_location
    session.delete(:return_to)
    nil
  end
end

# Internal: For debugging purpose
# usually we shouldn't have to include this module
module LoggerTracer
  extend ActiveSupport::Concern

  included do
    helper_method :logger_trace_bullet
  end

  protected

  def logger_trace_bullet(object, comment = '')
    Rails.logger.info("TRACE_BULLET : ")
    Rails.logger.info("TRACE_BULLET : @========================================================================================================@")
    Rails.logger.info("TRACE_BULLET : @____________________ TRACING: #{object.class == String ? object : object.class.to_s}#{ ': '+comment unless comment.blank?} ____________________@")
    Rails.logger.info("TRACE_BULLET : @========================================================================================================@")
    Rails.logger.info("TRACE_BULLET : ")
  end

end

# Internal: Manage user actors within the UserSessionator Concern
module UserActorize

  protected

  def set_current_actor_id(id)
    session[:current_actor_id] = id
  end

  def current_actor_id
    session[:current_actor_id]
  end

  def clean_current_actor_id
    session.delete(:current_actor_id)
  end

  def current_actor
    return @current_actor if @current_actor.present?
    if current_actor_id && current_logged_in_user_can_manage_current_actor?
      @current_actor ||= User.find(current_actor_id)
    end
  end

  def current_logged_in_user_can_manage_current_actor?
    current_logged_in_user.managed_users.compact.include?(current_actor_id)
  end

end

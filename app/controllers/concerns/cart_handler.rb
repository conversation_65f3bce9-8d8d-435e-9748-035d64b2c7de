module CartHandler
  extend ActiveSupport::Concern

  protected

  # This method is intended to be used on a before_filter
  # Examples
  #
  #   before_filter :bootstrap_cart
  def bootstrap_cart
    # We should remove this call
    # in a month since went live
    deprecated_cart_cookie_normalization

    cart = current_user.present? &&
              current_user.get_or_create_regular_cart(@network)

    @bootstrap_cart = Mkp::BootstrapCart.cookie_load(cookies[:cart],
                                                     cookies.signed[:cart_owner],
                                                     @network,
                                                     cart,
                                                     current_user.try(:id))

    if current_user.present?
      cookies.signed[:cart_owner] = \
        hashify_cart_owner.merge({ "#{@network}" => current_user.id }).to_json
    end

    cart_contents = { "#{@network}" => @bootstrap_cart.simple_items }
    cookies[:cart] = cart_contents.to_json

  rescue JSON::ParserError
    cookies[:cart] = { "#{@network}" => [] }.to_json
    cookies.delete(:cart_owner)
  end

  private

  # We should remove this method
  # in a month or two since went live
  def deprecated_cart_cookie_normalization
    return if cookies[:mkp_cart].nil?
    cookies[:cart] = JSON.parse(cookies.delete(:mkp_cart))
    cookies[:cart_owner] = cookies.delete(:mkp_cart_ow)
  end

  def hashify_cart_owner
    if cookies.signed[:cart_owner].is_a?(String)
      JSON.parse(cookies.signed[:cart_owner])
    else
      {}
    end
  end
end

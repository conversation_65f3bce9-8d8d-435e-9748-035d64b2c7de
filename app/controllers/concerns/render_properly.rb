##
# Internal: Give the ability to properly render an action
# on the correct version and device
#
module RenderProperly
  extend ActiveSupport::Concern

  DISPLAY_VERSION = 'v5'.freeze

  def render_properly(view, options = {}, *args)
    options[:version] ||= DISPLAY_VERSION
    _lyt = options.key?(:layout) ? options[:layout] : _layout
    if browser.mobile?
      options[:device] = "mobile"
      if template_exists?(lyt = "layouts/#{options[:version]}/#{options[:device]}/#{_lyt}")
        options[:layout] = lyt
      elsif template_exists?(lyt = "layouts/#{options[:version]}/#{_lyt}")
        options[:layout] = lyt
      end
    else
      if template_exists?(lyt = "layouts/#{options[:version]}/#{_lyt}")
        options[:layout] = lyt
      end
    end
    render view, options, *args
  end
end

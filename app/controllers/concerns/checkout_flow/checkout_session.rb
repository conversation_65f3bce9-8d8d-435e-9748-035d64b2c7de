module CheckoutFlow

  extend ActiveSupport::Concern

  included do
    helper_method :current_customer
  end

  class CheckoutSession
    attr_reader :session

    def initialize(session)
      @session = session
    end


    def cart
      @cart ||= CheckoutCart.new(self)
    end

    def customer
      return if @session[:customer].blank?
      customer_id, customer_type = @session[:customer].values_at(:id, :type)
      begin
        "Mkp::#{customer_type}".constantize.find(customer_id)
      rescue Exception => e
        User.find_by(id: customer_id, type: customer_type)
      end
    end

    def payment
      @session[:payment]
    end

    def set_payment(payment_data)
      @session.merge!(payment: payment_data)
    end

    def remove_coupon
      @session.delete(:coupon)
    end

    alias_method :payments, :payment
    alias_method :set_payments, :set_payment
  end

  protected

  def current_customer
    @checkout_session.customer if @checkout_session.present?
  end

end

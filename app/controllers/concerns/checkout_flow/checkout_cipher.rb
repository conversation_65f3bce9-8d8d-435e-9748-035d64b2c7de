module CheckoutFlow
  class CheckoutCipher

    CIPHER_CLASS = OpenSSL::Cipher::AES256
    MODE = :CBC

    def self.get_random_iv
      OpenSSL::Random.random_bytes(CIPHER_CLASS.new(MODE).iv_len)
    end

    def initialize(key, iv)
      @cipher = CIPHER_CLASS.new(MODE)
      @key = key
      @iv = iv
    end

    def protect(data)
      @cipher.encrypt
      process(data)
    end

    def unprotect(data)
      @cipher.decrypt
      process(data)
    end

    private

    def process(data)
      set_cipher_key_and_iv
      @cipher.update(data) + @cipher.final
    end

    def set_cipher_key_and_iv
      @cipher.key = @key
      @cipher.iv = @iv
    end
  end
end

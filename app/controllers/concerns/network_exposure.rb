# Internal: Set network based on active networks
module NetworkExposure
  extend ActiveSupport::Concern

  LOCAL_OR_INVALID_REMOTE_IPS = [ '127.0.0.1', '0.0.0.0']

  included do
    helper_method :current_network
    helper_method :current_full_network
    helper_method :real_remote_ip
  end

  protected

  # This method is intended to be used on a before_filter
  # Examples
  #
  #   before_filter :set_network
  def set_network
    network = params[:network]

    @network = if network.present? && Network[network].active?
      network.upcase
    elsif cookies[:network].present? && Network[cookies[:network]].active?
      cookies[:network]
    elsif current_user.present? && current_user.network.present? && Network.all_visible.include?(current_user.network)
      current_user.network
    elsif !!defined?(current_customer) && current_customer.present? && current_customer.network.present? && Network.all_visible.include?(current_customer.network)
      current_customer.network
    elsif is_valid_remote_ip?(real_remote_ip) && locator.localized? && locator.visible_network?
      locator.country_code
    else
      Network.default
    end

    @network
  end

  # This method is intended to be used on a before_filter
  # Examples
  #
  #   before_filter :simplified_set_network
  def simplified_set_network
    if params[:network].present? && Network[params[:network]].active?
      @network = params[:network].upcase
    else
      not_found
    end
  end

  # This method is intended to be used on a before_filter
  #
  # Examples
  #
  #   before_filter :set_country_cookie
  #
  def set_country_cookie
    return if cookies[:country].present? || !locator.localized?
    cookies[:country] = locator.country_name
  end

  def current_full_network
    current_network.thin.present? ? current_network : Network[Network.default]
  end

  def current_network
    @current_network ||= Network[@network]
  end

  def real_remote_ip
    return @real_remote_ip if defined?(@real_remote_ip)
    @real_remote_ip = request.remote_ip if !request.ssl? && is_valid_remote_ip?(request.remote_ip)
    @real_remote_ip ||= request.env['HTTP_X_FORWARDED_FOR'] if request.env['HTTP_X_FORWARDED_FOR'].present? &&
                                                               is_valid_remote_ip?(request.env['HTTP_X_FORWARDED_FOR'])
    @real_remote_ip ||= request.env['HTTP_X_REAL_IP'] if request.env['HTTP_X_REAL_IP'].present? &&
                                                         is_valid_remote_ip?(request.env['HTTP_X_REAL_IP'])
  end

  private

  def locator
    return @locator if @locator.present?
    @locator = Locator.new(real_remote_ip)
    set_country_cookie
    @locator
  end

  def is_valid_remote_ip?(ip)
    (ip =~ Resolv::IPv4::Regex) && !LOCAL_OR_INVALID_REMOTE_IPS.include?(ip)
  end
end

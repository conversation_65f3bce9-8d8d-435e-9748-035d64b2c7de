module CheckoutProcessable
  extend ActiveSupport::Concern

  def collect_payment
    # @payment = ::Mkp::PurchaseProcessor.collect_payment!(@checkout_cart)
    @payment = PaymentManager::CollectPayment.call(checkout_cart: @checkout_cart)
    Rails.logger.info(@payment)
  end

  def create_order(current_store_id = nil)
    @order = ::Mkp::PurchaseProcessor.create_order!(current_customer, @checkout_cart, @payment, current_store_id, @points_payments)
    Rails.logger.info(@order)
  end

  def get_payment_error_message(payment = nil)
    payment = payment || @payment.first
    error = payment.get_error
    if error.present?
      if (last_part = get_mapped_error(error)).blank?
        last_part = error[:status_detail].present? ? I18n.t("v5.controllers.checkout.payment.errors.#{error[:status_detail]}") : I18n.t("v5.controllers.checkout.payment.errors.3028")
      end
      last_part
    else
      I18n.t('v5.controllers.checkout.payment.default_msg')
    end
  end

  def get_mapped_error(error)
    external_id = error[:status_detail]
    return if external_id&.to_s.match(/^(\d)+$/).blank?

    payment_params = params[:payment]
    mapped_error = Mkp::GatewayError.where(store_id: @current_store.id,
                                           external_id: external_id.to_i).first
    mapped_error&.message
  end

  def missing_payment_error_message
    I18n.t('v5.controllers.checkout.payment.missing_msg')
  end

  def process_payment_params
    payment_params = params[:payment]
    if (gateway = ::Avenida::Payments.get_instance_of(payment_params[:gateway], @network)).present?
      payment_allowed_params = gateway.verify_payment_params(payment_params)
      @checkout_session.set_payment(payment_allowed_params)
    else
      redirect_to mkp_checkout_ride_path(
        @network.downcase, { fid: @checkout_flow_id }
      ) and return
    end
  end

  def process_payment_angular
    payment_params = params[:payment]
    # Deberia chequear que sea uno de Network[@network].payment_gateways?
    # gateway = ::Avenida::Payments.get_instance_of(payment_params[:gateway], @network)
    gateway      = payment_params[:gateway_class]
    gateway_name = payment_params[:gateway]
    ensure_gateway_is_enabled!(gateway_name)
    if gateway.present?
      gateway = GatewayAlternativeStrategy.get_main_gateway(@checkout_cart.store, gateway_name)
      gateway = Avenida::Payments::Bogus if @checkout_cart.total.zero?
      payment_allowed_params = gateway.verify_payment_params(payment_params)
      @checkout_session.set_payments(payment_allowed_params)
    else
      render json: {error: "An error has ocurred try to process your payment"}, status: 200
      return
    end
  end

  def create_and_associate_shipments
    current_address = current_customer.addresses.find(@checkout_cart.address_id)
    choosen_delivery_options = @checkout_cart.choosen_delivery_options

    @shipments = ::Mkp::PurchaseProcessor.create_shipments!(@order, current_address, choosen_delivery_options)
    # choosen_delivery_options = @checkout_cart.choosen_delivery_options
    # choosen_delivery_options.each_with_object([]) {|d, s| d[:delivery_option] = ::Mkp::Shipping::DeliveryOption::Shipping.new(d[:delivery_option])}
    @order.generate_brukman_order
  end

  def suborders_coupon_generator
    ::Mkp::PurchaseProcessor.generate_coupon_discount_for_suborder(@order)
  end

  def set_gross_total
    @order.reload
    @order.update(gross_total: @order.total)
  end

  #### WIP Modo Payments
  def associate_payments(order, checkout_cart, payments, points_payments)
    Mkp::PurchaseProcessor.associate_payments(order, checkout_cart, payments, points_payments)
  end

  def run_hooks_before_confirmation(order)
    Mkp::PurchaseProcessor.run_hooks_before_confirmation(order)
  end

  def run_hooks_after_confirmation(order)
    Mkp::PurchaseProcessor.run_hooks_after_confirmation(order)
  end

  def ensure_gateway_is_enabled!(gateway_name)
    raise Api::AngularApp::V1::InvalidGatewayException, 'Medio de pago no habilitado', [] unless @current_store.retrieve_gateways_array.include? gateway_name
  end

  def exit_payment_process
    if @points_payments.present?
      @points_payments.each { |payment| payment.cancel!(@current_store) }
    end

    cancelled_payment = @payment&.detect(&:cancelled?)
    cancel_existing_payments

    # error = @payment&.cancelled? ? get_payment_error_message : missing_payment_error_message
    error = cancelled_payment.present? ? get_payment_error_message(cancelled_payment) : missing_payment_error_message

    ### borrar esta linea cuando se implemente en metodo payment
    if @checkout_cart.choosen_delivery_options.blank?
      error = 'Debes elegir opciones de envío'
    end
    ###

    if @payment.present? && @payment.any? { |payment| payment.get_error[:status] == 'gateway_error' }
      ExceptionNotifier.notify_exception(StandardError.new, data: {
          error: error,
          payment: @payment || '',
          checkout_cart: @checkout_cart,
          params: params.dup,
          session: session.dup
      })
    end

    render_error(error)
  end
end

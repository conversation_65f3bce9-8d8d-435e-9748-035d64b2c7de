module PendingPaymentRefreshable
  class NoStockError < StandardError; end;
  extend ActiveSupport::Concern

  def refresh_and_confirm_payment(payment)
    old_status = payment.status
    order = payment.sale_item
    payment.refresh
    if payment.collected? && (payment.status != old_status)
      begin
        check_stock!(order) unless PENDING_PAYMENTS_AS_CONFIRMED
        shipment_confirm(order)
        unless PENDING_PAYMENTS_AS_CONFIRMED
          complete_after_checkout(order)
        end
      rescue NoStockError => e
        payment.cancel_by_store!(order.store) # unless PENDING_PAYMENTS_AS_CONFIRMED
      end
    end
  end

  private

  def check_stock!(order)
    service = Checkout::ValidateProductAvailability.new(order.items, order.store)
    service.perform
    raise NoStockError, service.error, [] unless service.valid
  end

  def shipment_confirm(order)
    order.shipments.each do |shipment|
      ::Mkp::StatusChange::EntityStatusManage.status_change(shipment, 'unfulfilled')
    end
  end

  def complete_after_checkout(order)
    ::Mkp::PurchaseProcessor.run_hooks_update_stock(order)
    ::Mkp::PurchaseProcessor.run_hooks_after_confirmation(order)

    # extracted from run_integration_services method
    order.suborders.each do |suborder|
      Lux::IntegrationSaleNotificationWorker.perform_async(suborder.shop.id, suborder.id, "Mercadolibre")
    end
    ###
  end

end

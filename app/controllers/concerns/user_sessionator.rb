# Internal: Manage user sessions
module UserSessionator
  extend ActiveSupport::Concern
  include UserActorize

  included do
    helper_method :current_user,
                  :logged_in?,
                  :current_logged_in_user
  end

  protected

  def current_user
    return nil unless logged_in?
    current_actor || current_logged_in_user
  end

  def current_logged_in_user
    @current_logged_in_user ||= current_user_session && current_user_session.user
  end

  def load_new_session
    @user_session = UserSession.new unless logged_in?
  end

  def logged_in?
    !!(current_logged_in_user)
  end

  private

  def current_user_session
    # This sentences is because the weird
    # of Authlogic::Session::Activation::NotActivatedError
    Authlogic::Session::Base.controller =
      Authlogic::ControllerAdapters::RailsAdapter.new(self)

    @current_user_session ||= UserSession.find
  end

end

module Mkp
  module CatalogLegacy
    extend ActiveSupport::Concern
    PARAMS_TO_PROPAGATE = UrlMapperHelper::FILTERS_KEYS.dup.concat([:p, :o, :s]).freeze

    def handle_redirect!(custom_params: nil)
      @params = custom_params || params
      models = retrieve_models

      UrlMapper.map_out(models, @network) unless UrlMapper.path_exist?(models, @network)
      canonical_url = UrlMapperHelper.absolute_mapper_path(
        models,
        @network,
        extra_params_for_redirect
      )

      redirect_to canonical_url, status: :moved_permanently
    end

    def retrieve_models(custom_params = nil, store = nil)
      @params = (custom_params || params) unless @params

      @store = store
      @categories = get_categories
      @manufacturers = get_manufacturers
      [@manufacturers, @categories].compact.flatten
    end

    private

    def extra_params_for_redirect
      @params.select { |k,v| PARAMS_TO_PROPAGATE.include?(k.to_sym) }
    end

    def get_categories
      return [] if (slugs = @params[:c]).blank?

      categories_ids = get_model_ids(Mkp::Category, slugs, @network)
      if @store.nil?
        Mkp::Category.where(id: categories_ids)
      else
        @store.categories.where(id: categories_ids)
      end
    end

    def get_manufacturers
      return [] if (slugs = @params[:b]).blank?

      manufacturers_ids = get_model_ids(Mkp::Manufacturer, slugs)

      Mkp::Manufacturer.where(id: manufacturers_ids)
    end

    def get_model_ids(model_class, slugs, network = nil)
      Array.wrap(slugs).map do |slug|
        (slug.to_i > 0) ? slug.to_i : RedisSlug.find(model_class, slug, network)
      end.compact.uniq.map(&:to_i)
    end

  end
end

module Mkp
  module AuthorizeTodoPago
    extend ActiveSupport::Concern

    def successful_authorization
      # no-op
    end

    def rejected_authorization
      # no-op
    end

    def authorize_payment
      header = { 'Authorization' => TODOPAGO_APP_KEY }

      connector = TodoPago::TodoPagoConector.new(header, TODOPAGO_ENDPOINT)

      commerce = {
        Security: TODOPAGO_SECURITY,
        Merchant: TODOPAGO_MERCHANT,
        EncodingMethod: 'XML',
        URL_OK: successful_authorization_url
      }

      operation = {
        MERCHANT: TODOPAGO_MERCHANT,
        OPERATIONID: '0001',
        CURRENCYCODE: '032',
        AMOUNT: params[:total],
        EMAILCLIENTE: params[:email]
      }
      operation[:CSMDD13] = 'retiro en sucursal' if params[:is_pickit]

      response = connector.sendAuthorizeRequest(commerce, build_parameters(operation))

      response = parse_response(response)

      if response["status_code"].to_i == -1
        render json: response.to_json
      else
        head 400
      end
    rescue
      ExceptionNotifier.notify_exception(StandardError.new, data: {
        response: response.to_json
      })
      head 400
    end

    def successful_authorization_url
      Rails.application.routes.url_helpers.mkp_checkout_successful_authorization_url(host: HOSTNAME, network: 'ar')
    end

    def parse_response(response)
      JSON.parse(response)['envelope']['body']['send_authorize_request_response']
    end

    def build_parameters(operation)
      operation.merge({
        ### Facturación
        CSBTCITY: params[:city],
        CSBTCOUNTRY: params[:country],
        # ID del comprador
        CSBTCUSTOMERID: '12345',
        CSBTIPADDRESS: request.remote_ip,
        CSBTEMAIL: params[:email],
        CSBTFIRSTNAME: params[:first_name],
        CSBTLASTNAME: params[:last_name],
        CSBTPHONENUMBER: params[:phone_number],
        CSBTPOSTALCODE: params[:zip_code],
        CSBTSTATE: params[:state],
        CSBTSTREET1: params[:address1],
        CSBTSTREET2: params[:address2],

        CSPTCURRENCY: 'ARS',
        CSPTGRANDTOTALAMOUNT: params[:total],
        # Canal de venta
        CSMDD6: 'Web',

        ### Envío
        CSSTCITY: params[:city],
        CSSTCOUNTRY: params[:country],
        CSSTEMAIL: params[:email],
        CSSTFIRSTNAME: params[:first_name],
        CSSTLASTNAME: params[:last_name],
        CSSTPHONENUMBER: params[:phone_number],
        CSSTPOSTALCODE: params[:zip_code],
        CSSTSTATE: params[:state],
        CSSTSTREET1: params[:address1],

        ### Datos del producto
        CSITPRODUCTCODE: "default", # TODO: ???
        CSITPRODUCTDESCRIPTION: 'Description', # TODO: ???
        CSITPRODUCTNAME: 'Product name', # TODO: ???
        CSITPRODUCTSKU: 'SKU1234', # TODO: ???
        CSITUNITPRICE: params[:total], # TODO: ???
        CSITQUANTITY: '1', # TODO: ???
        CSITTOTALAMOUNT: params[:total]
      })
    end
  end
end

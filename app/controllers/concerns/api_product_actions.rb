module ApiProductActions
  extend ActiveSupport::Concern
  
  private
  
  def post_create_actions(product_attr_builder)
    @entity.variants.each do |variant|
      variant.gp_sku = nil
      variant.send(:build_gp_sku, [])
      variant.save!
    end
    post_general_actions(product_attr_builder)
  end
  
  def post_update_actions(product_attr_builder)
    ::Mkp::Variant.where(id: @variants_to_delete).destroy_all if @variants_to_delete.present?
    ::Mkp::Package.where(id: @packages_to_delete).destroy_all if @packages_to_delete.present?
    post_general_actions(product_attr_builder)
  end
  
  def post_general_actions(product_attr_builder)
    product_attr_builder.relate_images(@entity.id)
    @entity.recreate_variants_visibility
    @entity.set_approved_for_stores(true)
  end
  
  def initialize_variants_to_delete(product_attr_builder)
    if product_attr_builder.result[:variants_attributes]
      sku_to_update = product_attr_builder.result[:variants_attributes].map {|each| each[:sku]}
      @variants_to_delete = (@entity.variants.select {|variant| (sku_to_update.include? variant.sku) || variant.sku.nil? }).map(&:id)
    end
  end
  
  def initialize_package_to_delete(product_attr_builder)
    if product_attr_builder.result[:packages_attributes]
      @packages_to_delete = @entity.packages.map(&:id)
    end
  end
end
# Internal: Network affiliates logic
module NetworkAffiliates
  extend ActiveSupport::Concern

  AffiliateData = Struct.new(:type, :id)

  protected

  def track_network_affiliate
    if affiliate_data_in_request?
      delete_previous_affiliate_data
      set_affiliate_cookie
    end
  end

  def get_affiliate_cookie
    @cookie_affiliate ||= network_affiliates.map do |affiliate_name|
      return unless (id = cookies.signed[cookie_key_name(affiliate_name)]).present?
      AffiliateData.new(affiliate_name, id)
    end.compact.first
  end

  def remove_affiliate_cookie(affiliate_name)
    cookies.delete(cookie_key_name(affiliate_name))
  end

  private

  def affiliate_data_in_request?
    affiliates_param_in_request.present?
  end

  def affiliates_param_in_request
    return @affiliates_param_in_request unless @affiliates_param_in_request.nil?
    @affiliates_param_in_request = (params.keys & affiliates_param_name.values)
  end

  def affiliates_param_name
    @affiliates_param_name ||= network_affiliates.each_with_object({}) do |affiliate_name, data|
      return unless (affiliate = AFFILIATES[affiliate_name.downcase]).present?
      data[affiliate_name.downcase] = affiliate[@network.downcase]["param_name"]
    end
  end

  def network_affiliates
    @network_affiliates ||= Network[@network].network_affiliates || []
  end

  def delete_previous_affiliate_data
    network_affiliates.each do |affiliate_name|
      remove_affiliate_cookie(affiliate_name)
    end
  end

  def cookie_key_name(affiliate_name, network = @network)
    "#{affiliate_name.downcase}_#{network.downcase}_ref_id".to_sym
  end

  def set_affiliate_cookie
    affiliates_param_name.each do |affiliate_name, param_name|
      key_name = cookie_key_name(affiliate_name)
      cookies.signed[key_name] = { value: params[param_name], expires: 5.days.from_now } if cookies[key_name].blank?
    end
  end

end

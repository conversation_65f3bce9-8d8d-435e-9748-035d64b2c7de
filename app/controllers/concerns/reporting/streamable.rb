module Reporting
  module Streamable
    include ActiveSupport::Concern

    def render_csv(exporter, *args)
      raise MistypeError.new(self, Exporter, __method__) unless exporter.ancestors.include? Exporter

      exporter = exporter.new(*args)

      set_file_headers(exporter.filename)
      set_streaming_headers

      response.status = 200

      self.response_body = exporter.stream
    end

    private

    def set_file_headers(filename)
      file_name = "avenida-#{filename}.csv"
      headers['Content-Type'] = 'text/csv; charset=utf-8; header=present'
      headers['Content-Disposition'] = "attachment; filename=\"#{file_name}\""
    end

    def set_streaming_headers
      headers['Cache-Control'] = 'no-cache'
      headers['X-Accel-Buffering'] = 'no'
      headers.delete('Content-Length')
    end
  end

  class MistypeError < StandardError
    attr_reader :object

    def initialize(object, expected_class, method_name)
      @object = object

      super("The method ##{method_name} called in #{object.class.name} was expecting an exporter kind of: #{expected_class.to_s} Class.")
    end
  end
end

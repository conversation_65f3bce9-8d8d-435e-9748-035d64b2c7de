class DistributionController < ActionController::Base
  include ErrorsHandler
  include NetworkExposure
  include SetLocale

  protect_from_forgery with: :exception

  layout 'distribution'

  before_filter :simplified_set_network, :set_locale

  def index
    @documents = Rails.cache.fetch("#{@network}_distribution_docs_sorted") do
      IssuuDocument.all(pageSize: 30, resultOrder: 'desc', documentSortBy: 'publishDate')
    end
  end

  def subscribe
    if (email = params[:email]).present?
      enroll_user(email)
    end

    # Return happily anyway.
    flash[:success] = I18n.t('distribution.suscription_success')

    redirect_to distribution_index_url(network: @network.downcase)
  end

  private

  def enroll_user(email)
    recipient_id = add_recipient({ email: email })

    if recipient_id.present?
      add_recipient_to_list(recipient_id, 'distribution')
    end
  end

  def add_recipient(recipient_data)
    sendgrid_client.add_recipient(recipient_data)
  end

  def add_recipient_to_list(recipient_id, list_type)
    sendgrid_client.add_recipient_to_list(recipient_id, list_type)
  end

  def sendgrid_client
    @client ||= SendgridClient.new
  end
end

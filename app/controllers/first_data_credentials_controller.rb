class FirstDataCredentialsController < ApplicationController
  def create
    @first_data_credentials = FirstDataCredential.new(first_data_credentials_params)
    @first_data_credentials.save
  end

  def update
    @first_data_credentials.update_attributes(first_data_credentials_params)
  end

  def destroy
    @first_data_credentials.destroy
  end

  private

  def firstdata_credentials_params
    params.require(:first_data_credentials).permit(:id, :store_id, :firstdata_store, :user, :password, :certificate_password, :ssl_cert, :ssl_cert_key, :priority, :category)
  end
end

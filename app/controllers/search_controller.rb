class SearchController < ApplicationController
  PREFILTERS = %w(products community)

  def search
    if params[:query].present?
      if params[:prefilter].present? && params[:prefilter] == 'products'
        redirect_to Mkp::Catalog::UrlHandler.path(query: params[:query],
                                                network: @network.downcase)
        return
      end
    elsif params[:q].present? #IO compatibility search with q param
      params[:query] = params[:q]
      redirect_to url_for(params.except(:q)), status: :moved_permanently
    elsif request.referer.present?
      redirect_to :back
    else
      redirect_to root_url
    end
  end
end

= content_for :title do
  #{@user.full_name} | #{t('.following')}

- content_for :meta do
  %meta{ name: 'robots', content: 'noindex, nofollow' }

.medium-4.columns
  = render partial: 'social/partials/user_profile', locals: { user: @user, user_stats: @user_stats }
.medium-8.columns
  .follow-box
    %h1= t('.following')
    %hr
    .mini-users
      - if @user.followed_users.present?
        - following = @user.followed_users.paginate(:page => 1, :per_page => 10)
        = render partial: 'users/mini_user', collection: following,
                                             as: :user
        - if @user.followed_users.count > 10
          .button-wrapper
            .button.small.show-more{ type: 'button',
                                     'data-current-page' => following.current_page,
                                     'data-total-pages' => following.total_pages,
                                     'data-path' => social_following_path }
              = t('.show_more')
      - else
        %p You don't follow any users yet.
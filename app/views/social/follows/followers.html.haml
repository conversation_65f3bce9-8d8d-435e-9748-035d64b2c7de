= content_for :title do
  #{@user.full_name} | #{t('.followers')}

- content_for :meta do
  %meta{ name: 'robots', content: 'noindex, nofollow' }

.medium-4.columns
  = render partial: 'social/partials/user_profile', locals: { user: @user, user_stats: @user_stats }
.medium-8.columns
  .follow-box
    %h1= t('.followers')
    %hr
    .mini-users
      - if @user.followers.present?
        - followers = @user.followers.paginate(:page => 1, :per_page => 10)
        = render partial: 'users/mini_user', collection: followers,
                                             as: :user
        - if @user.followers.count > 10
          .button-wrapper
            .button.small.show-more{ type: 'button',
                                     'data-current-page' => followers.current_page,
                                     'data-total-pages' => followers.total_pages,
                                     'data-path' => social_followers_path }
              = t('.show_more')
      - else
        %p You don't have any followers yet.
= content_for(:title, "Avenida.com | #{t('.head_title')}")
= content_for :body_class, 'signup-page'

.signup-step.recommendations.small-centered.columns
  .row.signup-step-top
    = image_tag 'social/signup-state-2.png', class: 'state'
  .row.signup-step-content
    .large-12.columns
      %p.msg.hide-before-medium= t('.title')
  .row.signup-step-content
    - if @recommendations[:users].present?
      .large-6.columns
        .block
          .block-title
            %h6= t('.follow_popular_people')
          .block-list
            = render partial: 'users/mini_user', collection: @recommendations[:users], as: :user
    - if @recommendations[:brands].present?
      .large-6.columns
        .block
          .block-title
            %h6= t('.follow_popular_brands')
          .block-list
            = render partial: 'users/mini_user', collection: @recommendations[:brands], as: :user
    - if @recommendations[:sports].present?
      .large-6.columns
        .block
          .block-title
            %h6= t('.choose_popular_sports')
          .block-list
            = render partial: 'sports/mini_sport', collection: @recommendations[:sports], as: :sport
  .row.signup-step-bottom
    .large-12.columns
      %a.button.red.right-arrow{href: social_after_signup_invite_friends_path} #{t('.next')}

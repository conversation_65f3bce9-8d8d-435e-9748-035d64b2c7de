= content_for :body_class, 'signup-page'

.signup-step.invite-friends.small-centered.columns
  .row.signup-step-top
    = image_tag 'social/signup-state-3.png', class: 'state'
  .row.signup-step-content
    .title.large-12.columns
      %h3= "#{t('.title')}, #{current_user.full_name}"
      %p= t('.subtitle')
    .block.large-12.columns
      .invite-facebook
        .button.facebook.wide
          %span
          = t('.invite_facebook')
      .invite-email
        %h4= t('.invite_email')
        = form_tag social_after_signup_send_invitations_path, id: 'send_invitations' do
          = text_field_tag :email, '', placeholder: t('.separate_with_commas')
          = text_area_tag :message, t('.email_message')
          %button.button.small.grey.right
            %span
            = t('.send_message')
  .row.signup-step-bottom
    .large-12.columns
      %a.button.left-arrow{href: social_after_signup_follow_recommendations_path} #{t('.previous')}
      %a.button.red.right-arrow{href: url_for(@after_complete_path)} #{t('.finish')}

= content_for :meta do
  - profile = user.profile
  - desc = profile.bio.presence
  - desc ||= t('.description', { login: user.login, full_name: user.full_name, locale: user.locale })
  %meta{ name: 'description', content: desc }
  %meta{ property: 'og:description', content: desc }

  %meta{ property: 'og:url', content: profiles_url(user.login) }
  %meta{ property: 'og:title', content: user.full_name }
  %meta{ property: 'og:type', content: 'profile' }
  - if user.avatar.persisted? && user.avatar.url(:original).include?('http')
    = render partial: 'partials/meta/og_image',
             locals: { url: user.avatar.url(:original),
                       type: user.avatar.photo.content_type }

  %meta{ property: 'profile:first_name', content: profile.respond_to?(:first_name) ? profile.first_name : profile.name }
  - if profile.respond_to?(:last_name)
    %meta{ property: 'profile:last_name', content: profile.last_name.presence }
  %meta{ property: 'profile:username', content: user.login }

  %meta{ name: 'twitter:card', content: 'summary' }
  - if twitter_username = Network[@network].twitter_account_username
    %meta{ name: 'twitter:site', content: '@'+twitter_username }

- profile = @user.profile
.user-resume
  = render partial: 'partials/v5/avatar', locals: { user: @user, size: :m }
  h1= @user.full_name
  - if profile.respond_to?(:badges) && profile.badges.present?
    .separator
    = render partial: 'social/profiles/v5/partials/user/badges',
             locals: { profile: profile }
  - if profile.bio.present? or profile.website_url.present?
    .separator
  - if profile.bio.present?
    p.desc= profile.bio

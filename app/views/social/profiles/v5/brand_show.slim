- content_for(:body_class) { 'profiles-user_show' }

= render partial: 'social/profiles/v5/partials/brand/meta'
= render partial: 'social/profiles/v5/partials/user/main_cover'
main
  .main-container
    .info-content
      = render partial: 'social/profiles/v5/partials/user/resume'
      - if @feed.present? && @featured_variants.present?
        = render partial: 'social/profiles/v5/partials/user/featured_products'
    .main-content
      - if @feed.present?
        = render partial: 'social/profiles/v5/partials/user/posts'
aside
  - if @variants.present?
    = render partial: 'social/profiles/v5/partials/user/variants'

- profile = @user.profile
- items_count = (profile.bio.present? && @user.aggregated_profiles.present?) ? 2 : 1
.user-resume-mobile class="with-#{items_count}"
  .scroll-wrapper
    .scroll-item
      .resume.cf
        = render partial: 'partials/v5/avatar', locals: { user: @user, size: :m }
        h1= @user.full_name
    - if profile.bio.present? && @user.aggregated_profiles.present?
      .scroll-item
        - if profile.bio.present?
          .resume.cf
            - if profile.bio.present?
              p.desc= profile.bio
        - if @user.aggregated_profiles.present?
          .social-resume
            = render partial: 'social/profiles/v5/partials/user/social_icons'
  .points
    span.active
    span

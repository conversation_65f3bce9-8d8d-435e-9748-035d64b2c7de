= content_for(:title) { "#{@user.full_name} @ GoodPeople" }
= content_for(:body_class) { 'v4_user_profile' }

= render partial: 'user_meta', locals: { user: @user }

= content_for :meta do
  %link{ rel: 'canonical', href: profiles_path(@user.login) }
  - if @feed.length == 0
    %meta{ content: 'noindex, nofollow', name: 'robots'}

.medium-4.columns
  = render partial: 'social/partials/user_profile', locals: { user: @user, user_stats: @user_stats }
  - if logged_in?
    = render partial: 'users/recommendations_item', locals: { users: @recommended_users }
.medium-8.columns
  = render partial: 'social/partials/media_filters', locals: { user: @user, user_stats: @user_stats }
  - if @invite_user_to_create_post
    = render partial: 'social/profiles/no_posts', locals: { name: @user.profile.name }
  = render partial: 'social/partials/feed_items_list', locals: { feed_items: @feed, feed_path: social_feeds_profile_path(@user.login, filter: params[:filter]), is_profile: true }

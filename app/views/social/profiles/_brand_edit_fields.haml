= render partial: 'settings/setting',
         locals: { label: form.label(:name), input: form.text_field(:name) }
= render partial: 'settings/setting',
         locals: { label: form.label(:description), input: form.text_area(:description, maxlength: @profile_bio_maxlength, rows: 3), help: t('.about_brand') }
= render partial: 'settings/setting',
         locals: { label: form.label(:website), input: form.text_field(:website_url) }
= content_for :title do
  #{@profile.user.full_name} | #{t('.profile')}
= content_for :body_class, 'settings-page'

.large-4.columns
  = render partial: 'settings/menu', locals: { user: @profile.user }
.user-info.large-8.columns
  = render partial: 'partials/alert'
  .settings-box
    = form_for @profile, url: :social_profile, as: :profile, method: :put,
               html: { id: 'edit_profile', 'data-profile_type' => @profile.class.name } do |f|
      = render partial: 'settings/header',
                        locals: { section: t('.profile'), description: t('.public_information') }
      .settings-divider
      .settings-section
        .setting
          .label
            %label= t('.photo')
          .field
            = render partial: 'social/partials/avatar_uploader',
                     locals: { avatar: @profile.avatar.url(:m),
                               id: 'avatar-uploader' }
            %p.help.big= t('.identity_on_goodpeople')
        .setting
          .label
            %label= t('.cover')
          .field
            = render partial: 'social/partials/avatar_uploader',
                     locals: { avatar: @profile.cover.url(:m_cover),
                               id: 'cover-uploader' }
            %p.help.big= t('.cover_dimensions')
            %p.help.big= t('.cover_max_size')
      .settings-divider
      .settings-section.personal-data
        = profile_edit_fields_for(@profile, f)
      - if @profile.is_a? Social::Profile::Brand
        .settings-divider
        = render partial: 'settings/header',
                          locals: { section: t('.aggregated_title'), description: t('.aggregated_description') }
        .settings-divider
        .settings-section
          - aggregated_profiles = @profile.user.aggregated_profiles
          - Social::AggregatedProfile::Base::TYPES.each do |type|
            - aggregated_profile = aggregated_profiles.where(type: type).first
            - aggregated_profile = aggregated_profiles.build(type: type) if aggregated_profile.blank?
            - texts = t(".aggregated_profiles.#{type.demodulize.underscore}")
            .setting
              .label
                %label= texts[:label]
              .field
                %input{ type: 'hidden', name: 'aggregated_profiles[][id]', value: aggregated_profile.id }
                %input{ type: 'hidden', name: 'aggregated_profiles[][type]', value: aggregated_profile.type }
                .input-with-status.js-validate-aggregated-profile{ 'data-aggregated-type' => aggregated_profile.type.demodulize }
                  %input{ type: 'text', name: 'aggregated_profiles[][name]', pattern: '[a-zA-Z0-9-._]*', value: aggregated_profile.name, placeholder: texts[:placeholder] }
                  .input-with-status-status
                %p.help= texts[:help].html_safe
      .settings-divider
      .settings-section.last-section
        = f.submit t('.save_changes')

= render partial: 'settings/setting',
         locals: { label: form.label(:first_name, t('.first_name')), input: form.text_field(:first_name), help: t('.real_name') }
= render partial: 'settings/setting',
         locals: { label: form.label(:last_name, t('.last_name')), input: form.text_field(:last_name) }
= render partial: 'settings/setting',
         locals: { label: form.label(:bio, t('.bio')), input: form.text_area(:bio, maxlength: @profile_bio_maxlength, rows: 3), help: t('.about_yourself') }
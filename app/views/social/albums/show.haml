= content_for :title do
  #{@album.title} #{t('social.partials.feed_item_author.by')} #{@album.author.full_name}

= content_for :meta do
  - description = (@album.description || '').strip.gsub(/\s+/, ' ')
  %meta{ name: 'description', content: description }

  %meta{ property: 'og:url', content: social_album_url(@album) }
  %meta{ property: 'og:title', content: @album.title }
  %meta{ property: 'og:description', content: description }
  %meta{ property: 'og:type', content: 'article' }
  - if @album.pictures.present?
    %meta{ property: 'og:image', content: @album.pictures.first.url(:m) }
  %meta{ property: 'article:published_time', content: @album.created_at.iso8601 }
  %meta{ property: 'article:author', content: "#profiles_url" }
  - sports = @album.respond_to?(:sports) && @album.sports.presence
  - if sports.present?
    %meta{ property: 'article:section', content: sports.first.name }
    - sports.each do |s|
      %meta{ property: 'article:tag', content: s.name }

  %meta{ name: 'twitter:card', content: 'gallery' }
  - @album.pictures[0..3].each_with_index do |pic, index|
    %meta{ name: "twitter:image#{index}", content: pic.url(:m) }
  - if twitter_username = Network[@network].twitter_account_username
    %meta{ name: 'twitter:site', content: '@'+twitter_username }

.small-12.columns
  = render partial: 'partials/v5/posts/body/albums', locals: { item: @album }

.small-12.columns
  = render partial: 'social/feeds/recommendations',
           locals: { type: @album.class.to_s, id: @album.id, limit: 6, network: @network }

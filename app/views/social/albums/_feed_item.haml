- network ||= @network
- locale = Network[network].locale
- author = feed_item.author
- redis_key = [feed_item, author.profile]
- redis_key.unshift('follow') if show_follow_button_by_id?(author.id, author.class) && !current_user_following_by_id?(author.id, author.class)
- redis_key.unshift(locale)
- favorited = current_user_favorited?(feed_item)
- redis_key.unshift('favorited') if favorited
- cache redis_key do
  - item = feed_item.reload
  .feed-item.album
    .feed-item-top
      = render partial: 'social/partials/feed_item_author', locals: { item: item }
    %hr
    .feed-item-content
      .albums-title.item-title
        %h5.title
          %a.show-item{ href: social_album_path(item), target: '_blank' }
            = item.title unless item.title.nil?
        %h6.pictures_count
          %a.show-item{ href: social_album_path(item), target: '_blank' }
            = t('social.albums.show.pictures_count', count: item.pictures.length)

      - count = item.pictures.size
      - if count == 1
        .pictures.has-1
          %a.show-item{ 'data-pic-index' => 1, href: social_album_path(item), target: '_blank' }
            %img.picture{ src: item.pictures.first.url(:l) }
      - elsif count >= 2
        - if count <= 4
          - pictures = item.pictures[0..3]
        - if count >= 5 && count <= 7
          - pictures = item.pictures[0..4]
        - else
          - pictures = item.pictures[0..7]
        .pictures{ class: "has-#{count}" }
          - pictures.each_with_index do |picture, i|
            .picture{ style: "background-image:url(#{picture.url(:m)});" }
              %a.full-link.show-item{ 'data-pic-index' => i, href: social_album_path(item), target: '_blank' }
      = render partial: 'social/partials/feed_item_share', locals: { item: item,
                                                                     favorite_url: social_album_favorites_path(item),
                                                                     favorite_type: item.class.name }
      .text
        %p
          %a.show-item{ href: social_album_path(item, only_path: false), target: '_blank' }= item.description

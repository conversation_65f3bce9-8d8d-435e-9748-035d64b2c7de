#albums-form.albums-form.reveal-modal
  = form_for Social::Album.new, multipart: true do |f|
    .gray-modal-top
      %h4.close-reveal-modal &nbsp;&nbsp;
      %h4.title= t('.title')
      -# .albums-form-title
      -#   = f.text_field :title, maxlength: 255, autofocus: 'true', placeholder: t('.title_placeholder')
      .album-inputs
        .row.collapse
          .small-12.medium-6.columns
            .row.collapse
              .small-12.medium-1.columns
                %label.right.inline{ for: 'social_album_title' } #{t('.title_label')}:
              .small-12.medium-11.columns
                = f.text_field :title, maxlength: 255, autofocus: 'true', placeholder: t('.title_placeholder')
          .small-12.medium-6.columns
            .row.collapse.sports-input
              .small-12.medium-3.columns
                %label.right.inline{ for: 'social_album_sport_ids' } #{t('.sports_label')}:
              .small-12.medium-9.columns
                = f.collection_select :sport_ids, @whatsup_sports, :id, :name, {}, multiple: true
    .gray-modal-content
      .hiddens
      .media-wrapper
        = render partial: 'social/albums/pictures_uploader',
                 locals: { url: social_unpublished_pictures_path,
                           type: 'Social::Attachment::AlbumPicture' }
    .gray-modal-bottom
      %button.button.share.facebook-share{ type: 'button' }
        %span.icon
      %button.button.share.twitter-share{ type: 'button' }
        %span.icon
      %button.post-item{ type: 'submit' }
        = t('.create')
        %span.arrow
- author = item.author
- edit ||= false
- creator = show_delete_button_by_id?(author.id, author.class) && edit ? 'owner' : 'author'
- locale = Network[@network].locale
- redis_key = [item, author.profile]
- redis_key.unshift('follow') if show_follow_button_by_id?(author.id, author.class) && !current_user_following_by_id?(author.id, author.class)
- redis_key.unshift(creator)
- redis_key.unshift(locale)
- cache redis_key do
  .feed-item-author
    = render partial: 'social/partials/user_avatar', locals: { user: author }
    - if show_follow_button_by_id?(author.id, author.class) && !current_user_following_by_id?(author.id, author.class)
      = render partial: 'social/partials/follow_button', locals: { user: author }
    - if show_delete_button_by_id?(author.id, author.class) && edit
      .delete-post
        .delete-post-button
          = form_for item, method: :delete do |f|
            - unless current_page?(item)
              = hidden_field_tag :previous_url, request.fullpath
            %button.button{ type: 'submit' }
              %span.icon
              = t('social.partials.feed_item_author.delete')
    .name.ellipsis
      = t('.by')
      %a{href: "#profiles_url"} #{author.full_name}
    %a.time{ href: url_for(item), target: '_blank', title: item.created_at.strftime('%Y-%m-%dT%H:%M:%S%z') }
    - if item.is_aggregated?
      %span.via via&nbsp;<a href="#{item.metadata[:url]}" target="_blank" rel="nofollow">#{item.metadata[:type]}</a>

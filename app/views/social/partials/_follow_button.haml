- following = current_user_following_by_id?(user.id, user.class)
- type ||= 'user'

%button.main-follow{ class: following ? 'unfollow' : 'follow',
                     'data-follow-id' => user.id,
                     'data-follow-action' => following ? 'unfollow' : 'follow',
                     'data-base-path' => user.class == Sport ? 'sports' : 'users' }
  %span.btn-text.following-text
    - if type == 'sport'
      = t('.chosen')
    - else
      = t('.following')
  %span.btn-text.unfollow-text
    - if type == 'sport'
      = t('.unchoose')
    - else
      = t('.unfollow')
  %span.btn-text.follow-text
    - if type == 'sport'
      = t('.choose')
    - else
      = t('.follow')

- is_profile ||= false

.feed-items-list
  .feed-items-list-items
    = render partial: 'social/partials/feed_item', collection: feed_items, as: :feed_item
  - if feed_items.total_pages > feed_items.current_page
    .load-more-items
      %a.button.load-more-items-button{ href: merge_params_on_url_string(feed_path, { page: feed_items.current_page + 1 }),
                                        rel: 'nofollow',
                                        'data-current-page' => feed_items.current_page,
                                        'data-total-pages' => feed_items.total_pages,
                                        'data-path' => feed_path }
        %span.load-more-items-button-icon
          %span.load-more-items-button-animation
        = t('.load_more')
  - if is_profile
    .joined-on
      .contents
        = image_tag 'header/logo.png'
        #{t('.joined_on')} #{l(@user.created_at.to_date, format: :long)}
- favorited = current_user.present? && item.favorited_by?(current_user)
- with_title ||= false
- cssClass = []
- cssClass << (favorited ? 'favorited' : 'unfavorited')
- cssClass << 'with-title' if with_title
.favorite-button{ class: cssClass, 'data-favorite-url' => url, 'data-favoritable-type' => type }
  %span.favorite-hand
  %span.favorite-title= t('.title')
  .high-fived
    %p= t('social.comments.high_fived')

- type = user.type == 'SocialUser' ? 'user-avatar' : 'brand-avatar'

.user-profile
  .profile-header{ style: "background-image: url(#{user.profile.cover.url(:m_cover)})"}
    .row
      .small-12.columns
        = render partial: 'social/partials/user_avatar', locals: { user: user, type: type, link: user.avatar.url(:m) }
        - content_for :js do
          :javascript
            $('.profile-header .hexagon a').magnificPopup({ type:'image' });
        .profile-name
          %p
            %a{ href: profiles_path(login: user.login) }= user.full_name
    - if show_edit_profile_button?(user)
      %a.edit-button{href: edit_social_profile_path, method: :get, class: 'button'}
        = t('social.profiles.user_show.edit')
  .profile-stats
    .follow-stats.row
      .small-3.columns
        %a{ href: social_followers_path, rel: 'nofollow' }
          .stat-value= user_stats.followers_count
          .stat-name= t('social.profiles.user_show.followers')
      .small-3.columns
        %a{ href: social_following_path, rel: 'nofollow'  }
          .stat-value= user_stats.followings_count
          .stat-name= t('social.profiles.user_show.following')
      .small-6.columns
        - if show_follow_button?(user)
          = render partial: 'social/partials/follow_button', locals: { user: user }
  -if user.profile.bio.present?
    .profile-bio
      .divider
      %p= user.profile.bio

.feed-item-share
  - favorited = logged_in? && item.favorited_by?(current_user)
  %a.button.tiny.high-five{ class: favorited && 'high-fived',
                            'data-favorite-url' => favorite_url,
                            'data-favorite-type' => favorite_type }
    %span.high-five-text= t('social.high_five_action')
    %span.high-fived-text= t('social.high_fived_action')
  %a.button.tiny.facebook{ 'data-link' => url_for(item) }= t('social.facebook_share_action')
  %a.button.tiny.twitter{ 'data-link' => url_for(item), 'data-title' => feed_item_title(item) }= t('social.twitter_share_action')
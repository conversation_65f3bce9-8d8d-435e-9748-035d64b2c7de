.media-filters
  %ul.small-block-grid-6
    %li
      - classes = params[:filter] == 'text' ? 'active ' : ''
      - classes += 'zero' unless user_stats.has_whatsups?
      .filter{ class: classes }
        %a{ href: profiles_path(user.login, filter: 'text') }
          %span.filter-icon.whatsups
          %span.filter-counter= user_stats.whatsups_count
    %li
      - classes = params[:filter] == 'picture' ? 'active ' : ''
      - classes += 'zero' unless user_stats.has_pictures?
      .filter{ class: classes }
        %a{ href: profiles_path(user.login, filter: 'picture') }
          %span.filter-icon.pictures
          %span.filter-counter= user_stats.pictures_count
    %li
      %a.remove-filters{ href: profiles_path(user.login), class: params[:filter].present? ? 'active' : '' }
        %span.show Show
        All

- type   ||= 'feed-avatar'
- avatar_key = user.avatar.present? && user.avatar.persisted? ? user.avatar : user.id
- cache [type, avatar_key] do
  - shape  = :square if %w(Brand).include?(user.type)
  - avatar = user.avatar.present? && user.avatar.persisted? && user.avatar.url(:t)
  - link = '#'

  = render partial: 'partials/hexagon',
           locals: { shape: shape,
                     type: type,
                     img: avatar,
                     link: link,
                     fallback_color: Social::Attachment::AvatarPicture.fallback_color_for(user),
                     fallback_text: Social::Attachment::AvatarPicture.fallback_text_for(user) }

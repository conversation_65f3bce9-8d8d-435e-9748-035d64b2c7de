- if logged_in? && current_logged_in_user.shops.any?
  %a.h-dropdown-item.grey{ href: lux_path }
    = t('.shop_admin').gsub(' ', '&nbsp;').html_safe
%a.h-dropdown-item.grey{ href: settings_account_path }
  = t('.account_settings').gsub(' ', '&nbsp;').html_safe
%a.h-dropdown-item{ href: logout_path }
  = t('.sign_out').gsub(' ', '&nbsp;').html_safe
- if managed_users.present?
  - managed_users.each do |u|
    %a.h-dropdown-item.ellipsis.bold{ href: users_current_path(id: u.id), class: (current_user == u) && 'active'}
      = u.login
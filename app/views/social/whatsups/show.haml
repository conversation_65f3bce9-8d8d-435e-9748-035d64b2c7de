= content_for :title, whatsup_post_title(@whatsup)

= content_for :meta do
  %meta{ name: 'description', content: whatsup_post_description(@whatsup) }

  %meta{ property: 'og:url', content: social_whatsup_url(@whatsup, protocol: 'http') }
  %meta{ property: 'og:title', content: whatsup_post_title(@whatsup) }
  %meta{ property: 'og:description', content: whatsup_post_description(@whatsup) }
  %meta{ property: 'og:type', content: 'article' }
  - if whatsup_post_image(@whatsup).present?
    %meta{ property: 'og:image', content: whatsup_post_image(@whatsup) }
  %meta{ property: 'article:published_time', content: @whatsup.created_at.iso8601 }
  %meta{ property: 'article:author', content: profiles_path(@whatsup.author.login) }
  - sports = @whatsup.respond_to?(:sports) && @whatsup.sports.presence
  - if sports.present?
    %meta{ property: 'article:section', content: sports.first.name }
    - sports.each do |s|
      %meta{ property: 'article:tag', content: s.name }

  -# Twitter Product Card metatags data
  - tw_card = 'summary_large_image'
  - tw_username = Network[@network].twitter_account_username
  - tw_title = whatsup_post_title(@whatsup)
  - tw_description = whatsup_post_description(@whatsup)
  - tw_image_url = whatsup_post_image(@whatsup)

  = render partial: 'partials/v5/meta/twitter',
             locals: { tw_card: tw_card,
                       tw_username: tw_username,
                       tw_title: tw_title,
                       tw_description: tw_description,
                       tw_image_url: tw_image_url }

.small-12.columns
  = render partial: 'partials/v5/posts/body/whatsups', locals: { item: @whatsup, picture_size: :l }

.small-12.columns
  = render partial: 'social/feeds/recommendations',
           locals: { type: @whatsup.class.to_s, id: @whatsup.id, limit: 6, network: @network }

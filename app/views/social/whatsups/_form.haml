#whatsups-form.whatsups-form.reveal-modal
  = form_for @new_whatsup, multipart: true, html: { data: { 'picture-url' => social_unpublished_pictures_path } } do |f|
    .gray-modal-top
      %h4.close-reveal-modal &nbsp;&nbsp;
      %h4.title= t('.create_goodpeople_post')
    .gray-modal-content
      .preload-image{ style: "background-image:url(#{image_path('social/whatsups_form/progress-bar.png')})" }
      .preload-image{ style: "background-image:url(#{image_path('social/whatsups_form/progress-cancel.png')})" }
      .preload-image{ style: "background-image:url(#{image_path('social/whatsups_form/small-x-sprite.png')})" }
      .hiddens
        = f.hidden_field 'target_type'
        = f.hidden_field 'target_id'
      .media-wrapper
        = f.text_area :body, maxlength: @whatsup_body_maxlength, rows: 3, autofocus: 'true'
        = render partial: 'social/partials/file_uploader',
                 locals: { url: social_unpublished_pictures_path,
                           type: 'Social::Attachment::WhatsupPicture',
                           id: 'whatsups-pic-uploader' }
      .sport-selector.without-select2
        %label{ for: 'social_whatsup_sport_ids' }= t('.select_a_sport')
        = f.collection_select :sport_ids, @whatsup_sports, :id, :name, {}, multiple: true
      %a.button.disabled.add-img= t('.upload_photo')
      %a.button.disabled.create-album= t('.create_album')
      .metadata-container
    .gray-modal-bottom
      %button.button.share.facebook-share{ type: 'button', 'data-can_write' => "#{@can_post_to_facebook}" }
        %span.icon
      %button.button.share.twitter-share{ type: 'button', 'data-can_write' => "#{@can_post_to_twitter}" }
        %span.icon
      %button.post-item{ type: 'submit' }
        = t('.send_post')
        %span.arrow
- content_for :before_js do
  = render partial: 'social/albums/form'

- item ||= nil
- metadata ||= item.metadata
- link ||= item && URI::join(root_url, social_whatsup_path(item))
- internal_link ||= false
- url_attrs = {}
- url_attrs[:class] = internal_link ? 'show-item' : nil
- url_attrs[:href] = internal_link ? link : metadata[:url]
- url_attrs[:rel] = internal_link ? nil : 'nofollow'
- url_attrs[:target] = internal_link ? nil : '_blank'
- case metadata[:type]
  - when 'instagram'
    .metadata{ class: metadata[:type] }
      - if metadata[:image].present?
        .content-thumb
          %a.show-item{ href: link, target: '_blank' }
            %img{ src: metadata[:image] }
  - when 'youtube', 'vimeo'
    .metadata.embeded-video{ class: metadata[:type] }
      %h6.video-title
        %a.show-item{ href: link, target: '_blank' }= metadata[:title]
      %p.video-url
        %a{ url_attrs }= metadata[:url].truncate(40)
      .video-thumb
        %a.show-item{ href: link, target: '_blank' }
          - if metadata[:image].present?
            %img.video-thumb-img{ src: metadata[:image] }
          .video-play-icon
  - when 'url'
    .metadata.url{ class: metadata[:image].present? && 'with-thumb' }
      - if metadata[:image].present?
        .content-thumb
          %a{ url_attrs }
            %img{ src: metadata[:image] }
      .content-data
        %h6.content-title
          %a{ url_attrs }= metadata[:title]
        %p.content-url
          %a{ url_attrs }= metadata[:url].truncate(40)
        - if metadata[:description].present?
          %p.content-description
            %a{ url_attrs }
              = metadata[:description]

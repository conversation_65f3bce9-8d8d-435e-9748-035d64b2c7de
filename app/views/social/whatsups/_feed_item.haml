- network ||= @network
- locale = Network[network].locale
- author = feed_item.author
- redis_key = [feed_item, author.profile]
- redis_key.unshift('follow') if show_follow_button_by_id?(author.id, author.class) && !current_user_following_by_id?(author.id, author.class)
- redis_key.unshift(locale)
- favorited = current_user_favorited?(feed_item)
- redis_key.unshift('favorited') if favorited
- cache redis_key do
  - item = feed_item.reload
  .feed-item.whatsup
    .feed-item-top
      = render partial: 'social/partials/feed_item_author', locals: { item: item }
    %hr
    - item_key = [locale, item]
    - item_key.unshift('favorited') if favorited
    - cache item_key do
      .feed-item-content
        - if pic = item.picture.presence
          %a.show-item.thumb-wrapper{ href: social_whatsup_path(item) }
            %img.pic{ src: pic.url(:m) }
        - if item.metadata.present?
          = render partial: 'social/whatsups/metadata', locals: { item: item, link: social_whatsup_path(item), internal_link: true }
        = render partial: 'social/partials/feed_item_share', locals: { item: item,
                                                                       favorite_url: social_whatsup_favorites_path(item),
                                                                       favorite_type: item.class.name }
        .text
          %p
            %a.show-item{ href: social_whatsup_path(item), target: '_blank' }
              = item.body_html

.row.reviews-title
  .small-12.large-6.large-offset-3.columns
    - product_name = @variants.length == 1 ? @variants.first.title : ''
    h2 = t('.title', count: @variants.length, product_name: product_name)
    h3 = t('.subtitle')
br
br
.row.reviews-container
  .small-12.columns
    - @variants.group_by(&:product).each do |product, variants|
      .row.review-container
        .small-12.large-8.large-offset-2.columns
          .row
            .small-12.medium-3.large-3.columns.img-container
              = image_tag variants.first.picture.try(:url)
            .small-12.medium-9.large-9.columns
              h4
                = t('.variant_text', count: variants.length, product_name: "#{product.title}")
                = variants_names(variants)
              .info_text
                = t('.rate_text')
              - review = Mkp::Review.where(product_id: product.id, customer_id: @communication.customer.id).first
              - if review.blank?
                = form_for Mkp::Review.new, url: mkp_order_orders_reviews_path(format: :json), html: { class: 'new_mkp_review' } do |f|
                  = f.hidden_field :product_id, value: product.id
                  = f.hidden_field :stars, value: Mkp::Review::MAX_STARS
                  = hidden_field_tag :communication_id, @communication.id
                  .product-rate
                    - selected_stars = Mkp::Review::MAX_STARS
                    .rate-stars class="stars-#{selected_stars}" data-stars=selected_stars data-max-stars=Mkp::Review::MAX_STARS
                      .rate &nbsp;
                      .stars
                        - Mkp::Review::MAX_STARS.times do |i|
                          button.star.needsclick class='star-action' type='button' data-stars="#{i + 1}" &nbsp;
                    .clear
                  p.info_text = t('.comments_text')
                  .form-fields
                    = f.text_field :title, placeholder: t('.title_placholder'), class: 'mkp_review_title'
                    .fields
                      = f.text_area :description, rows: 3, placeholder: t('.description_placeholder')
                    = f.submit t('.send')
                  p.title
                  p.description
              - else
                .product-rate
                  - selected_stars = review.stars
                  .rate-stars class="stars-#{selected_stars}" data-stars=selected_stars data-max-stars=Mkp::Review::MAX_STARS
                    .rate &nbsp;
                    .stars
                      - Mkp::Review::MAX_STARS.times do |i|
                        button.star.needsclick type='button' data-stars="#{i + 1}" &nbsp;
                p = review.title
                p = review.description

.question style="width:80%;margin:0 auto;margin-top:30px;"
  - user = question.user
  - fallback_color = Social::Attachment::AvatarPicture.fallback_color_for(user)
  - fallback_text = Social::Attachment::AvatarPicture.fallback_text_for(user)
  - avatar = user.respond_to?(:avatar) && \
             user.avatar.persisted? && \
             user.avatar.url(:t)
  .avatar style="width:3.5em;float: left;margin: 0 15px 0 0;"
    - if avatar
      img src=avatar alt=user.full_name style="width:100%;border-radius: 50%;"
    - else
      .fallback style="padding-top:100%;font-size:1.5em;border-radius:50%;position:relative;background-color:#{fallback_color};"
        span style="position:absolute;top:50%;left:0;width:100%;text-align:center;color:white;font-weight:bold;margin-top: -.48em;line-height: 1em;letter-spacing: -.05em;border-radius: 50%;"
          = fallback_text
  .bubble style="margin: 0 0 20px 60px;padding: 8px 20px;font-size: 13px;line-height: 1.3;color: #565656;border-radius: 3px;background-color: #E6E6E6;"
    .user style="margin-bottom: 5px;font-size=11px;"
      span style="font-weight: bold;" = t('mkp.shop_mailer.answer_shop.you_ask')
    .text= question.description
.answer style="width:80%;margin:0 auto;"
  - user = answer.user
  - fallback_color = Social::Attachment::AvatarPicture.fallback_color_for(user)
  - fallback_text = Social::Attachment::AvatarPicture.fallback_text_for(user)
  - avatar = user.respond_to?(:avatar) && \
             user.avatar.persisted? && \
             user.avatar.url(:t)
  .avatar style="width:3.5em;float: left;margin: 0 15px 0 0;"
    - if avatar
      img src=avatar alt=user.full_name style="width:100%;border-radius: 50%;"
    - else
      .fallback style="padding-top:100%;font-size:1.5em;border-radius:50%;position:relative;background-color:#{fallback_color};"
        span style="position:absolute;top:50%;left:0;width:100%;text-align:center;color:white;font-weight:bold;margin-top: -.48em;line-height: 1em;letter-spacing: -.05em;border-radius: 50%;"
          = fallback_text
  .bubble style="margin: 0 0 20px 60px;padding: 8px 20px;font-size: 13px;line-height: 1.3;color: #565656;border-radius: 3px;background-color: #FFFFFF;border: 1px solid #E6E6E6;"
    .user style="margin-bottom: 5px;font-size=11px;"
      span style="font-weight: 400;" #{l answer.created_at.to_date, format: :day_and_year}&nbsp;-&nbsp;
      span style="font-weight: 700;color: #307f39;" = answer.user.full_name
    .text= answer.description

- product = @question.product
- customer = @question.user

/ Good news, (user name) has asked a question about your product, the (product name). "(show the question)"
/ Here's a good chance to do some killer customer service!"
/ Button: "Reply Now" - takes them to the admin panel Q&A

%p.grey
  / - customer_login = link_to "@#{customer.login}", profiles_url(customer.login), target: "_blank"
  - link_product = link_to product.title, product.get_url(only_path: false), target: "_blank"
  = t('.msg_html', customer_login: customer_login, product_title: link_product, question_text: @question.description)

%h5
  = t('.msg_extra')

.custom-button
  %span
    = link_to t('.link_text'), lux.shop_questions_url(product.shop, network: product.shop.network.downcase)

%h4
  Avenida.com

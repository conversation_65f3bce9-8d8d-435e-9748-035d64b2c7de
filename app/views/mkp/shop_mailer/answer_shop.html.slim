- product = @question.product
- shop = product.shop
- url_product = product.get_url(only_path: false) + "?#{@google_analytics_params}"
- link_shop = link_to shop.title, url_product + '&utm_content=link', target: "_blank"
- link_product = link_to product.title, url_product + '&utm_content=link', target: "_blank"
- link_text = t('.link_text', product_title: product.title)
- manufacturer_url = Mkp::Catalog::UrlHandler.url(b: product.manufacturer.slug, network: @network.downcase)
- manufacturer_url += "?#{@google_analytics_params}"
- manufacturer_link = link_to shop.title, manufacturer_url + '&utm_content=link', target: "_blank"

h1 style= 'padding-bottom: 9px;margin-bottom: 0;' = t('.hi', name: @customer_name)
p style='padding-bottom: 16px;font-size: 15.3px;'
  strong= t('.msg_html', manufacturer_link: manufacturer_link, link_product: link_product)

= render partial: 'product', locals: { product: product, url_product: url_product }
= render partial: 'question', locals: { question: @question, answer: @answer }

= render partial: 'button', locals: { url_product: url_product, link_text: link_text }

= render 'recommended_variants' if @recommended_variants.any?


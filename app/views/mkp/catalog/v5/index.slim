- fullpath = request.fullpath.encode("utf-8", invalid: :replace, undef: :replace, replace: "_")
= render partial: 'mkp/catalog/v5/partials/head'
.gray-color.override-with-white-color
  main
    section.catalog-header
    - if @variants.present?
      = render partial: 'mkp/catalog/v5/partials/header'
      - unless browser.mobile?
        aside
          = render partial: 'mkp/catalog/v5/partials/filter', locals: { fullpath: fullpath }
      section.catalog-content class="#{"catalog-content--mobile" if browser.mobile?}"
        = render partial: 'mkp/catalog/v5/partials/sort_and_pagination', locals: { fullpath: fullpath }
        = render partial: 'mkp/catalog/v5/partials/cover'
        = render partial: 'partials/v5/variants/list', locals: { variants: @variants, list_name: "#{catalog_list_name}-#{@variants.current_page.to_s}" }
        .catalog-bottom
          = render partial: 'mkp/catalog/v5/partials/pagination'
    - else
      - if browser.mobile?
        section.catalog-content class="#{"catalog-content--mobile" if browser.mobile?}"
          .catalog-title itemscope=true
            h2 itemprop='name' class="#{'mobile' if browser.mobile?}"
              = t('.search_results')
          = render partial: 'partials/v5/variants/list',
                  locals: {variants: @recommended_variants, list_name: 'landing-featured-products'}
          = render partial: 'partials/v5/load_more_wide',
                  locals: {style: 'white-blue', attrs: {title: t('.see_more'), href: mkp_catalog_path}}
      - else
        .other-alternatives
          .catalog-title itemscope=true
            h2 itemprop='name' class="#{'mobile' if browser.mobile?}"
              = t('.search_results')
          .featured-variants-cmpnt
            .scroll-wrapper
              = render partial: 'partials/v5/variants/list',
                        locals: { variants: @recommended_variants, list_name: 'landing-featured-products' }
            = render partial: 'partials/v5/load_more_wide',
                     locals: { style: 'white-blue', attrs: { title: t('.see_more'), href: mkp_catalog_path } }
  - if INCLUDE_ANALYTICS
    - content_for :before_js do
      javascript:
        gp = window.gp || {}
        gp.google_tag_params = #{@tag_params.html_safe};

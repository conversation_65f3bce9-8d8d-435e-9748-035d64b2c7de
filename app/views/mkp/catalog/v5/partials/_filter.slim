.filter
  - @filter.each_pair do |key, value|
    - selected_items_length = value[:selected_items].present? ? value[:selected_items].length : 0
    - next if value[:items].present? && key.to_s != 'sale' && \
              value[:items].length != value[:items].select{|i| i[:active]}.length && \
              value[:items].detect{|i| i[:childs].present? }.blank? && \
              (value[:items].length + selected_items_length) <= 1
    - hide_some = value[:items].present? && value[:items].length > 5 && (/brand|price|sport|size/ =~ key.to_s).present?
    - class_name = [key]
    - facet_class = ['facet-item']
    - facet_class << 'multi' if (/brand|sport|size/ =~ key.to_s).present?
    - number_of_items_shown = 5
    .filter-option class=class_name data-name=key data-show=(hide_some && number_of_items_shown)
      h3
        = value[:title]
      - if /price|gender/ =~ key.to_s
        = render partial: "mkp/catalog/v5/partials/filter/#{key}", locals: { value: value, facet_class: facet_class, number_of_items_shown: number_of_items_shown, hide_some: hide_some, fullpath: fullpath }
      - else
        ul
          - if value[:selected_items].present?
            - value[:selected_items].each do |item|
              - class_name = ['active']
              - item_url = :href_remove
              li class=class_name
                a class=facet_class href=item[item_url] rel=catalog_url_rel(item[item_url]) = item[:name]
          - value[:items].each_with_index do |item, i|
            - class_name = []
            - class_name << 'active' if item[:active]
            - class_name << 'hide-for-seo' if hide_some && i >= (number_of_items_shown - selected_items_length)
            - item_url = item[:active] ? :href_remove : :href
            li class=class_name style=(item[:color_hex].present? && "background-color:#{item[:color_hex]};")
              a class=facet_class href=item[item_url] rel=catalog_url_rel(item[item_url]) = item[:name]
              - if childs = item[:childs]
                ul.children
                  - childs.each do |child|
                    - class_name = []
                    - class_name << 'active' if child[:active]
                    - child_url = child[:active] ? :href_remove : :href
                    li class=class_name
                      a class=facet_class href=child[child_url] rel=catalog_url_rel(child[child_url]) = child[:name]
        - if hide_some
          span.js-show-all= t(".show_all.#{key.to_s}")
          span.js-hide-all style='display:none'= t(".hide_all.#{key.to_s}")

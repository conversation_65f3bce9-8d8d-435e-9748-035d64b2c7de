- breadcrumb ||= @breadcrumb
- if breadcrumb.present?
  .breadcrumb-wrapper
    .breadcrumb
      - breadcrumb.each_with_index do |b, i|
        - if b[:query].present?
          span
            em.query= t('mkp.variants.search.info_html', term: b[:name])
        - else
          span itemscope= true itemtype= 'http://data-vocabulary.org/Breadcrumb'itemprop= (i > 0 && 'child')
            - if b[:href].present?
              a href= b[:href] itemprop= 'url'
                span itemprop= 'title'
                  = b[:name]
              / - if b[:href_remove].present?
              /   .remove
              /     a href= b[:href_remove] itemprop= 'url'
              /       span
            - else
              span itemprop= 'title'
                = b[:name]

ul
  - value[:items].each_with_index do |item, i|
    - class_name = []
    - class_name << 'active' if item[:active]
    - class_name << 'hide-for-seo' if hide_some && i >= number_of_items_shown
    - item_url = item[:active] ? :href_remove : :href
    li class=class_name style=(item[:color_hex].present? && "background-color:#{item[:color_hex]};")
      a href=item[item_url] rel=catalog_url_rel(item[item_url]) = item[:name]
  - unless value[:items][0][:active]
    .price_wrapper
      form action=fullpath method=:get class="price-selector"
        = hidden_field_tag "pr"
        .input-wrapper
          input type='text' id="pr-min" placeholder=t('.prices.minimum')
        .input-wrapper
          input type='text' id="pr-max" placeholder=t('.prices.maximum')
        button class='plain-button small' type='submit'
          i.i-flecha-right

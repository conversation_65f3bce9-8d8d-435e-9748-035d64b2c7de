ul
  - if value[:selected_items].present?
    - value[:selected_items].each do |item|
      - class_name = ['active']
      - item_url = :href_remove
      li class=class_name
        a class=facet_class href=item[item_url] rel=catalog_url_rel(item[item_url]) = item[:name]
  - else
    - value[:items].each_with_index do |item, i|
      - class_name = []
      - class_name << 'active' if item[:active]
      - class_name << 'hide-for-seo' if hide_some && i >= (number_of_items_shown - selected_items_length)
      - item_url = item[:active] ? :href_remove : :href
      li class=class_name style=(item[:color_hex].present? && "background-color:#{item[:color_hex]};")
        a class=facet_class href=item[item_url] rel=catalog_url_rel(item[item_url]) = item[:name]

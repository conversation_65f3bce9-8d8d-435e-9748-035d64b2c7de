- if browser.mobile?
  .sort-mobile
    button.mobile-button
      img src=image_path("icons/sort-by.svg") alt=""
      =t('.title').chomp ":"
  .options-mobile (style="display: none;")
    - order_options.each do |name, value|
      - active = value == default_order_option
      .option-mobile class=('active' if active)
        - if active
          = name
        - else
          = link_to name, fullpath_with_order(fullpath, value)
- else
  .sort
    span.title=t('.title')
    .options
      - order_options.each do |name, value|
        - active = value == default_order_option
        span.option class=('active' if active)
          - if active
            = name
          - else
            = link_to name, fullpath_with_order(fullpath, value)

- content_for :js do
  javascript:
    $(function(){
      $('.sort-mobile .mobile-button').click(function() {
        $('.options-mobile').toggle();
      });
    });


= render partial: 'mkp/catalog/v5/partials/breadcrumb'
- item = header_object
- schema_type = item.model.is_a?(Mkp::Manufacturer) ? "http://schema.org/Brand" : "http://schema.org/CollectionPage"
.catalog-title itemscope=true itemtype=schema_type class="#{'with-logo' if item.logo.present?}"
  - if item.is_catalog_main_model?
    - if url = absolute_mapper_url(item.model, @network)
      link itemprop='url' href=url
    / - if item.logo.present?
    /   img.catalog-title-logo itemprop='logo' src=item.logo alt=item.name
  h1.title itemprop='name' class="#{'mobile' if browser.mobile?}"
    = item.name

- title = catalog_title
- description = meta_description
- canonical_url = absolute_mapper_url(@models, @network)

= content_for(:title, title)

= content_for :meta do
  link rel='canonical' href=canonical_url
  - unless should_be_indexed?(canonical_url)
    meta name='robots' content='noindex'
  - if @variants.next_page.present?
    - options = { p: @variants.next_page, s: @seed }
    link rel='next' href=absolute_mapper_url(@models, @network, options)
  - if @variants.previous_page.present?
    - options = { p: @variants.previous_page, s: @seed }
    link rel='prev' href=absolute_mapper_url(@models, @network, options)

  meta content=description name='description'
  meta name='twitter:card' content='summary'
  - if twitter_username = Network[@network].twitter_account_username
    meta name='twitter:site' content=('@'+twitter_username)

= content_for :og_meta do
  = render partial: 'partials/v5/meta/open_graph',
           locals: open_graph_locals(canonical_url, description, title, catalog_main_model)

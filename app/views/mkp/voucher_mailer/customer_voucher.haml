- I18n.locale = @locale
%h2=t('.hi',name:@customer.first_name)
%p
  =t('.success')
%h5= t('.summary').upcase
%table
  %tbody
    - @order.shipments.each do |shipment|
      - shipment.items.each do |item|
        = render partial: 'mailer/partials/order_item', locals: {item: item}
        - if item.coupon&.code || item.external_coupon&.code
          = render partial: 'mailer/partials/order_coupon', locals: {coupon: item.try(:coupon) || item.try(:external_coupon) }
%br

%br
= render partial: 'v5/partials/mailer/contact_us'

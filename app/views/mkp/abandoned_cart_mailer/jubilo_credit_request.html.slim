- I18n.locale = @locale

table.order-item
  h2
    | Solicitud de crédito
  p
    | <PERSON><PERSON>, tienes una nueva solicitud de crédito:
    br
    br
    strong Nombre: 
    span #{@params[:name]}
    br
    strong Email: 
    span #{@params[:email]}
    br
    strong Cuil: 
    span #{@params[:cuil]}
    br
    strong Teléfono: 
    span #{@params[:phone_number]}
  br
  hr
  - @cart.items.each do |item|
    - variant = item.variant
    - product = variant.product
    - variant_url = variant.get_url
    - properties = Mkp::Product::AVAILABLE_PROPERTIES

    tr.border-bottom
      td
        a { href=variant_url }
          = image_tag variant.picture.url(:st), size: '100x100' if variant.picture
      td
        p
          strong Producto: 
          a { href=variant_url } #{product.title}
          - unless product.has_no_property?
            p
              - variant.properties.keys.each do |property|
                - if property.to_sym == :color
                  strong #{ t('.color') }:
                  span  #{ variant.color_name }
                - else
                  - if properties.include?(property)
                    strong #{ t('.' + property.to_s) }:
                  - else
                    strong #{ property.to_s.capitalize }:
                  span  #{variant.properties[property] }
        p
          strong Precio:
          span  #{number_to_currency product.price}
p
= render partial: 'v5/partials/mailer/contact_us'

- I18n.locale = @locale

table.order-item
  h2
    | ¡Hola #{@customer.first_name}!
  p
    strong Vimos que dejaste algunos productos en tu carrito.
  p
    strong ¡Podés completar tu compra! Ha<PERSON> click en el siguiente link:
  br
  - if @cart.coupon.present?
    .col-xs-12
      p
        | Ingresa el siguiente cupon al finalizar tu compra y
        strong  obtene $ #{@coupon.amount} de descuento.
    .col-md-4 style="padding: 8px 0px; background-color: transparent; height:30px; max-width:280px; border: 2px solid #4e564a; text-align: center; color: #222322; border-radius: 14px;margin: auto"
      strong  style="text-shadow: -1px 0 black, 0 1px black, 1px 0 black, 0 -1px black; font-size: 1.5em; margin auto" #{@coupon.code}
  br
  hr
  - @cart.items.each do |item|
    - is_admin ||= false
    - variant = item.variant
    - product = variant.product
    - variant_url = variant.get_url
    - properties = Mkp::Product::AVAILABLE_PROPERTIES

    tr.border-bottom
      td
        a { href=variant_url }
          = image_tag variant.picture.url(:st), size: '100x100' if variant.picture
      td
        p
          strong Producto:
          a { href=variant_url } #{product.title}
          - if is_admin && variant.sku.present?
            strong SKU: #{variant.sku}
          - unless product.has_no_property?
            p
              - variant.properties.keys.each do |property|
                - if property.to_sym == :color
                  strong #{ t('.color') }:
                  span  #{ variant.color_name }
                - else
                  - if properties.include?(property)
                    strong #{ t('.' + property.to_s) }:
                  - else
                    strong #{ property.to_s.capitalize }:
                  span  #{variant.properties[property] }
        p
          strong Precio:
          span  #{number_to_currency product.price}
        - if @cart.coupon.present?
          p
            strong Precio con descuento:
            span  #{number_to_currency <EMAIL>}
p
= render partial: 'v5/partials/mailer/contact_us'
br
  div style="text-align: center"
    p
      strong ¡Gracias por visitarnos!
    p
      span La disponibilidad de los productos queda sujeta a confirmación al momento del pago. Las fotos son a modo ilustrativo. La venta de cualquiera de los productos publicados está sujeta a la verificación de stock. Las especificaciones técnicas, descripciones y precios están sujetos a cambios sin previo aviso. Si no creaste ningún carrito en nuestro sitio, por favor desestima este e-mail.

- content_for :analytics
  = track_entering_checkout(@checkout_items)

- content_for(:body_class) { 'checkout' }
- content_for(:js) do
  = track_checkout_steps(@checkout_items)
  = render partial: 'partials/v5/javascripts/checkout/payment_gateways'
  = render partial: 'partials/v5/javascripts/checkout/payment_decidir'

main
  .main-container
    - if flash.present?
      .general-errors
        p= flash.alert
    .steps
      .shipping
        - if @checkout_cart.is_pickeable?
          = render partial: 'mkp/checkout/v5/partials/pickup_selector'
        = render partial: 'mkp/checkout/v5/partials/shipping_address'
        - if @checkout_cart.is_pickeable?
          = render partial: 'mkp/checkout/v5/partials/pickup_location'
        = render partial: 'mkp/checkout/v5/partials/shipping_methods'
      = render partial: 'mkp/checkout/v5/partials/coupon'
      .payment
        = render partial: 'mkp/checkout/v5/partials/payment'
    .summary
      = render partial: 'mkp/checkout/v5/partials/summary'

.address
  h4.i-flecha-right= " #{t('.title')}"
  .addresses
    - if is_user? && @addresses.present?
      - @addresses.each do |address|
        .address-option id="address_#{address.id}" data-id="#{address.id}"
          .hidden.jsdata { data-id="#{address.id}"
                         data-full_name="#{address.full_name}"
                         data-email="#{current_customer.email}"
                         data-address="#{address.address}"
                         data-address-2="#{address.address_2}"
                         data-telephone="#{address.telephone}"
                         data-zip="#{address.zip}"
                         data-city="#{address.city}"
                         data-state="#{address.state}"
                         data-country="#{address.country}" }
          i.i-radio-off
          h5= address.full_address
          .full_name= address.full_name
          .full_location= full_location(address)
          .country= GeoConstants::Countries.name_for_code(address.country)
          .actions
            span.edit= t('.edit_address')
            / span.delete= t('.delete_address')
      .address-option.new-address-option
        h4= t('.add_new_address')

  .address-form.address-form class="#{'hidden' if @addresses.present? && is_user? }"
    - unique_address = @addresses.first if @addresses.present? && is_not_user?
    - address_for_form = unique_address.present? ? unique_address : @address_form
    = form_for address_for_form, as: :address, url: api_mkp_checkout_addresses_path do |f|
      - if unique_address.present?
        .hidden
          = hidden_field_tag nil, unique_address.id, id: 'address_id'
      .full-name
        = f.label :full_name, t('.full_name')
        .input-wrapper
          = f.text_field :full_name, pattern: '^[a-zA-Z][a-zA-Z\\s]{6,60}$', class: 'required validate', autofocus: true
      .email
        = label_tag 'address_email', t('.email')
        .input-wrapper
          = text_field_tag 'address[email]', address_for_form.email , class: 'required validate', 'data-validation' => 'email'
      .phone
        = f.label :telephone, t('.phone')
        .input-wrapper
          = f.text_field :telephone, pattern: '^[0-9]{8,15}$', required: true, class: 'required validate'
      .street_1.address-field
        = f.label :address, t('.street_1')
        .input-wrapper
          - value = if address_for_form.street_number.present? then address_for_form.address[0..-(address_for_form.street_number.size+2)] else "" end
          = f.text_field :address, pattern: '^.{2,60}$', class: 'required validate', value: value
      .street_number.address-field
        = f.label :street_number, t('.number')
        .input-wrapper
          = f.text_field :street_number, pattern: '^.{1,20}$', class: 'required validate'
      .street_2.address-field
        = f.label :address_2, t('.street_2')
        .input-wrapper
          = f.text_field :address_2
      .country.address-field
        = f.label :country, t('.country')
        .select-wrapper
          - country_options = country_select_options(@network)
          - default_country = unique_address.present? ? unique_address.country : @network
          = f.select :country, options_for_select(country_options, default_country), { prompt: t('.select_country'), include_blank: false }, class: "required #{'no-options' if country_options.length <= 1}"
      .state.address-field
        = f.label :state, t('.state')
        .select-wrapper
          - states_options = unique_address.present? ? states_select_options(unique_address.country) : states_select_options(@network)
          - default_state = unique_address.present? ? unique_address.state : nil
          = f.select :state, create_custom_options_for_select(states_options, default_state), { prompt: "#{t('.select_state') if states_options.present?}", include_blank: false }, class: 'required validate'
      .city.address-field
        = f.label :city, t('.city')
        .input-wrapper
          = f.text_field :city, pattern: '^.{2,60}$', class: 'required validate'
      .zip-code.address-field
        = f.label :zip, t('.zip')
        .input-wrapper
          = f.text_field :zip, pattern: '^[0-9]{1,10}$', class: 'required validate'

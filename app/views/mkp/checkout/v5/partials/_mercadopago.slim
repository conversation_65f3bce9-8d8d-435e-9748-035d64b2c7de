.mercadopago
  .active-payments
    .payment-option.creditcards
      i.i-radio-on
      .header
        h5.title= t('.creditcards')
        span.installments-more-info= t('.check_promotions')
      .creditcard-form
        .mp-fields.hidden
          input#cardNumber data-checkout="cardNumber" type="hidden"
          input#securityCode data-checkout="securityCode" type="hidden"
          input#cardExpirationMonth data-checkout="cardExpirationMonth" type="hidden"
          input#cardExpirationYear data-checkout="cardExpirationYear" type="hidden"
          input#cardholderName data-checkout="cardholderName" type="hidden"
          input#docType data-checkout="docType" value="DNI" type="hidden"
          input#docNumber data-checkout="docNumber" type="hidden"
        = form_tag '#' do
          .full-name
            = label_tag 'cc_cardholder_name', t('.full_name')
            span.i-info.tooltip-item
              span.tooltip
                = t('.full_name_explanation_html')
            .input-wrapper
              = text_field_tag 'cc[cardholder_name]', customer_full_name, class: 'required validate'
          .doc-type
            = label_tag  'cc_cardholder_doc_type', t('.doc_type')
            .select-wrapper
              = select_tag 'cc[cardholder_doc_type]', options_for_select(ar_identification_types_options, 'DNI'), include_blank: false, class: 'required'
          .doc-number
            = label_tag 'cc_cardholder_doc_number', t('.doc_number')
            .input-wrapper
              = text_field_tag 'cc[cardholder_doc_number]', nil,  class: 'required validate'
          .card-number
            = label_tag 'cc_card_number', t('.card_number')
            .input-wrapper
              = password_field_tag 'cc[card_number]', '', class: 'required validate', autocomplete: 'off'
          .cvv
            = label_tag 'cc_cvv', t('.cvv')
            span.i-info.tooltip-item
              span.tooltip
                = t('.cvv_explanation_html')
            .input-wrapper
              = password_field_tag 'cc[cvv]', '', class: 'required validate', autocomplete: 'off'
          .expiration-month
            = label_tag 'cc_expiration_month', t('.expiration_month')
            span.i-info.tooltip-item
              span.tooltip
                = t('.expiration_month_explanation_html')
            .select-wrapper
              = select_tag 'cc[expiration_month]', options_for_select( (1..12), 1 ), include_blank: false, class: 'required'
          .expiration-year
            = label_tag 'cc_expiration_year', t('.expiration_year')
            span.i-info.tooltip-item
              span.tooltip
                = t('.expiration_year_explanation_html')
            .select-wrapper
              = select_tag 'cc[expiration_year]', options_for_select( (Time.now.year..(Time.now.year + 15)), Time.now.year ), include_blank: false, class: 'required'
          .installments
            = label_tag 'cc_installments', t('.installments')
            .select-wrapper
              = select_tag 'cc[installments]', options_for_select(t('.installments_default_options')), include_blank: false, class: 'required'
        .errors
    .payment-option.tickets
      i.i-radio-off
      .header
        h5.title= t('.tickets')
      .tickets-form style='display: none;'
        = form_tag '#' do
          .ticket-type
            = label_tag 'ticket_type', t('.ticket_type')
            .select-wrapper
              = select_tag 'ticket[type]', '', include_blank: false, class: 'required'
          .doc-type
            = label_tag  'ticket_doc_type', t('.doc_type')
            .select-wrapper
              = select_tag 'ticket[doc_type]', options_for_select(ar_identification_types_options, 'DNI'), include_blank: false, class: 'required validate'
          .doc-number
            = label_tag 'ticket_doc_number', t('.doc_number')
            .input-wrapper
              = text_field_tag 'ticket[doc_number]', nil,  class: 'required validate'

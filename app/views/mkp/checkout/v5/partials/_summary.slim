h4.i-flecha-right= " #{t('.title')}"
.product-list
  - @checkout_items.each do |item|
    .cart-item data-variant-id= item.variant_id
      .img
        img src=item.picture_thumb
      .data
        .name
          = item.name
        - unless item.has_no_property?
          .variant
            - item.properties.each do |k,v|
              - custom_property = find_hash_custom_properties(item.product[:available_properties], k)
              span.property class="#{'with-color-box' if k == :color && v.is_a?(Hash) && v[:hex].present?}"
                - name = get_name_for_property(item.product, k)
                = name

                - if k == :color && v.is_a?(Hash)
                  = v[:name]
                  - if v[:hex].present?
                    span.color-box style="background-color:#{v[:hex]}"
                - elsif custom_property.present?
                  = v
                - else
                  = v
                br
        .quantity
          = "#{t('.quantity')} #{item.quantity}"
        - if @checkout_items.size > 1
          a href='#' class='remove-item'
            = t('.remove')
      .price
        = number_to_currency(item.price)
- subtotal = @checkout_cart.subtotal
- promotion = @checkout_cart.get_promotion
.subtotal data-subtotal=subtotal data-discount=0 data-promotion_discount=promotion[:discount] data-promotion_name=promotion[:name]
  .subtotal_amount
    h4= t('.subtotal')
    .price
      = number_to_currency(subtotal)
  .discount style='display:none'
    h4= t('.discount')
    .price
  .delivery_cost
    h4= t('.total_shipping_cost')
    .price
  - if should_display_taxes?
    .taxes
      h4= t('.total_taxes')
      .price
  .handling-discount style='display:none'
    h4= t('.handling_discount')
    .price
  .promotion style='display:none;'
    h4= promotion[:name]
    .price
      = number_to_currency(promotion[:discount])
.total
  .order_total
    h3.order_price_with_financing= t('.total')
    .price_with_financing
      = number_to_currency(@checkout_cart.total)
    .financing_information
      .fees_and_interests
        h3= t('.interests')
        span
          = "0.00"
      .taxes
        span.cft
          = "CFT 0%"
        span.tea
          ="TEA 0%"
      .financed_price
        h3= t('.financed_total')
        span

= form_tag mkp_checkout_buy_path(network: @network.downcase), id: 'checkout-pay-form' do
  .hidden
    = hidden_field_tag 'fid', '', id: 'fid'
  .pay-button
    .button
      = t('.pay_button')
button id="MY_btnPagarConBilletera" class="hide"
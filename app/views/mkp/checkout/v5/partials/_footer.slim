- n = @network.downcase
- phone = Network[n].marketplace_customer_phone.presence
- cache_version = 'v9'
footer
  .bottom
    .row
      - cache ['footer', 'bottom', @network, I18n.locale, cache_version] do
        .payment-methods
          p = t('.payment_methods_title')
          .cards
            - payment_methods = t('.payment_methods')
            - check_payment_methods(payment_methods).each do |k, v|
              div class=k title=v
          p.secure.i-secure = t('.secure')

          p.secure style="max-width: 20em; text-align: left; margin-left: 20px; line-height: 1.5em"
            a itemprop='telephone' href="tel:#{t('.assistance.phone')}" = t('.assistance.tell_phone')
            br
            a href="mailto:#{t('.assistance.email')}" = t('.assistance.tell_email')

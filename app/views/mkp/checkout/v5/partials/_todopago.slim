.todopago
  .active-payments
    .payment-option.creditcards
      i.i-radio-on
      .header
        h5.title= t('.creditcards')
        span.todo_pago.extend-todo_pago
      .creditcard-form
        / Hidden fields for Todo Pago Hybrid Form
        .creditcard-todopago.hide
          label id="labelPromotionTextId"
          button id="MY_btnConfirmarPago"
        = form_tag '#' do
          .payment-method-name
            = label_tag 'formaDePagoCbx', t('.card_name')
            .select-wrapper
              select id="formaDePagoCbx"
          .payment-method-bank
            = label_tag 'bancoCbx', t('.bank_name')
            .select-wrapper
              select id="bancoCbx"
          .card-number
            = label_tag 'numeroTarjetaTxt', t('.card_number')
            .input-wrapper
              input id="numeroTarjetaTxt"
          .full-name
            = label_tag 'apynTxt', t('.full_name')
            span.i-info.tooltip-item
              span.tooltip
                = t('.full_name_explanation_html')
            .input-wrapper
              input id="apynTxt"
          .doc-type
            = label_tag  'tipoDocCbx', t('.doc_type')
            .select-wrapper
              select id="tipoDocCbx"
          .doc-number
            = label_tag 'nroDocTxt', t('.doc_number')
            .input-wrapper
              input id="nroDocTxt"
          .expiration-month
            = label_tag 'mesTxt', t('.expiration_month')
            span.i-info.tooltip-item
              span.tooltip
                = t('.expiration_month_explanation_html')
            .select-wrapper
              input id="mesTxt"
          .expiration-year
            = label_tag 'anioTxt', t('.expiration_year')
            span.i-info.tooltip-item
              span.tooltip
                = t('.expiration_year_explanation_html')
            .select-wrapper
              input id="anioTxt"
          .cvv
            label id="labelCodSegTextId" Cvv
            span.i-info.tooltip-item
              span.tooltip
                = t('.cvv_explanation_html')
            .input-wrapper
              input id="codigoSeguridadTxt"
          .installments
            = label_tag 'promosCbx', t('.installments')
            .select-wrapper
              select id="promosCbx"
              select.hide#backup-promos
          .emaily.hide
            = label_tag "emailTxt", "Email del comprador"
            .input-wrapper
              input id="emailTxt"
        .errors
    .payment-option.billetera
      i.i-radio-off
      .header
        h5.title Billetera virtual
        span.payment-icon-mercadopago
          img src="#{image_path("billetera.png")}"
    - unless Setting.first.only_ticket
      .payment-option.tickets
        i.i-radio-off
        .header
          h5.title= t('.tickets')
          span.payment-icon-mercadopago
            img src="#{image_path("mercadopago.png")}"
        .tickets-form style='display: none;'
          = form_tag '#' do
            .ticket-type
              = label_tag 'ticket_type', t('.ticket_type')
              .select-wrapper
                = select_tag 'ticket[type]', '', include_blank: false, class: 'required'
            .doc-type
              = label_tag 'ticket_doc_type', t('.doc_type')
              .select-wrapper
                = select_tag 'ticket[doc_type]', options_for_select(ar_identification_types_options, 'DNI'), include_blank: false, class: 'required validate'
            .doc-number
              = label_tag 'ticket_doc_number', t('.doc_number')
              .input-wrapper
                = text_field_tag 'ticket[doc_number]', nil, class: 'required validate'
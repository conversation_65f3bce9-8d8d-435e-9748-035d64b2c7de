- load_dropdowns = load_dropdowns == false ? false : true
header
  = render partial: 'partials/v5/header/slim_promo'
  nav.broad-nav
    ul
      li.logo itemscope=true itemtype='http://schema.org/Organization'
        a href=root_url title=t('.home')
          span = t('.home')
          img itemprop='logo' src=image_path(Social::HotSale.logoPath) alt='Avenida.com' onerror=svg_fallback
  = render partial: 'partials/v5/header/user_menu', locals: { display_cart: false }
  - if logged_in?
    .separator
  = render partial: 'partials/v5/footer/mkp_promos'
.header-phantom
.header-divider

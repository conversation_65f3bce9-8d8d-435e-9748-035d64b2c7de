.signup-body
    .logo
    h4.sign-up-message= "Sign Up!"
    p.sign-up-divider
      span.sign-up-email= "Stay in touch for community updates and special promotions!"
    .signup-post-checkout
      = form_for (PartialSignup.new) do |f|
      	p.input-title 
      	  = t('users.new.first_name')
        = f.text_field(:first_name, value: @order.customer.full_name)
        p.input-title 
          = t('users.new.email')
        = f.text_field(:email, value: @order.customer.email)
        p.input-title 
          = t('users.new.password')
        = f.text_field(:last_name, placeholder: t('users.new.password'))
        = f.submit('Sign Up', class: 'button')

.coupon
  .coupon-form
    h4.i-flecha-right
      = t('.title')
    = form_tag api_mkp_checkout_coupons_apply_path do
      = text_field_tag :coupon_code, nil, class: 'input-text', id: 'coupon-code', placeholder: "Escribir <PERSON>..."
      = hidden_field_tag :network, @network
    a.apply-coupon-button
      = t('.button')
    #coupon-errors
  .coupon-display
    = form_tag api_mkp_checkout_coupons_clear_path
      h3
        = t('.applied')
        span.coupon-applied
        span.remove-coupon
          = t('.remove')

= content_for :meta do
  meta name='google:maps' content=GOOGLE_MAPS

.pickup-location.hidden
  h4.i-flecha-right= " #{t('.title')}"
  .pickup-location-search-wrapper
    = label_tag 'cc_pickup_location_search', t('.search_label')
    .input-wrapper
      = text_field_tag nil, nil, id: 'cc_pickup_location_search', placeholder: t('.search_placeholder')
  .pickup-selector-wrapper.hidden
    = label_tag 'cc_pickup_location', t('.pickup_selector_label')
    .select-wrapper
      = select_tag 'cc[pickup_location]', nil, autocomplete: 'off', placeholder: 'Selecciona un punto de retiro en esta zona'
  .pickup-location-info

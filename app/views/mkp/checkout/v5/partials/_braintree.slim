.braintree
  .active-payments
    = form_tag '#', id: 'bt-form' do
      .payment-option.creditcards
        i.i-radio-off
        .header
          h5.title= t('.creditcards')
        .creditcard-form
          .card-number
            = label_tag 'cc_number', t('.creditcard_number')
            .input-wrapper
              = password_field_tag nil, '', id: 'cc_number', class: 'required validate', disabled: 'disabled', autocomplete: 'off'
          .cvv
            = label_tag 'cc_cvv', t('.cvv')
            span.i-info.cvv-explain.tooltip-item
              span.tooltip
                = t('.cvv_explanation_html')
            .input-wrapper
              = password_field_tag nil, '', id: 'cc_cvv', class: 'required validate', autocomplete: 'off'
          .full-name
            = label_tag 'cc_cardholder_name', t('.cardholder_name')
            .input-wrapper
              = text_field_tag nil, customer_full_name, id: 'cc_cardholder_name', class: 'required validate'
          .expiration-month
            = label_tag 'cc_expiration_month', t('.expiration_month'), class: 'ellipsis'
            .select-wrapper
              = select_tag 'cc[expiration_month]', options_for_select( (1..12), 1 ), include_blank: false, class: 'required validate', autocomplete: 'off'
          .expiration-year
            = label_tag 'cc_expiration_year', t('.expiration_year')
            .select-wrapper
              = select_tag 'cc[expiration_year]', options_for_select( (Time.now.year..(Time.now.year + 15)), Time.now.year ), include_blank: false, class: 'required validate', autocomplete: 'off'
          .billing-address
            .header
              h5.title= t('.billing-address')
            .fields
              .street-address
                = label_tag 'cc_street_address', t('.street_address')
                .input-wrapper
                  = text_field_tag nil, '', id: 'cc_street_address', class: 'required validate'
              .postal-code
                = label_tag 'cc_postal_code', t('.postal_code')
                .input-wrapper
                  = text_field_tag nil, '', id: 'cc_postal_code', class: 'required validate'
              .state
                = label_tag 'cc_state', t('.state')
                .select-wrapper
                  - states_options = states_select_options(@network)
                  = select_tag 'cc_state', create_custom_options_for_select(states_options, nil), include_blank: false, prompt: "#{t('.select_state') if states_options.present?}", autocomplete: 'off'
              .country
                = label_tag 'cc_country', t('.country')
                .select-wrapper
                  - country_options = country_select_options(@network)
                  - default_country = @network
                  = select_tag 'cc_country', options_for_select(country_options, default_country), include_blank: false, prompt: t('.select_country'), class: "#{'no-options' if country_options.length <= 1}", autocomplete: 'off'

        .errors
      .payment-option.paypal
        i.i-radio-off
        .header
          h5.title= t('.paypal')
          #paypal-container

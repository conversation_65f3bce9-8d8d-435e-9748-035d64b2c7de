- suborder_public_ids = @order.suborders.flat_map(&:public_id)
.thanks-body
  h3 = t('mkp.checkout.v5.partials.thanks.subtitle', name: @order.customer.first_name.titleize)
  .i-tilde
  .text
    h2 = t('mkp.checkout.v5.partials.thanks.body.thanks')
    .suborders
      - if suborder_public_ids.length > 1
        = t('mkp.checkout.v5.partials.thanks.body.multiple_suborders', ids: suborder_public_ids.join(', #'))
      - else
        = t('mkp.checkout.v5.partials.thanks.body.single_suborder', id: suborder_public_ids.first)
    .message = t(@order.pickeable? ? 'mkp.checkout.v5.partials.thanks.body.pickeable_message' : 'mkp.checkout.v5.partials.thanks.body.message')
    - if @ticket.present?
      = link_to t('mkp.checkout.v5.stompedit.print_ticket'), @ticket, target: '_blank',  class: "ticket-button"
    .email_awareness = t((@order.pickeable? ? 'mkp.checkout.v5.partials.thanks.body.pickeable_email_aware_html' : 'mkp.checkout.v5.partials.thanks.body.email_aware_html'), email: @order.customer.email)
    = image_tag(VEINTERACTIVE_PIXEL, size: 1) if VEINTERACTIVE_ENABLED
    = link_to t('mkp.checkout.v5.partials.thanks.continue'), Mkp::Catalog::UrlHandler.path(network: @network.downcase, d: 1), class: "continue-button i-carrito"

- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;" }
	%div.body
		%h2 
			=t('.hi',name: @customer.first_name, last:@customer.last_name)
		%p
			=t('.success') 
		%table.order-item{ :style => "width: 100%;" }
			%tbody
				%tr.border-bottom
					%td
						%div{ :style => "margin-right: 150px" }
							%strong
								= t('.details').upcase
							%p
								=t('.date')
								%strong
									= (@purchase.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")
							%p
								=t('.full_phone_number')
								%strong
									= "(#{@phone_code}) #{@phone_number}"
							%p 
								=t('.amount')
								%strong
									= number_to_currency(@purchase.total, unit: current_currency_format)
							%p 
								=t('.points')
								%strong
									= number_with_delimiter(@purchase.total_points, :delimiter => '.')
							%p
								=t('.transaction_number') 
								%strong
									= @purchase.id
					%td
						%p
							- if @phone_company == 'personal'
								= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/personal.png", size: '120x120'
							- if @phone_company == 'claro'
								= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/claro.png", size: '120x120'
							- if @phone_company == 'movistar'
								= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/movistar.png", size: '120x120'
							- if @phone_company == 'tuenti'
								= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/tuenti.png", size: '120x120'	
		.highlight{ :style => "padding: 15px; background-color: #BFBFBF" }
			.container-highlight
				%p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" }
					= t('.summary')
				%p{ :style => "margin-top: 20px" }
					%strong
						= number_to_currency(@purchase.total.round, unit: current_currency_format)
						= " de recarga por "
						= number_with_delimiter(@purchase.total_points, :delimiter => '.')
						= " Puntos Macro Premia"
		%p{ :style => "text-align: justify;" }
			=t('.legal')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal2')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal3')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal4')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal5')
%br
= render partial: 'v5/partials/mailer/contact_us'

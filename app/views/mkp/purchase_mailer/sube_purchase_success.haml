- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;" }
	%div.body
		%h2 
			=t('.hi',name: @customer.first_name, last:@customer.last_name)
		%p
			=t('.success') 
			=link_to raw('Terminal Automática'), 'https://tarjetasube.sube.gob.ar/subeweb/WebForms/admin/views/mapa-sube.aspx?id=5', :title => t('.terminal')
			=t('.else')
			=link_to raw('App Carga SUBE'), 'https://www.argentina.gob.ar/sube/carga-sube-beta', :title => t('.app')
		%table.order-item{ :style => "width: 100%;" }
			%tbody
				%tr.border-bottom
					%td
						%div{ :style => "margin-right: 150px" }
							%strong
								= t('.details').upcase
							%p
								=t('.date')
								%strong
									= (@purchase.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")
							%p
								=t('.card_number')
								%strong
									=@card_number
							%p 
								=t('.amount')
								%strong
									= number_to_currency(@purchase.amount, unit: current_currency_format)
							%p 
								=t('.points')
								%strong
									= number_with_delimiter(@purchase.points, :delimiter => '.')
							%p
								=t('.transaction_number') 
								%strong
									=@purchase.external_transaction_id
					%td
						%p
							= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/sube.jpg", size: '120x120'
		.highlight{ :style => "padding: 15px; background-color: #BFBFBF"}
			.container-highlight
				%p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" }
					= t('.summary')
				%p{ :style => "margin-top: 20px" }
					%strong
						= number_to_currency(@purchase.amount.round, unit: current_currency_format)
						= " de carga en tu tarjeta SUBE por "
						= number_with_delimiter(@purchase.points, :delimiter => '.')
						= " Puntos Macro Premia"
		%p{ :style => "text-align: justify;" }
			=t('.legal')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal2')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal3')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal4')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal5')
%br
= render partial: 'v5/partials/mailer/contact_us'

- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;"}
	%div.body
		%h2 
			=t('.hi',name: @customer.first_name, last:@customer.last_name)
		%p
			=t('.success') 
		%table.order-item{ :style => "width: 100%;" }
			%tbody
				%tr.border-bottom
					%td
						%div{ :style => "margin-right: 150px" }
							%strong
								= t('.details').upcase
							%p
								=t('.date')
								%strong
									= (@purchase.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")
							%p
								=t('.miles')
								%strong
									= number_with_delimiter(@purchase.miles, :delimiter => '.')
							%p 
								=t('.points')
								%strong
									= number_with_delimiter(@purchase.points, :delimiter => '.')
							%p
								=t('.transaction_number') 
								%strong
									=@purchase.id
							%p 
								-if  @purchase.document_type == 'SOC'
									=t('.frequent_traveller') 
								-if  @purchase.document_type == 'DNI'
									=t('.document') 
								-if  @purchase.document_type == 'PAS'
									=t('.passport') 
								%strong
									=@purchase.document_number
					%td
						%p
							= image_tag "https://s3.amazonaws.com/a01.gpstatic.com/integrations/aerolineasplus.png", size: '210x40'
		%p
			=t('.disclaimer')
		.highlight{ :style => "padding: 15px; background-color: #BFBFBF" }
			.container-highlight
				%p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" } 
					= t('.summary')
				%p{ :style => "margin-top: 20px" }
					%strong
						= number_with_delimiter(@purchase.miles, :delimiter => '.')
						= " Millas Aerolineas Plus por "
						= number_with_delimiter(@purchase.points, :delimiter => '.')
						= " Puntos Macro Premia"
		%p{ :style => "text-align: justify;" }
			=t('.legal')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal2')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal3')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal4')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal5')
%br
= render partial: 'v5/partials/mailer/contact_us'

%body{ :style => "font-family:Verdana, Geneva, sans-serif;" }
    
    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 10px 0px;" }
        %tr 
            %td
                %h1{ :style => "font-family: Georgia, Times, 'Times New Roman', serif; margin-top:0px;" }
                    ="Hola, Sucursal #{@id_store}"
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "El concesionario #{@shop.title} solicita la cancelación de la siguiente reserva que se encuentra en estado SOLICITAR CANCELACION A SUCURSAL. Luego de verificada la situación con el cliente, pasar la orden a estado NO APROBADO, para que el stock sea devuelto en el Marketplace."

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed;" }
        %tr 
            %td
                %h3{ :style => "margin-bottom:5px;" }
                    ="Datos del cliente"
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Nombre: #{@customer.first_name.capitalize()} #{@customer.last_name.capitalize()}"
                    %br
                    = "ID de solicitud: #{@order.id}"
                    %br
                    = "CUIL: #{@order.customer.doc_number}"
                    %br
                    = "Monto Preaprobado: #{number_to_currency(@bna_info.limit_amount, unit: "$", separator: ",", delimiter: ".")}" if @bna_info
                    %br
                    = "Sucursal: #{@id_store}"
                    %br
                    = "Email Solicitante: #{@customer.email}"
                    %br
                    = "Telefono: #{@customer.telephone}"

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 20px 0px;" }
        
        %tr
            %td{ :width => "100", :height => "100", :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing:0; padding-top:18px; padding-right:20px;"}
                
                = image_tag @variant.picture.url(:st), width: '100' if @variant.picture

            %td{ :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing: 0; margin: 0; padding: 0; padding-top: 15px; color: #000000; "}
                
                %h3{ :style => "margin-bottom:5px;" }
                    ="Detalle de la reserva - Cod. #{@order.id}"
                
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Fecha y Hora: #{(@order.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")}"
                    %br
                    = "Modelo: #{@variant.title}"
                    %br
                    = "Código de reserva: #{@order.id}"
                    %br
                    = "Método de pago: Prestamo BNA"
                    %br
                    = "Método de envío: Retiro por concesionaria "
                    %br
                    = "Total del préstamo utilizado: #{number_to_currency(@order.total, unit: "$", separator: ",", delimiter: ".")}: "
    
    
    %table#footer{ :align => 'center', :bgcolor => "#017894", :style => "padding:20px 30px; width: 100%; max-width: 600px;" }
        %tr
            %td
                %h3{ :color => "#ffffff", :align => 'left', :style => "color:#fff; margin:0px;"}
                    = "Por cualquier consulta puede contactarse con:"
                %a{ :style => "color:#ffffff; font-size:12px; line-height: 20px; margin:0px;"  }
                    = "<EMAIL> "

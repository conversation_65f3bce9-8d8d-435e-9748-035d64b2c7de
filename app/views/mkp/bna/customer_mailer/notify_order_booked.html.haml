%body{ :style => "font-family:Verdana, Geneva, sans-serif;" }
    
    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 10px 0px;" }
        %tr 
            %td
                %h1{ :style => "font-family: Georgia, Times, 'Times New Roman', serif; margin-top:0px;" }
                    ="Hola, #{@customer.first_name.capitalize()} #{@customer.last_name.capitalize()}"
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "Felicitaciones por tu reserva en Tienda BNA. Dentro de las próximas 72 horas hábiles te contactaremos desde la Sucursal del Banco Nación que elegiste para que te acerques a conformar tu crédito y finalizar la solicitud de compra. En caso de que esto no suceda podrás concurrir a la Sucursal directamente con tu DNI y este correo electrónico."
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "Tené en cuenta que de no ser Aprobada, una vez transcurridos 15 días corridos, tu reserva será cancelada."
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "Si todavía no abriste tu cuenta es fundamental que, antes de concurrir a la sucursal realices la apertura desde BNA+. Ya nos ocupamos de notificar al concesionario elegido de tu reserva."
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "Recibirás otras notificaciones vía email cuando tu reserva avance."

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 0px 0px 30px 0px;" }
        
        %tr
            %td{ :width => "100", :height => "100", :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing:0; padding-top:18px; padding-right:20px;"}
                
                = image_tag @variant.picture.url(:st), width: '100' if @variant.picture

            %td{ :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing: 0; margin: 0; padding: 0; padding-top: 15px; color: #000000; font-family: sans-serif;"}
                
                %h3{ :style => "margin-bottom:0px;" }
                    ="Detalle de la reserva - Cod. #{@order.id}"
                
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 15px; margin:3px 0px;"  }
                    = "Fecha y Hora: #{(@order.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")}"
                    %br
                    = "Modelo: #{@variant.title}"
                    %br
                    = "Código de reserva: #{@order.id}"
                    %br
                    = "Método de pago: Prestamo BNA"
                    %br
                    = "Método de envío: Retiro por concesionaria "
                    %br
                    = "Total del préstamo utilizado: #{number_to_currency(@order.total, unit: "$", separator: ",", delimiter: ".")}: "
    
    %table#footer{ :align => 'center', :bgcolor => "#017894", :style => "padding:20px 30px; width: 100%; max-width: 600px;" }
        %tr
            %td
                %h2{ :color => "#ffffff", :align => 'left', :style => "color:#fff; margin:0px;"}
                    = "Gracias por tu reserva"

- I18n.locale = @locale

- if @seller.present?
  %p
    - if @network == 'US'
      = t('.congrats_en', customer_name: @customer.first_name)
    - else
      #{t('.congrats_pre')} <a href="#" target="_blank">@#{@seller.login}</a>#{t('.congrats_post',store: @suborder.store.title.titleize) }

= render :partial => 'mailer/partials/user', :locals => { user: @customer, title: t('.data_title') }
%br

%table.email_item.shop_paid_confirmation
  %tbody
    %tr
      %td{ colspan: 2 }
        %h3{ style: 'margin-bottom: 0;'}= t('.order_title')
    %tr
      %td{ colspan: 2 }
        - @suborder.items.each do |item|
          = render :partial => 'mailer/partials/order_item', locals: { item: item, is_admin: true }

    %tr
      %td{ colspan: 2 }
        %br/
        - if @suborder.has_coupon?
          %p
            %strong #{t('.coupon_bonification')}:
            = number_to_currency(@suborder.coupon_discount)
        - if @suborder.have_taxes?
          %p
            %strong #{t('.taxes')}:
            = number_to_currency(@suborder.taxes)
        - unless @suborder.fulfilled_by_gp?
          - if @suborder.has_shipments?
            - if @suborder.has_shipment_bonification?
              %p= t('.shipping_bonification')
            - else
              %p
                %strong #{t('.shipping_service')}:
                #{@shipment.gateway_data[:service_level]}
              - if @shipment.extra_info[:carrier].present?
                %p
                  %strong #{t('.shipping_carrier')}:
                  #{@shipment.extra_info[:carrier].camelize}

              %p
                %strong #{t('.shipping_cost')}:
                = number_to_currency @shipment.charged_amount
            %p
              %strong #{t('.shipping_to')}:
              .destination-address{style: 'margin-left: 10px'}= render :partial => 'partials/mailer/address', :locals => {address: @shipment.destination_address}
%p
  %strong #{t('.total')}:
  #{number_to_currency(@suborder.total)}
- fulfilled = @suborder.fulfilled_by_gp?
- if @shipment.present? && !fulfilled
  .shipping-buttons
    -#%span
    -#  = link_to t('.download_packing_list'), @packing_list_url, style: 'text-decoration:none;color:#fff'
    - if customer_choose_label?(@shipment) || allowed_to_purchase_labels_on_demand?(@shop)
      %span
        = link_to t('.download_shipping_labels'), 'https://api.avenida.com.ar' + lux.shop_suborder_path(id: @suborder.public_id, shop_id: @shop, network: @network.downcase ), style: 'text-decoration:none;color:#fff', target: '_blank'
    - else
      %span
        = link_to t('.enter_tracking_number'), lux.shop_suborder_url(id: @suborder.public_id, shop_id: @shop, network: @network.downcase), target: "_blank"

- elsif fulfilled
  %p
    %strong
      = t('.fulfillment_message', store: @suborder.store.title.titleize)
%p
  %strong
    = t('.purchase_code')
  = link_to @suborder.public_id, lux.shop_suborder_url(id: @suborder.public_id, shop_id: @suborder.shop.friendly_id, network: @suborder.shop.network.downcase), target: "_blank"

- I18n.locale = @locale
%p
  = t('.congrats', shops_titles: @shops_titles, count: @order.suborders.count,store: @order.store.title.titleize)

= render partial: 'mailer/partials/user', locals: { user: @customer, title: t('.data_title') }
%br

- @order.suborders.each do |suborder|
  = render partial: 'mailer/partials/shop', locals: { shop: suborder.shop, title: t('.seller_data') }
  %table.email_item.w530
    %tbody
      %tr
        %td{ colspan: 2 }
          %h3.product= t('.order_products')
      %tr
        %td{ colspan: 2 }
          - suborder.items.each do |item|
            = render partial: 'mailer/partials/order_item', :locals => { item: item, is_admin: true }
      %tr
        %td{ colspan: 2 }
          %br/
          - if suborder.has_coupon?
            %p
              %strong #{t('.coupon_bonification')}:
              = number_to_currency suborder.coupon_discount, unit: current_currency_format
          - if suborder.have_taxes?
            %p
              %strong #{t('.taxes')}:
              = number_to_currency suborder.taxes, unit: current_currency_format
          - if suborder.has_shipments?
            - if suborder.has_shipment_bonification?
              %p= t('.shipping_bonification')
            - else
              %p
                %strong #{t('.shipping_service')}:
                #{suborder.shipment.gateway_data[:service_level]}
              - if suborder.shipment.extra_info[:carrier].present?
                %p
                  %strong #{t('.shipping_carrier')}:
                  #{suborder.shipment.extra_info[:carrier]}
              %p
                %strong #{t('.shipping_cost')}:
                = number_to_currency suborder.shipment.charged_amount, unit: current_currency_format

            %p
              %strong #{t('.shipping_to')}:
              .destination-address= render partial: 'partials/mailer/address', locals: {address: suborder.shipment.destination_address}
%br

- if @order.coupon.present?
  %p
    %strong #{t('.coupon_bonification')}:
    = number_to_currency @order.coupon_discount, unit: current_currency_format
%p
  %strong #{t('.total')}:
  #{number_to_currency(@order.total, unit: current_currency_format)}
%p
  %strong #{t('.purchase_code')}:
  #{@transaction_id}

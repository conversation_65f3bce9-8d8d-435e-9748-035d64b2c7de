- I18n.locale = @locale
%h2= t('.hi', name: @customer.first_name)
%p
  = t('.success')
  %br
  = @order.shipment_is_pickup? ? t('.confirmation_pickit') : t('.confirmation')
- if @gateway.present?
  %p
    = t('.gateway', gateway:@gateway.titleize).html_safe
%h5= t('.summary').upcase
%table.order-item{ :style => "width: 100%;" }
  %tbody
    - @order.shipments.each do |shipment|
      - shipment.items.each do |item|
        = render partial: 'mailer/partials/order_item', locals: {item: item}
%br
- shipment = @order.shipments.first
= render partial: 'mailer/partials/shipped', locals: {shipment: shipment}
.highlight
  %div.container-highlight
    - if @order.avg_of_discount_applied > 0
      %p
        %strong #{t('.discount')}:
        = t('.applied_discount', avg: @order.avg_of_discount_applied)
    %p
      %strong #{t('.shipping_cost')}:
      %span.currency= number_to_currency(@order.shipments_cost, unit: current_currency_format)
    %p
      %strong{'font-size': 'x-large'} #{t('.total')}:
      - if @order.points > 0
        = @order.total_points
        = " puntos - "
        = number_to_currency(@order.total_without_points, unit: current_currency_format)
      - else
        = number_to_currency(@order.total_without_points, unit: current_currency_format)
%br
= render partial: 'v5/partials/mailer/contact_us'

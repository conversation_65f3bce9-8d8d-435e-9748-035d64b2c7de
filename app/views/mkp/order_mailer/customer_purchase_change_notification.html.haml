%h2= t('.hi', name: @customer.full_name)

- if @current_store.name == 'bancomacro'
  %p
    = t('.msg_macro1', transaction_id: @order.id)
    %br
    %br
    = t('.msg_macro2')
    %br
    = t('.msg_macro3')
- else
  - if @order.respond_to?(:cancel_from_worker) && @order.cancel_from_worker.present?
    %p
      = t('.msg_html', customer_name: (@customer.first_name || @customer.login), order_title: @order.title)
    %p
      = t('.msg2_html', store_email: @order.store.email)
  - else
    %p
      = t('.msg3_html')
      %br
      = t('.msg4_html')
%br
%hr
= render partial: 'v5/partials/mailer/contact_us'

- I18n.locale = @locale

%p{:style => "margin-top:0;"}
  #{t('.hi')} <a href="#profiles_url" target="_blank">@#{@seller.login}</a>#{t('.fulfilled_msg')}

= render :partial => 'mailer/partials/user', :locals => { user: @customer, title: t('.data_title') }

%table.email_item{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :style => "margin:0 auto;", :width => "560"}
  %tbody
    - @suborders.each do |suborder|
      %tr
        %td{ colspan: 2 }
          %br
          %h3{ style: 'margin-bottom: 0;'}= t('.shop_title', shop: suborder.shop.title)
      %tr
        %td{ colspan: 2 }
          %p
            %b #{t('.purchase_code')}:
            = suborder.purchase_id
      %tr
        %td{ colspan: 2 }
          - suborder.items.each do |item|
            = render :partial => 'mailer/partials/order_item', :locals => { item: item, is_admin: true }

- suborder = @suborders.first
- if suborder.has_shipments?
  %h3{ style: 'margin-bottom: 0;'}= t('.shipping_title')
  - if suborder.has_shipment_bonification?
    %p= t('.shipping_bonification')
  - else
    %p
      %strong #{t('.shipping_service')}:
      #{suborder.shipment.gateway_data[:service_level]}
    - if suborder.shipment.extra_info[:carrier].present?
      %p
        %strong #{t('.shipping_carrier')}:
        #{suborder.shipment.extra_info[:carrier].camelize}

    %p
      %strong #{t('.shipping_cost')}:
      = number_to_currency suborder.shipment.charged_amount
  %p
    %strong #{t('.shipping_to')}:
    .destination-address{style: 'margin-left: 10px'}= render :partial => 'partials/mailer/address', :locals => {address: suborder.shipment.destination_address}
%br
%p
  %strong #{t('.questions')}
  =t('.contact_us')
  %a{href: "mailto:#{@current_store.email}", taget: '_blank'} #{@current_store.email}

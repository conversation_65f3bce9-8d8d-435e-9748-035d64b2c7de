- I18n.locale = @locale
- address_to_print = []
- address_to_print << @shipment.destination_address.address
- address_to_print << @shipment.destination_address.address_2
- address_to_print << @shipment.destination_address.city
- address_to_print << @shipment.destination_address.state
- address_to_print << @shipment.destination_address.zip.to_s
- address_to_print << GeoConstants::Countries.name_for_code(@shipment.destination_address.country) if @shipment.destination_address.country.present?
- order_link = lux.shop_suborder_url(id: @suborder.public_id, shop_id: @suborder.shop.friendly_id, network: @suborder.shop.network.downcase)
%p
  = t(".reminder_title_#{@timeframe}_html", suborder_link: order_link, suborder_id: @suborder.public_id)
  %br
  %br
  = t(".reminder_text_#{@timeframe}_html")

%table.shipping-table
  %tbody
    %tr
      %td{:colspan => 2}
        %h3{:style=>"margin-bottom: 0;"}
          = t('.shipment_title')
    %tr
      %td{:style=>"width: 120px; text-align: right;"}
        %strong= t('.name')
      %td= @shipment.destination_address.full_name.try(:titleize)
    %tr
      %td{:style=>"width: 120px; text-align: right;"}
        %strong= t('.address')
      %td= address_to_print.compact.reject(&:empty?).join(', ').titleize
    %tr
      %td{:colspan => 2}
        %br
        %h3{:style=>"margin-bottom: 0;"}= t('.products')
    %tr
      %td{:colspan => 2}
        %table.order-items{ :style => "width: 100%;" }
          - @suborder.items.each do |item|
            - variant = item.variant
            %tr
            %td{:style=>"width: 50px; text-align: center;"}
              = image_tag variant.picture.url(:st), size: '30x30' if variant.picture
            %td
              = variant.product.title.titleize
              - if variant.sku.present?
                %br
                %strong SKU:
                %span= variant.sku
              - variant.properties.keys.each do |property|
                %br
                %strong= "#{property.to_s.capitalize}: "
                - if property.to_sym == :color
                  %span= variant.color_name
                - else
                  %span= variant.properties[property]
              = " | "
              %strong= t('.quantity')
              %span = item.quantity
%br
  %div.shipping-buttons
    - if customer_choose_label?(@shipment) || allowed_to_purchase_labels_on_demand?(@shop)
      - if allowed_to_purchase_labels_on_demand?(@shop)
        - gateway = @shop.setting.label_gateways.last
        - if gateway && gateway.needs_extra_docs_to_ship?
          %div.label-buttons
            %span
              = link_to t('.download_shipping_labels'), order_link, style: 'text-decoration:none;color:#fff', target: '_blank'
            %span
              = link_to t('.download_shipping_label_extra_docs'), @shipment_label_extra_docs_url, style: 'text-decoration:none;color:#fff', target: '_blank'
        - else
          %span
            = link_to t('.download_shipping_labels'), order_link, style: 'text-decoration:none;color:#fff', target: '_blank'
      - else
        %span
          = link_to t('.download_shipping_labels'), order_link, style: 'text-decoration:none;color:#fff', target: '_blank'
    - else
      %span
        = link_to t('.enter_tracking_number'), lux.shop_suborder_url(id: @suborder.public_id, shop_id: @suborder.shop.friendly_id, network: @suborder.shop.network.downcase), target: "_blank"

%br
%p= t('.issues_html', store_email: @suborder.store.email)
%br
%div.shipping-own-labels
  %p.title
    %strong= t('.own_labels_title')
  %p= t('.own_labels_text_html')
%br

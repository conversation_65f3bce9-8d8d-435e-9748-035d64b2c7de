.question-form.jscomponent data-component='question_form'
  - if current_user
    .avatar-container
      = render partial: 'partials/v5/avatar', locals: { user: current_user }
    = form_for @product.questions.new, url: mkp_product_questions_path(@product) do |f|
      .content
        .textarea
          = f.text_area :description, placeholder: t('.description.placeholder'),
                                      title: t('.description.name'),
                                      maxlength: html5_maxlength(Mkp::Question, :description),
                                      rows: 2,
                                      autofocus: true
        .actions
          button type='submit'= t('.submit')
          a.faqs-link href=pages_faqs_path(network: @network.downcase) target='_blank'
            = t('.faqs_button')
  - else
    .logged-in-required= t('.logged_in_required')

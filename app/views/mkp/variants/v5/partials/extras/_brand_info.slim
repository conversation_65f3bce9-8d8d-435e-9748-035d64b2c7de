- manufacturer = @product.manufacturer
- if description = manufacturer.get_product_description(@network)
  section.brand-info
    a href="#"= t('.tab_title', brand: manufacturer.name)
    .content
      - related_brand = manufacturer.related_brand(@network)
      a href=(related_brand ? profiles_path(login: related_brand.login) : "#") target="_blank"
        img src=manufacturer.logo.url(:lt) alt=manufacturer.name
      = simple_format(description)

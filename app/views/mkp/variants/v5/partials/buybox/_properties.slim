.product-properties
  .properties-wrapper
    - if @product.has_no_property?
      .noproperty
    - else
      - properties = @product.available_properties
      - properties.each do |property_name|
        - partial_name = [:color, :size].include?(property_name) ? property_name : :other
        = render partial: "mkp/variants/v5/partials/buybox/properties/#{partial_name}", locals: {property_name: property_name, product: @product}
  = render partial: 'mkp/variants/v5/partials/buybox/quantity'

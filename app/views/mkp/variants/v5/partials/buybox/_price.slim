- currency_symbol = @product.currency.symbol
- sale_detector = product_sale_detector(@product)
.product-price class="#{'on-sale' if sale_detector.is_on_sale?}"
  .price itemscope=true itemprop='offers' itemtype='http://schema.org/Offer'
    - stock = @product.total_stock_cached > 0 ? 'InStock' : 'OutOfStock'
    meta itemprop='availability' itemtype='http://schema.org/ItemAvailability' content="http://schema.org/#{stock}"
    meta itemprop='priceCurrency' content=currency_symbol
    span.currency=currency_symbol
    span.amount itemprop='price'= price_format(@product.regular_price)
  - if sale_detector.is_on_sale?
    .sale-price
      span.currency = currency_symbol
      span.amount = price_format(sale_detector.calculate)
      span.discount = t(".percent_off", percentage: sale_detector.percent_off)

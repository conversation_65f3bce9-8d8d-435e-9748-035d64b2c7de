.product-name
  h1.name
    a itemprop='url' href=@product.get_url(only_path: false)
      span itemprop='name'
        = @product.title
.brand-name-wrapper
  - manufacturer = @product.manufacturer
  hr
  .brand-name itemprop='manufacturer' itemscope=true itemtype='http://schema.org/Organization'
    meta itemprop='logo' content=manufacturer.logo.url(:lt)
    span=t('.brand')
    a itemprop='url' href=UrlMapperHelper.absolute_mapper_path(manufacturer, @network.downcase)
      span itemprop='name'= manufacturer.name
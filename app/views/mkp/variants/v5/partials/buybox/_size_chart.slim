#size-chart style='display:none;'
  .size-chart
    h2= t('mkp.variants.show.size_chart')
    .content
      - category = settings[:category]
      - genders = settings[:genders]
      - network = @network.downcase
      - if network == 'ar' && (genders.include?('ninos') || genders.include?('ninas'))
        = render partial: "partials/v5/size_charts/sc_ar_kids_#{category}"
      - if genders.empty? || genders.include?('men') || genders.include?('hombres')
        = render partial: "partials/v5/size_charts/sc_#{network}_men_#{category}"
      - if genders.empty? || genders.include?('women') || genders.include?('mujeres')
        = render partial: "partials/v5/size_charts/sc_#{network}_women_#{category}"


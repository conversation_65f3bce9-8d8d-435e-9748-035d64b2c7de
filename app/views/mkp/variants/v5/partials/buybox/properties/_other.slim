- class_name = property_name.is_a?(Hash) ? property_name[:slug] : property_name.to_s
.other-property class=class_name data-property=class_name
  - name = product.available_properties_names.key?(property_name) && product.available_properties_names[property_name].present? ? product.available_properties_names[property_name] : get_property_name(property_name)
  - name = name.titleize
  h4= name
  select
    - @product.available_values_for(property_name).each do |prop_value|
      option value=prop_value disabled=true
        = prop_value

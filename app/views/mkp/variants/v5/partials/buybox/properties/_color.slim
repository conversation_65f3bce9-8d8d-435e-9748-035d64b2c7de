.color-property
  h4= t('.available_colors')
  - colors = @product.available_values_for(:color)
  - if colors.all? { |c| c[:hex].present? }
    ul
      - colors.each do |color|
        - hex, name, slug_name = color.values_at(:hex, :name, :slug_name)
        - generic_hex = Mkp::ProductColors.generic_hex_for(hex)
        li.color data-color=slug_name data-color-name=name style="background-color:#{hex};#{'border-color:#c7c7c7' if generic_hex == '#ffffff'};" class=(name == (@selected_color[:name]) && 'selected')
          a href=@product.get_url(color: slug_name)
  - else
    select
      - colors.each do |color|
        - hex, name, slug_name = color.values_at(:hex, :name, :slug_name)
        option.color value=slug_name data-color=slug_name data-color-name=name selected= (name == (@selected_color[:name]) && 'selected') class=(name == (@selected_color[:name]) && 'selected')
          = name

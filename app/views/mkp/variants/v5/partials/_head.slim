- title = product_head_title(@product, @selected_color)
- description = get_meta_description
- canonical_url = @product.get_url(only_path: false)
- price, price_title = price_metas(@product)
- currency = @product.currency

= content_for(:title, title)

= content_for :meta do
  -# BEWARE, rel="canonical" is neccesary also for the JS routing.
  link rel='canonical' href=canonical_url
  meta property='product:brand' content=@product.manufacturer_name
  - if @selected_color.present?
    meta property='product:color' content=@selected_color[:name]
  meta property='product:price:amount' content=price
  meta property='product:price:currency' content=currency.identifier
  meta name='description' content=description
  meta name='keywords' content=keywords_for(@product)

  = render partial: 'partials/v5/meta/twitter',
           locals: product_twitter_card(canonical_url, @product, @selected_color, price, price_title, currency)

= content_for :og_meta do
    = render partial: 'partials/v5/meta/open_graph',
             locals: product_open_graph(canonical_url, @product, @selected_color)

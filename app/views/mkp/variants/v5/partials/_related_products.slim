- if request.params['store_id'].present? && request.params['store_id'].scan(/\D/).empty? && Integer(request.params['store_id']) != 21
  .related-products#related-products
    h3= t('.title')
    = render partial: 'partials/v5/variants/list', locals: { variants: @recommended_products, list_name: 'related-products'}
    = render partial: 'partials/v5/load_more_wide', locals: { style: 'white-blue', attrs: { title: t('.see_more', name: @product.category.name), href: UrlMapperHelper.absolute_mapper_path(@product.category, @network) } }

- content_for :analytics
  = track_product_detail(@product)

= render partial: 'mkp/variants/v5/partials/head'
.gray-color
  main
    section.product-breadcrumb
      = render partial: 'mkp/catalog/v5/partials/breadcrumb'
    article itemscope=true itemtype='http://schema.org/Product'
      - if @selected_color.present?
        meta itemprop='color' content=@selected_color[:name]
      .product-main
        - if @unavailable
          = render partial: 'mkp/variants/v5/partials/other_options'
        = render partial: 'mkp/variants/v5/partials/pictures'
        = render partial: 'mkp/variants/v5/partials/buybox'
      = render partial: 'mkp/variants/v5/partials/product_extras'
      = render partial: 'mkp/variants/v5/partials/related_products'

- content_for :before_js do
  javascript:
    gp = window.gp || {}
    gp.product = #{ @product_json.merge({compatibility: Mkp::Product::COMPATIBILITY_PROPERTIES}).to_json.html_safe }
    gp.selectedColor = '#{ @selected_color.blank? ? "" : @selected_color[:slug_name] }'
    gp.bootstrapVariant = #{ bootstrap_variant(@product, @selected_color) }
    gp.google_tag_params = #{ INCLUDE_ANALYTICS ? google_tag_params(@product).html_safe : {} }

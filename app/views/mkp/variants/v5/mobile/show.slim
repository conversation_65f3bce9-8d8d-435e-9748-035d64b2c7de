- content_for :analytics
  = track_product_detail(@product)

= render partial: 'mkp/variants/v5/partials/head'

main
  article itemscope=true itemtype='http://schema.org/Product'
    - if @selected_color.present?
      meta itemprop='color' content=@selected_color[:name]
    .product-main
      = render partial: 'mkp/variants/v5/mobile/partials/title_and_pictures'
      = render partial: 'mkp/variants/v5/mobile/partials/buybox'
    = render partial: 'mkp/variants/v5/partials/product_extras'
    - if @unavailable
      = render partial: 'mkp/variants/v5/partials/related_products'

- content_for :before_js do
  javascript:
    gp = window.gp || {}
    gp.product = #{ @product_json.merge({compatibility: Mkp::Product::COMPATIBILITY_PROPERTIES}).to_json.html_safe }
    gp.selectedColor = '#{ @selected_color.blank? ? "" : @selected_color[:slug_name] }'
    gp.bootstrapVariant = #{ bootstrap_variant(@product, @selected_color) }
    gp.google_tag_params = #{ INCLUDE_ANALYTICS ? google_tag_params(@product).html_safe : {} }

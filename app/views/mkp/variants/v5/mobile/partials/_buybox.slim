section.buybox
  = render partial: 'mkp/variants/v5/partials/buybox/price_and_rate'
  - if @unavailable
    = render partial: 'mkp/variants/v5/partials/buybox/unavailable'
  - else
    = render partial: 'mkp/variants/v5/partials/buybox/properties'
    = render partial: 'mkp/variants/v5/partials/buybox/actions'
  = render partial: 'mkp/variants/v5/partials/services'
  .comment
    = t('mkp.variants.v5.partials.buybox.price.currency', name: @product.currency.name.pluralize)

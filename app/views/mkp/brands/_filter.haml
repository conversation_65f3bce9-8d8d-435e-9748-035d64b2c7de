.filter-wrapper
  - if @active_filter.present?
    .filter-option{ :class => 'active-filter' }
      %h3= t('mkp.variants.filter.filtering_by')
      %ul
        %li
          %a.button.red{ href: mkp_brands_path }
            = @active_filter.name
            %span.close

  - @filter.each_pair do |k, v|
    .filter-option
      %h3
        = v[:title]
      %ul
        - v[:items].each do |item|
          %li
            - options = k == :sports ? { sp: item[:slug] } : { c: item[:slug] }
            %a{ href: mkp_brands_path(options) }= item[:name]

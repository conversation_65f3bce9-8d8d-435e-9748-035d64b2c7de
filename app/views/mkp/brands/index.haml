- title = "#{t('.title')} | #{t('mkp.title')}"
- desc = t('.description')
= content_for(:title, title)

= content_for :meta do
  %link{ rel: 'canonical', href: mkp_brands_path }
  %meta{ content: desc, name: 'description' }
  %meta{ content: title, name: 'title' }
  %meta{ property: 'og:title', content: title }
  %meta{ property: 'og:description', content: desc }
  %meta{ property: 'og:type', content: 'website' }
  %meta{ property: 'og:url', content: request.url }
  = render partial: 'partials/meta/og_logo'

.large-3.columns.hide-for-small.show-for-large
  .row
    .small-12.columns
      %h2= t('.filter_brands')
      %hr
  .row
    .small-12.columns
      = render partial: 'mkp/brands/filter'
.small-12.large-9.columns
  .row
    .small-12.columns
      - if params[:c]
        %h2= t('.categories_brands', category_name: @active_filter.name, total: @brands.length )
      - else
        %h2= t('.all_brands', total: @brands.length )
      %hr
.small-12.large-9.columns
  .row
    = render partial: 'mkp/brands/brand', collection: @brands
    = render partial: 'mkp/brands/dummy_brands'

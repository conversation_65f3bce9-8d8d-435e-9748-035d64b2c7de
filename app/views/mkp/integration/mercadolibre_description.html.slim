.description style=("width: 100%; text-align: center; background: url('https://s3.amazonaws.com/a01.gpstatic.com/argentina/ml/gpcom_publish/gpml_bg.gif') repeat-y center;")
  .logo width="width:760px;"
    img border="0px" src="https://s3.amazonaws.com/gpcdn-dev/avenida/mercadolibre/meli_header.jpg" width="760px"
  .content style=("text-align: center;")
    table cellpadding="0" cellspacing="0" style=("margin-left: auto;margin-right: auto;") width="760px;"
      tr
        td width="20px"  &nbsp;
        td style="width:620px;" width="620px;"
          h1 style=("font-family:helvetica;letter-spacing:-1px;font-size:2.5em;line-height: 1em;text-align: left;")
            = title
            br/
            span style="color:#666;font-size:0.8em"= extra_title
        / td style="width:100px;" width="100px"
        /   img  width="100px" src=manufacturer_logo_url alt=manufacturer_name
        td width="20px"  &nbsp;
  .content style=("text-align: center;")
    table cellpadding="0" cellspacing="0" style=("margin-left: auto;margin-right: auto;") width="760px;"
      tr
        td width="20px"  &nbsp;
        - if pictures_urls.length >= 2
          td style="width:350px;" width="350px;"
            -pic_url = pictures_urls[0]
            img src=pic_url width="350px"
          td width="20px"  &nbsp;
          td style="width:350px;" width="350px"
            -pic_url = pictures_urls[1]
            img src=pic_url width="350px"
        - else
          td style="width:720px;text-align:center;" width="710px;"
            -pic_url = pictures_urls[0]
            img src=pic_url width="350px"
        td width="20px"  &nbsp;
  .content style=("text-align: center;")
    table cellpadding="0" cellspacing="0" style=("margin-left: auto;margin-right: auto;") width="760px;"
      tr
        td width="20px"  &nbsp;
        td style=("width:720px; text-align: left;") width="720px;"
          h2 style=("font-family:helvetica;letter-spacing:-1px;font-size:2em;margin-top:1em; line-height: 1em;")
            | Descripción
          div style=("font-family:helvetica;font-size:1.3em;line-height:1.4em;margin-bottom:1em;")
            = description
        td width="20px"  &nbsp;
  .extras width="width:760px;"
    img border="0px" src="https://s3.amazonaws.com/a01.gpstatic.com/argentina/ml/gpcom_publish/content_bottom.jpg" width="760px"
    img border="0px" src="https://s3.amazonaws.com/a01.gpstatic.com/argentina/ml/gpcom_publish/mercado_pago.jpg" width="760px"

%h2=t('.hi',name:@customer.first_name)
%p= @shipment_title
%hr
%table.order-item{ :style => "width: 100%;" }
  %tbody
    %tr.border-bottom
      %td
        %p
          %strong= t('.orders_being_tracked')
          %span= @orders_being_tracked
        %p
          %strong= t('.courier')
          - courier = @shipment.label.try(:courier).eql?('other') ? 'Logística Privada' : @shipment.label.try(:courier)
          %strong{style:"text-decoration:none;color:#007bff"}=courier
        - unless courier.eql? 'Logística Privada'
          %p
            %strong= t('.tracking_number')
            %span=@shipment.label.try(:tracking_number)
          %br
          %p
            %a{href:"#{@current_store.hostname}/tracking/#{@shipment.label.try(:tracking_number)}" ,target:"_blank",style:"text-decoration:none;color:#353535"}
              %strong= t('.link')
        - if @shipment.order.suborders.size > 1
          .highlight
            .container-highlight
              %p= t('.legend')

%br
= render partial: 'v5/partials/mailer/contact_us'

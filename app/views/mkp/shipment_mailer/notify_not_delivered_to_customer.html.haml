- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;"}
  %div.body
    %h2 
      =t('.hi',name: @customer.first_name, last:@customer.last_name)
    %p
      - if @shipment.order.store.name == 'bancomacro'
        = t('.error_macro')
      - else
        = t('.error')
    %table.order-item{ :style => "margin: auto; width: 100%;" }
      %tbody
        - @shipment.items.each do |item|
          = render partial: 'mailer/partials/order_item', locals: {item: item, hide_purchase_date: true}

    %br
    - if @current_store.name == 'bancomacro'
      %p
        = t('.message_macro_1')
      %p
        %ul
          %li= t('.message_macro_2')
          %li= t('.message_macro_3')
          %li= t('.message_macro_4')
      %br
= render partial: 'v5/partials/mailer/contact_us'

- I18n.locale = @locale
- shipment = @shipment
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;"}
  %div.body
    %h2 
      =t('.hi',name: shipment.origin_address.full_name.try(:titleize))
    %p
      = t('.error')
    %table.order-item{ :style => "margin: auto; width: 100%;" }
      %tbody
        - shipment.items.each do |item|
          = render partial: 'mailer/partials/order_item', locals: {item: item, shipment: shipment}

    %br
    %hr
    - if shipment.present?
      %table
        %tbody
          %tr
            %td{ colspan: 2}
              - address = shipment.destination_address
              %p
                = t('.reciver')
                %strong #{address.full_name.try(:titleize)}
              %p
                = t('.address')
                - address_to_print = []
                - address_to_print << address.address
                - address_to_print << address.street_number if address.street_number.present?
                - address_to_print << address.address_2
                - address_to_print << address.city
                - address_to_print << address.state
                - address_to_print << address.zip.to_s
                - address_to_print << GeoConstants::Countries.name_for_code(address.country) if address.country.present?
                %strong 
                  = address_to_print.compact.reject(&:empty?).join(', ').titleize
  %br
  = render partial: 'v5/partials/mailer/contact_us'

- cache ['mobile_categories_menu', @network, @menu[:timestamp]] do
  .mobile-categories-menu
    - @menu[:items].each do |item|
      - has_childs = item[:childs].present?
      .category-item class=(has_childs ? 'with-submenu' : '')
        a.link.category-item-title href=item[:url] class=(has_childs ? 'with-flecha' : '')
          = item[:title].downcase.html_safe
          - if has_childs
            .i-flecha-down
            .submenu
              - item[:childs].each do |column|
                -if item[:title] == 'Marcas' || item[:title] == 'Brands'
                  - column[:url] = Mkp::Catalog::UrlHandler.path(b: column[:slug], network: @network.downcase)
                a.i-flecha-right.link.category-item-subitem href=column[:url]
                  = column[:name].downcase.html_safe
              a.i-flecha-right.link.category-item-subitem href=item[:url]
                = t('mkp.partials.see_all')

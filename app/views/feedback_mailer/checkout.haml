- I18n.locale = @locale
%p{:style => "margin-top:0;"}
  #{t('.issue', shops_titles: @shops_titles)}

%table{style: 'margin-top:13px;'}
  %tbody
    - unless @customer.is_a? Mkp::Guest
      %tr
        %th
          %p= t('.username')
        %td
          %p <a href="#profiles_url" target="_blank">@#{@customer.login}</a>
    %tr
      %th
        %p= t('.buyer_credentials')
      %td
        %p= @customer.full_name
    %tr
      %th
        %p= t('.email')
      %td
        %p= @customer.email


%br
%table.email_item{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :style => ";margin-top:3px;", :width => "530"}

  - @items.group_by{|item| item.shop}.each do |shop, items|
    %tbody
      %tr
        %td{ colspan: 2 }
          %h3{ style: 'margin-bottom: 0;'}= t('.order_products', shop: shop.title)
      %tr
        %td{ colspan: 2 }
          - items.each do |item|
            = render :partial => 'mailer/partials/order_item', :locals => {:item => item}

%br
%h3{ style: 'margin-bottom: 0;'}= t('.destination')
= render :partial => 'partials/mailer/address', :locals => {address: @destination}

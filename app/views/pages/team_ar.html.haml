= render_content do
  .row
    .small-12.medium-6.large-3.columns
      .pages-personita
        %a{ href: 'http://goodpeople.com/Orlipablo' }
          %img{ src: asset_path('pages/team/user1.png'), width: '126px', height: '149px' }
        %p
          %a{ href: 'http://goodpeople.com/Orlipablo' }
            %strong <PERSON>
        %p Co-Founder
        .personita-social
          %a.twitter{ href: 'https://twitter.com/orlandopablo', taget: '_blank', rel: 'nofollow'}
          %a.linkedin{ href: 'http://www.linkedin.com/in/orlandopablo', taget: '_blank', rel: 'nofollow'}
    .small-12.medium-6.large-3.columns
      .pages-personita
        %a{ href: 'http://goodpeople.com/Melbs' }
          %img{ src: asset_path('pages/team/user2.png'), width: '126px', height: '149px' }
        %p
          %a{ href: 'http://goodpeople.com/Melbs' }
            %strong <PERSON>
        %p Co-Founder & CEO
        .personita-social
          %a.twitter{ href: 'https://twitter.com/Melbs22', taget: '_blank', rel: 'nofollow'}
          %a.linkedin{ href: 'http://www.linkedin.com/in/mcelberts', taget: '_blank', rel: 'nofollow'}
    .small-12.medium-6.large-3.columns
      .pages-personita
        %a{ href: 'http://goodpeople.com/Danojej' }
          %img{ src: asset_path('pages/team/user3.png'), width: '126px', height: '149px' }
        %p
          %a{ href: 'http://goodpeople.com/Danojej' }
            %strong Daniel Jejcic
        %p Co-Founder & CMO
        .personita-social
          %a.twitter{ href: 'https://twitter.com/DanoJejcic', taget: '_blank', rel: 'nofollow'}
          %a.linkedin{ href: 'http://ar.linkedin.com/in/danieljejcic', taget: '_blank', rel: 'nofollow'}
    .small-12.medium-6.large-3.columns
      .pages-personita
        %a{ href: 'http://goodpeople.com/cavi21' }
          %img{ src: asset_path('pages/team/user4.png'), width: '126px', height: '149px' }
        %p
          %a{ href: 'http://goodpeople.com/cavi21' }
            %strong Agustin Cavilliotti
        %p Co-Founder & CTO
        .personita-social
          %a.twitter{ href: 'https://twitter.com/cavi21', taget: '_blank', rel: 'nofollow'}
          %a.github{ href: 'https://github.com/cavi21', taget: '_blank', rel: 'nofollow'}
          %a.linkedin{ href: 'http://ar.linkedin.com/pub/agustin-cavilliotti/11/b01/787', taget: '_blank', rel: 'nofollow'}

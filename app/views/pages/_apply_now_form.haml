#apply-now-form-modal.apply-now-form.mfp-hide
  .loader
  .apply-now-form-title
    .apply-now-form-title-icon
    %h2= t('pages.brands.apply_now_form.title')
    %p= t('pages.brands.apply_now_form.subtitle')
  .apply-now-form-content
    %form{ action: brand_application_url, method: 'POST' }
      %input{ type: 'text', name: 'brand_application[im_a_bot]', placeholder: "Complete this only if you're a bot.", style: 'display:none' }
      %input{ type: 'text', name: 'brand_application[company_name]', placeholder: t('pages.brands.apply_now_form.company_name'), autofocus: true, required: true }
      %input{ type: 'email', name: 'brand_application[email]', placeholder: t('pages.brands.apply_now_form.email'), required: true }
      %input{ type: 'text', name: 'brand_application[website]', placeholder: t('pages.brands.apply_now_form.website') }
      %p= t('pages.brands.apply_now_form.do_good_desc')
      %textarea{ name: 'brand_application[how_good]', placeholder: t('pages.brands.apply_now_form.how_good') }
      %textarea{ name: 'brand_application[what_unique]', placeholder: t('pages.brands.apply_now_form.what_unique') }

      %button{ type: 'submit' }= t('pages.brands.apply_now_form.apply_now')
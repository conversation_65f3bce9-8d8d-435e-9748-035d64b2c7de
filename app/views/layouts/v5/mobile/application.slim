doctype html
html.mobile.no-js lang=I18n.locale.to_s
  head
    = render partial: 'partials/base_layout_head', locals: { load_fonts: false }

    title
      - if content_for?(:title)
        = yield(:title)
      - else
        = "Avenida.com | #{t('.head_title')}"

    - if content_for?(:meta)
      = yield :meta
    - else
      meta content=t('.head_desc') name='description'

    = stylesheet_link_tag 'v5/mobile/application'
    = yield :css
    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }
  body id=body_id class=body_class
    = render partial: 'partials/v5/mobile/header'
    = yield
    = render partial: 'partials/v5/mobile/footer'

    = yield :before_js
    = javascript_include_tag 'v5/mobile/application'
    = yield :js
    = render partial: 'partials/v5/fonts'

    = yield :bottom

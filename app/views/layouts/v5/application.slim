doctype html
html.desktop.no-js lang=I18n.locale.to_s
  head
    = render partial: 'partials/base_layout_head', locals: { load_fonts: false }

    title
      - if content_for?(:title)
        = yield(:title)
      - else
        = "Avenida.com | #{t('.head_title')}"

    - if content_for?(:meta)
      = yield :meta
    - else
      meta content=t('.head_desc') name='description'

    - if content_for?(:og_meta)
      = yield :og_meta
    - else
      = render partial: 'partials/v5/meta/open_graph'

    = stylesheet_link_tag 'v5/application'
    = yield :css
    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }

  body id=body_id class=body_class
    = yield :google_tag_manager

    = render partial: 'partials/v5/browse_happy'

    = render partial: 'partials/v5/header'
    = yield
    = render partial: 'partials/v5/footer'

    = yield :before_js
    = javascript_include_tag 'v5/application'
    = yield :js
    = render partial: 'partials/javascripts/google_adwords'
    = render partial: 'partials/v5/fonts'

    / = render partial: 'partials/v5/olark_chat'
    = yield :bottom

doctype html
html.desktop.no-js lang=I18n.locale.to_s
  head
    = render partial: 'partials/base_layout_head'

    = yield :meta

    title
      - if content_for?(:title)
        = yield(:title)
      - else
        = "Avenida.com | #{t('layouts.v5.mkp.checkout.head_title')}"

    meta content=t('mkp.description') name='description'

    - unless Rails.env.test?
      = stylesheet_link_tag '//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&subset=latin,latin-ext'
    = stylesheet_link_tag 'v5/mkp/checkout'
    = yield :css

    = render partial: 'partials/v5/javascripts/services'

  body id=body_id class=body_class
    = yield :google_tag_manager

    = render partial: 'partials/v5/browse_happy'

    = render partial: 'mkp/checkout/v5/partials/header'
    = yield
    = render partial: 'mkp/checkout/v5/partials/footer'

    = yield :before_js
    = javascript_include_tag 'v5/mkp/checkout'
    = yield :js
    = render partial: 'partials/v5/fonts'
    /= render partial: 'partials/v5/olark_chat'
    = yield :bottom

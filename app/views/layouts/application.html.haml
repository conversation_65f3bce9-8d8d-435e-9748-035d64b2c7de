%html.no-js{ lang: I18n.locale.to_s, class: browser.mobile? && :mobile  }
  %head
    = render partial: 'partials/base_layout_head'

    %title<
      = content_for?(:title) ? yield(:title) : 'Avenida.com | '+t('.head_title')

    - if content_for?(:meta)
      = yield :meta
    - else
      %meta{ content: t('social.description'), name: 'description' }

    = stylesheet_link_tag 'social/application'
    - if browser.mobile?
      = stylesheet_link_tag 'v5/mobile/application'
    - else
      = stylesheet_link_tag 'v5/application'

    = yield :css

    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }

  %body{ id: body_id, class: content_for?(:body_class) && yield(:body_class) }
    = yield :google_tag_manager

    = render partial: 'partials/v5/browse_happy'
    = render partial: 'partials/header'

    .row
      = yield

    = render partial: 'partials/footer'

    = yield :before_js

    = javascript_include_tag 'social/application'

    = yield :js

    = render partial: 'partials/v5/fonts'

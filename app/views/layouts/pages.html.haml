%html.no-js{ lang: I18n.locale.to_s, class: browser.mobile? && :mobile }
  %head
    = render partial: 'partials/base_layout_head'

    %title<
      = "#{t("pages.#{@page_name}.name")} | Avenida.com"

    - if content_for?(:meta)
      = yield :meta
    %meta{ content: t('social.description'), name: 'description' }

    - if content_for?(:og_meta)
      = yield :og_meta
    - else
      = render partial: 'partials/v5/meta/open_graph'

    = stylesheet_link_tag 'social/application'
    - if browser.mobile?
      = stylesheet_link_tag 'v5/mobile/application'
    - else
      = stylesheet_link_tag 'v5/application'

    = yield :css

    = render partial: 'pages/internationalization'
    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }

  %body.pages-action{ id: body_id, class: content_for?(:body_class) && yield(:body_class) }
    - if browser.mobile?
      = render partial: 'pages/mobile_menu'
    = render partial: 'partials/v5/browse_happy'
    = render partial: 'partials/header'

    .pages-title-wrapper{ class: I18n.locale }
      .row.pages-title-row
        .pages-title.small-12.medium-9.after-wide-10.medium-offset-3.after-wide-offset-2.columns
          %h2= t("pages.#{@page_name}.title")
    - if browser.mobile?
      = render partial: 'pages/mobile_menu_header'
    .pages-content-wrapper
      = yield

    = render partial: 'partials/footer'

    = stylesheet_link_tag 'https://s3.amazonaws.com/gpcdn-static/fonts/ostrich-sans/ostrich-sans.css'

    = yield :before_js

    = javascript_include_tag 'social/application'
    = javascript_include_tag '//cdn.jsdelivr.net/fittext/1.1/jquery.fittext.js'

    = yield :js

    = render partial: 'partials/v5/fonts'

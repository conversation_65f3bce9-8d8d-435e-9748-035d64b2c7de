!!!
%html{ lang: I18n.locale.to_s }
  %head
    = render partial: 'partials/base_layout_head'

    %title< #{t('distribution.title')} | Avenida.com
    %meta{ content: t('distribution.description'), name: 'description' }

    = yield :css

    = render partial: 'partials/v5/javascripts/services'

  %body{ id: body_id, class: content_for?(:body_class) && yield(:body_class) }
    = render partial: 'distribution/header'
    .row
      .small-12.columns
        = yield
    = render partial: 'distribution/footer'

    = stylesheet_link_tag '//cdnjs.cloudflare.com/ajax/libs/foundation-essential/5.2.2/css/foundation.min.css'
    = stylesheet_link_tag 'distribution/distribution'
    = javascript_include_tag 'distribution/distribution'
    = yield :js
    - if flash[:success].present? || flash[:error].present?
      :javascript
        window.gp && gp.Helpers.alert("#{flash[:success] || flash[:error]}")

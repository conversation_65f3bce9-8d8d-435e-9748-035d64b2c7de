- network = @network || 'AR'
- language = @language || 'es'

!!!
%html
  %head
  %body
    %table#background-table{border: "0", cellpadding: "0", cellspacing: "0", width: "100%", bgcolor: "#E8E8E8"}
      %tbody
        %tr
          %td{align: 'center'}
            %table.w600
              %tbody
                %tr
                  %td.w600{height: "20"}
                /= render partial: 'partials/mailer/header'
                /- if content_for?(:subheader)
                  = yield :subheader
                /- else
                  = render partial: 'partials/mailer/mkp_subheader', locals: {network: network}
                %tr
                  %td.w600
                    %table#body.w600#content
                      %tbody
                        %tr
                          %td.w15
                          %td.w570
                            = yield
                          %td.w15 
              = render partial: 'partials/mailer/footer'

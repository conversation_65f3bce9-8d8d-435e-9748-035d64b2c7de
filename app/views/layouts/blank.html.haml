!!!
%html{ lang: I18n.locale.to_s }
  %head
    = render partial: 'partials/base_layout_head'

    %title<
      = content_for?(:title) ? yield(:title) : 'Avenida.com | '+t('mkp.title')

    - if content_for?(:meta)
      = yield :meta
    - else
      %meta{ content: t('mkp.description'), name: 'description' }

    = yield :css

    = render partial: 'partials/v5/javascripts/services'

  %body{ id: body_id, class: content_for?(:body_class) && yield(:body_class) }
    = yield

%html.no-js{ lang: I18n.locale.to_s, class: browser.mobile? && :mobile }
  %head
    = render partial: 'partials/base_layout_head'

    %title<
      = content_for?(:title) ? yield(:title) : 'Avenida.com | ' + t('mkp.title')

    - if content_for?(:meta)
      = yield :meta
    - else
      %meta{ content: t('mkp.description'), name: 'description' }

    - if content_for?(:og_meta)
      = yield :og_meta
    - else
      = render partial: 'partials/v5/meta/open_graph'

    = stylesheet_link_tag 'mkp/application'
    - if browser.mobile?
      = stylesheet_link_tag 'v5/mobile/application'
    - else
      = stylesheet_link_tag 'v5/application'

    = yield :css

    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }

  %body{ id: body_id, class: content_for?(:body_class) && yield(:body_class) }
    = yield :google_tag_manager

    = render partial: 'partials/v5/browse_happy'
    = render partial: 'partials/header'

    = yield(:hero) if content_for?(:hero)

    %section.row
      = yield

    = render partial: 'partials/footer'

    = yield :before_js

    = javascript_include_tag 'mkp/application'

    = render partial: 'partials/javascripts/google_adwords'

    = yield :js

    = render partial: 'partials/v5/fonts'

    /= render partial: 'partials/v5/olark_chat'

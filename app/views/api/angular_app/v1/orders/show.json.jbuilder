json.extract! @order, :id, :title, :coupon_discount, :total
json.extract! @order.payment, :status, :gateway_object_id, :gateway_data
json.suborders @order.suborders do |suborder|
  json.status t('api.mkp.tracking.show.status.' + suborder.shipment.status)
  json.tracking_number suborder.shipment.label.try(:tracking_number)
  json.courier suborder.shipment.label.try(:courier)
  json.items suborder.items do |item|
    json.partial! 'api/angular_app/v1/products/product', product: item.product
  end
end

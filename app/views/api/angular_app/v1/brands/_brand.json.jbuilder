json.extract! brand, :id, :login, :full_name
json.profile do
  json.cover brand.profile.cover.url(:m_cover) == "/assets/mkp/header-avenida.jpg" ? (root_url + "assets/mkp/header-avenida.jpg") : brand.profile.cover.url(:m_cover)
  json.bio brand.profile.bio

  avatar = brand.respond_to?(:avatar) && \
           brand.avatar.persisted? && \
           brand.avatar.url(:t) || \
           "//s3.amazonaws.com/non-existent-pic.jpg"

  json.avatar avatar
  json.text "Social::Attachment::AvatarPicture.fallback_text_for(brand)"
  json.color Social::Attachment::AvatarPicture.fallback_color_for(brand)
end

json.shop_id brand.shop.try(:id)

json.variants brand.shop.variants.limit(12) do |variant|
  json.partial! 'api/angular_app/v1/variants/variant', variant: variant
end

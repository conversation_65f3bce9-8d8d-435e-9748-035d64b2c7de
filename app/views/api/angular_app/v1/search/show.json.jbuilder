json.category do
  json.partial! 'api/angular_app/v1/categories/category', category: @category
end unless @category.nil?

json.manufacturer do
 json.partial! 'api/angular_app/v1/manufacturers/manufacturer', manufacturer: @manufacturer
end unless @manufacturer.nil?

json.variants @variants do |variant|
  json.partial! 'api/angular_app/v1/variants/variant', variant: variant
end

json.filters @filters
json.breadcrumb @breadcrumb

json.title catalog_title(false)
json.catalog_name header_object.try(:name)

json.pagination do
  json.current_page @variants.current_page
  json.total_pages @variants.total_pages
end

json.not_found @not_found

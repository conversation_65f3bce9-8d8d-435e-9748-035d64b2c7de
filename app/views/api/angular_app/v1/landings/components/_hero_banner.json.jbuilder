json.slides component.slides do |slide|
  json.open_in (/avenida\.com\.ar/ =~ slide.link).nil? ? '_blank' : '_self'
  json.image ::Pages::Picture.find_by_id(slide.desktop_picture_id).try(:image).try(:url, :desktop)
  json.title slide.title
  json.description slide.description
  json.link slide.link
end

json.banners component.banners do |banner|
  json.image ::Pages::Picture.find_by_id(banner.desktop_picture_id).try(:image).try(:url, :mobile)
  json.title banner.title
  json.link banner.link
end
json.extract! variant, :id, :quantity, :properties, :sku, :gp_sku, :ean_code, :points_price, :discount_top
json.picture_id variant.picture_id.present? ? variant.picture_id : nil
json.picture_thumb variant.get_thumb(:t)
json.picture_medium variant.get_thumb(:ml)
json.picture_medium_tm variant.get_thumb(:tm)
json.picture_medium_l variant.get_thumb(:l)
json.url variant.get_url

if variant.properties.key?(:color)
  variant.properties[:color].merge!(slug_name: variant.color_slug_name) if variant.properties[:color].is_a?(Hash)
end

json.properties variant.properties

max_installments = variant.product.category.max_installments(@current_store, variant.product)
json.promotions Promotions::Serializer.new(variant.product, variant.product.price, max_installments, @current_store).build do |promotion|
  json.partial! 'api/angular_app/v1/promotions/promotion', promotion: promotion
end

json.product do
  json.extract! variant.product, :id, :shop_id, :title, :available_properties, :available_properties_names,
                                      :currency_symbol, :currency_code, :slug, :manufacturer, :regular_price, :regular_price_without_taxes,  :transaction_type
  sale_calculator = Mkp::ProductSaleDetector.new(variant.product)
  json.max_installments variant.product.category.max_installments(@current_store, variant.product)
  json.max_installments_info variant.product.max_installments_info(@current_store, variant.product)
  json.is_on_sale sale_calculator.is_on_sale?
  json.points variant.product.get_points_price(@current_store)
  if sale_calculator.is_on_sale?
    json.sale_price sale_calculator.calculate
    json.percent_off sale_calculator.percent_off
    json.sale_price_without_taxes sale_calculator.calculate_without_taxes
  end
  json.min_points_price_for_variants variant.product.min_points_price_for_variants
  json.shop_title variant.product.shop.title
end

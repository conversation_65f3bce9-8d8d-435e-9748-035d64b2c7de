json.extract! variant, :id, :quantity, :properties, :sku, :gp_sku, :ean_code
json.picture_id variant.picture_id.present? ? variant.picture_id : nil
json.picture_thumb variant.get_thumb(:t)
json.picture_medium variant.get_thumb(:ml)
json.url variant.get_url

if variant.properties.key?(:color)
  variant.properties[:color].merge!(slug_name: variant.color_slug_name) if variant.properties[:color].is_a?(Hash)
end

json.properties variant.properties


json.extract! product, :id, :title, :available_properties, :available_properties_names,
                                    :currency_symbol, :currency_code, :slug, :manufacturer, :regular_price
sale_calculator = Mkp::ProductSaleDetector.new(product)
json.is_on_sale sale_calculator.is_on_sale?
if sale_calculator.is_on_sale?
  json.sale_price sale_calculator.calculate
  json.percent_off sale_calculator.percent_off
end
json.total_stock product.total_stock_cached
json.unavailable product.unavailable?

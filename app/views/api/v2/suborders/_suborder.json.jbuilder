  json.status t('api.mkp.tracking.show.status.' + suborder.shipment.try(:status))
  json.tracking_number suborder.shipment.label.try(:tracking_number)
  json.courier suborder.shipment.label.try(:courier)
  json.address  suborder.shipment.address.to_h.slice( :telephone, :address, :address_2, :city, :state, :country, :zip, :doc_type, :doc_number, :street_number, :floor, :dpto, :tower, :body, :lateral_street_1, :lateral_street_2, :county, :country_club, :full_name )

  json.items suborder.items do |item|
    json.partial! 'api/v2/products/product', product: item.product
    json.quantity item.quantity
  end

html
  head
    title Documentacion
    sass:
      .docBlock
        color: red
        border: 1px red solid
        padding: 0 10px
        max-width: 50%
        margin: 20px 0
        display: block

.row
  .col-md-12
    h2 = "API Documentacion"
  .row
    .col-md-6.docBlock
      h4 = "Index Orders"
      ul#selector
        li = "Metodo: GET"
        li = "URL: https://api.avenida.com.ar/api/v2/orders"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
  .row
    .col-md-6.docBlock
      h4 = "Cancel Order"
      ul#selector
        li = "Metodo: POST"
        li = "URL: https://api.avenida.com.ar/api/v2/orders/{id}/cancel"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
  .row
    .col-md-6.docBlock
      h4 = "Create Orders"
      ul#selector
        li = "Metodo: POST"
        li = "URL: https://api.avenida.com.ar/api/v2/orders"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
        li = "Parameters:"
        ul#selector
          li = "id"
          li = "customer_id"
          li = "customer_type"
          li = "ip"
          li = "network"
          li = "created_at"
          li = "updated_at"
          li = "purchase_id"
          li = "title"
          li = "coupon_id"
          li = "coupon_discount"
          li = "data"
          li = "auto_invoiced"
          li = "store_id"
          li = "bonified_amount"
          li = "gross_total"
          li = "jubilo_liquided"
  .row
    .col-md-6.docBlock
      h4 = "Update Order"
      ul#selector
        li = "Metodo: PUT"
        li = "URL: https://api.avenida.com.ar/api/v2/orders/{id}/update_liquided"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
        li = "Parameters:"
        ul#selector
          li = "id"
          li = "customer_id"
          li = "customer_type"
          li = "ip"
          li = "network"
          li = "created_at"
          li = "updated_at"
          li = "purchase_id"
          li = "title"
          li = "coupon_id"
          li = "coupon_discount"
          li = "data"
          li = "auto_invoiced"
          li = "store_id"
          li = "bonified_amount"
          li = "gross_total"
          li = "jubilo_liquided"
  .row
    .col-md-6.docBlock
      h4 = "Show Order"
      ul#selector
        li = "Metodo: GET"
        li = "URL: https://api.avenida.com.ar/api/v2/orders/{id}"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
  .row
    .col-md-6.docBlock
      h4 = "Update Order"
      ul#selector
        li = "Metodo: PATCH"
        li = "URL: https://api.avenida.com.ar/api/v2/orders/{id}"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
        ul#selector
          li = "id"
          li = "title"
          li = "coupon_discount"
          li = "total"
          li = "status"
          li = "coupon_discount"
          li = "total"
          li = "status"
          li = "gateway_object_id"
          li = "gateway_data"
          li = "created_at"
          li = "shipments_cost"
          li = "suborders"
  .row
    .col-md-6.docBlock
      h4 = "Update Order"
      ul#selector
        li = "Metodo: PUT"
        li = "URL: https://api.avenida.com.ar/api/v2/orders/{id}"
        li = "Headers:"
        ul#selector
          li = "Api-Key"
          li = "auth-token"
        ul#selector
          li = "id"
          li = "title"
          li = "coupon_discount"
          li = "total"
          li = "status"
          li = "coupon_discount"
          li = "total"
          li = "status"
          li = "gateway_object_id"
          li = "gateway_data"
          li = "created_at"
          li = "shipments_cost"
          li = "suborders"

def payment_present_attibutes(payment)
  @payment_attributes.select{|attr|  payment.send(attr).present?}
end

json.extract! order, :id, :title, :coupon_discount, :total, :created_at
if (payment = order.payment).present?
  json.extract! order.payment, *payment_present_attibutes(payment)
end

json.suborders order.suborders do |suborder|
  json.partial! 'api/v2/suborders/suborder', suborder: suborder
end

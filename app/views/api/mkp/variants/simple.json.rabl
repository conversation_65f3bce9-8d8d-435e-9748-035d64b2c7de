object @variant

attributes :id, :quantity, :properties, :sku, :gp_sku, :name, :ean_code, :points_price, :discount_top

node :picture_id do |v|
  v.picture_id.present? ? v.picture_id : nil
end

node :picture_thumb do |v|
  v.get_thumb(:t)
end

node :url do |v|
  v.get_url
end

node :properties do |v|
  if v.properties.key?(:color)
    v.properties[:color].merge!(slug_name: v.color_slug_name) if v.properties[:color].is_a?(Hash)
  end

  v.properties
end

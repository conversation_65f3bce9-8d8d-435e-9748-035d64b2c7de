object @product

attributes :id, :title, :available_properties, :available_properties_names, :currency_symbol, :currency_code, :transaction_type

attribute total_stock_cached: :total_stock
attribute unavailable?: :unavailable
attribute pickeable?: :pickeable
attribute reservable?: :reservable

node :regular_price do |p|
  "%.2f" % p.regular_price
end

node :sale_price, :if => lambda { |p| Mkp::ProductSaleDetector.is_on_sale?(p) } do |p|
  "%.2f" % Mkp::ProductSaleDetector.calculate(p)
end

node :color_slugs do |product|
  product.available_values_for(:color).each_with_object({}) do |color, hash|
    hash[color[:slug_name]] = color[:hex] if color.is_a?(Hash)
  end
end

node :pictures do |product|
  product.pictures.each_with_object([]) do |picture, array|
    hash = {
      id: picture.id,
      thumb: picture.url(:t)
    }.tap do |h|
      variant = product.variants.find { |v| v.picture_id == picture.id }

      h[:color] = (variant.present? && variant.has_property?(:color)) ? variant.color_slug_name : nil
    end

    array << hash
  end
end

child :category => :category do
  extends 'mkp/categories/simple'
end

child :manufacturer => :manufacturer do
  extends 'mkp/manufacturers/simple'
end

child :shop => :shop do
  extends 'mkp/shops/simple'
end

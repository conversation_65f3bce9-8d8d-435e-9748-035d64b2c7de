object @suborder
shipment = @suborder.shipment
payment = @suborder.payment

attributes :id, :public_id, :created_at, :updated_at

node(:shop) do |_|
  hash = {
    id: @shop.id,
    title: @shop.title
  }
end

node(:payment_status) do |suborder|
  if payment.present?
    payment.status
  elsif suborder.is_paid?
    'collected'
  else
    'error'
  end
end

node(:payment_collected_at) do |suborder|
  if payment.present?
    payment.collected_at
  elsif suborder.is_paid?
    suborder.created_at
  else
    '-'
  end
end

node(:shipment) do |_|
  destination_address = shipment.destination_address
  hash = {
    charged_amount: shipment.charged_amount.to_f,
    bonified_amount:  shipment.bonified_amount.to_f,
    destination_address: {
      full_name: destination_address.full_name,
      address: destination_address.address,
      address_2: destination_address.address_2,
      zip: destination_address.zip,
      city: destination_address.city,
      state: destination_address.state,
      country: destination_address.country,
      telephone: destination_address.telephone
    }
  }
end

node :items do |_|
  @suborder.items.each_with_object([]) do |item, array|
    variant = item.variant
    product = item.product
    hash = {
      product_id: product.id,
      product_name: product.title,
      variant_id: variant.id,
      variant_name: variant.name,
      variant_sku: variant.sku,
      variant_gp_sku: variant.gp_sku,
      variant_properties: variant.properties.to_hash,
      quantity: item.quantity,
      price: product.price.to_f,
      sale_price: product.sale_price.to_f
    }
    array << hash
  end
end
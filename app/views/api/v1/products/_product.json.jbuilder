json.extract! product, :id, :title, :description, :category_id, :manufacturer_id, :regular_price, :regular_price_without_taxes, :iva,
              :available_on, :transaction_type, :shop_id, :sale_on, :sale_until, :sale_price, :sale_price_without_taxes

json.variants product.variants do |variant|
  json.extract! variant, :sku, :quantity, :ean_code, :properties, :points_price, :discount_top
end

json.packages product.packages do |package|
  json.extract! package,  :width, :height, :length, :weight, :length_unit, :mass_unit
end

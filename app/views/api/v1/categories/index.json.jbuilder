json.categories @entities do |category|
  json.extract! category, :id, :name, :slug, :description, :external_id, :ancestry, :ancestry_depth, :width, :height, :length, :weight, :mass_unit, :length_unit, :network, :display_order, :active, :created_at, :updated_at, :pickeable_status, :google_category_id, :full_path_name, :visa_puntos_equivalence, :children
end

json.meta do
  json.pagination do
    json.page @entities.current_page
    json.per_page @entities.per_page
    json.total_pages @entities.total_pages
  end
end
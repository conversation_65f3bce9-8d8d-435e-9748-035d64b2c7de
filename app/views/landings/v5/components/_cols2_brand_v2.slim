- cache_key = ['component', 'brands_c2v22', component.id, component.updated_at]
- setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
- setup = setup.first
- brand = component.brand
= cache cache_key, expires_in: 20.minutes do
  .featured-brands-cmpnt.margin-landing
    - if component.title.present?
      h2.component-title = component.title
    - elsif setup.title.present?
      h2.component-title = setup.title

    .cols2-generic-v1-vnrs
      article.featured-brand-new.cols-4
        a(class="brand-coverando" href=profiles_path(brand.login) style="background-position: center center; background-size: cover; width: 100%; background-image: url(#{brand.profile.cover.url(:m_cover)}); height: 160px; display: block")
          .avatar-wrapper
            = render partial: 'partials/v5/avatar', locals: {user: brand}
        div.info
          div.links
            a href=profiles_path(brand.login) = brand.name
      - component.variants.each do |v|
        - p = v.product
        - on_sale = Mkp::ProductSaleDetector.is_on_sale?(p)

        article.cols-4
          a(href=v.get_url style="background-color: white; background-repeat: no-repeat; background-position: center center; background-size: contain; width: 100%; background-image: url(#{v.get_thumb}); height: 160px; display: block")

          div.info
            div.links
              a href=v.get_url data-label='Product Title' = p.title
            span.links.price data-label='Price' #{current_currency_format}#{price_format('%.2f' % p.regular_price)}
            - if on_sale
              span.sale-price data-label='Sale Price' #{current_currency_format}#{price_format('%.2f' % Mkp::ProductSaleDetector.calculate(p))}

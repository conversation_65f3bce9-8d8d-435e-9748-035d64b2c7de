- cache_key = ['component', 'generic_c5v1', component.id, component.updated_at]
= cache cache_key do
  - setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
  .cols5-generic-v1-vnrs
    - if component.title.present?
      h2.component-title = component.title
    .scroll-wrapper
      .articles-list
        - setup.each do |sport|
          - img = ::Pages::Picture.find_by_id(sport.picture_id).try(:image, :desktop)
          article
            img src='#{img}' alt='#{sport.title}'
            - if sport.title.present?
              div
                h2 = sport.title
            a.link href='#{sport.link}'

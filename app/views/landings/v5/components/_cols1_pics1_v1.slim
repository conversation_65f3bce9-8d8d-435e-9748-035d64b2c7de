- cache_key = ['component', 'brands_c1v22', component.id, component.updated_at]
- setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
- setup = setup.first
= cache cache_key, expires_in: 20.minutes do
  .featured-brandy
    - if component.title.present?
      h2.component-title = component.title
    - elsif setup.title.present?
      h2.component-title = setup.title

    - component.setup[:items].each.with_index do |item, index|
      - pic = ::Pages::Picture.find_by_id(item[:picture_id])
      div.full-width
        a(href=item[:link])
          img src=pic.image.url(:banner_large) style="width: 1260px; max-width: 100%;"
- cache_key = ['component', 'posts_c4v1', component.id, component.updated_at]
= cache cache_key, expires_in: 20.minutes do
  - load_more_locals_extras = { attrs: { href: social_community_path, rel: 'prefetch' }, style: 'white-blue' }
  - locals = { posts: component.featured_posts,
               total_pages: 200,
               posts_path: social_feeds_all_path,
               load_more_locals_extras: load_more_locals_extras }
  .featured-posts-cmpnt
    - if component.title.present?
      h2.component-title = component.title
    = render partial: 'partials/v5/posts/list', locals: locals

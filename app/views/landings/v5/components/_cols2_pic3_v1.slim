- cache_key = ['component', '3pic_c2v1', component.id, component.updated_at]
= cache cache_key do
  - setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
  .cols2-pic3-v1-vnrs
    - if component.title.present?
      h2.component-title = component.title

    section
      - img = ::Pages::Picture.find_by_id(setup[0].picture_id).try(:image, :banner)
      article style='background-image:url(#{img});'
        .image
          .bg
          img src='#{img}' alt='#{setup[0].title}'
          h2
            a.content-table href='#{setup[0].link}'
              - if setup[0].title.present?
                .content-table-cell
                  .text = setup[0].title
    aside
      - img = ::Pages::Picture.find_by_id(setup[1].picture_id).try(:image, :desktop)
      article style='background-image:url(#{img});'
        .image
          .bg
          img src='#{img}' alt='#{setup[1].title}'
          h2
            a.content-table href='#{setup[1].link}'
              - if setup[1].title.present?
                .content-table-cell
                  .text = setup[1].title

      - img = ::Pages::Picture.find_by_id(setup[2].picture_id).try(:image, :desktop)
      article  style='background-image:url(#{img});'
        .image
          .bg
          img src='#{img}' alt='#{setup[2].title}'
          h2
            a.content-table href='#{setup[2].link}'
              - if setup[2].title.present?
                .content-table-cell
                  .text = setup[2].title

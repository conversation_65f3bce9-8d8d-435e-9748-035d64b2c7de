- cache_key = ['component', 'brands_c4v1', component.id, component.featured_brands, component.updated_at]
= cache cache_key, expires_in: 20.minutes do
  .featured-brands-cmpnt
    - if component.title.present?
      h2.component-title = component.title
    = render partial: 'partials/v5/users_with_cover',
             locals: { users: component.featured_brands.shuffle }
    - component.setup[:view_more_url] ||= nil
    - url = component.setup[:view_more_url]
    -if url.present?
      = render partial: 'partials/v5/load_more_wide',
               locals: { style: 'white-blue', attrs: { title: t('.see_more'), href: url.present? ? url : mkp_brands_path } }

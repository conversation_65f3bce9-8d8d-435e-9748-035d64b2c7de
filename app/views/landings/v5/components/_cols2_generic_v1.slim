- cache_key = ['component', 'generic_c2v1', component.id, component.updated_at]
= cache cache_key do
  - setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
  .cols2-generic-v1-vnrs
    - if component.title.present?
      h2.component-title = component.title
    - setup.each do |category|
      - img = ::Pages::Picture.find_by_id(category.picture_id).try(:image, :desktop)
      article style='background-image:url(#{img});'
        .bg
        img src='#{img}' alt='#{category.title}'
        h2
          a.content-table href='#{category.link}'
            - if category.title.present?
              .content-table-cell
                .text = category.title

- cache_key = ['component', 'hero', component.id, component.updated_at]
= cache cache_key do
  .hero-component
    .divider
      .main class="with-#{component.slides.size}"
        .wrapper
          .sizer: span
          - component.slides.each_with_index do |slide, index|
            - open_in = (/avenida\.com\.ar/ =~ slide.link).nil? ? '_blank' : '_self'
            - img = ::Pages::Picture.find_by_id(slide.desktop_picture_id).try(:image).try(:url, :desktop)
            article style='background-image:url(#{img});' class=(index == 0 ? 'active' : '')
              h2 = slide.title
              p = slide.description
              a href="#{slide.link}" target="#{open_in}"
        - if component.slides.size > 1
          span.prev
          span.next
          .points
            - component.slides.size.times do |index|
              strong class=(index == 0 ? 'active' : '')
                span
      .banner
        - component.banners.each do |banner|
          - img = ::Pages::Picture.find_by_id(banner.desktop_picture_id).try(:image).try(:url, :mobile)
          a href="#{banner.link}" title="#{banner.title}"
            img src='#{img}'

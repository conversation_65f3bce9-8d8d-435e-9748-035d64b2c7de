- cache_key = ['component', 'hero', component.id, component.updated_at]
= cache cache_key do
  - setup = component.setup[:items].map { |hash| OpenStruct.new(hash) }
  .hero-main-vnrs class="with-#{setup.size}"
    .wrapper
      .sizer: span
      - setup.each_with_index do |banner, index|
        /- cache_key_banner = ['homepage', @network, 'banners', i,banner.desktop_pic_id, banner.title.try(:sum), banner.description.try(:sum), banner.link.try(:sum)]
        /= cache cache_key_banner do
        - if true
          - open_in = (/avenida\.com\.ar/ =~ banner.link).nil? ? '_blank' : '_self'
          - img = ::Pages::Picture.find_by_id(banner.desktop_picture_id).try(:image).try(:url, :desktop)
          article style='background-image:url(#{img});' class=(index == 0 ? 'active' : '')
            h2 = banner.title
            p = banner.description
            a href="#{banner.link}" target="#{open_in}"
    - if setup.size > 1
      span.prev
      span.next
      .points
        - setup.size.times do |index|
          strong class=(index == 0 ? 'active' : '')
            span

= content_for :title do
  = @landing.meta_title || t('.title')

= content_for :meta do
  meta content=(@landing.meta_description || t('.description')) name='description'
  link href=root_url

  - Network.all_visible.each do |n|
    - unless Network[n].default?
      link rel='alternate' hreflang='#{Network[n].locale}-#{n}' href=network_root_url(network: n.downcase)

- content_for :before_js do
  javascript:
    gp = window.gp || {}
    gp.google_tag_params = {
      ecomm_pagetype: "#{@landing.blank? || @landing.is_home? ? 'homepage' : @landing.slug}",
      ecomm_network: '#{@network}'
    }

main
  div.gray-color.override-with-white-color
    .page-title = @landing.meta_title || t('.title')
    h2.page-subtitle = @landing.meta_description || t('.description')
    - if browser.mobile?
      = render partial: 'partials/v5/header/slim_promo'

    = render_components(@landing.components.order(:position)) if @landing.present?


.error-box
  p.background-error = @code
  h1 = t("error_pages.error_#{@code}.title")
  p.description = t("error_pages.error_#{@code}.description")
  .featured-variants-cmpnt
    .scroll-wrapper
      = render partial: 'partials/v5/variants/list',
        locals: {variants: @recommended_variants, list_name: 'landing-featured-products'}
  = link_to t('error_pages.go_to_home'), "/", :class => "home-button"

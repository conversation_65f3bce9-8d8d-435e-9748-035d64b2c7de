table.w560
  - items = @items.first(3)
  tr
    - items.each do |item|
      td align= 'center' style= "width: #{100/items.count}%"
        = link_to @cart_url + '&utm_content=image'
          = image_tag item.variant.get_thumb, class: "product-image"
  tr
    - items.each do |item|
      td.item
        p
          = link_to @cart_url + '&utm_content=link', style: 'text-decoration:none;color:black'
            strong = item.variant.product.title
  tr
    - items.each do |item|
      td align= 'center'
        p.paragraph-color
          strong = number_with_precision(item.variant.product.price)
- content_for :js do
  :javascript
    ;(function(){
      var arranged = false
      function numOfCol(){
        var w = $(window).width()
        if( w >= 1024 ) return 4
        if( w >= 768 ) return 3
        if( w >= 450 ) return 2
        return 1
      }

      var $el = $('.issuu-doc-list')
      var $items = $el.find('.issuu-doc')
      var $toRemoveLoading = $()
      var blockear = _.debounce(function(cbk){
        var cols = numOfCol()
        $el.BlocksIt({
          numOfCol: cols,
          offsetX: 10,
          offsetY: 15,
          blockElement: '.issuu-doc'
        })
        if( !arranged ){
          $el.removeClass('loading')
          arranged = true
        }
        if( $toRemoveLoading.length ) {
          $toRemoveLoading.removeClass('loading')
          $toRemoveLoading = $()
        }
      }, 25)

      $items.each(function(i, el){
        var $item = $(el)
        $item.find('.thumb').one('load', function(e){
          $toRemoveLoading = $toRemoveLoading.add($item)
          blockear()
        })
      })

      $(window).on('resize', blockear)
      setTimeout(function(){
        blockear()
        $items.removeClass('loading')
      }, 2000)
    })()

- if @documents.present?
  .issuu-doc-list
    - @documents.each do |doc|
      .issuu-doc.loading
        %a.link{ href: doc.link, target: '_blank', rel: 'nofollow', title: doc.title }
        %img.thumb{ src: doc.thumb }
        .info
          %h2.title= doc.title
          %p.date= l doc.created_at, format: :long
          - if doc.description.present?
            %p.description= doc.description

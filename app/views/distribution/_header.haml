.header
  .row
    .small-12.columns
      .subscribe
        %a.facebook.button{ href: 'https://www.facebook.com/distribution.goodpeople', target: '_blank', rel: 'nofollow' }
        .subscribe-form
          %form{ action: distribution_subscribe_url(network: @network.downcase), method: :post, rel: 'nofollow' }
            %input{ type: :email, placeholder: 'Suscribite: <EMAIL>', name: :email }
            %button.button{ type: :submit } Ok
      %a{ href: distribution_index_path(network: @network.downcase), title: "#{t('distribution.title')} | Avenida.com" }
        %img.logo{ src: image_path('distribution/logo-header.svg'),  onerror: "this.src='#{image_path('distribution/logo-header.png')}'", alt: 'GoodPeople', width: 189, height: 132 }
        .title
          %span.title-bg
            %span.title-clipped= t('distribution.title')
      %p.desc= t('distribution.header_description')
      %a.contacto.button.tiny{ href: '#distribution-footer', onclick: "window.$ && $(document).scrollTo( $('#distribution-footer'), 300 ); return false;" } contactanos!

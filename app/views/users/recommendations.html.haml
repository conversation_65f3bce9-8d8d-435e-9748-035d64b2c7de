- if with_menu
  .users-recommendations{ 'data-url' => users_recommendations_path, 'data-page' => users.current_page, 'data-seed' => seed, 'data-type' => type }
    = render partial: 'users/recommendations_types'
    .users-list-wrapper{ class: users.length == 0 && 'empty' }
      %h3.empty-msg= t('.empty')
      .users-list.cf{ class: users.current_page >= users.total_pages && 'last-page' }
        = render partial: 'users/mini_user', collection: users, as: :user
- else
  = render partial: 'users/mini_user', collection: users, as: :user
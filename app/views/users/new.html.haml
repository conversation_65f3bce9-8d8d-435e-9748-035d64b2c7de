= content_for :title do
  Avenida.com | Sign up

= content_for :meta do
  %meta{ name: 'description', content: t('.meta_description') }

= content_for :body_class, 'signup-page'

.signup-step.user-info.small-centered.columns
  = form_for @user, url: users_path, html: { autocomplete: 'off', 'data-availability-path' => '/users/<attr>_available' } do |f|
    = hidden_field_tag :auth_id, @auth_id
    = hidden_field_tag :avatar_url, params[:avatar_url]
    .row.signup-step-top
      = image_tag "social/signup-state-1.png", class: 'state'
    .row.signup-step-content
      .medium-3.columns
        = render partial: 'social/partials/avatar_uploader', locals: { avatar: @avatar_url, id: 'avatar-uploader' }
      .medium-9.columns.profile-form
        = f.fields_for :profile do |p|
          .row
            .medium-6.columns
              = p.text_field :first_name, placeholder: t('.first_name')
            .medium-6.columns
              = p.text_field :last_name, placeholder: t('.last_name')
        .row
          .medium-12.columns
            = f.text_field :email, placeholder: t('.email_address')
        .row
          .medium-12.columns
            = f.text_field :login, placeholder: t('.username')
        .row
          .medium-6.columns
            = f.password_field :password, placeholder: t('.password')
          .medium-6.columns
            = f.password_field :password_confirmation, placeholder: t('.confirm_password')
        = f.fields_for :profile do |p|
          .row
            .medium-12.columns
              = p.text_area :bio, placeholder: t('.bio'), maxlength: @profile_bio_maxlength, rows: 3
    .row.signup-step-bottom
      .medium-12.columns
        %button.button.red.right-arrow{ type: 'submit', disabled: 'disabled' }= t('.next')

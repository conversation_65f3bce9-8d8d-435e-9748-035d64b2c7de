- shortlist ||= false
- user_counter ||= 0
.mini-user.cf{ class: shortlist && user_counter > 4 && 'hidden' }
  = render partial: 'social/partials/user_avatar', locals: { user: user }
  - if show_follow_button?(user)
    = render partial: 'social/partials/follow_button', locals: { user: user }
  %p.name.ellipsis= link_to user.full_name, profiles_path(user.login)
  %p.bio= user.profile.bio.presence || "@#{user.login}"
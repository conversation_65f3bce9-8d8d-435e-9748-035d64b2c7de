- types = Network.recommendations_types_for(@network)
- active_type = users.first.try(:class).to_s || 'SocialUser'
- inactive_types = types - [active_type]
.users-recommendations-item
  .loader
  .head
    = render partial: 'users/recommendations_types'
  .mini-users-wrapper{ class: 'active', 'data-type' => active_type }
    = render partial: 'users/mini_user', collection: users, as: :user, locals: { shortlist: true }
    %p.no-recommendations= t('social.profiles.user_show.no-recommendations')
  - inactive_types.each do |_type|
    .mini-users-wrapper{ 'data-type' => _type }
      %p.no-recommendations= t('social.profiles.user_show.no-recommendations')
  .users-recommendations-types-show.unselectable
    .show-type.ellipsis{ class: 'active', 'data-type' => active_type }
      = t(".show_types.#{active_type}")
    - inactive_types.each do |_type|
      .show-type.ellipsis{ 'data-type' => _type }
        = t(".show_types.#{_type}")

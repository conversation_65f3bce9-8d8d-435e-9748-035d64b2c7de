- on_modal ||= false
.signup-modal
  - if on_modal
    %button.mfp-close
      %i.mfp-close-icn &times;
  .sign-up-header
  .sign-up.cf
    .logo
    %h4.sign-up-message= t('.signup_message')
    %p.sign-up-divider
      %span.sign-up-email= t('.sign_up_with')
    %a.button.facebook{ href: '/auth/facebook', rel: 'nofollow' }= t('.facebook')
    %a.button.twitter{ href: '/auth/twitter', rel: 'nofollow' }= t('.twitter')
    %p.sign-up-divider
      %span.sign-up-email= t('.sign_up_with_email')
    .signup-with-email-wrapper.cf
      = form_for (PartialSignup.new) do |f|
        = f.text_field(:email, placeholder: t('.enter_email_address'))
        %button.button.grey{ type: 'submit' }= t('.sign_up')
    %p.login-message
      %span.login-message-button{ onclick: "javascript: if( window.$ ) _.defer(function(){$('.h-btn-login:visible').click()})" }= t('.login_msg')
    %p.terms-and-policy-check
      = t '.terms_and_policy_html', terms_link: link_to(t('.terms'), pages_terms_path(network: @network.downcase)),
                                    privacy_link: link_to(t('.privacy'), pages_privacy_path(network: @network.downcase))
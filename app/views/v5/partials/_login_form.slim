- title ||= t('.title')
- msg ||= nil
- with_guest ||= false
- cssClass = []
- cssClass << 'with-guest' if with_guest
- cssClass << 'with-msg' if msg.present?
- for_review ||= false

.login-form.v5-login-form class=cssClass
  .loader

  - if msg.present?
    p.msg = msg
  - if flash[:alert]
    .flash_alert.error = flash[:alert]

  .login-user
    h2 = title
    = form_for @user_session, url: new_login_path(checkout: (with_guest ? true : nil), return_to: params[:return_to]) do |f|
      = f.text_field :login, placeholder: t('.enter_email'), autofocus: true
      = f.password_field :password, placeholder: t('.password')
      p.remember
        label
          input name='user_session[remember_me]' type='checkbox' value=1
          = t('.remember_me')
      p.forgot
        a href=new_password_reset_url rel='nofollow' = t('.forgot_password')
      .submit: button type='submit' = t('.sign_in')

  .login-social
    h2 = t('.use_these_networks')
    a.i-logo-facebook.js-allow-leave href='/auth/facebook' rel='nofollow' Facebook
    a.i-logo-twitter.js-allow-leave href='/auth/twitter' rel='nofollow' Twitter
    p.register
      = t('.new_gp')
      span
        == t('.create_account', url: new_user_path)
  - if with_guest
    .login-guest
      h2 = t(".guest_alternative#{for_review && '_for_review' || ''}")
      = form_tag mkp_guests_path, id: 'guest-form' do
        = hidden_field_tag :_method, 'create'
        = hidden_field_tag 'guest[network]', @network
        - if params[:return_to].present?
          = hidden_field_tag :return_to, params[:return_to]
        = text_field_tag 'guest[first_name]', nil, placeholder: t('.guest_firstname'), required: true
        = text_field_tag 'guest[last_name]', nil, placeholder: t('.guest_lastname'), required: true
        = text_field_tag 'guest[email]', nil, placeholder: t('.guest_email'), type: 'email', required: true
        .submit: button type='submit' = t('.as_guest')

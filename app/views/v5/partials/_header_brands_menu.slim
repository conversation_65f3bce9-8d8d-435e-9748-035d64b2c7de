.category class=(item[:childs].present? ? 'with-childs' : 'without-childs')
  span.category-link type='button' = item[:title]
  a.category-link href=item[:url] = item[:title]
  - if item[:childs].present?
    - i = 4
    - columns = item[:childs].group_by { i == 4 ? i = 1 : i+=1; i }
    .subcategories-wrapper: .subcategories
      - columns.values.each do |childs|
        .subcategories-column
          - childs.each do |child|
            .subcategory
              .brand
                - img = child[:icon]
                - link = Mkp::Catalog::UrlHandler.path(b: child[:slug], network: @network.downcase)
                = render partial: 'partials/hexagon',
                         locals: { shape: :square, type: 'catalog-avatar', img: img, link: link }
                .ellipsis
                  = link_to child[:name], link
      .load-more
        a href= mkp_brands_url
          strong.link-brands= t('v5.partials.header.categories.view_more')

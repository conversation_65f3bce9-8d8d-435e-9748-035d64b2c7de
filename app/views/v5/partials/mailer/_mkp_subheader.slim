/ - menu = Mkp::Menu.build_mailer_menu(network)
/ tr
/   td.w600
/     table.w600 align="center" border= "0" cellpadding= "0" cellspacing= "0" width= "600"
/       tr#marketplace_menu
/       	-unless menu.nil?
/ 	        -total = menu.length
/ 	        -menu.each do |menu_item|
/ 	          td width="#{(600/total).to_i}"
/ 	            a href= "#{menu_item.url}"
/ 	              = menu_item.name_display

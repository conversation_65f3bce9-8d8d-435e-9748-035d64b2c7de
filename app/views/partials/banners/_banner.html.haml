.gp-vnr{ class: "with-#{banner.columns}-columns #{banner.template}" }
  .gp-vnr-content.desktop-content{ style: "background-image: url(#{banner.desktop_image.url});" }
    - if banner.web_headline.present? || banner.web_description.present?
      .gp-vnr-content-text{ style: banner.position }
        - if banner.web_headline.present?
          %h1.headline= banner.web_headline
        - if banner.web_description.present?
          %p.description= banner.web_description
    = banner_link(banner, class: 'full-link')
  .gp-vnr-content.mobile-content{ style: "background-image: url(#{banner.mobile_image.url});" }
    - if banner.mobile_headline.present? || banner.mobile_description.present?
      .gp-vnr-content-text
        - if banner.mobile_headline.present?
          %h1.headline= banner.mobile_headline
        - if banner.mobile_description.present?
          %p.description= banner.mobile_description
    = banner_link(banner, class: 'full-link')
- networks = Network.all_visible
- cache ['network_selector', @network, networks] do
  - currencies = Mkp::Currency.select('identifier, symbol, network').index_by(&:network)
  .network-selector
    .network-selector-selected.cf{ class: @network }
      .title><
        #{Network[@network].title} #{network_currency(currencies, @network)}
      .flag><
    .network-selector-dropdown
      - networks.each do |network|
        .network-selector-item.cf{ class: network }
          %a{ href: network_selector_item_url(networks, network), 'data-network' => network }><
            .flag><
            .title><
              #{Network[network].title} #{network_currency(currencies, network)}

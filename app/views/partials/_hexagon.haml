- shape ||= :hexagon
- type ||= nil
- img ||= nil
- link ||= nil
- fallback_color ||= nil
- fallback_text ||= nil
- label ||= 'Avatar Thumb'

- style = ''
- style << "background-image:url(#{img});" if img.present?
- style << "background-color:#{fallback_color};" if fallback_color.present?

- onerror = "this.parentNode.className = '#{type} #{shape} show-fallback';"

- class_name = [type]
- class_name << (img.present? ? 'hide-fallback' : 'show-fallback')

- case shape
  - when :hexagon
    .hexagon{ class: class_name, style: style }
      - if fallback_color.present? && img.present?
        %img.fallback-img{ src: img, onerror: onerror }
      - if fallback_text.present?
        .hexagon-fallback-text= fallback_text
      .hexagon-container
        .hexagon-in1
          .hexagon-in2{ style: style }
      - if link.present?
        %a.full-link{ href: link, "data-label" => label }
  - when :square
    .square{ class: class_name, style: style }
      - if fallback_color.present? && img.present?
        %img.fallback-img{ src: img, onerror: onerror }
      - if fallback_text.present?
        .hexagon-fallback-text= fallback_text
      - if link.present?
        %a{ href: link, "data-label" => label }

%p
  = t('mkp.shipment_mailer.notify_shipped_to_user.customer.full_name')
  #{address.full_name.try(:titleize)}
%p
  = t('mkp.shipment_mailer.notify_shipped_to_user.customer.address')
  - address_to_print = []
  - address_to_print << address.address
  - address_to_print << address.street_number if address.street_number.present?
  - address_to_print << address.address_2
  - address_to_print << address.city
  - address_to_print << address.state
  - address_to_print << address.zip.to_s
  - address_to_print << GeoConstants::Countries.name_for_code(address.country) if address.country.present?
  = address_to_print.compact.reject(&:empty?).join(', ').titleize

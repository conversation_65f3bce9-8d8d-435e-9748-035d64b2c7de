- menu = Mkp::Menu.active_by_network(network).order("display_order asc").limit(6)
- menu = menu.map { |menu_item| menu_item.url = "http://#{HOSTNAME}#{menu_item.url}"; menu_item }
%tr
  %td.w600
    %table.w600{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "600"}
      %tr#marketplace_menu
        - total = menu.length
        - menu.each do |menu_item|
          %td{width: (600/total).to_i }
            %a{:href => menu_item.url, :style => "font-size:12px;background-color:whitesmoke;color:#333;text-decoration:none;text-transform:uppercase;"}
              = menu_item.name_display
              = "holaaaaaaaaaaaa"
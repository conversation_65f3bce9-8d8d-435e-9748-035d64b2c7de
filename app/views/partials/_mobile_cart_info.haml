%script#mobile-cart-info{ type: 'text/template' }><
  .mobile-header-overlay.mobile-cart-info.empty{ style: 'display: none' }
    %h3.empty-cart-msg= t('partials.cart_info.empty_cart_msg')
    .mobile-cart-info-list
    .mobile-cart-info-total
      %span.text #{t('.total')}:&nbsp;$
      %span.value
    .mobile-cart-info-actions
      %button.continue= t('.continue')
      %button.checkout= t('.checkout')
    %script#mobile-cart-info-item{ type: 'text/template' }
      .mobile-cart-info-list-item.cf{ 'data-item-id' => '<%=data.id%>' }
        %img.thumb{ src: '<%=data.thumb%>' }
        %button.remove
          .icon
        .manufacturer.ellipsis
          %a.t{ href: '<%=data.product.manufacturer.url%>' }
            <%=data.product.manufacturer.name%>
        .title.ellipsis
          %a.t{ href: '<%=data.url%>' }
            <%=data.product.title%>
        .attrs
          %span.t <%=data.attrs%>
        .buy-info.cf
          .qty
            %span.t #{t('.quantity')} <span class="val"><%=data.quantity%></span>
          .price
            %span.t $<%=data.price%>

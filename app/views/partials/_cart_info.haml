- content_for :js do
  :javascript
    ;if( window.gp ) (function(){
      var cart_items = #{@bootstrap_cart && @bootstrap_cart.items.to_json}
      gp.cart.bootstrap(cart_items)
      $('.h-btn-cart')
        .prop('disabled', false)
        .css('opacity', '1')
    })()
#cart-info-modal.cart-info-modal.reveal-modal
  .close
  .headline
    %h5>
      #{t('.your_cart')} (
      %span.count-modal>
      &nbsp;
      %span.products= t('.items', count: 0)
  %ul.cart-items
    .loader
    %h3.empty-cart-msg= t('.empty_cart_msg')
    %script#cart-item{ type: 'text/template' }
      %li.cart-item.row.collapse{ id: 'variant-<%= data.id %>' }
        .small-2.columns
          %a(href="<%=data.link%>")
            %img{ style: "background-image: url('<%=data.thumb%>')" }
        .small-7.columns
          .info
            %div
              %a.title(href="<%= data.link %>") <%= data.title %>
              |
              %a(href="<%= data.manufacturer.url %>") <%= data.manufacturer.name %>
            %div
              <% $.each(data.properties, function(key, value){ %>
              <% if(key == 'color'){ %>
              .property-wrapper
                %label #{t('.color')}:
                .color{ style: 'background-color:<%= color %>;' }
              <% } %>
              <% if(key == 'size'){ %>
              .property-wrapper
                %label #{t('.size')}:
                .size
                  %span <%= value %>
              <% } %>
              <% if(key == 'material'){ %>
              .property-wrapper
                %label #{t('.material')}:
                .material
                  %span <%= value %>
              <% } %>
              <% if(key == 'hardness'){ %>
              .property-wrapper
                %label #{t('.hardness')}:
                .hardness
                  %span <%= value %>
              <% } %>
              <% if(key == 'dimensions'){ %>
              .property-wrapper
                %label #{t('.dimensions')}:
                .dimensions
                  %span <%= value %>
              <% } %>
              <% if(key == 'length'){ %>
              .property-wrapper
                %label #{t('.length')}:
                .length
                  %span <%= value %>
              <% } %>
              <% if(key != 'color' && key != 'size' && key != 'material' && key != 'hardness' && key != 'dimensions' && key != 'length'){ %>
              .property-wrapper
                %label <%= key %>:
                .custom-property
                  %span <%= value %>
              <% } %>
              <% }) %>
              .property-wrapper
                %label #{t('.quantity')}:
                .quantity
                  %input.val{ type: 'text', value: '<%= quantity %>', maxlength: '3', disabled: 'disabled' }
        .small-3.columns.remove-item-container
          %button.button.remove-item= t('.remove_item')
          .subtotal-wrapper
            .price
              %span.per-unit #{t('.per_unit')}:
              <% if( data.on_sale ) { %>
              %span.original-price <%= data.currency_symbol %> <%= data.regular_price %>
              %span.sale-price <%= data.currency_symbol %> <%= data.sale_price %>
              <% } else { %>
              %span.simple-price <%= data.currency_symbol %> <%= data.regular_price %>
              <% } %>
            .subtotal
              #{t('.subtotal')}:
              %span.currency <%= data.currency_symbol %>
              %span.numbers  <%= data.total %>
        .msg{ style: 'display:none;' }
  .actions
    %a.button.small.continue-shopping=t('.continue_shopping')
    .total{ style: 'display:none;' }
      #{t('.total')}:
      %span.currency $
      %span.numbers
    %form#go-to-checkout-form{ action: mkp_checkout_url(@network.downcase) }
      %button.button.red.small.go-to-checkout{ type: 'submit' }=t('.checkout')

- load_fonts = load_fonts.nil? ? true : load_fonts
link rel="dns-prefetch" href='//s3.amazonaws.com'
meta charset='UTF-8'
meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"
meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'
meta content='always' name='referrer'
meta content=I18n.locale.to_s name='locale'
- if defined?(current_user) && current_user.present?
  meta content=current_user.id name='user'
- if Rails.env.development? || Rails.env.test?
  meta content=Rails.env name='environment'
- if ON_STAGING
  meta content='staging' name='context'
meta content=@network name='network'
- network = Network.config[@network]
meta name='geo.region' content= network['region']
meta name='geo.placename' content= network['placename']
meta name='geo.position' content= network['position']
meta name='ICBM' content= network['icbm']
meta property='og:site_name' content='Avenida.com'
meta property='fb:app_id' content=FACEBOOK_API

link rel='apple-touch-icon' href='/assets/ios-icon.png'
link rel='apple-touch-icon' href='/assets/ios-icon-ipad.png' size='72x72'
link rel='apple-touch-icon' href='/assets/ios-icon-retina.png' size='114x114'
link rel='apple-touch-icon' href='/assets/ios-icon-ipad-retina.png' size='144x144'

= render partial: 'partials/polyfills'

- if load_fonts
  - content_for :before_js do
    = stylesheet_link_tag '//fonts.googleapis.com/css?family=Open+Sans:400,700&subset=latin,latin-ext'
= csrf_meta_tags

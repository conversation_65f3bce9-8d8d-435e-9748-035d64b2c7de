.login-dropdown
  = form_for @user_session, url: login_url do |f|
    = f.text_field :login, placeholder: t('.enter_email')
    = f.password_field :password, placeholder: t('.password')
    .remember_me
      %input{ id: 'user_session_remember_me', name: 'user_session[remember_me]', type: 'checkbox', value: '1' }
      %label{ for: 'user_session_remember_me' }= t('.remember_me')
    %p.forgot_password
      %a{ href: new_password_reset_url, rel: 'nofollow' }= t('.forgot_password')
    %button.button.grey{ type: 'submit' }= t('.sign_in')
  .dropdown-divider
    .other-networks
      = t('.use_these_networks')
  .social-media
    %a.button.facebook{ href: '/auth/facebook', rel: 'nofollow' }= t('.facebook')
    %a.button.twitter{ href: '/auth/twitter', rel: 'nofollow' }= t('.twitter')

  %p.register
    %strong
      %a{ href: new_user_path }= t('partials.login_form.register_message')
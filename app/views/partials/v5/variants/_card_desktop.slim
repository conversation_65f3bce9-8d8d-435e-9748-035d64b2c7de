article class=(on_sale && 'on-sale') data-id=v.id data-url=v.get_url
  h2.manufacturer-name
    a href=Mkp::Catalog::UrlHandler.path(network: @network.downcase, b: p.manufacturer.slug) data-label='Manufacturer Name'
      = p.manufacturer.name
  .img-wrapper
    - if on_sale
      span.sale-discount
        = "-#{ Mkp::ProductSaleDetector.new(p).percent_off}%"
    a href=v.get_url(network: @network.downcase) data-label='Product Image'
      img src=v.get_thumb alt=alt_to_product_images(p) style="background-image:url(#{v.get_thumb})"
    a.more href=v.get_url data-label='Get Details'
      div
        span data-label='Get Details' = t('.details')
  h2
    a href=v.get_url data-label='Product Title' = p.title
  div
    span.price data-label='Price' #{current_currency_format}#{price_format('%.2f' % p.regular_price)}
    - if on_sale
      span.sale-price data-label='Sale Price' #{current_currency_format}#{price_format('%.2f' % Mkp::ProductSaleDetector.calculate(p))}
- list_name ||= 'v5-variants-list'
- content_for :analytics_events
  = track_product_list(list_name, variants) if variants.present?
- content_for :js do
  = track_product_list_clicks(list_name, variants, 'v5') if variants.present?
.variants-list data-name=list_name
  - variants.each do |v|
    - p = v.product
    - on_sale = Mkp::ProductSaleDetector.is_on_sale?(p)
    - cache_key = ['v17', v, p.updated_at.to_i, p.shop.updated_at.to_i, p.manufacturer.updated_at.to_i]
    - cache_key.push('sale') if on_sale
    - cache_key.push("v1.4")
    - cache_key.push("mobile") if browser.mobile? && controller_name == 'catalog'
    - if @debug
      .ranking
        / .score= "Score: #{@search.hits[v_counter].instance_variable_get("@stored_values")["variant_ranking"]}"
        .relevance
          = "Relevance: #{p.relevance_score}"
          - data = Analytics::Reporter.get_stored_product_data(p.id)
          - unless data.blank?
            .explain="(pid: #{p.id} / views: #{data.productDetailViews} / carts: #{data.productAddsToCart} / sells: #{p.sold_count})"
        .availability= "Availability: #{v.availability_score}"
    = cache cache_key do
      -  if browser.mobile? && controller_name == 'catalog'
        = render partial: "partials/v5/variants/card_mobile", locals: {on_sale: on_sale, v: v, p: p}
      - else
        = render partial: "partials/v5/variants/card_desktop", locals: {on_sale: on_sale, v: v, p: p}
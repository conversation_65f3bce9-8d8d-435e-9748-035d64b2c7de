article.catalog-card-mobile class=(on_sale && 'on-sale') data-id=v.id data-url=v.get_url
  a.background-image-mobile style="background-image: url(#{v.get_thumb});" href="#{v.get_url(network: @network.downcase)}"
    - if on_sale
      span.on-sale-mobile -#{Mkp::ProductSaleDetector.new(p).percent_off}%
  .product-info-mobile
    span.h2
      a href=v.get_url data-label='Product Title' = p.title
    span.h3
      a href=Mkp::Catalog::UrlHandler.path(network: @network.downcase, b: p.manufacturer.slug) data-label='Manufacturer Name'
        = p.manufacturer.name
    .prices-container-mobile
      .prices-mobile
        span.price-mobile data-label="Price" #{current_currency_format}#{price_format('%.2f' % p.regular_price)}
        - if on_sale
          span.sale-price-mobile data-label='Sale Price' #{current_currency_format}#{price_format('%.2f' % Mkp::ProductSaleDetector.calculate(p))}

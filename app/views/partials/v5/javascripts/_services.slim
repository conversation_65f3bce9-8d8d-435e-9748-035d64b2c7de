= load_analytics_libraries
= content_for :analytics if content_for? :analytics
= track_pageview
= content_for :analytics_events if content_for? :analytics_events

- if defined?(load_facebook) && load_facebook
  = render partial: 'partials/v5/javascripts/services/facebook'

/ PENDING: Eventually, we wil migrate all tags to the GTM.
= render partial: 'partials/v5/javascripts/services/google_tag_manager'

= javascript_include_tag VEINTERACTIVE_TAG, :async => true if VEINTERACTIVE_ENABLED
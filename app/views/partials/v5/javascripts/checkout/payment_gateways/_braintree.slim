= javascript_include_tag 'https://js.braintreegateway.com/v2/braintree.js'
javascript:
  ;(function(){
    var BT = { name: 'braintree' }
    BT.token = "#{get_braintree_client_token}"
    gp.checkout.payment = BT
    BT[BT.name] = braintree
    BT.client = new braintree.api.Client({ clientToken: BT.token });
    // braintree.setup(BT.token, 'custom', { id: 'bt-form' });
    $(window).load(function(){
      gp.pubSub.trigger('checkout:payment_gateway:loaded')
    })
  })()

= javascript_include_tag 'https://secure.mlstatic.com/sdk/javascript/v1/mercadopago.js'
javascript:
  ;(function(){
    Mercadopago.setPublishableKey("#{MERCADOPAGO_PUBLIC_KEY}")
    var MP = { name: 'mercadopago' }
    gp.checkout.payment = MP
    MP['client'] = Mercadopago
    MP.active_payments = function(){
      var payments = Mercadopago.getPaymentMethods()
      return _(payments).select(function(payment){ return payment.status === 'active' })
    }
    $(window).load(function(){
      var count = 0
      var __gateway_loaded_status = function(){
        count = count + 1
        if( MP.active_payments().length > 0 ){
          gp.pubSub.trigger('checkout:payment_gateway:loaded')
        } else {
          if( count <= 13 ){
            _.delay(__gateway_loaded_status, 300)
          }
        }
      }
      __gateway_loaded_status()
    })
  })()

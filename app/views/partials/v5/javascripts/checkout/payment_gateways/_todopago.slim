= javascript_include_tag TODOPAGO_HYBRID_JS
= javascript_include_tag 'https://secure.mlstatic.com/sdk/javascript/v1/mercadopago.js'

javascript:
  var todoPagoTokenized = false;

  window.TPFORMAPI.hybridForm.initForm({
    callbackValidationErrorFunction: 'validationCollector',
    callbackCustomSuccessFunction: 'customPaymentSuccessResponse',
    callbackCustomErrorFunction: 'customPaymentErrorResponse',
    callbackBilleteraFunction: 'billeteraPaymentResponse',
    botonPagarId: 'MY_btnConfirmarPago',
    botonPagarConBilleteraId: 'MY_btnPagarConBilletera',
    modalCssClass: 'modal-class',
    modalContentCssClass: 'modal-content',
    beforeRequest: 'initLoading',
    afterRequest: 'stopLoading'
  });

  var _getCreditCardPaymentObject = function (response, payment_method) {
    return {
      gateway: gp.checkout.payment.name,
      public_request_key: gp.publicRequestKey,
      request_key: gp.requestKey,
      operation_id: response.AuthorizationKey,
      doc_number: gp.doc_number,
      doc_type: gp.doc_type,
      payment_method: payment_method
    }
  };

  function validationCollector(parametros) {
    showError(parametros.error);
  }

  function billeteraPaymentResponse(response) {
    gp.pubSub.trigger( 'checkout:pay', _getCreditCardPaymentObject(response, "billetera_todopago") )
  }

  function customPaymentSuccessResponse(response) {
    gp.pubSub.trigger( 'checkout:pay', _getCreditCardPaymentObject(response, "creditcard") )
  }

  function customPaymentErrorResponse(response) {
    showError(response.ResultMessage);
  }

  function showError(error) {
    $('.errors').html(" ");
    $('.errors')
        .append('<span class="title">Error al realizar pago</span>')
        .append('<span class="error">' + error + '</span>')
        .show();

    gp.pubSub.trigger('checkout:payment:fail');

    $('.creditcard-form').find('form input, form select').each(function () {
      this.disabled = false;
    });
  }
  function initLoading() {}
  function stopLoading() {}

  function TodoPagoClient( merchant ) {
    this.merchant = merchant;
  }

  TodoPagoClient.arrayify = function( obj ) {
    if ( obj instanceof Array )
      return obj;
    return [ obj ];
  };

  (function(){
    Mercadopago.setPublishableKey("#{MERCADOPAGO_PUBLIC_KEY}");
    var MP = {name: 'mercadopago'};
    gp.checkout.payment_2 = MP;
    MP['client'] = Mercadopago;

    MP.active_payments = function () {
      var payments = Mercadopago.getPaymentMethods();
      return _(payments).select(function (payment) {
        return payment.status === 'active'
      })
    };

    var count = 0;
    var __gateway_loaded_status = function () {
      count = count + 1;
      if (MP.active_payments().length > 0) {
        gp.pubSub.trigger('checkout:payment_gateway:loaded');
      } else {
        if (count <= 13) {
          _.delay(__gateway_loaded_status, 300)
        }
      }
    };

    __gateway_loaded_status();

    gp.checkout.payment = {
      name: 'todopago',
      client: new TodoPagoClient( #{TODOPAGO_MERCHANT.to_i} ),
      active_payments: function() {
       return [];
      }
    };
  })();


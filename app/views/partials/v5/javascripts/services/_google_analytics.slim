- extra_param = !!defined?(current_user) && current_user.present? ? "{ 'userId': #{current_user.id} }" : nil
- create_params = [ "'#{GOOGLE_ANALYTICS['tracking_id']}'", "'auto'", extra_param ].compact.join(', ')
javascript:
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', #{create_params.html_safe});
  // Updates the tracker to use `navigator.sendBeacon` if available.
  ga('set', 'transport', 'beacon');
  ga('require', 'ec');
  ga('set', 'dimension1', "#{@network}");

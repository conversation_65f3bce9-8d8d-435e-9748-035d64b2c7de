#fb-root
javascript:
  ;(function(){
    // Function to have a list of functions to load on fbAsyncInit
    var toLoad = []
    window.fbReady = function(func){
      window.FB ? func.call(window) : toLoad.push(func)
    }
    window.fbAsyncInit = function() {
      FB.init({
        appId: "#{FACEBOOK_API}",
        status: true,
        cookie: true,
        xfbml: true
      })
      // Execute all the fbReady pending functions
      toLoad.forEach(function(func){
        func.call(window)
      })
    }
  })();
script id= 'facebook-jssdk' src='//connect.facebook.net/en_US/all.js' async=true

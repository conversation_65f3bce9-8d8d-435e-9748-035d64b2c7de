- og_url ||= request.url.encode("utf-8", invalid: :replace, undef: :replace, replace: "_")
- og_type ||= 'website'
- og_description ||= t('social.description')
- og_title ||= "Avenida.com | #{t('layouts.v5.application.head_title')}".truncate(70)
- og_image_url ||= nil

meta property='og:type' content=og_type
meta property='og:url' content=og_url
meta property='og:description' content=og_description
meta property='og:title' content=og_title

- if og_image_url.nil?
	= render partial: 'partials/v5/meta/og_logo'
- else
	= render partial: 'partials/v5/meta/og_image', locals: { url: og_image_url }

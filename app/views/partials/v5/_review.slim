- date = review.created_at.to_date
- reviewer = review.respond_to?(:user) ? review.user : nil
.review itemprop='review' itemscope=true itemtype='http://schema.org/Review'
  meta itemprop='datePublished' content=l(date, format: :default)
  div itemprop='reviewRating' itemscope=true itemtype='http://schema.org/Rating'
    meta itemprop='worstRating' content='1'
    meta itemprop='ratingValue' content=review.stars
    meta itemprop='bestRating' content=Mkp::Review::MAX_STARS
  .reviewer
    - if reviewer
      .avatar
        = render partial: 'partials/v5/avatar', locals: { user: reviewer }
    span itemprop='author'= reviewer ? reviewer.full_name + " - " : review.email + " - "
    span.date
      =l(date, format: :day_and_year)
  .content
    .title
      .stars
        - Mkp::Review::MAX_STARS.times do |i|
          span.i-star class=((i < review.stars) && 'gold')
      span itemprop='name'= review.title
    .description itemprop='description'= review.description
    .actions
      span= t('.helpful', count: review.votes.count)
      - if current_user
        - appropriate = review.inappropriate == false && 'appropriate'
        span.i-like.like-review data-url=api_mkp_votes_path(review_id: review.id, product_id: review.product.id) class=(review.voted_by?(current_user) && 'active')
        span.i-flag.report-review data-url=mkp_product_review_report_path(@network.downcase, review.product_id, review.id) class=(review.reported_by?(current_user) && 'active') data-appropriate=appropriate

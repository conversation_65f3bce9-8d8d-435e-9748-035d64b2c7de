.mobile-header-phantom
.mobile-header
  button.h-btn-menu.reset-button
    span.i-menu
  a.h-logo href=root_url title='Home'
  img itemprop='logo' src=@current_store.logo alt=@current_store.title
  button.h-btn.h-btn-cart type='button' title=t('partials.header.cart')
    span.h-btn-icon.i-carrito
    span.h-btn-count data-count=0 0
  /button.h-btn.h-btn-search type='button'
  /  span.h-btn-icon.i-lupa
.searcher-mobile-fixed
  = render partial: 'partials/mobile_search_bar', locals: { prefilter: 'products' }
.searcher-mobile-fixed-phantom

= content_for :before_js do
  = render partial: 'partials/v5/mobile/menu'
  = render partial: 'partials/v5/cart'

- content_for :js do
  = track_add_remove_from_cart

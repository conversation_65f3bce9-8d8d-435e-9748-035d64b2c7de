#cart style='display:none;'
  .cart.empty
    h2= t('.title')
    h3.empty-msg= t('partials.cart_info.empty_cart_msg')
    .list
    .actions
      .total
        span.text
          | #{t('.total')}:&nbsp;$
        span.value
      a.continue
        = t('.continue')
      a.checkout href=mkp_checkout_url(@network.downcase)
        = t('.checkout')
    script#item-template type='text/template'
      .item data-item-id='<%=data.id%>'
        .img style='background-image:url(<%=data.thumb%>)'
          span
        .i-icon-trash-bin
        .title
          a href='<%=data.url%>' <%=data.product.title%>
        .mfr
          a href='<%=data.product.manufacturer.url%>' <%=data.product.manufacturer.name%>
        .attrs <%=data.attrs%>
        .qty
          | #{t('.quantity')} <span class="val"><%=data.quantity%></span>
        .buy-info
          .price $<%=data.price%>

- if @bootstrap_cart.present?
  - content_for :js do
    javascript:
      ;if( window.gp ) (function(){
        var cart_items = #{{@bootstrap_cart && @bootstrap_cart.items.to_json}}
        gp.compatibility_properties = #{{Mkp::Product::COMPATIBILITY_PROPERTIES.to_json}}
        gp.cart.bootstrap(cart_items)
      })()

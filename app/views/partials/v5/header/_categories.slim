- cache_key = ['avenida_menu3', @network, @menu[:timestamp]]
- cache_key.push("v1.6")
- cache cache_key do
  div.header-categories-top
    nav.header-categories
      - @menu[:items].each do |item|
        - if item.key?(:menu_display)
          = render partial: 'partials/v5/header/brands_menu', locals: { item: item }
        - else
          = render partial: 'partials/v5/header/category_menu', locals: { item: item }
        | &nbsp;
      div.last &nbsp;

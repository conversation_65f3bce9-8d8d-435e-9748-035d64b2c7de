.category class=(item[:childs].present? ? 'with-childs' : 'without-childs')
  span.category-link type='button' = item[:title]
  - if item[:title] == "HOTSALE" || item[:title] == "OFERTAS"
    a.category-link.red-one href=item[:url] = item[:title]
  - else
    a.category-link.gray-one href=item[:url] = item[:title]
  - if item[:childs].present?
    - i = 4
    - columns = item[:childs].group_by { i == 4 ? i = 1 : i+=1; i }
    .subcategories-wrapper: .subcategories
      - columns.values.each do |childs|
        .subcategories-column
          - childs.each do |child|
            .subcategory
              a href=child[:url]
                strong.i-flecha-right = child[:name]
              - if child[:childs]
                - child[:childs].each do |nephew|
                  a href=nephew[:url] = nephew[:name]

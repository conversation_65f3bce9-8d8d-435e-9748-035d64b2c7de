.header-search itemscope=true itemtype="http://schema.org/WebSite"
  .loader
  meta itemprop="url" content=root_url
  form action=Mkp::Catalog::UrlHandler.path(network: @network.downcase) method='get' itemprop="potentialAction" itemscope=true itemtype="http://schema.org/SearchAction"
    meta itemprop="target" content==Mkp::Catalog::UrlHandler.url(network: @network.downcase, query: '{query}').sub('%7B', '{').sub('%7D', '}')
    /[if IE]: input type='hidden' name='utf8' value='✓'
    input type='search' name='query' required=true placeholder=t('.search') itemprop="query-input"
    button.i-lupa type='submit' title=t('.find')
  script#header-search-suggester-item type='text/template'
    .item
      a href="<%= data.url %>"
        span
          <%= data.text %>
  script#header-search-categories-header type='text/template'
    h1.i-flecha-right = t('.categories_header_title')
  script#header-search-categories-item type='text/template'
    .item
      a href="<%= data.url %>"
        == t('.categories_item_encouragement_text')
        span
          <%= data.text %>
  script#header-search-brands-header type='text/template'
    h1.i-flecha-right = t('.brands_header_title')
  script#header-search-users-item type='text/template'
    .item.users-item class="<%= data.type %>"
      | <%= data.avatar %>
      h2 <%= data.full_name %>
      p <%= data.type === 'socialuser' ? data.login : data.bio %>
  script#header-search-variants-header type='text/template'
    h1.i-flecha-right = t('.variants_header_title')
  script#header-search-variants-item type='text/template'
    .item class="<% if( data.on_sale ) { %>on-sale<% } %>"
      .pic style="background-image:url(<%= data.picture %>)": span
      h2
        span <%= data.title %>
      |  <% if( data.on_sale ) { %>
      |  <div class="sale-price"> $&nbsp;<%= data.sale_price %></div>
      |  <% } %>
      .regular-price $&nbsp;<%= data.regular_price %>
  script#header-search-categories-facet-header type='text/template'
    h1.i-flecha-right = t('.categories_facet_header_title')
  script#header-search-categories-facet-item type='text/template'
    .item.i-lupa
      | <%= data.text %>
  script#header-search-manufacturers-facet-header type='text/template'
    h1.i-flecha-right = t('.manufacturers_facet_header_title')
  script#header-search-manufacturers-facet-item type='text/template'
    .item
      a href="<%= data.url %>"
        | <%= data.text %>

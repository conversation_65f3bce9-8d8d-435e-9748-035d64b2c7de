- display_cart         = true if display_cart.nil?
- display_notification = display_cart
- display_post         = display_cart
.user-menu
  - if display_cart
    - content_for :js do
      = track_add_remove_from_cart
    button.i-carrito title=t('.cart') type='button'
      span data-count='0' 0
  - if logged_in?
    - if display_notification
      button.i-bell.notifications-btn title=t('.notifications') type='button'
        span data-count='0' 0
    - if display_post
      button.i-post title=t('.post') type='button'
    .separator
    .avatar-wrapper
      - type = current_user.type == 'SocialUser' || current_user.type == 'ProAthlete' ? 'user-avatar' : 'brand-avatar'
      a href=profiles_path(login: current_user.login)
        = render partial: 'social/partials/user_avatar', locals: { user: current_user, type: type, link: nil }
    .sub-menu onclick='this.classList.toggle("active")'
      | &nbsp;
      span.arrow
      nav
        - if current_logged_in_user.shops.any?
          a href=lux_path = t('.dropdown.shop_admin').gsub(' ', '&nbsp;').html_safe
        a href=settings_orders_url = t('.dropdown.my_orders').gsub(' ', '&nbsp;').html_safe
        a href=settings_account_path = t('.dropdown.settings').gsub(' ', '&nbsp;').html_safe
        a href=logout_path = t('.dropdown.sign_out').gsub(' ', '&nbsp;').html_safe
        - managed_users.each do |u|
          strong: a href=users_current_path(id: u.id) = u.login

#login-dropdown style='display:none'
  .login-dropdown
    = form_for @user_session, url: login_url do |f|
      = f.text_field :login, placeholder: t('.enter_email'), autofocus: true
      = f.password_field :password, placeholder: t('.password')
      p.remember
        label
          input name='user_session[remember_me]' type='checkbox' value='1'
          = t('.remember_me')
      p.forgot
        a href=new_password_reset_url rel='nofollow' = t('.forgot_password')
      .submit
        button type='submit' = t('.sign_in')
    .social-media
      p = t('.use_these_networks')
      a.i-logo-facebook href='/auth/facebook' rel='nofollow' Facebook
      a.i-logo-twitter href='/auth/twitter' rel='nofollow' Twitter
    p.register
      = t('.new_gp')
      span
        == t('.create_account', url: new_user_path)

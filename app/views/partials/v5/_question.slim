- date = question.created_at.to_date
- questioner = question.user
.question
  .questioner
    .avatar
      = render partial: 'partials/v5/avatar', locals: { user: questioner }
    span.name= question.user.full_name
    span.date=l(date, format: :day_and_year)
  .content
    .description
      = question.description
    - if reply = question.answer
      .reply
        .description
          = reply.description
        .replier
          span= t('.reply_by')
          = reply.user.first_name

- author = item.author
- edit ||= false
- show_follow_button = show_follow_button_by_id?(author.id, author.class) && !current_user_following_by_id?(author.id, author.class)
- show_delete_button = show_delete_button_by_id?(author.id, author.class)
- creator = show_delete_button && edit ? 'owner' : 'author'
- locale = Network[@network].locale
- redis_key = [item, author.profile]
- redis_key.unshift('follow') if show_follow_button
- redis_key.unshift(creator)
- redis_key.unshift(locale)
- cache redis_key do
  - post = post_presenter.new item
  .posts-body-author
    = render partial: 'partials/v5/avatar', locals: { user: author,  style: :hexagonal }
    - if show_follow_button
      = render partial: 'partials/v5/social/follow_btn', locals: { user: author }
    - if show_delete_button && edit
      .delete-post
        = form_for item, method: :delete do |f|
          - unless current_page?(item)
            = hidden_field_tag :previous_url, request.fullpath
          button type='submit'
            = t('social.partials.feed_item_author.delete')
    h2
      .author
        | by&nbsp;
        a href=#profiles_url = author.full_name
      .date
        a href=post.path target='_blank' data-created-at=post.created_at
        - if post.is_aggregated?
          | &nbsp;via&nbsp;
          a href=post.metadata[:url] target='_blank' rel='nofollow'
            = post.metadata[:type]

- post = post_presenter.new item
- picture_size ||= :l
- metadata = item.metadata.presence
- has_media = item.metadata_has_media? || item.picture.present?
- css_classes = []
- css_classes << 'has-media' if has_media
- body = item.body_html
- body = metadata[:description] if metadata.present? && body.blank?
.whatsups-body.item-body.loading data-type='Whatsups' class=css_classes
  - if has_media
    .item-body-media
      - if item.picture.present?
        img src=item.picture.url(picture_size) alt=h(item.body)
      - elsif metadata.present?
        - if metadata[:type] == 'instagram'
          img src=metadata[:image] alt=h(item.body)
        - elsif metadata[:type] == 'youtube'
          .embed-video-wrapper
            iframe(src="#{request.scheme}://www.youtube.com/embed/#{metadata[:id]}?wmode=opaque&rel=0&autoplay=1"
                   width="100%"
                   height="100%"
                   frameborder="0"
                   allowfullscreen)
        - elsif metadata[:type] == 'vimeo'
          .embed-video-wrapper
            iframe(src="#{request.scheme}://player.vimeo.com/video/#{metadata[:id]}?portrait=0&byline=0&autoplay=1&color=#EB2227"
                   width="100%"
                   height="100%"
                   frameborder="0"
                   allowFullScreen)
      = render partial: 'partials/v5/posts/body/navigation'
  .item-body-content
    - if metadata.present? && metadata[:type] == 'url'
      = render partial: 'social/whatsups/metadata', locals: { metadata: metadata, link: social_whatsup_path(item) }
    = render partial: 'partials/v5/posts/body/author', locals: { item: item, edit: true }
    p.text = body
    = render partial: 'social/partials/item_body/social_actions', locals: { item: item }
    = render partial: 'partials/v5/social/comments', locals: { post: post }
    .recommended-products data-url=recommended_products_social_whatsup_path(item) style='display:none'
      hr

- picture_size ||= :l
- post = post_presenter.new item
.albums-body.item-body.loading data-type='Albums' class=(item.pictures.present? ? 'has-media' : '')
  - if item.pictures.present?
    .item-body-media
      - item.pictures.each_with_index do |pic, index|
        img.item src=pic.url(picture_size) alt=h(item.description) class=(index == 0 ? 'active' : '') data-pic-index=index
      - if item.pictures.size > 1
        .navigation-left title=t('social.albums.body.prev_pic')
          .icon
        .navigation-right title=t('social.albums.body.next_pic')
          .icon
  .item-body-content
    = render partial: 'partials/v5/posts/body/author', locals: { item: item, edit: true }
    .albums-title.item-title
      - unless item.title.nil?
        h5.title = item.title
      h6.pictures_count
        = t('social.albums.show.pictures_count', count: item.pictures.length)
    - if item.description.present?
      p.text= item.description
    - if item.pictures.present?
      - item.pictures.each_with_index do |pic, index|
        p.album-picture-description class=(index == 0 ? 'active' : '') data-pic-index=index
          -if pic.description.present?
            = pic.description_html
    = render partial: 'social/partials/item_body/social_actions', locals: { item: item }
    = render partial: 'partials/v5/social/comments', locals: { post: post }
    .recommended-products data-url=recommended_products_social_whatsup_path(item) style='display:none'
      hr

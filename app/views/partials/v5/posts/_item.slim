- without_author ||= false
- network ||= @network
- locale = Network[network].locale
- author = post.author
- redis_key = ['posts_list_item', post.class.to_s, post.id, author.profile.updated_at.to_i]
- redis_key.unshift(locale)
- redis_key.unshift('mobile') if browser.mobile?
- favorited = post.favorited_by?(current_user)
- redis_key.unshift("favorited_#{current_user.id}") if favorited
- redis_key.unshift('without_author') if without_author
/- cache redis_key do
- if true
  - post = post_presenter.new post
  article
    - unless without_author
      .author
        a href=profiles_path(login: author.login)
          = render partial: 'partials/v5/avatar', locals: { user: author }
        h2
          | by&nbsp;
          a href=profiles_path(login: author.login) = author.full_name
    h3.date
      a href=post.path target='_blank' data-created-at=post.created_at
      - if post.is_aggregated?
        |&nbsp;via&nbsp;
        a href=post.metadata[:url] target='_blank' rel='nofollow'
          = post.metadata[:type]
    - if post.pictures.present?
      .pictures class="with-#{post.pictures.length}"
        - post.pictures.each_with_index do |pic, i|
          a.show data-pic-index=i href=post.path target='_blank'
            img src=pic alt=post.title
    - if post.title.present?
      h1: a.show href=post.path target='_blank' == post.title
    - if post.description.present?
      h2: a.show href=post.path target='_blank'
        = strip_tags post.description.truncate(140).encode("utf-8", invalid: :replace, undef: :replace, replace: "_")
    - if post.embedded.present?
      .embedded
        - if post.embedded[:image].present?
          .embedded-img-wrapper
            a.show href=post.path target='_blank'
              img { src=post.embedded[:image]
                    alt=post.embedded[:title]
                    class=(post.embedded_is_video? ? 'i-play' : nil) }
        - if post.embedded[:title].present?
          h1: a.show href=post.path target='_blank' = post.embedded[:title]
        - if post.embedded[:description].present?
          p: a.show href=post.path target='_blank'
            = strip_tags post.embedded[:description].truncate(140)
    - unless browser.mobile?
      .more
        a.show href=post.path target='_blank'
        .share
          .buttons
            = render partial: 'partials/v5/social/favorite_btn',
                     locals: { post: post.subject,
                               url: post.favorites_path,
                               type: post.subject.class.name }
            - if post.pictures.present?
              button.round-social-icon.i-social-pinterest type='button' data-link=post.path data-pic=post.pictures.first
            button.round-social-icon.i-social-twitter type='button' title=t('social.twitter_share_sentence') data-link=post.path data-title=feed_item_title(post.subject)
            button.round-social-icon.i-social-facebook type='button' data-link=post.path title=t('social.facebook_share_sentence')
          - if post.favorites_count > 0
            .favorites-count == favorites_pretty_count(post.subject)

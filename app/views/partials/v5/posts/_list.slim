- load_more_locals_extras ||= {}
- without_author ||= false
- total_pages ||= nil
.posts-list.loading
  .posts-list-wrapper
    = render partial: 'partials/v5/posts/item', collection: posts, as: :post, locals: { without_author: without_author }
  .clear
  - next_page = posts.next_page
  - load_more_locals = { \
      attrs: { \
        href: merge_params_on_url_string(posts_path, { page: posts.next_page }),
        title: t('social.profiles.v5.brand_show.load_more_posts'),
        rel: 'nofollow',
        'data-current-page' => posts.current_page,
        'data-total-pages' => total_pages || posts.total_pages,
        'data-path' => posts_path \
      } \
    }.deep_merge(load_more_locals_extras)
  = render partial: 'partials/v5/load_more_wide',
           locals: load_more_locals



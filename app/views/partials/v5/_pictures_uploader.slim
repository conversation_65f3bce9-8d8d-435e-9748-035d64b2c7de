.pictures-uploader data-url=url data-type=type
  .pictures-uploader-items
    .pictures-uploader-item.add-photos
      .add-photos-selector
      h6.add-photos-text = t('.add_photos')
      input.pictures-uploader-item-description type='text'
  script#uploader-item type='text/template'
    .pictures-uploader-item.loading
      button.pictures-uploader-item-cancel.i-close type='button'
      .pictures-uploader-item-name <%= data.name %>
      .pictures-uploader-item-thumb-background
      .pictures-uploader-item-thumb style='background-image:url( <%= data.thumb %> )'
      .pictures-uploader-item-progress
        .pictures-uploader-item-progress-bar style='width:<%= data.progress %>%'
      input.pictures-uploader-item-description type='text' name='unpublished_picture[description]' maxlength=255 placeholder=t('.pic_description_placeholder')

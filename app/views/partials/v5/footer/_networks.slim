.networks
  p = t('.around_the_world')
  div
    - visible_networks = Network.all_visible
    - Network.all.each do |network|
      - is_enable = (Network[network].active? && network == current_network.identifier) || (Network[network].active? && Network[network].visible)
      a [class=(network.downcase + (' disabled' unless is_enable).to_s)
         onclick=("window.cookie.set('network', '#{network}')" if is_enable)
         href=(network_selector_item_url(visible_networks, network).encode("utf-8", invalid: :replace, undef: :replace, replace: "_") if is_enable)
         title=t(".networks.#{network.downcase}")]

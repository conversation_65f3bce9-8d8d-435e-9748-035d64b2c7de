- fallback_color = Social::Attachment::AvatarPicture.fallback_color_for(user)
- fallback_text = Social::Attachment::AvatarPicture.fallback_text_for(user)
- onerror = "this.parentNode.className += ' show-fallback'"
- size ||= :t
- avatar = user.respond_to?(:avatar) && \
           user.avatar.persisted? && \
           user.avatar.url(size) || \
           "//s3.amazonaws.com/non-existent-pic.jpg"
.avatar class='simple' style="background-color:#{fallback_color};"
  img src=avatar alt=user.full_name onerror=onerror
  .fallback
    span= fallback_text

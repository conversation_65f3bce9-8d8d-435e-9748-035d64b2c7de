- n = @network.downcase
- with_qr = @network == 'AR'
- cache_version = 'v35'
- full_network = Network[@network].thin? ? Network.default : @network
footer
  - cache ['footer', 'top', @network, I18n.locale, cache_version] do
  .bottom
    .row
      - phone = Network[full_network].marketplace_customer_phone.presence
      - cache ['footer', 'bottom', @network, I18n.locale, cache_version] do
        .payment-methods
          p = t('.payment_methods_title')
          .cards
            - check_payment_methods(t('.payment_methods')).each do |k, v|
              div class=k title=v
          p.secure.i-secure = t('.secure')
          -if phone
            p.secure = phone

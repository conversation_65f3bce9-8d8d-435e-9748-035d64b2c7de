- if browser.modern?
  javascript:
    // https://gist.github.com/mjlescano/be85e5724d9967475b18
    (function(){function t(e,t){localStorage["CACHE_"+e]=t}function n(e){return window.localStorage&&localStorage["CACHE_"+e]}function r(e,t){link=document.createElement("link");link.href=e;link.rel="stylesheet";link.type="text/css";link.onerror=t;link.onload=t;document.getElementsByTagName("head")[0].appendChild(link);if(!"onload"in document.createElement("link"))t()}function i(e){var t=document.createElement("style");t.setAttribute("type","text/css");if(t.styleSheet){t.styleSheet.cssText=e}else{t.innerHTML=e}document.getElementsByTagName("head")[0].appendChild(t)}function s(e,t){$.ajax({url:e}).done(function(e){t(e)}).fail(function(){t()})}var e=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||function(e){e()};window.loadCachedCss=function(o,u){var a=false;var f=function(){if(!a)e(u);a=true};window.setTimeout(f,5e3);if(!window.localStorage||!window.XMLHttpRequest){r(o,f);return null}var l=n(o);if(l){i(l);f();return true}else{s(o,function(e){i(e);f();t(o,e)});return false}}})()
    loadCachedCss('//s3.amazonaws.com/gpcdn-static/fonts/Lato.css.gz', function(){
      document.body.className += ' fonts-loaded'
    })
- else
  link href="//fonts.googleapis.com/css?family=Lato:400,700,900"
  script document.body.className += ' fonts-loaded'

#albums-form style='display:none'
  .albums-form
    = form_for Social::Album.new, multipart: true do |f|
      .title= t('.title')
      -# .albums-form-title
      -#   = f.text_field :title, maxlength: 255, autofocus: 'true', placeholder: t('.title_placeholder')
      .inputs
        .col
          label for='social_album_title' = t('.title_label')
          = f.text_field :title, maxlength: 255, autofocus: 'true', placeholder: t('.title_placeholder')
        .col
          label for='social_album_sport_ids' = t('.sports_label')
          = f.collection_select :sport_ids, @whatsup_sports, :id, :name, {}, multiple: true
      .hiddens
      .media-wrapper
        = render partial: 'partials/v5/pictures_uploader',
                 locals: { url: social_unpublished_pictures_path,
                           type: 'Social::Attachment::AlbumPicture' }
      .actions
        button.post-item type='submit' = t('.create')

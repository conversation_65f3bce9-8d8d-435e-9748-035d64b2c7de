- following = current_user_following_by_id?(user.id, user.class)
- type ||= 'user'
button.follow-btn{ class=(following ? 'unfollow' : 'follow')
                   data-follow-id=user.id
                   data-follow-action=(following ? 'unfollow' : 'follow')
                   data-base-path=(user.class == Sport ? 'sports' : 'users') }
  span.following= type == 'sport' ? t('.chosen') : t('.following')
  span.unfollow= type == 'sport' ? t('.unchoose') : t('.unfollow')
  span.follow= type == 'sport' ? t('.choose') : t('.follow')

.comments class=(post.comments.blank? ? 'empty' : '')
  .comments-list
    - post.comments.limit(50).reverse_each do |comment|
      = render partial: 'partials/v5/social/comment', locals: { item: comment }
  .comments-bottom
    = render partial: 'partials/v5/social/favorite_btn',
                     locals: { post: post.subject,
                               url: post.favorites_path,
                               type: post.subject.class.name,
                               without_text: true }
    .comments-form
      - if current_user.present?
        = form_for [post.subject, post.comments.new], action: (current_user.present? ? nil : ' ') do |f|
          = f.text_field :body, placeholder: t('social.comments.write_comment'), maxlength: 255,  pattern: '\S+'
          = f.hidden_field :target_id, value: post.id
          = f.hidden_field :target_type, value: post.subject.class.to_s
      - else
        form
          input#social_comment_body type='text' name='social_comment[body]' placeholder=t('social.comments.write_comment') maxlength=255
    = render partial: 'social/partials/item_body/user_menu', locals: { item: post.subject }

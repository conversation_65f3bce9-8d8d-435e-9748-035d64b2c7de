#whatsups-form style='display:none'
  .whatsups-form
    = form_for @new_whatsup, multipart: true, html: { data: { 'picture-url' => social_unpublished_pictures_path } } do |f|
      h2.whatsups-form-title= t('.create_goodpeople_post')
      .hiddens
        = f.hidden_field 'target_type'
        = f.hidden_field 'target_id'
      .media-wrapper.i-registro
        = f.text_area :body, maxlength: @whatsup_body_maxlength, rows: 3, autofocus: 'true'
        = render partial: 'partials/v5/social/file_uploader',
                 locals: { url: social_unpublished_pictures_path,
                           type: 'Social::Attachment::WhatsupPicture',
                           id: 'whatsups-pic-uploader' }
      .sport-selector
        = f.collection_select :sport_ids, @whatsup_sports, :id, :name, {}, multiple: true, placeholder: t('.select_a_sport')
      .actions
        a.add-img = t('.upload_photo')
        a.create-album = t('.create_album')
      .metadata-container
      .share
        p = t('.share')
        button.facebook-share type='button' data-can_write=@can_post_to_facebook Facebook
        button.twitter-share type='button' data-can_write=@can_post_to_twitter Twitter
      button.post-item type='submit' = t('.send_post')
- content_for :before_js do
  = render partial: 'partials/v5/social/albums_form'

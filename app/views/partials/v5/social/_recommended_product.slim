- v = variant
- p = v.product
.recommended-product class=(Mkp::ProductSaleDetector.is_on_sale?(p) ? 'on-sale' : '')
  - pic = v.picture
  - if pic.present?
    .picture style="background-image: url(#{pic.url(:st)})"
      a href=v.get_url(only_path: false) target='_blank'
  .title-wrapper
    - if Mkp::ProductSaleDetector.is_on_sale?(p)
      .sale-price
        span.currency = p.currency_symbol
        span = number_with_precision(Mkp::ProductSaleDetector.calculate(p))
    .price
      span.currency = p.currency_symbol
      span = number_with_precision(p.regular_price)
    .title
      a href=v.get_url(only_path: false) target='_blank' = v.title
  .manufacturer
    | by&nbsp;
    a href=Mkp::Catalog::UrlHandler.path(network: p.shop.network.downcase, b: p.manufacturer.slug)
      = p.manufacturer.name
  p.description
    a href=v.get_url(only_path: false) target='_blank'
      = p.description.strip.gsub(/\s+/, ' ').truncate(120)

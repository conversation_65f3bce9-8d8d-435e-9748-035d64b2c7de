.users-with-cover
  - users.each do |user|
    - cache_key = ['fragment', 'user_with_cover', user, user.profile.updated_at.to_i, user.profile.cover.updated_at.to_i]
    - cache_key << user.avatar.updated_at.to_i if user.respond_to?(:avatar)
    = cache cache_key do
      article
        a href=profiles_path(user.login)
          .cover style="background-image:url(#{user.profile.cover.url(:m_cover)})"
            span
          .avatar-wrapper
            = render partial: 'partials/v5/avatar', locals: { user: user }
          h2= user.full_name
          - if user.profile.bio.present?
            p: span= user.profile.bio

- type ||= nil
- width ||= nil
- height ||= nil
%meta{ property: 'og:image', content: url }
- if url.include? 'http:'
  %meta{ property: 'og:image:secure_url', content: url.sub('http:', 'https:') }
- if type.present?
  %meta{ property: 'og:image:type', content: type }
- if width.present?
  %meta{ property: 'og:image:width', content: width }
- if height.present?
  %meta{ property: 'og:image:height', content: height }
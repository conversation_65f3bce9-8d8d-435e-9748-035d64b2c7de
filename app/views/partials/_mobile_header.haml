.mobile-header.offcanvy-move
  %button.h-btn-menu.reset-button
    %span.i-menu
  %a.h-logo{ href: main_app.root_url, title: 'Home' }
    %img{ width: 100, height: 18, src: image_path('logos/logo-full.png'), onerror: "this.src='#{image_path('logos/logo-full.png')}'", alt: 'Avenida.com' }
  %button.h-btn.h-btn-cart{ type: 'button', 'data-template' => '#mobile-cart-info', title: t('partials.header.cart') }
    %span.h-btn-icon.i-carrito
    %span.h-btn-count{ 'data-count' => 0 } 0
  = content_for :before_js do
    = render partial: 'partials/mobile_header_notifications'
    = render partial: 'partials/mobile_cart_info'

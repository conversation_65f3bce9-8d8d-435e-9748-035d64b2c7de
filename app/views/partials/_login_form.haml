- title ||= t('.title')
- msg ||= nil
- with_guest ||= false
- cssClass = []
- cssClass << 'with-guest' if with_guest
- cssClass << 'with-msg' if msg.present?
- for_review ||= false
.login-form{ class: cssClass }
  .loader
  - if msg.present?
    %p.msg= msg
  - if flash[:alert]
    .flash_alert.error
      = flash[:alert]
  %div
    .login-form-data.medium-7.columns
      %h5= title
      = form_for @user_session, url: login_url(checkout: (with_guest ? true : nil), return_to: params[:return_to]) do |fl|
        .row
          .column{ class: with_guest && 'medium-6' }
            = fl.text_field :login, tabindex: 1, label: false, placeholder: t('.email_placeholder')
          .column{ class: with_guest && 'medium-6' }
            = fl.password_field :password, tabindex: 2, label: false, placeholder: t('.password_placeholder'), class: 'password'
          .column.medium-6
            %a.forgot-password{ href: new_password_reset_url, rel: 'nofollow' }=t('.forgot_password')
          .column.medium-6
            .remember_me
              %input{id: "user_session_remember_me", name: "user_session[remember_me]", type: "checkbox", value: "1"}
              %label= t('.remember_me')
          .medium-5.columns.actions
            %button.button.grey.wide{ type: 'submit' }= t('.sign_in')
    .clear

= content_for :title do
  Avenida.com | #{t('.title')}

= content_for :body_class, 'settings-page'

.large-12.columns.basic-form-page
  .settings-box
    = form_for @user, as: :user, url: password_reset_path, method: :put do |f|
      = render partial: 'settings/header',
                        locals: { section: t('.title'), description: t('.description') }
      .settings-divider
      .settings-section
        = render partial: 'settings/setting',
                          locals: { label: label_tag(t('.new_password')), input: f.password_field(:password) }
        = render partial: 'settings/setting',
                          locals: { label: label_tag(t('.confirm_password')), input: f.password_field(:password_confirmation) }
      .settings-divider
      .settings-section.last-section
        = submit_tag t('.update_password'), class: 'small button'

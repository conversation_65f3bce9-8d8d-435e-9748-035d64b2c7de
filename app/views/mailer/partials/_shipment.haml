%br/
- if shipment.has_bonification?
  %p= t('.shipping_bonification')
- else
  %p
    %strong #{t('.shipping_cost')}:
    #{number_to_currency(shipment.charged_amount, unit: current_currency_format)}

  - carrier_name, service_name = get_carrier_and_service_options(shipment)

  - if carrier_name.present?
    %p
      %strong #{t('.shipping_carrier')}:
      #{carrier_name}
  - if service_name.present?
    %p
      %strong #{t('.shipping_service')}:
      #{service_name}
      = '***' if speed_clarification

%p
  %strong #{t('.shipping_to')}:
  .destination-address{style: 'margin-left: 10px'}= render partial: 'partials/mailer/address', locals: { address: shipment.destination_address }
%br/

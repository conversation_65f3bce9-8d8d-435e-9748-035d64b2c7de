- is_admin ||= false
- hide_purchase_date = hide_purchase_date.present? ? hide_purchase_date : false
- variant = item.variant
- product = variant.product
- variant_url = item.suborder.store.hostname + variant.get_url
- properties = Mkp::Product::AVAILABLE_PROPERTIES

%tr.border-bottom{:style => ""}
  %td{width: '40%', :style => "text-align: center;"}
    %a{ href: variant_url }
      = image_tag variant.picture.url(:tm), size: '198x200' if variant.picture
  %td
    %h4= t('.summary').upcase
    
    %p
      = t('.description')
      %br
      %p{:style => "line-height: 19px;"}
        - if @current_store.name == 'bancomacro'
          %strong  
            %a{ href: variant_url }= variant.description.truncate(100)
        - else
          %strong
            %a{ href: variant_url }= product.title
          %br
          = variant.description.truncate(100)
    
    %p{:style => "line-height: 17px;"}
      
      - if @current_store.name == 'bna'
        ="Vendedor #{item.suborder.shop.name}"
      %br
      - unless hide_purchase_date
        = t('.date')
        %strong= item.created_at.strftime("%d/%m/%y - %H:%M:%S")
      
      - unless product.has_no_property?
        - variant.properties.keys.each do |property|
          %br
            =t('.' + property.to_s)
            - if property.to_sym == :color
              %strong= variant.color_name
            - else
              %strong= variant.properties[property]

      = t('.quantity')
      %strong= item.quantity
      - if item.points > 0
        = t('.points')
        %strong.currency= number_with_delimiter(item.points, :delimiter => '.')
      %br
        = t('.price')
        %strong.currency= number_to_currency(item.total_without_points, unit: current_currency_format)

      - payments = item.suborder.order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint'])
      - if payments.any?
        - installments = payments.first.installments || 1
        = t('.installments')
        %strong= installments
      %br
        = t('.order_id')
        %strong= item.suborder.public_id
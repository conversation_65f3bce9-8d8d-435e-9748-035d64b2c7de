- content_for(:title) { @shop.title }
- content_for(:css) do
  = stylesheet_link_tag 'widgets/shop_products'

.products-list
  - if @orientation == 'horizontal'
    = render partial: 'partials/v5/social/recommended_product',
             collection: @variants,
             as: :variant
  - elsif @orientation == 'vertical'
    = render partial: 'mkp/variants/list', locals: { variants: @variants, out_links: true }

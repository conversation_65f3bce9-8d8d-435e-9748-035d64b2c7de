= content_for :title do
  #{@user.full_name} | #{t('.password')}
= content_for :body_class, 'settings-page'

.large-4.columns
  = render partial: 'settings/menu', locals: { user: @user }
.large-8.columns
  .settings-box
    = form_for @user, url: settings_password_url, as: :user, method: :put do |f|
      = render partial: 'settings/header',
                        locals: { section: t('.password'), description: t('.change_password') }
      .settings-divider
      .settings-section
        = render partial: 'settings/setting',
                          locals: { label: f.label(:current_password, t('.current_password')), input: f.password_field(:current_password) }
        = render partial: 'settings/setting',
                          locals: { label: f.label(:password, t('.new_password')), input: f.password_field(:password) }
        = render partial: 'settings/setting',
                          locals: { label: f.label(:password_confirmation, t('.verify_password')), input: f.password_field(:password_confirmation) }
      .settings-divider
      .settings-section.last-section
        = f.submit t('.save_changes')
= content_for :title do
  = "#{@user.full_name} | #{t('.account')}"
= content_for :body_class, 'settings-page'
- networks_to_display = Network[@network].visible ?  Network.all_visible.map { |network| [ Network[network].title, network ] } : Network.all_active.map { |network| [ Network[network].title, network ] }
.large-4.columns
  = render partial: 'settings/menu', locals: { user: @user }
.large-8.columns
  .settings-box
    = form_for @user, url: settings_account_url, as: :user do |f|
      = render partial: 'settings/header',
                        locals: { section: t('.account'), description: t('.change_account') }
      .settings-divider
      .settings-section
        = render partial: 'settings/setting',
                          locals: { label: f.label(:email), input: f.email_field(:email), help: t('.email_display') }
        = render partial: 'settings/setting',
                          locals: { label: f.label(:network, t('.network')), input: f.select(:network, networks_to_display) }
      .settings-divider
      .settings-section.last-section
        = f.submit t('.save_changes')

- shipments = order.shipments
- payment = order.payment
.gp-simple-panel
  .date.large-1.columns
    span= order.created_at.strftime("%d-%m-%y")
  .general-data.large-11.columns
    .small-12.columns
      .row
        .order-description.row
          .title.large-12.columns
            .large-10.columns
              h3.ellipsis
                = order.title
            .total-price.large-2.columns
              h3
                  #{number_to_currency order.total, precision: 2}
          .payment.large-12.columns
            p
              span
                = t('.payment')
                | &nbsp;
              == payment_details(payment)
              | &nbsp;&nbsp;&nbsp;

              span.status class=order.payment.status
                = t(".statuses.payment.#{order.payment.status}")
            p
              = t('.shipping_address')
              = address_line(shipments.first.destination_address)
          .shipments.large-12.columns
            - shipments.each_with_index do |shipment, index|
              h3 = t('.shipment_title', shipment_number: index + 1, total_shipments: shipments.size, shipment_id: shipment.id)
              hr
              .shipment.large-12
                p
                  span
                    = t('.orders_title')
                    = shipment.suborders.map(&:public_id).join('/')
                  span.status class=shipment.status
                    = t(".statuses.shipment.#{shipment.status}")
              .order-items.large-12
                - shipment.items.each do |item|
                  .order-item.row
                    - variant = item.variant
                    .large-1.columns
                      = image_tag variant.picture.url(:st), size: '45x45' if variant.picture
                    .large-7.columns
                      h3 = variant.title
                      p
                        - variant.properties.each do |property|
                          span = t(".#{property.first}")
                          - if property.first == :color
                            - color = property.last
                            span = color[:name]
                            | &nbsp;
                            span.color-box style="background-color:#{color[:hex]};"
                          - else
                            span = property.last
                          | &nbsp;&nbsp;
                        span = t('.brand', brand: variant.product.manufacturer.name)
                    .large-4.columns
                      p.quantity
                        | x&nbsp;
                        span
                          b = item.quantity
              - if shipment.has_labels?
                - label = shipment.label
                .gateway.large-12
                  p
                    span = t('.tracking')
                    span
                      b = label.courier
                      | &nbsp;&nbsp;
                    - if label.tracking_number.present?
                      span = label.tracking_number
                      | &nbsp;&nbsp;
                      - if label.tracking_url.present?
                        = link_to t('.tracking_url'), label.tracking_url, target: "_blank"

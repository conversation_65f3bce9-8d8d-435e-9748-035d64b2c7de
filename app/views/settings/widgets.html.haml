= content_for :title do
  #{current_user.full_name} | #{t('.title')}
= content_for :body_class, 'settings-page'

.large-4.columns
  = render partial: 'settings/menu', locals: { user: current_user }
.user-info.large-8.columns
  .settings-box
    = render partial: 'settings/header',
             locals: { section: t('.social_title'), description: t('.social_description') }
    .settings-divider
    .settings-section{ class: @shop.blank? && 'last-section' }
      .row.widgets-widget
        .small-4.columns
          .widgets-widget-preview
            = render partial: 'widget_social_code', locals: { user: @user }
          = render partial: 'widgets_widget_code_selector'
        .small-8.columns
          %textarea.widgets-widget-code.js-select-all-on-click{ readonly: 'readonly', rows: 6 }
            = render partial: 'widget_social_code', locals: { user: @user }

    - if @shop.present?
      .settings-divider
      = render partial: 'settings/header',
               locals: { section: t('.shop_title'), description: t('.shop_description') }
      .settings-divider
      .settings-section
        .row.widgets-widget
          .small-4.columns
            .widgets-widget-preview
              = render partial: 'widget_shop_code', locals: { shop: @shop }
            = render partial: 'widgets_widget_code_selector'
          .small-8.columns
            %textarea.widgets-widget-code.js-select-all-on-click{ readonly: 'readonly', rows: 6 }
              = render partial: 'widget_shop_code', locals: { shop: @shop }

      .settings-divider
      = render partial: 'settings/header',
               locals: { section: t('.shop_products_title'), description: t('.shop_products_description') }
      .settings-divider
      .settings-section.last-section
        .widgets-products-widget
          .row
            .small-4.columns
              %label{ for: 'products-widget-arrangement' } #{t('.products_widget.arrangement')}:
              %select#products-widget-orientation{ name: 'orientation' }
                - t('.products_widget.arrangement_options').each do |value, name|
                  %option{ value: value }= name
              %label{ for: 'products-widget-type' } #{t('.products_widget.type')}:
              %select#products-widget-show{ name: 'show' }
                - t('.products_widget.type_options').each do |value, name|
                  %option{ value: value }= name
              %label{ for: 'products-widget-size' } #{t('.products_widget.size')}:
              %select#products-widget-size{ name: 'size' }
                - t('.products_widget.size_options').each do |value, name|
                  %option{ value: value }= name
            .small-8.columns
              %textarea.widgets-widget-code.js-select-all-on-click{ readonly: 'readonly', rows: 6 }
                = render partial: 'widget_products_code', locals: { shop: @shop }
          .row
            .small-12.columns
              .widgets-widget-preview
                = render partial: 'widget_products_code', locals: { shop: @shop }

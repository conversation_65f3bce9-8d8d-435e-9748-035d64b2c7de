- if connected
  = form_tag authentication_path(provider),
             method: :delete, id: "destroy_#{provider}_auth" do
    %button.button{ type: 'submit' }
      = t('.revoke_access')
- else
  = form_tag authentications_path, id: "new_#{provider}_auth" do
    = hidden_field_tag 'authentication[type]', Authentication.of_provider(provider)
    %button.button{ type: 'submit' }
      = expired ? t('.renew_access') : t('.grant_access')
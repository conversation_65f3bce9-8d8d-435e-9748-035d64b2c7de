= content_for :title do
  = "#{@user.full_name} | #{t('.my_orders', orders_counter: @orders.count)}"
= content_for :body_class, 'settings-page'

.large-3.columns
  = render partial: 'settings/menu', locals: { user: @user }
.large-9.columns
  .settings-box
    = render partial: 'settings/header',
                      locals: { section: t('.my_orders', orders_counter: @orders.count), description: t('.view_recent_orders') }
    .settings-divider
    .settings-section.orders
      - if @orders.present?
        = render partial: 'settings/order', collection: @orders
      - else
        .no-orders= t('.no_orders_found')

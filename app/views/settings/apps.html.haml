= content_for :title do
  #{@user.full_name} | #{t('.applications')}
= content_for :body_class, 'settings-page'

- connected_to_facebook = @facebook_auth.present? && !@facebook_auth.expired?
- expired_facebook = @facebook_auth.present? && @facebook_auth.expired?
- connected_to_twitter = @twitter_auth.present? && !@twitter_auth.expired?
- expired_twitter = @twitter_auth.present? && @twitter_auth.expired?

.large-4.columns
  = render partial: 'settings/menu', locals: { user: @user }
.large-8.columns
  .settings-box
    = render partial: 'settings/header',
                      locals: { section: t('.applications'), description: t('.apps_access') }
    .settings-divider
    .settings-section.apps
      .app
        .app-image
          = image_tag 'social/account_settings/fb.png'
        .app-info
          %p
            %span= t('.facebook')
            = t('.by')
            = t('.facebook_developer')
          %p= t('.facebook_description')
          - if connected_to_facebook
            %p.date-approved
              = t('.approved')
              = l(@facebook_auth.updated_at, format: :long)
          - if expired_facebook
            %p.date-expired
              = t('.expired')
              = l(@facebook_auth.updated_at, format: :long)
        .app-access
          = render partial: 'app_permission_button',
                   locals: { provider: 'facebook', connected: connected_to_facebook, expired: expired_facebook }
    .settings-divider
    .settings-section.apps
      .app
        .app-image
          = image_tag 'social/account_settings/tw.png'
        .app-info
          %p
            %span= t('.twitter')
            = t('.by')
            = t('.twitter_developer')
          %p= t('.twitter_description')
          - if connected_to_twitter
            %p.date-approved
              = t('.approved')
              = l(@twitter_auth.updated_at, format: :long)
        .app-access
          = render partial: 'app_permission_button',
                   locals: { provider: 'twitter', connected: connected_to_twitter, expired: expired_twitter }
= content_for :title do
  #{current_user.full_name} | #{t('.admins')}
= content_for :body_class, 'settings-page'

.large-4.columns
  = render partial: 'settings/menu', locals: { user: current_user }
.large-8.columns
  .settings-box
    - account_type = current_user.is_a?(Brand) ? 'brand' : 'non_profit'
    = render partial: 'settings/header',
                      locals: { section: t('.admins'), description: t('.admins_description', account_type: t(".#{account_type}")) }
    .settings-divider
    - if @admins.present?
      .admins
        - @admins.each do |admin|
          .row.settings-section.admin
            .small-1.columns.small-offset-1
              .admin
                = render partial: 'social/partials/user_avatar', locals: { user: admin }
            .small-4.columns
              .login-and-email
                %div= admin.login
                %p= admin.email
            .small-3.columns
              = form_tag settings_admin_path(admin) do |f|
                = hidden_field_tag '_method', 'put'
                - if current_user.owns_a_shop? && current_user.owned_shop.can_be_managed_by?(admin)
                  - selected = 'shop'
                - if current_user.owns_a_brand? && current_user.owned_brand.can_be_managed_by?(admin)
                  - selected = 'community'
                - if current_user.owns_a_shop? && current_user.owned_shop.can_be_managed_by?(admin) && current_user.owns_a_brand? && current_user.owned_brand.can_be_managed_by?(admin)
                  - selected = 'all'
                = select_tag :role, options_for_select(@available_roles, selected), id: nil, class: 'role-selector', disabled: disable_administrators_management
            .small-3.columns
              = form_tag remove_admin_url(id: admin.id) do |f|
                = hidden_field_tag '_method', 'delete'
                %button.button.remove-admin-button{ type: 'submit', disabled: disable_administrators_management }
                  = t('.remove')

    - unless disable_administrators_management
      .settings-divider
      .settings-section.admins-form
        .row
          .small-12.medium-6.columns
            = form_tag add_admin_url, id: 'add-administrator' do
              = hidden_field_tag('id',
                                 nil,
                                 id: 'users',
                                 'data-users-url' => api_social_users_path)

              %div
                %button#save.button.small.red{ type: 'submit', disabled: true}= t('.add_admin')

.user-box.cf
  .user-avatar
    = render partial: 'social/partials/user_avatar', locals: { user: user, type: 'settings-avatar' }
  .user-name
    %h5= user.full_name
    = link_to t('.view_my_profile'), profiles_path(user.login)
.menu-box
  %a{ href: settings_account_url,
      class: 'first_link' + (current_page?(settings_account_path) ?  ' active' : '') }
    = t('.account')
  .menu-divider
  %a{ href: settings_password_url,
      class: current_page?(settings_password_path) && 'active' }
    = t('.password')
  .menu-divider
  %a{ href: edit_social_profile_url,
      class: current_page?(edit_social_profile_path) && 'active' }
    = t('.profile')
  .menu-divider
  %a{ href: settings_widgets_path,
      class: current_page?(settings_widgets_path) && 'active' }
    = t('.widgets')
  .menu-divider
  %a{ href: settings_orders_url,
      class: current_page?(settings_orders_path) && 'active' }
    = t('.my_orders')
  .menu-divider
  %a{ href: settings_apps_url,
      class: 'last_link' + (current_page?(settings_apps_path) ?  ' active' : '') }
    = t('.apps')
  .menu-divider
  - if show_administrators_link
    %a{ href: settings_admins_url,
        class: 'last_link' + (current_page?(settings_admins_path) ?  ' active' : '') }
      = t('.admins')
.good-people
  %p © #{Date.today.year()} Avenida.com

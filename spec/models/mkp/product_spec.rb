require 'rails_helper'

RSpec.describe Mkp::Product, type: :model do
  let(:shop) { create(:shop) }
  let(:category) { create(:category) }
  let(:manufacturer) { create(:manufacturer) }

  describe '#set_third_party_code_required_based_on_transaction_type' do
    context 'when creating a new product' do
      context 'with reservable transaction type' do
        it 'sets third_party_code_required to true' do
          product = build(:product, 
            shop: shop,
            category: category,
            manufacturer: manufacturer,
            transaction_type: :reservable
          )
          
          expect { product.save! }.to change { product.third_party_code_required }.to(true)
        end
      end

      context 'with purchasable transaction type' do
        it 'sets third_party_code_required to false' do
          product = build(:product, 
            shop: shop,
            category: category,
            manufacturer: manufacturer,
            transaction_type: :purchasable
          )
          
          expect { product.save! }.to change { product.third_party_code_required }.to(false)
        end
      end

      context 'with voucher transaction type' do
        it 'sets third_party_code_required to false' do
          product = build(:product, 
            shop: shop,
            category: category,
            manufacturer: manufacturer,
            transaction_type: :voucher
          )
          
          expect { product.save! }.to change { product.third_party_code_required }.to(false)
        end
      end

      context 'with points transaction type' do
        it 'sets third_party_code_required to false' do
          product = build(:product, 
            shop: shop,
            category: category,
            manufacturer: manufacturer,
            transaction_type: :points
          )
          
          expect { product.save! }.to change { product.third_party_code_required }.to(false)
        end
      end

      context 'with other transaction type' do
        it 'sets third_party_code_required to false' do
          product = build(:product, 
            shop: shop,
            category: category,
            manufacturer: manufacturer,
            transaction_type: :other
          )
          
          expect { product.save! }.to change { product.third_party_code_required }.to(false)
        end
      end
    end

    context 'when updating an existing product' do
      let!(:product) { create(:product, 
        shop: shop,
        category: category,
        manufacturer: manufacturer,
        transaction_type: :purchasable,
        third_party_code_required: false
      ) }

      context 'changing from purchasable to reservable' do
        it 'sets third_party_code_required to true' do
          expect { 
            product.update!(transaction_type: :reservable) 
          }.to change { product.third_party_code_required }.from(false).to(true)
        end
      end

      context 'changing from reservable to purchasable' do
        before do
          product.update!(transaction_type: :reservable, third_party_code_required: true)
        end

        it 'sets third_party_code_required to false' do
          expect { 
            product.update!(transaction_type: :purchasable) 
          }.to change { product.third_party_code_required }.from(true).to(false)
        end
      end

      context 'when transaction_type does not change' do
        it 'does not modify third_party_code_required if explicitly set' do
          product.update!(third_party_code_required: true)
          
          expect { 
            product.update!(title: 'New Title') 
          }.not_to change { product.third_party_code_required }
        end
      end
    end

    context 'when third_party_code_required is explicitly set' do
      it 'respects the explicit value on creation' do
        product = build(:product, 
          shop: shop,
          category: category,
          manufacturer: manufacturer,
          transaction_type: :reservable,
          third_party_code_required: false
        )
        
        product.save!
        expect(product.third_party_code_required).to be(true) # callback overrides because transaction_type changed
      end
    end
  end

  describe 'transaction type methods' do
    let(:product) { create(:product, shop: shop, category: category, manufacturer: manufacturer) }

    context 'when transaction_type is reservable' do
      before { product.update!(transaction_type: :reservable) }

      it 'returns true for reservable?' do
        expect(product.reservable?).to be(true)
      end

      it 'returns false for points?' do
        expect(product.points?).to be(false)
      end
    end

    context 'when transaction_type is points' do
      before { product.update!(transaction_type: :points) }

      it 'returns true for points?' do
        expect(product.points?).to be(true)
      end

      it 'returns false for reservable?' do
        expect(product.reservable?).to be(false)
      end
    end
  end
end

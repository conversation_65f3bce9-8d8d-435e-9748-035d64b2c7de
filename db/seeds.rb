require 'factory_girl'
include ActionDispatch::TestProcess

require File.expand_path('../seeds/development.rb', __FILE__)

# Currencies identifiers must comply ISO-4217: http://en.wikipedia.org/wiki/ISO_4217
Mkp::Currency.create!({
  name: 'Peso Argentino',
  identifier: 'ARS',
  symbol: '$',
  network: 'AR'
})

Mkp::Currency.create!({
  name: 'Dollar',
  identifier: 'USD',
  symbol: '$',
  network: 'US'
})

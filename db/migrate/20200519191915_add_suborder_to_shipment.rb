class AddSuborderToShipment < ActiveRecord::Migration
  def change
    add_reference :mkp_shipments, :suborder

    Mkp::Shipment.all.each do |shipment|
      sql = "SELECT order_item_id from mkp_shipment_items where shipment_id = #{shipment.id}"
      result = ActiveRecord::Base.connection.execute(sql)
      suborder_ids = Mkp::OrderItem.where(id: result.to_a.flatten.uniq).pluck :suborder_id
      if suborder_ids.flatten.uniq.size > 1
        Rails.logger.info("ERROR el shipment #{shipment.id} tiene mas de una suborder")
        shipment.update_attributes(suborder_id: suborder_ids.flatten.uniq.last)
      else
        shipment.update_attributes(suborder_id: suborder_ids.flatten.uniq.last)
      end
    end
  end
end

class CreateMkpGatewayErrors < ActiveRecord::Migration
  def up
    create_table :mkp_gateway_errors do |t|
      t.references :store
      t.string :gateway
      t.integer :external_id
      t.string :description_short
      t.string :description_large
      t.string :message

      t.timestamps null: false
    end
    add_index :mkp_gateway_errors, [:store_id, :gateway, :external_id], :unique => true, :name => 'mkp_gateway_errors_ix'
  end

  def down
    remove_index :mkp_gateway_errors, :name => 'mkp_gateway_errors_ix'
    drop_table :mkp_gateway_errors
  end
end

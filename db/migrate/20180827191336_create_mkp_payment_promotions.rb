class CreateMkpPaymentPromotions < ActiveRecord::Migration
  def change
    create_table :mkp_payment_promotions do |t|
      t.datetime    :start
      t.datetime    :expire
      t.boolean     :active
      t.decimal     :coefficient, precision: 8, scale: 6, default: 0.0
      t.decimal     :tna, precision: 4, scale: 2, default: 0.0
      t.decimal     :cft, precision: 4, scale: 2, default: 0.0
      t.decimal     :tea, precision: 4, scale: 2, default: 0.0
      t.integer     :brand
      t.integer     :installment
      t.string      :message
      t.text        :legal
      t.references  :bank, index: true
      t.timestamps
    end
  end
end

class CreateSettingCoupons < ActiveRecord::Migration
  def change
    create_table :setting_coupons do |t|
      t.integer :minimum_value
      t.integer :percent
      t.text :description
      t.datetime :starts_at
      t.datetime :expires_at
      t.integer :total_available
      t.text :restrictions
      t.text :policy
      t.boolean :apply_on_sale
      t.integer :discount_limit
      t.references :voucher, index: true, foreign_key: true

      t.timestamps null: false
    end
  end
end

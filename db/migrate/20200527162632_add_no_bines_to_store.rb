class AddNoBinesToStore < ActiveRecord::Migration
  def change
    add_column :mkp_stores, :use_bines, :boolean

    Mkp::Store.all.each do |store|
      if store.retrieve_gateways_array.include? 'decidir_no_bines'
        gateways = store.retrieve_gateways_array.dup
        gateways.delete 'decidir_no_bines'
        gateways << 'decidir'
        store.update_attributes(gateway: gateways.join(','), use_bines: false)
      else
        store.update_attributes(use_bines: true)
      end
    end
  end
end

class AddProductionToMkpStore < ActiveRecord::Migration
  def change
    add_column :mkp_stores, :production, :boolean, default: true

    ActiveRecord::Base.transaction do
      jubilostaging = Mkp::Store.find_or_create_by(
            name: "jubilostaging", 
            token: "eKf-4okhniDJREIeGR7VZQ", 
            title: "Jubilo staging"
      )
      jubilostaging.update(production: false)
    end
  end
end

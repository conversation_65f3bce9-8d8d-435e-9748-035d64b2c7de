class CreatePayments < ActiveRecord::Migration
  def change
    create_table :payments do |t|
      t.integer :status
      t.decimal :amount
      t.datetime :collected_at
      t.string :gateway
      t.text :gateway_data
      t.string :gateway_object_id
      t.string :payment_method
      t.references :subscription

      t.timestamps null: false
    end
    add_index :payments, :subscription_id
  end
end

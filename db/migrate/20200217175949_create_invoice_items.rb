class CreateInvoiceItems < ActiveRecord::Migration
  def change
    create_table :invoice_items do |t|
      t.references :suborder, index: true
      t.decimal :amount
      t.integer :points
      t.datetime :fulfilled_at

      t.timestamps null: false
    end

    Mkp::Suborder.where("created_at >= '#{(Time.zone.now - 60.days).strftime("%Y-%m-%d")}'").each do |suborder|
      fulfilled_at =
        if suborder.fulfilled?
          shipments_delivered = suborder.shipments.select {|each| each.delivered?}
          if shipments_delivered.any?
            shipments_delivered.first.updated_at
          else
            if suborder.shipments.any?
              suborder.shipments.last.updated_at
            else
              suborder.updated_at
            end
          end
        else
          nil
        end
      InvoiceItem.create(suborder: suborder, created_at: suborder.created_at, amount: suborder.total, points: suborder.total_points, fulfilled_at: fulfilled_at)
    end
  end
end

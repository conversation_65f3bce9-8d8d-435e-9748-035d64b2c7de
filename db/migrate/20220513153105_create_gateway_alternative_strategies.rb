class CreateGatewayAlternativeStrategies < ActiveRecord::Migration
  #def change
  #  add_column :mkp_stores, :use_alternative_payment, :boolean
  #end
  def change
    create_table :gateway_alternative_strategies do |t|
      t.references :store
      t.integer :strategy
      t.string :gateway

      t.timestamps null: false
    end

    add_index :gateway_alternative_strategies, :store_id
    add_foreign_key :gateway_alternative_strategies, :mkp_stores, column: "store_id"
  end
end
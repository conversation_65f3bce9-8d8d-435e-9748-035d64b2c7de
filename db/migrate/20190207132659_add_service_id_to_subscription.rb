class AddServiceIdToSubscription < ActiveRecord::Migration

  class Service < ActiveRecord::Base
    has_many :subscriptions
    has_and_belongs_to_many :subscriptions
  end

  class Subscription < ActiveRecord::Base
    belongs_to :service
    has_and_belongs_to_many :services
  end

  def up
    add_column :subscriptions, :service_id, :integer

    subscriptions = {}
    Subscription.all.each do |subscription|
      subscriptions[subscription.id] = {:service_id => subscription&.services&.first&.id}
    end
    Subscription.update(subscriptions.keys, subscriptions.values)

    drop_table :services_subscriptions
  end

  def down
     remove_column :subscriptions, :service_id, :integer
  end

end

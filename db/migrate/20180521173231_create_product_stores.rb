class CreateProductStores < ActiveRecord::Migration
  def change
    create_table :mkp_product_stores do |t|
      t.references  :product
      t.references  :store
      t.integer     :min_points
      t.integer     :max_points
      t.integer     :points
      t.timestamps
    end
    add_index :mkp_product_stores, :product_id
    add_index :mkp_product_stores, :store_id
  end

  def down
    drop_table :mkp_product_stores
  end

end

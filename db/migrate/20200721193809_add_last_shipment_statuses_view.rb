class AddLastShipmentStatusesView < ActiveRecord::Migration
  def change
    execute <<-SQL
      CREATE OR REPLACE VIEW mkp_last_shipment_statuses AS
        select 
          id, suborder_id, status 
        from 
          mkp_shipments 
        where 
          (suborder_id, created_at) in (select suborder_id id, max(created_at) max_date from mkp_shipments group by suborder_id);
    SQL
  end
end

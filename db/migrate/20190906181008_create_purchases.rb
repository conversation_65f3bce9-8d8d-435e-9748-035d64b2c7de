class CreatePurchases < ActiveRecord::Migration
  def change
    create_table :purchases do |t|
      t.references  :store, index: true
      t.references  :payment, index: true
      t.integer     :points
      t.integer     :miles
      t.string      :id_cobis
      t.string      :document_type
      t.string      :document_number
      t.string      :email
      t.string      :status

      t.timestamps null: false
    end
  end
end

class AddShipmentStatusToInvoiceItems < ActiveRecord::Migration
  def change
    add_column :invoice_items, :shipment_status, :string

    InvoiceItem.where(suborder_type: 'Mkp::Suborder').each do |invoice_item|
        last_shipment_status = Mkp::LastShipmentStatus.where(suborder_id: invoice_item.suborder_id).last
        invoice_item.update_attributes(shipment_status: last_shipment_status.status) if last_shipment_status
    end
    InvoiceItem.where(suborder_type: 'Purchase').update_all(shipment_status: 'delivered')
    InvoiceItem.where('shipment_status is null').update_all(shipment_status: 'delivered')
  end
end

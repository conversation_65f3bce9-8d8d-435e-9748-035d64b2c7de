class ChangeVisaPointsEquivalencesPrecision < ActiveRecord::Migration
  def change
  	change_column :mkp_stores, :visa_puntos_equivalence, :decimal, :precision => 15, :scale => 6
  	change_column :mkp_stores, :visa_puntos_sube_equivalence, :decimal, :precision => 15, :scale => 6
  	change_column :mkp_shops, :visa_puntos_equivalence, :decimal, :precision => 15, :scale => 6
  	change_column :mkp_categories, :visa_puntos_equivalence, :decimal, :precision => 15, :scale => 6
  	change_column :mkp_product_stores, :visa_puntos_equivalence, :decimal, :precision => 15, :scale => 6
  end
end

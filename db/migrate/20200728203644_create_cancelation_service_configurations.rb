class CreateCancelationServiceConfigurations < ActiveRecord::Migration
  def change
    create_table :cancelation_service_configurations do |t|
      t.references :store
      t.boolean :generate_refund_shipments
      t.boolean :generate_coupons
      t.boolean :generate_credit_notes
      t.boolean :cancel_payments

      t.timestamps null: false
    end

    Mkp::Store.all.each {|store| CancelationServiceConfiguration.create(store: store, generate_refund_shipments: false, generate_coupons: true, generate_credit_notes: true)}
    Mkp::Store.where(name: 'bancomacro').each {|store| (CancelationServiceConfiguration.where(store: store)).update_all(generate_refund_shipments: true, generate_coupons: false, generate_credit_notes: false)}
  end
end

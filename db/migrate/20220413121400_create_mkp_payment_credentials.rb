class CreateMkpPaymentCredentials < ActiveRecord::Migration
  def change
    create_table :mkp_payment_credentials do |t|
      t.integer :store_id
      t.integer :shop_id
      t.string :name
      t.string :value

      t.timestamps null: false
    end

    add_index :mkp_payment_credentials, :store_id
    add_foreign_key :mkp_payment_credentials, :mkp_stores, column: "store_id"
    add_index :mkp_payment_credentials, :shop_id
    add_foreign_key :mkp_payment_credentials, :mkp_shops, column: "shop_id"
  end
end
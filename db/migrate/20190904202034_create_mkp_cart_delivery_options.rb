class CreateMkpCartDeliveryOptions < ActiveRecord::Migration
  def change
    create_table :mkp_cart_delivery_options do |t|
      t.decimal :charge
      t.string :title
      t.string :service_level
      t.string :carrier
      t.string :service_level_real
      t.string :external_shipment_id
      t.integer :shop_id
      t.boolean :selected, default: false
      t.references :cart, index: true

      t.timestamps null: false
    end
  end
end

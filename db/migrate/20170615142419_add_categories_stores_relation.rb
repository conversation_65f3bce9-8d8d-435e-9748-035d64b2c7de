class AddCategoriesStoresRelation < ActiveRecord::Migration
  def up
    create_table :mkp_category_stores do |t|
      t.integer :category_id, :null => false
      t.integer :store_id, :null => false

      t.timestamps

    end
    add_index :mkp_category_stores, [:category_id, :store_id], :unique => true, :name => 'mkp_category_stores_ix'
  end

  def down
    remove_index :mkp_category_stores, :name => 'mkp_category_stores_ix'
    drop_table :mkp_category_stores
  end
end

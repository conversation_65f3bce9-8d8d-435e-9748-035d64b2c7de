class CreateMkpPackages < ActiveRecord::Migration
  def change
    create_table :mkp_packages do |t|
      t.decimal :width, precision: 8, scale: 2, default: 0.0
      t.decimal :height, precision: 8, scale: 2, default: 0.0
      t.decimal :length, precision: 8, scale: 2, default: 0.0
      t.decimal :weight, precision: 8, scale: 2, default: 0.0
      t.string  :mass_unit
      t.string  :length_unit
      t.references :product

      t.timestamps
    end
  end

end

class CreateInvoiceReports < ActiveRecord::Migration
  def change
    create_table :invoice_reports do |t|
      t.string :name
      t.string :description, :length => 150
      t.datetime :created_from
      t.datetime :created_to
      t.datetime :fulfilled_from
      t.datetime :fulfilled_to
      t.string :shipment_statuses
      t.references :store
      t.references :shop
      t.string :search_query

      t.timestamps null: false
    end
  end
end

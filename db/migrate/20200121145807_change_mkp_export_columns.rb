class ChangeMkpExportColumns < ActiveRecord::Migration
  def up
    remove_column :mkp_exports, :type
  	add_reference :mkp_exports, :store, index: true
  	add_column :mkp_exports, :export_type, :integer
  	add_column :mkp_exports, :title, :string
  end

  def down
		remove_column :mkp_exports, :title
  	remove_column :mkp_exports, :export_type
		remove_index :mkp_exports, :store_id
		remove_column :mkp_exports, :store_id
    add_column :mkp_exports, :type, :string
  end
end

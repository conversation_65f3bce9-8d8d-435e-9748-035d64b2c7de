class CreateMkpCreditNotes < ActiveRecord::Migration
  def change
    create_table :mkp_credit_notes do |t|
      t.string      :gateway
      t.integer     :gateway_object_id
      t.text        :gateway_data
      t.references  :order
      t.references  :suborder
      t.references  :invoice

      t.timestamps
    end
    add_index :mkp_credit_notes, :order_id
    add_index :mkp_credit_notes, :invoice_id
  end
end

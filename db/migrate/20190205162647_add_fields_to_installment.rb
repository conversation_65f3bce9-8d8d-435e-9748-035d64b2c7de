class AddFieldsToInstallment < ActiveRecord::Migration
  def change
    add_column :installments, :cft, :decimal, precision: 8, scale: 2, default: 0.0
    add_column :installments, :tea, :decimal, precision: 8, scale: 2, default: 0.0
    add_column :installments, :coef, :decimal, precision: 8, scale: 5, default: 0.0
    add_column :installments, :number, :integer
    add_column :installments, :active, :boolean, default: false
    add_reference :installments, :bin, index: true

    Bin.find_each do |bin|
      bin.installments_availables.blank? && next
      installments = (1..12).to_a | [15,18,24,36,50]
      attributes = installments.map do |n|
        { number: n }.tap do |attr|
          installments_availables = bin.installments_availables.map(&:to_i)
          attr.merge!(active: true) if installments_availables.include?(n)
        end
      end
      bin.installments.build(attributes)
      bin.save
    end

    remove_column :bines, :installments_availables
  end
end

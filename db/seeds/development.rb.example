module Seeds
  module Development

    # Rake::Task['io:migrate'].invoke

    user = FactoryGirl.create(:user, {
      login: 'goodpeople',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: '<PERSON><PERSON><PERSON><PERSON>', last_name: '<PERSON>'
      })
    })

    5.times { FactoryGirl.create(:brand) }

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    FactoryGirl.create(:event, {
      user_id: user.id,
      title: 'Longboard Day',
      sport_ids: [17]
    })

    FactoryGirl.create(:spot, {
      title: '<PERSON> Rosedal',
      user_id: user.id,
      sport_ids: [16, 17, 18]
    })

    user = FactoryGirl.create(:user, {
      login: 'tout',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: '<PERSON><PERSON>', last_name: '<PERSON><PERSON>'
      }),
      albums: FactoryGirl.create_list(:album, 2, pictures: FactoryGirl.create_list(:album_picture, 2))
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'josethernandezc',
      email:'<EMAIL>',
      password: 'cualquiervaina',
      password_confirmation: 'cualquiervaina',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Jose', last_name: 'Hernandez'
      }),
      albums: FactoryGirl.create_list(:album, 1, pictures: FactoryGirl.create_list(:real_album_picture, 1))
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'ernesto',
      email:'<EMAIL>',
      password: 'ernesto',
      password_confirmation: 'ernesto',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Ernesto', last_name: 'Miguez'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'santiago',
      email:'<EMAIL>',
      password: 'gpdev2013',
      password_confirmation: 'gpdev2013',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Santiago', last_name: 'Mamone'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'guly',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Hernan', last_name: 'Guggisberg'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'maxi',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Maximiliano', last_name: 'Gonzalez'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'cavi21',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 3,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Agustin', last_name: 'Cavilliotti'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    user = FactoryGirl.create(:user, {
      login: 'melbs',
      email:'<EMAIL>',
      password: 'nadanada',
      password_confirmation: 'nadanada',
      roles_mask: 1,
      profile: FactoryGirl.build(:profile, {
        first_name: 'Mike', last_name: 'Elberts'
      })
    })

    FactoryGirl.create(:whatsup, {
      author_id: user.id,
      target_id: user.id,
      target_type: 'User'
    })

    address = FactoryGirl.create(:address)
    FactoryGirl.create(:order, shop: Mkp::Shop.first, address: address)

    FactoryGirl.create(:coupon, shop: Mkp::Shop.first)
    FactoryGirl.create(:coupon_percent, shop: Mkp::Shop.first)
    question = FactoryGirl.create(:question,
                                  product: Mkp::Shop.first.products.first,
                                  user: User.last, read: 1)

    FactoryGirl.create(:answer, question: question, user: User.first)

    question = FactoryGirl.create(:question,
                                  product: Mkp::Shop.first.products.first,
                                  user: User.last(2).first, read: 0)

    question = FactoryGirl.create(:question,
                                  product: Mkp::Shop.first.products.first,
                                  user: User.first, read: 0)


    x
  end
end

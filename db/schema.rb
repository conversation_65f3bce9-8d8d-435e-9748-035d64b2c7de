# encoding: UTF-8
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 20220923112803) do

  create_table "TABLE 146", id: false, force: :cascade do |t|
    t.string "COL 1",  limit: 8
    t.string "COL 2",  limit: 18
    t.string "COL 3",  limit: 18
    t.string "COL 4",  limit: 8
    t.string "COL 5",  limit: 8
    t.string "COL 6",  limit: 6
    t.string "COL 7",  limit: 10
    t.string "COL 8",  limit: 10
    t.string "COL 9",  limit: 5
    t.string "COL 10", limit: 7
    t.string "COL 11", limit: 130
    t.string "COL 12", limit: 9
  end

  create_table "aerolineas_argentinas_credentials", force: :cascade do |t|
    t.integer  "store_id",          limit: 4
    t.string   "user",              limit: 255
    t.string   "password",          limit: 255
    t.string   "partner_code",      limit: 255
    t.string   "partner_id",        limit: 255
    t.string   "partner_nbr",       limit: 255
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
    t.float    "points_equivalent", limit: 24
  end

  add_index "aerolineas_argentinas_credentials", ["store_id"], name: "index_aerolineas_argentinas_credentials_on_store_id", using: :btree

  create_table "aggregated_profiles", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.string   "type",       limit: 255
    t.integer  "user_id",    limit: 4
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "albums", force: :cascade do |t|
    t.string   "title",       limit: 255
    t.text     "description", limit: 65535
    t.integer  "author_id",   limit: 4
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "async_request_jobs", force: :cascade do |t|
    t.string   "worker",      limit: 255
    t.integer  "status",      limit: 4
    t.integer  "status_code", limit: 4
    t.text     "response",    limit: 65535
    t.string   "uid",         limit: 255
    t.text     "params",      limit: 65535
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  add_index "async_request_jobs", ["status"], name: "index_async_request_jobs_on_status", using: :btree
  add_index "async_request_jobs", ["uid"], name: "index_async_request_jobs_on_uid", unique: true, using: :btree

  create_table "attempted_answers", force: :cascade do |t|
    t.string   "question_type",    limit: 25
    t.string   "doc_number",       limit: 25
    t.string   "gender",           limit: 1
    t.integer  "question_id",      limit: 4
    t.string   "question",         limit: 255
    t.string   "answer",           limit: 255
    t.string   "ip",               limit: 46
    t.string   "purchase_id",      limit: 255
    t.boolean  "correct"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.datetime "deleted_at"
    t.integer  "store_id",         limit: 4
    t.integer  "pioneer_admin_id", limit: 4
    t.string   "type",             limit: 255
  end

  add_index "attempted_answers", ["deleted_at"], name: "index_attempted_answers_on_deleted_at", using: :btree
  add_index "attempted_answers", ["pioneer_admin_id"], name: "index_attempted_answers_on_pioneer_admin_id", using: :btree
  add_index "attempted_answers", ["purchase_id"], name: "index_attempted_answers_on_purchase_id", using: :btree
  add_index "attempted_answers", ["store_id"], name: "index_attempted_answers_on_store_id", using: :btree

  create_table "authentications", force: :cascade do |t|
    t.integer  "user_id",          limit: 4
    t.string   "type",             limit: 255
    t.string   "uid",              limit: 255
    t.string   "oauth_token",      limit: 255
    t.string   "oauth_secret",     limit: 255
    t.string   "avatar_url",       limit: 255
    t.datetime "oauth_expires_at"
    t.boolean  "can_write",                    default: false
    t.datetime "created_at",                                   null: false
    t.datetime "updated_at",                                   null: false
  end

  add_index "authentications", ["type", "uid"], name: "index_authentications_on_type_and_uid", using: :btree
  add_index "authentications", ["type", "user_id"], name: "index_authentications_on_type_and_user_id", using: :btree
  add_index "authentications", ["user_id"], name: "index_authentications_on_user_id", using: :btree

  create_table "banners", force: :cascade do |t|
    t.string   "title",                      limit: 255
    t.string   "type",                       limit: 255
    t.integer  "columns",                    limit: 4
    t.string   "desktop_image_file_name",    limit: 255
    t.string   "desktop_image_content_type", limit: 255
    t.integer  "desktop_image_file_size",    limit: 4
    t.datetime "desktop_image_updated_at"
    t.string   "token",                      limit: 255
    t.string   "link",                       limit: 255
    t.integer  "brand_id",                   limit: 4
    t.string   "network",                    limit: 255
    t.boolean  "draft"
    t.date     "starts_on"
    t.date     "ends_on"
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
    t.integer  "shop_id",                    limit: 4
    t.text     "content",                    limit: 65535
    t.string   "template",                   limit: 255
    t.string   "mobile_image_file_name",     limit: 255
    t.string   "mobile_image_content_type",  limit: 255
    t.integer  "mobile_image_file_size",     limit: 4
    t.datetime "mobile_image_updated_at"
  end

  create_table "bines", force: :cascade do |t|
    t.integer  "number",     limit: 4
    t.string   "network",    limit: 255
    t.date     "starts_at"
    t.date     "ends_at"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.integer  "store_id",   limit: 4
    t.string   "bank",       limit: 255
    t.integer  "brand",      limit: 4
    t.integer  "bank_id",    limit: 4
  end

  add_index "bines", ["bank_id"], name: "index_bines_on_bank_id", using: :btree
  add_index "bines", ["number"], name: "idx_bines_number", using: :btree

  create_table "bna_sellers", force: :cascade do |t|
    t.integer "shared_id",               limit: 4
    t.string  "cuit",                    limit: 11
    t.string  "fantasy_name",            limit: 255
    t.string  "id_shop_av_regular",      limit: 11
    t.string  "id_shop_av_promo",        limit: 11
    t.string  "id_shop_av_linea_blanca", limit: 11
  end

  add_index "bna_sellers", ["id_shop_av_linea_blanca"], name: "index_bna_sellers_on_id_shop_av_linea_blanca", using: :btree
  add_index "bna_sellers", ["id_shop_av_promo"], name: "index_bna_sellers_on_id_shop_av_promo", using: :btree
  add_index "bna_sellers", ["id_shop_av_regular"], name: "index_bna_sellers_on_id_shop_av_regular", using: :btree

  create_table "brand_admins", force: :cascade do |t|
    t.integer "brand_id", limit: 4
    t.integer "admin_id", limit: 4
  end

  create_table "brand_profiles", force: :cascade do |t|
    t.string   "name",            limit: 255
    t.string   "description",     limit: 255
    t.string   "website_url",     limit: 255
    t.string   "language",        limit: 255
    t.integer  "user_id",         limit: 4
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
    t.integer  "manufacturer_id", limit: 4
    t.string   "sports_ids",      limit: 255
    t.text     "data",            limit: 65535
  end

  add_index "brand_profiles", ["user_id"], name: "index_brand_profiles_on_user_id", using: :btree

  create_table "brands_users", force: :cascade do |t|
    t.integer "brand_id", limit: 4
    t.integer "user_id",  limit: 4
  end

  create_table "bulk_imports", force: :cascade do |t|
    t.string   "token",             limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "file_file_name",    limit: 255
    t.string   "file_content_type", limit: 255
    t.integer  "file_file_size",    limit: 4
    t.datetime "file_updated_at"
  end

  create_table "cancelation_service_configurations", force: :cascade do |t|
    t.integer  "store_id",                  limit: 4
    t.boolean  "generate_refund_shipments"
    t.boolean  "generate_coupons"
    t.boolean  "generate_credit_notes"
    t.boolean  "cancel_payments"
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
  end

  create_table "categories_on_genders", id: false, force: :cascade do |t|
    t.integer "category_id", limit: 4, null: false
    t.integer "gender_id",   limit: 4, null: false
  end

  add_index "categories_on_genders", ["category_id", "gender_id"], name: "categories_on_genders_ix", unique: true, using: :btree

  create_table "categories_on_products", id: false, force: :cascade do |t|
    t.integer "category_id", limit: 4, null: false
    t.integer "product_id",  limit: 4, null: false
  end

  add_index "categories_on_products", ["category_id", "product_id"], name: "categories_on_products_ix", unique: true, using: :btree

  create_table "categories_on_sports", id: false, force: :cascade do |t|
    t.integer "category_id", limit: 4, null: false
    t.integer "sport_id",    limit: 4, null: false
  end

  add_index "categories_on_sports", ["category_id", "sport_id"], name: "categories_on_sports_ix", unique: true, using: :btree

  create_table "comments", force: :cascade do |t|
    t.text     "body",        limit: 65535, null: false
    t.integer  "user_id",     limit: 4,     null: false
    t.integer  "target_id",   limit: 4,     null: false
    t.string   "target_type", limit: 255,   null: false
    t.datetime "created_at"
  end

  create_table "components", force: :cascade do |t|
    t.string   "title",      limit: 255
    t.integer  "landing_id", limit: 4
    t.string   "type",       limit: 255,                   null: false
    t.integer  "position",   limit: 4,                     null: false
    t.integer  "columns",    limit: 4,                     null: false
    t.text     "setup",      limit: 65535,                 null: false
    t.boolean  "active",                   default: false
    t.datetime "starts_on"
    t.datetime "ends_on"
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
  end

  create_table "components_pictures", force: :cascade do |t|
    t.string   "image_file_name",    limit: 255
    t.string   "image_content_type", limit: 255
    t.integer  "image_file_size",    limit: 4
    t.datetime "image_updated_at"
    t.string   "token",              limit: 255
    t.text     "available_styles",   limit: 65535
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
  end

  create_table "curated_feed_items", force: :cascade do |t|
    t.integer  "item_id",    limit: 4
    t.string   "item_type",  limit: 255
    t.string   "network",    limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "curated_users", force: :cascade do |t|
    t.integer  "user_id",    limit: 4
    t.string   "network",    limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "customer_reservation_purchases", force: :cascade do |t|
    t.string   "customer_dni", limit: 255, null: false
    t.integer  "product_id",   limit: 4,   null: false
    t.integer  "store_id",     limit: 4,   null: false
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "dnis", force: :cascade do |t|
    t.integer  "number",       limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "store_id",     limit: 4
    t.integer  "installments", limit: 4
  end

  add_index "dnis", ["number"], name: "index_dnis_on_number", using: :btree
  add_index "dnis", ["store_id"], name: "index_dnis_on_store_id", using: :btree

  create_table "equifax_id_validator_configurations", force: :cascade do |t|
    t.integer  "store_id",      limit: 4
    t.boolean  "active"
    t.string   "user",          limit: 255
    t.string   "client",        limit: 255
    t.string   "matrix",        limit: 255
    t.string   "password",      limit: 255
    t.string   "sector",        limit: 255
    t.string   "office",        limit: 255
    t.string   "s2s_user",      limit: 255
    t.string   "s2s_password",  limit: 255
    t.string   "questionnaire", limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  add_index "equifax_id_validator_configurations", ["store_id"], name: "index_equifax_id_validator_configurations_on_store_id", using: :btree

  create_table "events", force: :cascade do |t|
    t.integer  "user_id",     limit: 4,     null: false
    t.string   "title",       limit: 255
    t.text     "description", limit: 65535
    t.datetime "start_time"
    t.datetime "end_time"
    t.string   "location",    limit: 255
    t.string   "slug",        limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
    t.text     "sport_ids",   limit: 65535
    t.string   "ip",          limit: 255
  end

  add_index "events", ["slug"], name: "index_events_on_slug", unique: true, using: :btree

  create_table "export_recurrent_payments", force: :cascade do |t|
    t.string   "period",     limit: 255
    t.string   "brand",      limit: 255
    t.string   "card_type",  limit: 255
    t.string   "url",        limit: 255
    t.integer  "size",       limit: 4
    t.decimal  "total",                  precision: 10, scale: 2, default: 0.0
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "exports_payments", id: false, force: :cascade do |t|
    t.integer "export_id",  limit: 4
    t.integer "payment_id", limit: 4
  end

  add_index "exports_payments", ["export_id"], name: "index_exports_payments_on_export_id", using: :btree
  add_index "exports_payments", ["payment_id"], name: "index_exports_payments_on_payment_id", using: :btree

  create_table "external_redemption_logs", force: :cascade do |t|
    t.integer  "suborder",   limit: 4
    t.text     "redemption", limit: 65535
    t.text     "response",   limit: 65535
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
  end

  create_table "favorites", force: :cascade do |t|
    t.integer  "user_id",          limit: 4
    t.integer  "favoritable_id",   limit: 4
    t.string   "favoritable_type", limit: 255
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  add_index "favorites", ["favoritable_id", "favoritable_type", "user_id"], name: "by_user_id_and_favoritable", unique: true, using: :btree

  create_table "feedbacks", force: :cascade do |t|
    t.integer  "customer_id",   limit: 4
    t.string   "customer_type", limit: 255
    t.text     "data",          limit: 65535
    t.string   "network",       limit: 255
    t.string   "type",          limit: 255
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  create_table "first_data_credentials", force: :cascade do |t|
    t.integer  "store_id",                  limit: 4
    t.string   "firstdata_store",           limit: 255
    t.string   "user",                      limit: 255
    t.string   "password",                  limit: 255
    t.string   "certificate_password",      limit: 255
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
    t.string   "ssl_cert_file_name",        limit: 255
    t.string   "ssl_cert_content_type",     limit: 255
    t.integer  "ssl_cert_file_size",        limit: 4
    t.datetime "ssl_cert_updated_at"
    t.string   "ssl_cert_key_file_name",    limit: 255
    t.string   "ssl_cert_key_content_type", limit: 255
    t.integer  "ssl_cert_key_file_size",    limit: 4
    t.datetime "ssl_cert_key_updated_at"
    t.integer  "priority",                  limit: 4
    t.text     "category",                  limit: 65535
    t.integer  "shop_id",                   limit: 4
  end

  add_index "first_data_credentials", ["store_id"], name: "index_first_data_credentials_on_store_id", using: :btree

  create_table "follows", force: :cascade do |t|
    t.integer  "follower_id",   limit: 4
    t.integer  "followed_id",   limit: 4
    t.string   "followed_type", limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string   "slug",           limit: 255, null: false
    t.integer  "sluggable_id",   limit: 4,   null: false
    t.string   "sluggable_type", limit: 40
    t.datetime "created_at"
  end

  add_index "friendly_id_slugs", ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type", unique: true, using: :btree
  add_index "friendly_id_slugs", ["sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_id", using: :btree
  add_index "friendly_id_slugs", ["sluggable_type"], name: "index_friendly_id_slugs_on_sluggable_type", using: :btree

  create_table "gateway_alternative_strategies", force: :cascade do |t|
    t.integer  "store_id",   limit: 4
    t.integer  "strategy",   limit: 4
    t.string   "gateway",    limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "gateway_alternative_strategies", ["store_id"], name: "index_gateway_alternative_strategies_on_store_id", using: :btree

  create_table "gateway_credentials", force: :cascade do |t|
    t.integer  "store_id",   limit: 4
    t.integer  "gateway_id", limit: 4
    t.string   "name",       limit: 255
    t.string   "value",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "genders_on_products", id: false, force: :cascade do |t|
    t.integer "gender_id",  limit: 4, null: false
    t.integer "product_id", limit: 4, null: false
  end

  add_index "genders_on_products", ["gender_id", "product_id"], name: "genders_on_products_ix", using: :btree

  create_table "gift_cards", force: :cascade do |t|
    t.integer "coupon_id",     limit: 4
    t.integer "voucher_id",    limit: 4
    t.integer "order_item_id", limit: 4
  end

  add_index "gift_cards", ["order_item_id"], name: "index_gift_cards_on_order_item_id", using: :btree

  create_table "identities", force: :cascade do |t|
    t.string   "uuid",       limit: 255
    t.string   "provider",   limit: 255
    t.integer  "user_id",    limit: 4
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "import_recurrent_payments", force: :cascade do |t|
    t.string   "period",     limit: 255
    t.string   "brand",      limit: 255
    t.string   "card_type",  limit: 255
    t.string   "url",        limit: 255
    t.decimal  "total",                  precision: 10, scale: 2, default: 0.0
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "imports_payments", id: false, force: :cascade do |t|
    t.integer "import_id",  limit: 4
    t.integer "payment_id", limit: 4
  end

  add_index "imports_payments", ["import_id"], name: "index_imports_payments_on_import_id", using: :btree
  add_index "imports_payments", ["payment_id"], name: "index_imports_payments_on_payment_id", using: :btree

  create_table "installments", force: :cascade do |t|
    t.decimal  "price",                            precision: 10, scale: 2
    t.datetime "from_at"
    t.datetime "until_at"
    t.integer  "service_id",           limit: 4
    t.datetime "created_at",                                                                null: false
    t.datetime "updated_at",                                                                null: false
    t.integer  "points",               limit: 4,                            default: 0
    t.decimal  "cft",                              precision: 8,  scale: 2, default: 0.0
    t.decimal  "tea",                              precision: 8,  scale: 2, default: 0.0
    t.decimal  "coef",                             precision: 8,  scale: 5, default: 0.0
    t.integer  "number",               limit: 4
    t.integer  "bin_id",               limit: 4
    t.integer  "category_id",          limit: 4
    t.integer  "product_id",           limit: 4
    t.integer  "purchase_total_limit", limit: 4
    t.integer  "payment_program_id",   limit: 4
    t.string   "valid_for",            limit: 255
    t.integer  "store_id",             limit: 4
    t.string   "cftna",                limit: 255
    t.string   "tna",                  limit: 255
    t.integer  "tna_coef",             limit: 4
    t.boolean  "government_program",                                        default: false
  end

  add_index "installments", ["bin_id"], name: "index_installments_on_bin_id", using: :btree
  add_index "installments", ["category_id"], name: "index_installments_on_category_id", using: :btree
  add_index "installments", ["product_id"], name: "index_installments_on_product_id", using: :btree
  add_index "installments", ["service_id"], name: "index_installments_on_service_id", using: :btree

  create_table "insurance_configurations", force: :cascade do |t|
    t.integer  "store_id",     limit: 4
    t.boolean  "active"
    t.text     "categories",   limit: 65535
    t.float    "percentage",   limit: 24
    t.string   "installments", limit: 255
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  add_index "insurance_configurations", ["store_id"], name: "index_insurance_configurations_on_store_id", using: :btree

  create_table "insurance_tokens", force: :cascade do |t|
    t.string   "token",              limit: 255
    t.integer  "order_id",           limit: 4
    t.float    "amount",             limit: 24
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.integer  "installments",       limit: 4
    t.string   "name",               limit: 255
    t.string   "email",              limit: 255
    t.string   "gender",             limit: 255
    t.string   "birthday",           limit: 255
    t.string   "birth_location",     limit: 255
    t.string   "nationality",        limit: 255
    t.string   "doc_type",           limit: 255
    t.string   "doc_number",         limit: 255
    t.string   "phone",              limit: 255
    t.string   "address",            limit: 255
    t.string   "street_number",      limit: 255
    t.string   "dept",               limit: 255
    t.string   "country",            limit: 255
    t.string   "state",              limit: 255
    t.string   "city",               limit: 255
    t.string   "postal_code",        limit: 255
    t.string   "civil_status",       limit: 255
    t.string   "profession",         limit: 255
    t.boolean  "political_exposure"
    t.integer  "reference_id",       limit: 4
    t.string   "extra_data",         limit: 255
    t.string   "cbu",                limit: 255
    t.string   "floor",              limit: 255
  end

  add_index "insurance_tokens", ["order_id"], name: "insurance_tokens_order_id_IDX", using: :btree

  create_table "insurances_NO_USAR", id: :bigserial, force: :cascade do |t|
    t.string   "token",              limit: 255
    t.integer  "installments",       limit: 4
    t.float    "amount",             limit: 24
    t.string   "name",               limit: 255
    t.string   "email",              limit: 255
    t.string   "gender",             limit: 255
    t.string   "birthday",           limit: 255
    t.string   "birth_location",     limit: 255
    t.string   "nationality",        limit: 255
    t.string   "doc_type",           limit: 255
    t.string   "doc_number",         limit: 255
    t.string   "phone",              limit: 255
    t.string   "address",            limit: 255
    t.string   "street_number",      limit: 255
    t.string   "dept",               limit: 255
    t.string   "country",            limit: 255
    t.string   "state",              limit: 255
    t.string   "city",               limit: 255
    t.string   "postal_code",        limit: 255
    t.string   "civil_status",       limit: 255
    t.string   "profession",         limit: 255
    t.boolean  "political_exposure"
    t.integer  "reference_id",       limit: 4
    t.string   "extra_data",         limit: 255
    t.datetime "created_at",                     precision: 6, null: false
    t.datetime "updated_at",                     precision: 6, null: false
  end

  add_index "insurances_NO_USAR", ["created_at"], name: "insurances_created_at_IDX", using: :btree
  add_index "insurances_NO_USAR", ["token"], name: "index_insurances_on_token", unique: true, using: :btree

  create_table "invoice_items", force: :cascade do |t|
    t.integer  "suborder_id",            limit: 4
    t.decimal  "amount",                             precision: 10
    t.integer  "points",                 limit: 4
    t.datetime "fulfilled_at"
    t.datetime "created_at",                                        null: false
    t.datetime "updated_at",                                        null: false
    t.integer  "supplier_invoice_id",    limit: 4
    t.string   "transaction_id",         limit: 255
    t.string   "suborder_type",          limit: 255
    t.integer  "shop_id",                limit: 4
    t.integer  "store_id",               limit: 4
    t.string   "order_id",               limit: 255
    t.string   "invoice_status",         limit: 255
    t.string   "invoice_status_history", limit: 255
    t.float    "total",                  limit: 24
    t.float    "point_equivalent",       limit: 24
    t.float    "iva",                    limit: 24
    t.string   "shipment_status",        limit: 255
  end

  add_index "invoice_items", ["suborder_id"], name: "index_invoice_items_on_suborder_id", using: :btree

  create_table "invoice_reports", force: :cascade do |t|
    t.string   "name",                 limit: 255
    t.string   "description",          limit: 255
    t.datetime "created_from"
    t.datetime "created_to"
    t.datetime "fulfilled_from"
    t.datetime "fulfilled_to"
    t.string   "with_shipment_status", limit: 255
    t.integer  "store_id",             limit: 4
    t.integer  "shop_id",              limit: 4
    t.string   "search_query",         limit: 255
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.string   "with_invoice_status",  limit: 255
    t.integer  "last_export_id",       limit: 4
    t.string   "shop_ids",             limit: 255
  end

  create_table "invoices", force: :cascade do |t|
    t.decimal  "amount",                          precision: 10, scale: 2
    t.string   "number",            limit: 255
    t.string   "gateway",           limit: 255
    t.integer  "gateway_object_id", limit: 4
    t.text     "gateway_data",      limit: 65535
    t.integer  "payment_id",        limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "invoices", ["payment_id"], name: "index_invoices_on_payment_id", using: :btree

  create_table "landings", force: :cascade do |t|
    t.boolean  "home",                         default: false
    t.string   "name",             limit: 255
    t.string   "meta_title",       limit: 255
    t.string   "meta_description", limit: 255
    t.string   "slug",             limit: 255
    t.string   "network",          limit: 255,                 null: false
    t.boolean  "active",                       default: false
    t.datetime "starts_on"
    t.datetime "ends_on"
    t.datetime "created_at",                                   null: false
    t.datetime "updated_at",                                   null: false
    t.integer  "store_id",         limit: 4
  end

  create_table "mail_by_idcobis", force: :cascade do |t|
    t.string   "idcobis",    limit: 255
    t.string   "email",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "mkp_addresses", force: :cascade do |t|
    t.string   "first_name",               limit: 255
    t.string   "last_name",                limit: 255
    t.string   "telephone",                limit: 255
    t.string   "address",                  limit: 255
    t.string   "address_2",                limit: 255
    t.string   "city",                     limit: 255
    t.string   "state",                    limit: 255
    t.string   "country",                  limit: 255
    t.string   "zip",                      limit: 255
    t.integer  "addressable_id",           limit: 4
    t.string   "addressable_type",         limit: 255
    t.datetime "created_at",                                             null: false
    t.datetime "updated_at",                                             null: false
    t.string   "doc_number",               limit: 15
    t.boolean  "pickup",                                 default: false
    t.string   "open_hours",               limit: 255
    t.string   "retardment",               limit: 255
    t.integer  "priority",                 limit: 4
    t.datetime "deleted_at"
    t.string   "type",                     limit: 255
    t.string   "doc_type",                 limit: 255
    t.text     "street_number",            limit: 65535
    t.integer  "customer_id",              limit: 4
    t.string   "smartcarrier_id",          limit: 255
    t.string   "floor",                    limit: 255
    t.string   "dpto",                     limit: 255
    t.string   "tower",                    limit: 255
    t.string   "body",                     limit: 255
    t.string   "lateral_street_1",         limit: 255
    t.string   "lateral_street_2",         limit: 255
    t.string   "county",                   limit: 255
    t.string   "country_club",             limit: 255
    t.string   "internal_country_address", limit: 255
    t.text     "closing_hours",            limit: 65535
    t.string   "billing_address",          limit: 2000
    t.text     "requirements",             limit: 65535
  end

  add_index "mkp_addresses", ["addressable_id", "addressable_type"], name: "index_order_addresses_on_customer", using: :btree
  add_index "mkp_addresses", ["customer_id"], name: "index_mkp_addresses_on_customer_id", using: :btree
  add_index "mkp_addresses", ["smartcarrier_id"], name: "index_mkp_addresses_on_smartcarrier_id", using: :btree

  create_table "mkp_answers", force: :cascade do |t|
    t.integer  "user_id",         limit: 4
    t.integer  "mkp_question_id", limit: 4
    t.text     "description",     limit: 65535
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  add_index "mkp_answers", ["mkp_question_id"], name: "index_mkp_answers_on_mkp_question_id", using: :btree
  add_index "mkp_answers", ["user_id"], name: "index_mkp_answers_on_user_id", using: :btree

  create_table "mkp_authorizations", id: false, force: :cascade do |t|
    t.string   "token",      limit: 255, null: false
    t.string   "user_login", limit: 255, null: false
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "mkp_authorizations", ["token"], name: "index_mkp_authotizations_on_token", using: :btree
  add_index "mkp_authorizations", ["user_login"], name: "index_mkp_authotizations_on_user_login", using: :btree

  create_table "mkp_banks", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.integer  "code",       limit: 4
    t.boolean  "is_issuer",              default: true
  end

  create_table "mkp_bna_info_packages", force: :cascade do |t|
    t.integer  "solicitude_id",  limit: 4
    t.string   "program_type",   limit: 255
    t.string   "client_profile", limit: 255
    t.string   "order_date",     limit: 255
    t.string   "limit_amount",   limit: 255
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.integer  "order_id",       limit: 4
    t.string   "office_token",   limit: 255
  end

  add_index "mkp_bna_info_packages", ["order_id"], name: "idx_mkp_bna_info_packages_order_id", using: :btree

  create_table "mkp_bna_offices", force: :cascade do |t|
    t.string   "email",      limit: 255
    t.string   "token",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.integer  "user_id",    limit: 4
  end

  add_index "mkp_bna_offices", ["token"], name: "idx_mkp_bna_offices_token", using: :btree

  create_table "mkp_cards", force: :cascade do |t|
    t.string   "card_type",  limit: 255
    t.string   "brand",      limit: 255
    t.string   "category",   limit: 255
    t.integer  "member_id",  limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "mkp_cards", ["member_id"], name: "index_mkp_cards_on_member_id", using: :btree

  create_table "mkp_carrier_stores", force: :cascade do |t|
    t.integer  "zip_from",         limit: 4
    t.integer  "zip_to",           limit: 4
    t.integer  "store_id",         limit: 4
    t.string   "gateway",          limit: 255
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
    t.float    "price",            limit: 24
    t.float    "weight_min",       limit: 24
    t.float    "weight_max",       limit: 24
    t.string   "delivery_message", limit: 150
    t.integer  "shop_id",          limit: 4
    t.integer  "operative",        limit: 4,   default: 0
  end

  add_index "mkp_carrier_stores", ["shop_id"], name: "index_mkp_carrier_stores_on_shop_id", using: :btree
  add_index "mkp_carrier_stores", ["store_id"], name: "index_mkp_carrier_stores_on_store_id", using: :btree
  add_index "mkp_carrier_stores", ["weight_min", "weight_max"], name: "idx_mkp_carrier_stores_weight_min_weight_max", using: :btree
  add_index "mkp_carrier_stores", ["zip_from", "zip_to"], name: "idx_mkp_carrier_stores_zip_from_zip_to", using: :btree

  create_table "mkp_cart_delivery_options", force: :cascade do |t|
    t.decimal  "charge",                           precision: 10, default: 0
    t.string   "title",                limit: 255
    t.string   "service_level",        limit: 255
    t.string   "carrier",              limit: 255
    t.string   "service_level_real",   limit: 255
    t.string   "external_shipment_id", limit: 255
    t.integer  "shop_id",              limit: 4
    t.boolean  "selected",                                        default: false
    t.integer  "cart_id",              limit: 4
    t.datetime "created_at",                                                      null: false
    t.datetime "updated_at",                                                      null: false
    t.boolean  "pickup",                                          default: false
    t.string   "pickup_address",       limit: 255
  end

  add_index "mkp_cart_delivery_options", ["cart_id"], name: "index_mkp_cart_delivery_options_on_cart_id", using: :btree

  create_table "mkp_cart_items", force: :cascade do |t|
    t.integer  "quantity",    limit: 4
    t.integer  "cart_id",     limit: 4
    t.integer  "variant_id",  limit: 4
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.decimal  "price",                 precision: 10
    t.integer  "currency_id", limit: 4
    t.integer  "points",      limit: 4,                default: 0
  end

  add_index "mkp_cart_items", ["cart_id"], name: "index_mkp_cart_items_on_cart_id", using: :btree
  add_index "mkp_cart_items", ["variant_id"], name: "index_mkp_cart_items_on_variant_id", using: :btree

  create_table "mkp_carts", force: :cascade do |t|
    t.integer  "customer_id",        limit: 4
    t.string   "customer_type",      limit: 255
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.string   "purchase_id",        limit: 255,                   null: false
    t.string   "status",             limit: 255
    t.string   "ip",                 limit: 255
    t.string   "network",            limit: 255
    t.string   "type",               limit: 255
    t.integer  "coupon_id",          limit: 4
    t.text     "data",               limit: 65535
    t.integer  "store_id",           limit: 4
    t.integer  "address_id",         limit: 4
    t.string   "delivery_option_id", limit: 255
    t.integer  "is_pickup",          limit: 4
    t.boolean  "newsletter",                       default: true
    t.boolean  "left_cart_email",                  default: false
  end

  add_index "mkp_carts", ["created_at"], name: "idx_mkp_carts_created_at", using: :btree
  add_index "mkp_carts", ["customer_id"], name: "index_mkp_carts_on_user_id", using: :btree
  add_index "mkp_carts", ["customer_type", "customer_id"], name: "index_mkp_carts_on_customer_type_and_customer_id", using: :btree
  add_index "mkp_carts", ["ip"], name: "idx_mkp_carts_ip", using: :btree
  add_index "mkp_carts", ["purchase_id"], name: "index_mkp_carts_on_purchase_id", using: :btree

  create_table "mkp_categories", force: :cascade do |t|
    t.string   "name",                    limit: 255
    t.string   "slug",                    limit: 255
    t.string   "description",             limit: 255
    t.string   "external_id",             limit: 255
    t.string   "ancestry",                limit: 255
    t.integer  "ancestry_depth",          limit: 4,                            default: 0
    t.decimal  "width",                               precision: 11, scale: 2
    t.decimal  "height",                              precision: 11, scale: 2
    t.decimal  "length",                              precision: 11, scale: 2
    t.decimal  "weight",                              precision: 11, scale: 2
    t.string   "mass_unit",               limit: 255
    t.string   "length_unit",             limit: 255
    t.string   "network",                 limit: 255
    t.integer  "display_order",           limit: 4
    t.boolean  "active",                                                       default: true
    t.datetime "created_at",                                                                  null: false
    t.datetime "updated_at",                                                                  null: false
    t.integer  "pickeable_status",        limit: 4,                            default: 0,    null: false
    t.integer  "google_category_id",      limit: 4
    t.string   "full_path_name",          limit: 255
    t.decimal  "visa_puntos_equivalence",             precision: 15, scale: 6
  end

  create_table "mkp_category_stores", force: :cascade do |t|
    t.integer  "category_id",          limit: 4,                                             null: false
    t.integer  "store_id",             limit: 4,                                             null: false
    t.datetime "created_at",                                                                 null: false
    t.datetime "updated_at",                                                                 null: false
    t.text     "installments",         limit: 65535
    t.decimal  "commission",                         precision: 5, scale: 2
    t.boolean  "active_insurance",                                           default: false
    t.float    "insurance_coef",       limit: 24
    t.datetime "insurance_start_date"
    t.datetime "insurance_end_date"
  end

  add_index "mkp_category_stores", ["category_id", "store_id"], name: "mkp_category_stores_ix", unique: true, using: :btree

  create_table "mkp_coupons", force: :cascade do |t|
    t.string   "type",                    limit: 255,                                            null: false
    t.string   "code",                    limit: 255,                                            null: false
    t.decimal  "amount",                                precision: 11, scale: 2, default: 0.0
    t.decimal  "minimum_value",                         precision: 11, scale: 2
    t.integer  "percent",                 limit: 4,                              default: 0
    t.text     "description",             limit: 65535,                                          null: false
    t.datetime "starts_at"
    t.datetime "expires_at"
    t.integer  "still_available",         limit: 4
    t.integer  "total_available",         limit: 4
    t.integer  "shop_id",                 limit: 4
    t.datetime "deleted_at"
    t.datetime "created_at",                                                                     null: false
    t.datetime "updated_at",                                                                     null: false
    t.text     "restrictions",            limit: 65535
    t.string   "network",                 limit: 255
    t.string   "policy",                  limit: 255
    t.boolean  "apply_on_sale",                                                  default: false
    t.integer  "repetitive_amount_limit", limit: 4
    t.decimal  "discount_limit",                        precision: 11, scale: 2, default: 0.0
    t.integer  "store_id",                limit: 4
    t.integer  "customer_id",             limit: 4
    t.decimal  "amount_available",                      precision: 11, scale: 2, default: 0.0
  end

  add_index "mkp_coupons", ["code"], name: "index_mkp_coupons_on_code", using: :btree
  add_index "mkp_coupons", ["customer_id"], name: "index_mkp_coupons_on_customer_id", using: :btree
  add_index "mkp_coupons", ["expires_at"], name: "index_mkp_coupons_on_expires_at", using: :btree
  add_index "mkp_coupons", ["type", "id"], name: "index_mkp_coupons_on_type_and_id", using: :btree

  create_table "mkp_credit_notes", force: :cascade do |t|
    t.string   "gateway",           limit: 255
    t.integer  "gateway_object_id", limit: 4
    t.text     "gateway_data",      limit: 65535
    t.integer  "order_id",          limit: 4
    t.integer  "suborder_id",       limit: 4
    t.integer  "invoice_id",        limit: 4
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
  end

  add_index "mkp_credit_notes", ["invoice_id"], name: "index_mkp_credit_notes_on_invoice_id", using: :btree
  add_index "mkp_credit_notes", ["order_id"], name: "index_mkp_credit_notes_on_order_id", using: :btree

  create_table "mkp_currencies", force: :cascade do |t|
    t.string   "name",       limit: 50
    t.string   "identifier", limit: 5
    t.string   "symbol",     limit: 5
    t.string   "network",    limit: 8
    t.datetime "created_at",            null: false
    t.datetime "updated_at",            null: false
  end

  create_table "mkp_customers", force: :cascade do |t|
    t.string   "email",                  limit: 255
    t.string   "encrypted_password",     limit: 255, default: "",    null: false
    t.string   "reset_password_token",   limit: 255
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer  "sign_in_count",          limit: 4,   default: 0,     null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string   "current_sign_in_ip",     limit: 255
    t.string   "last_sign_in_ip",        limit: 255
    t.string   "first_name",             limit: 255
    t.string   "last_name",              limit: 255
    t.string   "image",                  limit: 255
    t.string   "doc_number",             limit: 25
    t.string   "provider",               limit: 255
    t.string   "uuid",                   limit: 255
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.integer  "store_id",               limit: 4
    t.integer  "gender",                 limit: 4,   default: 0
    t.datetime "birthday_at"
    t.string   "telephone",              limit: 255
    t.string   "user_identify",          limit: 255
    t.integer  "doc_type",               limit: 4
    t.boolean  "temporary_email",                    default: false
    t.integer  "macro_category",         limit: 4
    t.datetime "deleted_at"
  end

  add_index "mkp_customers", ["reset_password_token"], name: "index_mkp_customers_on_reset_password_token", unique: true, using: :btree
  add_index "mkp_customers", ["store_id"], name: "index_mkp_customers_on_store_id", using: :btree
  add_index "mkp_customers", ["user_identify"], name: "index_mkp_customers_on_user_identify", using: :btree

  create_table "mkp_decidir_credentials", force: :cascade do |t|
    t.string  "private_key",                 limit: 255
    t.string  "public_key",                  limit: 255
    t.integer "store_id",                    limit: 4
    t.string  "avenida_distributed_site_id", limit: 255
  end

  add_index "mkp_decidir_credentials", ["store_id"], name: "index_mkp_decidir_credentials_on_store_id", using: :btree

  create_table "mkp_documents", force: :cascade do |t|
    t.integer  "suborder_id",           limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "pdf_file_file_name",    limit: 255
    t.string   "pdf_file_content_type", limit: 255
    t.integer  "pdf_file_file_size",    limit: 8
    t.datetime "pdf_file_updated_at"
    t.string   "token",                 limit: 255
    t.string   "pdf_file_type",         limit: 255
  end

  create_table "mkp_entity_status_changes", force: :cascade do |t|
    t.integer  "entity_id",   limit: 4
    t.string   "entity_type", limit: 255
    t.string   "status",      limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  add_index "mkp_entity_status_changes", ["entity_id"], name: "idx_mkp_entity_status_changes_entity_id", using: :btree

  create_table "mkp_exports", force: :cascade do |t|
    t.string   "url",                   limit: 255
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.integer  "store_id",              limit: 4
    t.integer  "export_type",           limit: 4
    t.string   "title",                 limit: 255
    t.string   "csv_file_file_name",    limit: 255
    t.string   "csv_file_content_type", limit: 255
    t.integer  "csv_file_file_size",    limit: 4
    t.datetime "csv_file_updated_at"
    t.integer  "owner",                 limit: 4,   default: 0
    t.integer  "shop_id",               limit: 4
  end

  add_index "mkp_exports", ["shop_id"], name: "index_mkp_exports_on_shop_id", using: :btree
  add_index "mkp_exports", ["store_id"], name: "index_mkp_exports_on_store_id", using: :btree

  create_table "mkp_external_coupons", force: :cascade do |t|
    t.string  "code",          limit: 255
    t.decimal "amount",                    precision: 8, scale: 2, default: 0.0
    t.integer "voucher_id",    limit: 4
    t.integer "order_item_id", limit: 4
  end

  add_index "mkp_external_coupons", ["order_item_id"], name: "index_mkp_external_coupons_on_order_item_id", using: :btree
  add_index "mkp_external_coupons", ["voucher_id"], name: "index_mkp_external_coupons_on_voucher_id", using: :btree

  create_table "mkp_free_shippings", force: :cascade do |t|
    t.datetime "from"
    t.datetime "to"
    t.decimal  "amount",               precision: 10
    t.boolean  "active"
    t.integer  "store_id",   limit: 4
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
  end

  create_table "mkp_gateway_errors", force: :cascade do |t|
    t.integer  "store_id",          limit: 4
    t.string   "gateway",           limit: 255
    t.integer  "external_id",       limit: 4
    t.string   "description_short", limit: 255
    t.string   "description_large", limit: 255
    t.string   "message",           limit: 255
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  add_index "mkp_gateway_errors", ["store_id", "gateway", "external_id"], name: "mkp_gateway_errors_ix", unique: true, using: :btree

  create_table "mkp_genders", force: :cascade do |t|
    t.string   "name",       limit: 20,  null: false
    t.string   "slug",       limit: 255
    t.string   "ancestry",   limit: 255
    t.string   "network",    limit: 8
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "mkp_genders", ["ancestry"], name: "genders_on_ancestry_ix", using: :btree

  create_table "mkp_guests", force: :cascade do |t|
    t.string   "first_name",              limit: 255
    t.string   "last_name",               limit: 255
    t.string   "email",                   limit: 255
    t.boolean  "verify_email"
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.integer  "braintree_customer_id",   limit: 4
    t.text     "braintree_data",          limit: 65535
    t.string   "network",                 limit: 255
    t.integer  "mercadopago_customer_id", limit: 4
    t.text     "mercadopago_data",        limit: 65535
    t.string   "doc_number",              limit: 255
    t.string   "telephone",               limit: 255
  end

  create_table "mkp_integration_logs", force: :cascade do |t|
    t.integer  "integration_id",   limit: 4
    t.string   "integration_name", limit: 255
    t.string   "action_type",      limit: 255
    t.integer  "shop_id",          limit: 4
    t.integer  "user_id",          limit: 4
    t.string   "ip",               limit: 255
    t.text     "inbound_data",     limit: 65535
    t.text     "data",             limit: 65535
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  add_index "mkp_integration_logs", ["id"], name: "index_mkp_integration_logs_on_id", using: :btree
  add_index "mkp_integration_logs", ["integration_id", "shop_id"], name: "index_mkp_integration_logs_on_integration_of_shop", using: :btree
  add_index "mkp_integration_logs", ["integration_id"], name: "index_mkp_integration_logs_on_integration_id", using: :btree

  create_table "mkp_integration_objects", force: :cascade do |t|
    t.string   "integrable_type",  limit: 255
    t.integer  "integrable_id",    limit: 4
    t.string   "external_id",      limit: 255
    t.string   "integration_name", limit: 255
    t.integer  "integration_id",   limit: 4
    t.datetime "created_at",                                   null: false
    t.datetime "updated_at",                                   null: false
    t.boolean  "single",                       default: false
    t.boolean  "display",                      default: true
  end

  add_index "mkp_integration_objects", ["external_id", "integration_id"], name: "index_mkp_integration_objects_on_external_integration", using: :btree
  add_index "mkp_integration_objects", ["id"], name: "index_mkp_integration_objects_on_id", using: :btree
  add_index "mkp_integration_objects", ["integrable_type", "integrable_id"], name: "index_mkp_integration_objects_on_integrable", using: :btree

  create_table "mkp_integrations", force: :cascade do |t|
    t.integer  "shop_id",       limit: 4
    t.string   "type",          limit: 255
    t.string   "account_name",  limit: 255
    t.string   "scopes",        limit: 255
    t.string   "code",          limit: 255
    t.string   "access_token",  limit: 255
    t.string   "refresh_token", limit: 255
    t.boolean  "expires",                     default: false
    t.datetime "expires_at"
    t.text     "data",          limit: 65535
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.text     "settings",      limit: 65535
  end

  add_index "mkp_integrations", ["shop_id"], name: "index_mkp_integrations_on_shop_id", using: :btree
  add_index "mkp_integrations", ["type", "account_name"], name: "index_mkp_integrations_on_type_and_account_name", using: :btree
  add_index "mkp_integrations", ["type", "shop_id"], name: "index_mkp_integrations_on_type_and_shop_id", using: :btree

  create_table "mkp_invoices", force: :cascade do |t|
    t.decimal  "amount",                          precision: 10, scale: 2
    t.integer  "order_id",          limit: 4
    t.string   "gateway",           limit: 255
    t.integer  "gateway_object_id", limit: 4
    t.text     "gateway_data",      limit: 65535
    t.datetime "created_at",                                               null: false
    t.datetime "updated_at",                                               null: false
  end

  create_table "mkp_manufacturers", force: :cascade do |t|
    t.string   "token",             limit: 255
    t.string   "name",              limit: 100,               null: false
    t.string   "origin_country",    limit: 100
    t.integer  "products_count",    limit: 4,     default: 0
    t.string   "logo_file_name",    limit: 255
    t.string   "logo_content_type", limit: 255
    t.integer  "logo_file_size",    limit: 4
    t.datetime "logo_updated_at"
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.string   "slug",              limit: 255
    t.text     "description",       limit: 65535
    t.string   "sku_id",            limit: 255
    t.datetime "deleted_at"
  end

  add_index "mkp_manufacturers", ["slug"], name: "index_mkp_manufacturers_on_slug", unique: true, using: :btree

  create_table "mkp_members", force: :cascade do |t|
    t.integer  "nationality", limit: 4,   default: 0
    t.string   "mobile",      limit: 255
    t.integer  "customer_id", limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "code",        limit: 255
    t.string   "token",       limit: 255
    t.string   "origin",      limit: 255
    t.boolean  "enabled",                 default: true
  end

  add_index "mkp_members", ["customer_id"], name: "index_mkp_members_on_customer_id", using: :btree

  create_table "mkp_menus", force: :cascade do |t|
    t.string   "name",          limit: 50
    t.string   "name_display",  limit: 50
    t.text     "structure",     limit: 65535
    t.string   "img_path",      limit: 255
    t.boolean  "active",                      default: true
    t.integer  "display_order", limit: 4,     default: 0
    t.string   "slug",          limit: 50
    t.string   "url",           limit: 255
    t.string   "network",       limit: 8
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.boolean  "custom"
    t.integer  "store_id",      limit: 4
  end

  add_index "mkp_menus", ["store_id"], name: "index_mkp_menus_on_store_id", using: :btree

  create_table "mkp_order_items", force: :cascade do |t|
    t.integer  "product_id",       limit: 4
    t.integer  "variant_id",       limit: 4
    t.integer  "suborder_id",      limit: 4
    t.integer  "currency_id",      limit: 4
    t.string   "status",           limit: 255,                            default: "available"
    t.integer  "quantity",         limit: 4,                                                    null: false
    t.decimal  "price",                          precision: 8,  scale: 2,                       null: false
    t.decimal  "sale_price",                     precision: 8,  scale: 2, default: 0.0
    t.text     "data",             limit: 65535
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "points",           limit: 4,                              default: 0
    t.decimal  "point_equivalent",               precision: 15, scale: 6
    t.decimal  "iva",                            precision: 3,  scale: 1
  end

  add_index "mkp_order_items", ["product_id"], name: "index_order_items_on_product_id", using: :btree
  add_index "mkp_order_items", ["suborder_id"], name: "index_order_items_on_order_id", using: :btree
  add_index "mkp_order_items", ["updated_at"], name: "idx_mkp_order_items_updated_at", using: :btree
  add_index "mkp_order_items", ["variant_id"], name: "index_order_items_on_variant_id", using: :btree

  create_table "mkp_orders", force: :cascade do |t|
    t.integer  "customer_id",        limit: 4
    t.string   "customer_type",      limit: 255
    t.string   "ip",                 limit: 255
    t.string   "network",            limit: 8
    t.datetime "created_at",                                                                null: false
    t.datetime "updated_at",                                                                null: false
    t.binary   "purchase_id",        limit: 255,                                            null: false
    t.string   "title",              limit: 255
    t.integer  "coupon_id",          limit: 4
    t.decimal  "coupon_discount",                  precision: 8,  scale: 2, default: 0.0
    t.text     "data",               limit: 65535
    t.boolean  "auto_invoiced",                                             default: false
    t.integer  "store_id",           limit: 4
    t.decimal  "bonified_amount",                  precision: 8,  scale: 2, default: 0.0
    t.decimal  "gross_total",                      precision: 10, scale: 2, default: 0.0
    t.boolean  "jubilo_liquided",                                           default: false
    t.string   "reservation_code",   limit: 255
    t.integer  "reservation_status", limit: 4,                              default: 0
    t.string   "id_cobis",           limit: 255
  end

  add_index "mkp_orders", ["created_at"], name: "idx_mkp_orders_created_at", using: :btree
  add_index "mkp_orders", ["customer_id", "customer_type"], name: "index_orders_on_customer", using: :btree
  add_index "mkp_orders", ["store_id"], name: "idx_mkp_orders_store_id", using: :btree
  add_index "mkp_orders", ["updated_at"], name: "mkp_orders_updated_at_IDX", using: :btree

  create_table "mkp_packages", force: :cascade do |t|
    t.decimal  "width",                   precision: 8, scale: 2, default: 0.0
    t.decimal  "height",                  precision: 8, scale: 2, default: 0.0
    t.decimal  "length",                  precision: 8, scale: 2, default: 0.0
    t.decimal  "weight",                  precision: 8, scale: 2, default: 0.0
    t.string   "mass_unit",   limit: 255
    t.string   "length_unit", limit: 255
    t.integer  "product_id",  limit: 4
    t.datetime "created_at",                                                    null: false
    t.datetime "updated_at",                                                    null: false
  end

  create_table "mkp_payment_credentials", force: :cascade do |t|
    t.integer  "store_id",   limit: 4
    t.integer  "shop_id",    limit: 4
    t.string   "name",       limit: 255
    t.string   "value",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "mkp_payment_credentials", ["shop_id"], name: "index_mkp_payment_credentials_on_shop_id", using: :btree
  add_index "mkp_payment_credentials", ["store_id"], name: "index_mkp_payment_credentials_on_store_id", using: :btree

  create_table "mkp_payment_promotions", force: :cascade do |t|
    t.datetime "start"
    t.datetime "expire"
    t.boolean  "active"
    t.decimal  "coefficient",               precision: 8, scale: 6, default: 0.0
    t.decimal  "tna",                       precision: 8, scale: 2, default: 0.0
    t.decimal  "cft",                       precision: 8, scale: 2, default: 0.0
    t.decimal  "tea",                       precision: 8, scale: 2, default: 0.0
    t.integer  "brand",       limit: 4
    t.integer  "installment", limit: 4
    t.string   "message",     limit: 255
    t.text     "legal",       limit: 65535
    t.integer  "bank_id",     limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "gateway",     limit: 255
  end

  add_index "mkp_payment_promotions", ["bank_id"], name: "index_mkp_payment_promotions_on_bank_id", using: :btree

  create_table "mkp_payments", force: :cascade do |t|
    t.string   "status",            limit: 255
    t.decimal  "collected_amount",                   precision: 11, scale: 2, default: 0.0
    t.datetime "collected_at"
    t.string   "gateway",           limit: 255
    t.text     "gateway_data",      limit: ********
    t.string   "gateway_object_id", limit: 255
    t.string   "payment_method",    limit: 255
    t.integer  "installments",      limit: 4,                                 default: 1
    t.integer  "order_id",          limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "document_type",     limit: 255
    t.string   "document_number",   limit: 255
    t.integer  "sale_item_id",      limit: 4
    t.string   "sale_item_type",    limit: 255
    t.integer  "order_item_id",     limit: 4
  end

  add_index "mkp_payments", ["created_at"], name: "idx_mkp_payments_created_at", using: :btree
  add_index "mkp_payments", ["gateway_object_id"], name: "index_mkp_payments_on_gateway_object_id", using: :btree
  add_index "mkp_payments", ["order_id"], name: "index_mkp_payments_on_order_id", using: :btree
  add_index "mkp_payments", ["order_item_id"], name: "index_mkp_payments_on_order_item_id", using: :btree
  add_index "mkp_payments", ["sale_item_type", "sale_item_id"], name: "index_mkp_payments_on_sale_item_type_and_sale_item_id", using: :btree

  create_table "mkp_pictures", force: :cascade do |t|
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size",    limit: 4
    t.datetime "photo_updated_at"
    t.integer  "view_order",         limit: 4,   default: 0
    t.integer  "product_id",         limit: 4
    t.boolean  "processing",                     default: true
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.string   "token",              limit: 255
    t.string   "type",               limit: 255
    t.integer  "owner_id",           limit: 4
    t.string   "owner_type",         limit: 255
    t.string   "photo_name",         limit: 255
    t.integer  "variant_id",         limit: 4
  end

  add_index "mkp_pictures", ["type", "product_id"], name: "index_mkp_pictures_on_type_and_product_id", using: :btree
  add_index "mkp_pictures", ["variant_id"], name: "fk_rails_c59a13bcc5", using: :btree

  create_table "mkp_product_stores", force: :cascade do |t|
    t.integer  "product_id",              limit: 4
    t.integer  "store_id",                limit: 4
    t.integer  "points",                  limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.decimal  "visa_puntos_equivalence",           precision: 15, scale: 6
    t.boolean  "approved",                                                   default: false
    t.integer  "status",                  limit: 4,                          default: 0
    t.integer  "order_visibility",        limit: 4,                          default: 0
  end

  add_index "mkp_product_stores", ["product_id"], name: "index_mkp_product_stores_on_product_id", using: :btree
  add_index "mkp_product_stores", ["store_id"], name: "index_mkp_product_stores_on_store_id", using: :btree

  create_table "mkp_products", force: :cascade do |t|
    t.string   "title",                      limit: 255,                                            null: false
    t.text     "description",                limit: 65535
    t.integer  "shop_id",                    limit: 4
    t.integer  "category_id",                limit: 4
    t.datetime "available_on"
    t.datetime "deleted_at"
    t.datetime "created_at",                                                                        null: false
    t.datetime "updated_at",                                                                        null: false
    t.integer  "manufacturer_id",            limit: 4
    t.datetime "valid_to"
    t.decimal  "regular_price",                            precision: 11, scale: 2, default: 0.0
    t.integer  "currency_id",                limit: 4
    t.datetime "sale_on"
    t.datetime "sale_until"
    t.decimal  "sale_price",                               precision: 11, scale: 2, default: 0.0
    t.string   "slug",                       limit: 255,                                            null: false
    t.decimal  "width",                                    precision: 11, scale: 2
    t.decimal  "height",                                   precision: 11, scale: 2
    t.decimal  "length",                                   precision: 11, scale: 2
    t.decimal  "weight",                                   precision: 11, scale: 2
    t.string   "mass_unit",                  limit: 255
    t.string   "length_unit",                limit: 255
    t.text     "available_properties",       limit: 65535
    t.boolean  "display_variants",                                                  default: false
    t.boolean  "handle_stock"
    t.decimal  "relevance_score",                          precision: 10, scale: 5, default: 1.0
    t.decimal  "score_multiplier",                         precision: 10, scale: 2, default: 1.0
    t.text     "data",                       limit: 65535
    t.text     "available_properties_names", limit: 65535
    t.integer  "packages",                   limit: 4,                              default: 1
    t.integer  "pickeable_status",           limit: 4,                              default: 0,     null: false
    t.text     "data_shipment",              limit: 65535
    t.decimal  "iva",                                      precision: 3,  scale: 1, default: 21.0
    t.boolean  "purchasable",                                                       default: true
    t.boolean  "reservable"
    t.integer  "transaction_type",           limit: 4,                              default: 0
    t.integer  "points_price",               limit: 4
    t.boolean  "third_party_code_required",                                         default: true
    t.integer  "origin_of_product",          limit: 4
    t.string   "brand",                      limit: 255
    t.string   "energy_efficiency",          limit: 255
  end

  add_index "mkp_products", ["available_on"], name: "idx_mkp_products_available_on", using: :btree
  add_index "mkp_products", ["deleted_at"], name: "idx_mkp_products_deleted_at", using: :btree
  add_index "mkp_products", ["shop_id"], name: "index_mkp_products_on_shop_id", using: :btree
  add_index "mkp_products", ["slug"], name: "index_mkp_products_on_slug", unique: true, using: :btree

  create_table "mkp_promotions", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.text     "display_name",        limit: 65535
    t.text     "description",         limit: 65535
    t.integer  "priority",            limit: 4
    t.boolean  "active"
    t.datetime "starts_at"
    t.datetime "expires_at"
    t.string   "network",             limit: 255
    t.boolean  "allow_coupon"
    t.boolean  "allow_items_on_sale",               default: false
    t.text     "condition",           limit: 65535
    t.text     "action",              limit: 65535
    t.datetime "created_at",                                        null: false
    t.datetime "updated_at",                                        null: false
    t.integer  "store_id",            limit: 4
  end

  create_table "mkp_questions", force: :cascade do |t|
    t.integer  "product_id",  limit: 4
    t.integer  "user_id",     limit: 4
    t.text     "description", limit: 65535
    t.datetime "created_at",                                null: false
    t.datetime "updated_at",                                null: false
    t.boolean  "read",                      default: false
  end

  add_index "mkp_questions", ["product_id"], name: "index_mkp_question_on_product_id", using: :btree
  add_index "mkp_questions", ["user_id"], name: "index_mkp_question_on_user_id", using: :btree

  create_table "mkp_reviews", force: :cascade do |t|
    t.boolean  "curated",                     default: false
    t.integer  "product_id",    limit: 4
    t.integer  "customer_id",   limit: 4
    t.string   "type",          limit: 255
    t.integer  "stars",         limit: 4
    t.string   "title",         limit: 255
    t.text     "description",   limit: 65535
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.datetime "reported_at"
    t.integer  "reported_by",   limit: 4
    t.boolean  "inappropriate"
    t.text     "data",          limit: 65535
    t.string   "email",         limit: 255
  end

  add_index "mkp_reviews", ["type", "product_id"], name: "index_mkp_reviews_on_type_and_product_id", using: :btree

  create_table "mkp_shipment_items", id: false, force: :cascade do |t|
    t.integer "shipment_id",   limit: 4
    t.integer "order_item_id", limit: 4
  end

  add_index "mkp_shipment_items", ["shipment_id", "order_item_id"], name: "index_mkp_shipment_items_on_shipment_id_and_order_item_id", using: :btree

  create_table "mkp_shipment_labels", force: :cascade do |t|
    t.integer  "shipment_id",             limit: 4
    t.string   "tracking_number",         limit: 255
    t.string   "courier",                 limit: 255
    t.decimal  "price",                                    precision: 8, scale: 2
    t.datetime "estimated_delivery_date"
    t.string   "gateway",                 limit: 255
    t.string   "gateway_object_id",       limit: 255
    t.text     "gateway_data",            limit: ********
    t.text     "activity_log",            limit: 65535
    t.datetime "cancelled_at"
    t.datetime "created_at",                                                       null: false
    t.datetime "updated_at",                                                       null: false
  end

  add_index "mkp_shipment_labels", ["courier", "tracking_number"], name: "labels_courier_tracking_number_index", using: :btree
  add_index "mkp_shipment_labels", ["gateway", "gateway_object_id"], name: "labels_gateway_gateway_object_id_index", using: :btree
  add_index "mkp_shipment_labels", ["shipment_id"], name: "idx_mkp_shipment_labels_shipment_id", using: :btree

  create_table "mkp_shipments", force: :cascade do |t|
    t.string   "status",              limit: 255,                              default: "unfulfilled"
    t.text     "destination_address", limit: 65535
    t.text     "origin_address",      limit: 65535
    t.text     "extra_info",          limit: 65535
    t.decimal  "charged_amount",                       precision: 8, scale: 2
    t.decimal  "bonified_amount",                      precision: 8, scale: 2, default: 0.0
    t.string   "direction",           limit: 255,                              default: "outgoing"
    t.string   "gateway",             limit: 255
    t.string   "gateway_object_id",   limit: 255
    t.text     "gateway_data",        limit: ********
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "sale_item_id",        limit: 4
    t.string   "sale_item_type",      limit: 255
    t.string   "shipment_kind",       limit: 255
    t.integer  "suborder_id",         limit: 4
  end

  add_index "mkp_shipments", ["sale_item_type", "sale_item_id"], name: "index_mkp_shipments_on_sale_item_type_and_sale_item_id", using: :btree
  add_index "mkp_shipments", ["suborder_id"], name: "idx_mkp_shipments_suborder_id", using: :btree

  create_table "mkp_shipping_methods", force: :cascade do |t|
    t.string   "title",      limit: 255
    t.integer  "zone_id",    limit: 4
    t.text     "setup",      limit: 65535
    t.string   "service",    limit: 255
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.datetime "deleted_at"
  end

  create_table "mkp_shop_admins", force: :cascade do |t|
    t.integer  "shop_id",    limit: 4
    t.integer  "admin_id",   limit: 4
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
    t.boolean  "owner",                default: false
  end

  add_index "mkp_shop_admins", ["admin_id"], name: "index_mkp_shops_users_on_merchant_id", using: :btree
  add_index "mkp_shop_admins", ["shop_id"], name: "index_mkp_shops_users_on_shop_id", using: :btree

  create_table "mkp_shop_settings", force: :cascade do |t|
    t.text     "tax_rates",               limit: 65535
    t.text     "commercial_agreement",    limit: 65535
    t.text     "wide_discount",           limit: 65535
    t.text     "notify_purchases",        limit: 65535
    t.string   "customs_signer_name",     limit: 255
    t.integer  "mkp_shop_id",             limit: 4
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.text     "notification_policy",     limit: 65535
    t.text     "delivery_channel_policy", limit: 65535
  end

  add_index "mkp_shop_settings", ["mkp_shop_id"], name: "index_mkp_shop_settings_on_mkp_shop_id", using: :btree

  create_table "mkp_shop_stores", force: :cascade do |t|
    t.integer  "shop_id",            limit: 4
    t.integer  "store_id",           limit: 4
    t.datetime "created_at",                                           null: false
    t.datetime "updated_at",                                           null: false
    t.decimal  "commission",                   precision: 5, scale: 2
    t.integer  "payment_program_id", limit: 4
    t.datetime "start_date"
    t.datetime "end_date"
    t.boolean  "active"
  end

  add_index "mkp_shop_stores", ["shop_id"], name: "index_mkp_shop_stores_on_shop_id", using: :btree
  add_index "mkp_shop_stores", ["store_id"], name: "index_mkp_shop_stores_on_store_id", using: :btree

  create_table "mkp_shops", force: :cascade do |t|
    t.string   "title",                     limit: 255
    t.text     "description",               limit: 65535
    t.string   "network",                   limit: 255
    t.datetime "created_at",                                                                       null: false
    t.datetime "updated_at",                                                                       null: false
    t.string   "slug",                      limit: 255
    t.boolean  "visible",                                                          default: true
    t.integer  "fulfillment_shop_id",       limit: 4
    t.boolean  "fc",                                                               default: false
    t.boolean  "reminder",                                                         default: true
    t.string   "decidir_site_id",           limit: 255
    t.integer  "decidir_percentage",        limit: 4
    t.datetime "deleted_at"
    t.boolean  "delivery_by_matrix",                                               default: false
    t.decimal  "visa_puntos_equivalence",                 precision: 15, scale: 6
    t.string   "decidir_public_key",        limit: 255
    t.string   "decidir_private_key",       limit: 255
    t.string   "mercadolibre_app_id",       limit: 255
    t.string   "mercadolibre_secret_key",   limit: 255
    t.string   "shipment_strategy",         limit: 255
    t.string   "mercadolibre_redirect_uri", limit: 255
    t.integer  "office_id",                 limit: 4
    t.boolean  "allows_pickup",                                                    default: false
    t.string   "public_name",               limit: 255,                            default: ""
    t.string   "phone",                     limit: 255,                            default: ""
    t.string   "web",                       limit: 255,                            default: ""
    t.string   "public_email",              limit: 255,                            default: ""
    t.boolean  "allow_products_delete",                                            default: true
    t.integer  "order_visibility",          limit: 4,                              default: 0
    t.string   "cuit",                      limit: 255
    t.string   "external_shop_id",          limit: 255
  end

  add_index "mkp_shops", ["deleted_at"], name: "index_mkp_shops_on_deleted_at", using: :btree
  add_index "mkp_shops", ["network", "slug"], name: "index_mkp_shops_on_network_slug", unique: true, using: :btree
  add_index "mkp_shops", ["title"], name: "idx_mkp_shops_title", using: :btree

  create_table "mkp_stores", force: :cascade do |t|
    t.string   "name",                             limit: 255
    t.string   "token",                            limit: 255
    t.string   "title",                            limit: 255
    t.boolean  "active"
    t.string   "logo",                             limit: 255
    t.string   "gateway",                          limit: 255,                            default: "decidir"
    t.datetime "created_at",                                                                                  null: false
    t.datetime "updated_at",                                                                                  null: false
    t.string   "email",                            limit: 255
    t.boolean  "inherit_free_shipping",                                                   default: true
    t.string   "hostname",                         limit: 255
    t.integer  "percentage_fee",                   limit: 4,                              default: 0
    t.string   "image_dues_file_name",             limit: 255
    t.string   "image_dues_content_type",          limit: 255
    t.integer  "image_dues_file_size",             limit: 4
    t.datetime "image_dues_updated_at"
    t.string   "facebook",                         limit: 255
    t.string   "instagram",                        limit: 255
    t.string   "twitter",                          limit: 255
    t.string   "telephone",                        limit: 255
    t.string   "popup_image",                      limit: 255
    t.boolean  "invoiceable",                                                             default: false
    t.text     "preferred_sorting",                limit: 65535
    t.boolean  "abandoned_cart_email",                                                    default: false
    t.boolean  "production",                                                              default: true
    t.decimal  "equivalent_points",                              precision: 10, scale: 2, default: 0.0
    t.decimal  "visa_puntos_equivalence",                        precision: 15, scale: 6
    t.string   "visa_puntos_api_key",              limit: 255
    t.decimal  "visa_puntos_sube_equivalence",                   precision: 15, scale: 6
    t.decimal  "visa_puntos_recargas_equivalence",               precision: 15, scale: 6
    t.boolean  "product_approval",                                                        default: false
    t.boolean  "allow_orders_cancellation",                                               default: true
    t.boolean  "use_bines"
    t.boolean  "allow_meli_integration",                                                  default: false
  end

  add_index "mkp_stores", ["title"], name: "idx_mkp_stores_title", using: :btree

  create_table "mkp_suborders", force: :cascade do |t|
    t.decimal  "coupon_discount",                             precision: 8, scale: 2
    t.integer  "coupon_id",                     limit: 4
    t.integer  "order_id",                      limit: 4
    t.binary   "purchase_id",                   limit: 255
    t.integer  "shop_id",                       limit: 4
    t.decimal  "taxes",                                       precision: 8, scale: 2, default: 0.0
    t.string   "title",                         limit: 255
    t.datetime "created_at",                                                                          null: false
    t.datetime "updated_at",                                                                          null: false
    t.boolean  "fulfilled_by_gp",                                                     default: false
    t.boolean  "refunded",                                                            default: false
    t.text     "data",                          limit: 65535
    t.decimal  "shop_commission",                             precision: 5, scale: 2
    t.boolean  "external_redemption_completed"
    t.integer  "office_id",                     limit: 4
  end

  add_index "mkp_suborders", ["order_id"], name: "idx_mkp_suborders_order_id", using: :btree

  create_table "mkp_variant_costs", force: :cascade do |t|
    t.decimal  "cost",                 precision: 10, scale: 2, null: false
    t.datetime "date_from",                                     null: false
    t.datetime "date_to",                                       null: false
    t.integer  "variant_id", limit: 4,                          null: false
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
  end

  create_table "mkp_variants", force: :cascade do |t|
    t.integer  "product_id",         limit: 4
    t.string   "sku",                limit: 255
    t.string   "gp_sku",             limit: 255
    t.string   "ean_code",           limit: 255
    t.integer  "quantity",           limit: 4
    t.integer  "reserved_quantity",  limit: 4,                              default: 0
    t.datetime "deleted_at"
    t.integer  "picture_id",         limit: 4
    t.datetime "created_at",                                                              null: false
    t.datetime "updated_at",                                                              null: false
    t.boolean  "visible"
    t.text     "properties",         limit: 65535
    t.integer  "shop_id",            limit: 4
    t.decimal  "availability_score",               precision: 10, scale: 6, default: 1.0
    t.integer  "points_price",       limit: 4
    t.integer  "discount_top",       limit: 4
  end

  add_index "mkp_variants", ["deleted_at"], name: "idx_mkp_variants_deleted_at", using: :btree
  add_index "mkp_variants", ["gp_sku"], name: "index_mkp_variants_on_gp_sku", using: :btree
  add_index "mkp_variants", ["product_id"], name: "product_variants_on_product_id_ix", using: :btree
  add_index "mkp_variants", ["sku"], name: "product_variants_on_sku_ix", using: :btree

  create_table "mkp_variants_wishlists", id: false, force: :cascade do |t|
    t.integer "wishlist_id", limit: 4
    t.integer "variant_id",  limit: 4
  end

  add_index "mkp_variants_wishlists", ["variant_id"], name: "index_mkp_variants_wishlists_on_variant_id", using: :btree
  add_index "mkp_variants_wishlists", ["wishlist_id"], name: "index_mkp_variants_wishlists_on_wishlist_id", using: :btree

  create_table "mkp_votes", force: :cascade do |t|
    t.integer  "product_id", limit: 4
    t.integer  "review_id",  limit: 4
    t.integer  "user_id",    limit: 4
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
  end

  create_table "mkp_wishlists", force: :cascade do |t|
    t.integer  "customer_id", limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "mkp_wishlists", ["customer_id"], name: "index_mkp_wishlists_on_customer_id", using: :btree

  create_table "mkp_zones", force: :cascade do |t|
    t.string   "title",        limit: 255
    t.text     "countries",    limit: 65535
    t.integer  "warehouse_id", limit: 4
    t.integer  "shop_id",      limit: 4
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.datetime "deleted_at"
  end

  create_table "notification_callbacks", force: :cascade do |t|
    t.string   "integration_name", limit: 255
    t.string   "external_id",      limit: 255
    t.integer  "shop_id",          limit: 4
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  create_table "notifications", force: :cascade do |t|
    t.integer  "user_id",    limit: 4
    t.integer  "actor_id",   limit: 4
    t.string   "type",       limit: 255
    t.text     "event",      limit: 65535
    t.boolean  "read",                     default: false
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
  end

  add_index "notifications", ["user_id"], name: "index_notifications_on_user_id", using: :btree

  create_table "ocasa_zip_codes", force: :cascade do |t|
    t.integer  "cp",         limit: 4
    t.string   "province",   limit: 255
    t.string   "location",   limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "partial_signups", force: :cascade do |t|
    t.string   "email",               limit: 255
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.string   "first_name",          limit: 255
    t.string   "last_name",           limit: 255
    t.string   "brand_name",          limit: 255
    t.string   "brand_kind",          limit: 255
    t.string   "brand_contact_name",  limit: 255
    t.string   "brand_contact_title", limit: 255
  end

  create_table "payment_programs", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "payments", force: :cascade do |t|
    t.integer  "status",            limit: 4
    t.decimal  "amount",                          precision: 10
    t.datetime "collected_at"
    t.string   "gateway",           limit: 255
    t.text     "gateway_data",      limit: 65535
    t.string   "gateway_object_id", limit: 255
    t.string   "payment_method",    limit: 255
    t.integer  "subscription_id",   limit: 4
    t.datetime "created_at",                                                     null: false
    t.datetime "updated_at",                                                     null: false
    t.boolean  "recurrent",                                      default: false
    t.string   "period",            limit: 255
    t.text     "error",             limit: 65535
    t.boolean  "billable",                                       default: true
  end

  add_index "payments", ["subscription_id"], name: "index_payments_on_subscription_id", using: :btree

  create_table "pictures", force: :cascade do |t|
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size",    limit: 4
    t.datetime "photo_updated_at"
    t.string   "token",              limit: 255
    t.string   "type",               limit: 255
    t.string   "ip",                 limit: 255
    t.integer  "user_id",            limit: 4
    t.integer  "owner_id",           limit: 4
    t.string   "owner_type",         limit: 255
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.string   "position",           limit: 255
    t.boolean  "processing",                       default: false
    t.text     "description",        limit: 65535
  end

  add_index "pictures", ["type", "owner_id"], name: "index_pictures_on_type_and_owner_id", using: :btree
  add_index "pictures", ["user_id"], name: "index_pictures_on_user_id", using: :btree

  create_table "pioneer_admin_roles", force: :cascade do |t|
    t.integer  "admin_id",   limit: 4
    t.integer  "role_id",    limit: 4
    t.integer  "store_id",   limit: 4
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
  end

  create_table "pioneer_admin_stores", force: :cascade do |t|
    t.integer "admin_id", limit: 4
    t.integer "store_id", limit: 4
  end

  add_index "pioneer_admin_stores", ["admin_id"], name: "index_pioneer_admin_stores_on_admin_id", using: :btree
  add_index "pioneer_admin_stores", ["store_id"], name: "index_pioneer_admin_stores_on_store_id", using: :btree

  create_table "pioneer_admins", force: :cascade do |t|
    t.string   "first_name",          limit: 255
    t.string   "last_name",           limit: 255
    t.string   "crypted_password",    limit: 255,                 null: false
    t.string   "password_salt",       limit: 255,                 null: false
    t.string   "email",               limit: 255,                 null: false
    t.string   "persistence_token",   limit: 255,                 null: false
    t.string   "single_access_token", limit: 255,                 null: false
    t.string   "perishable_token",    limit: 255,                 null: false
    t.integer  "login_count",         limit: 4,   default: 0,     null: false
    t.integer  "failed_login_count",  limit: 4,   default: 0,     null: false
    t.datetime "last_request_at"
    t.datetime "current_login_at"
    t.datetime "last_login_at"
    t.string   "current_login_ip",    limit: 255
    t.string   "last_login_ip",       limit: 255
    t.string   "network",             limit: 255,                 null: false
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.boolean  "galilei",                         default: false
    t.boolean  "be_notified",                     default: false
    t.integer  "role_id",             limit: 4
  end

  create_table "pioneer_blacklists", force: :cascade do |t|
    t.integer  "store_id",   limit: 4
    t.string   "doc_number", limit: 255
    t.string   "doc_type",   limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "pioneer_blacklists", ["store_id"], name: "index_pioneer_blacklists_on_store_id", using: :btree

  create_table "pioneer_permissions", force: :cascade do |t|
    t.string  "section", limit: 255
    t.integer "access",  limit: 4
    t.integer "role_id", limit: 4
  end

  add_index "pioneer_permissions", ["role_id"], name: "index_pioneer_permissions_on_role_id", using: :btree

  create_table "pioneer_roles", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.integer  "store_id",   limit: 4
    t.integer  "office_id",  limit: 4
  end

  create_table "products_users", force: :cascade do |t|
    t.integer "product_id", limit: 4
    t.integer "user_id",    limit: 4
  end

  add_index "products_users", ["product_id", "user_id"], name: "index_products_users_on_product_id_and_user_id", using: :btree

  create_table "profiles", force: :cascade do |t|
    t.string   "first_name",         limit: 255
    t.string   "last_name",          limit: 255
    t.string   "bio",                limit: 255
    t.string   "language",           limit: 255
    t.integer  "user_id",            limit: 4
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.text     "followed_sport_ids", limit: 65535
  end

  add_index "profiles", ["user_id"], name: "index_profiles_on_user_id", using: :btree

  create_table "purchases", force: :cascade do |t|
    t.integer  "store_id",                limit: 4
    t.integer  "payment_id",              limit: 4
    t.integer  "points",                  limit: 4
    t.integer  "miles",                   limit: 4
    t.string   "id_cobis",                limit: 255
    t.string   "document_type",           limit: 255
    t.string   "document_number",         limit: 255
    t.string   "email",                   limit: 255
    t.string   "status",                  limit: 255
    t.datetime "created_at",                                                   null: false
    t.datetime "updated_at",                                                   null: false
    t.string   "external_transaction_id", limit: 255
    t.decimal  "amount",                              precision: 10
    t.string   "gateway",                 limit: 255
    t.decimal  "point_equivalent",                    precision: 15, scale: 6
    t.integer  "customer_id",             limit: 4
    t.string   "customer_type",           limit: 255
    t.string   "target_item",             limit: 255
    t.string   "full_name",               limit: 255
  end

  add_index "purchases", ["payment_id"], name: "index_purchases_on_payment_id", using: :btree
  add_index "purchases", ["store_id"], name: "index_purchases_on_store_id", using: :btree

  create_table "pyp_imported_products", force: :cascade do |t|
    t.string   "sku",        limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "renaper_answers", force: :cascade do |t|
    t.string   "question_type",  limit: 25
    t.integer  "question_id",    limit: 4
    t.string   "doc_number",     limit: 25
    t.string   "gender",         limit: 1
    t.string   "question",       limit: 255
    t.string   "correct_answer", limit: 255
    t.string   "purchase_id",    limit: 255
    t.boolean  "answered"
    t.string   "token",          limit: 30
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.integer  "store_id",       limit: 4
  end

  add_index "renaper_answers", ["purchase_id"], name: "index_renaper_answers_on_purchase_id", using: :btree
  add_index "renaper_answers", ["store_id"], name: "index_renaper_answers_on_store_id", using: :btree

  create_table "renaper_configurations", force: :cascade do |t|
    t.integer  "store_id",             limit: 4
    t.boolean  "active"
    t.boolean  "show_question_always"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  add_index "renaper_configurations", ["store_id"], name: "index_renaper_configurations_on_store_id", using: :btree

  create_table "renaper_failed_services", force: :cascade do |t|
    t.integer  "store_id",    limit: 4
    t.string   "purchase_id", limit: 255
    t.string   "doc_type",    limit: 10
    t.string   "doc_number",  limit: 255
    t.string   "description", limit: 255
    t.datetime "deleted_at"
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  add_index "renaper_failed_services", ["deleted_at"], name: "index_renaper_failed_services_on_deleted_at", using: :btree
  add_index "renaper_failed_services", ["store_id"], name: "index_renaper_failed_services_on_store_id", using: :btree

  create_table "services", force: :cascade do |t|
    t.integer  "subscription_id", limit: 4
    t.datetime "created_at",                                                         null: false
    t.datetime "updated_at",                                                         null: false
    t.string   "name",            limit: 255
    t.decimal  "amount",                      precision: 10, scale: 2, default: 0.0
    t.integer  "points",          limit: 4,                            default: 0
  end

  add_index "services", ["subscription_id"], name: "index_services_on_subscription_id", using: :btree

  create_table "sessions", force: :cascade do |t|
    t.string   "session_id", limit: 255,   null: false
    t.text     "data",       limit: 65535
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
  end

  add_index "sessions", ["session_id"], name: "index_sessions_on_session_id", using: :btree
  add_index "sessions", ["updated_at"], name: "index_sessions_on_updated_at", using: :btree

  create_table "setting_coupons", force: :cascade do |t|
    t.integer  "minimum_value",   limit: 4
    t.integer  "percent",         limit: 4
    t.text     "description",     limit: 65535
    t.datetime "starts_at"
    t.datetime "expires_at"
    t.integer  "total_available", limit: 4
    t.text     "restrictions",    limit: 65535
    t.text     "policy",          limit: 65535
    t.boolean  "apply_on_sale"
    t.integer  "discount_limit",  limit: 4
    t.integer  "voucher_id",      limit: 4
    t.datetime "created_at",                                                          null: false
    t.datetime "updated_at",                                                          null: false
    t.decimal  "amount",                        precision: 8, scale: 2, default: 0.0
  end

  add_index "setting_coupons", ["voucher_id"], name: "index_setting_coupons_on_voucher_id", using: :btree

  create_table "settings", force: :cascade do |t|
    t.string   "key",         limit: 255
    t.string   "value",       limit: 255
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
    t.boolean  "only_ticket",             default: false
  end

  create_table "sport_taggings", force: :cascade do |t|
    t.integer "sport_id",            limit: 4,   null: false
    t.integer "sport_taggable_id",   limit: 4,   null: false
    t.string  "sport_taggable_type", limit: 255, null: false
  end

  create_table "sports", force: :cascade do |t|
    t.string   "name",          limit: 70,    null: false
    t.string   "slug",          limit: 255,   null: false
    t.integer  "position",      limit: 4
    t.string   "ancestry",      limit: 255
    t.integer  "display_order", limit: 4
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.text     "description",   limit: 65535
  end

  add_index "sports", ["ancestry"], name: "index_sports_on_ancestry", using: :btree
  add_index "sports", ["slug"], name: "index_sports_on_slug", using: :btree

  create_table "sports_on_products", id: false, force: :cascade do |t|
    t.integer "sport_id",   limit: 4, null: false
    t.integer "product_id", limit: 4, null: false
  end

  add_index "sports_on_products", ["sport_id", "product_id"], name: "sports_on_products_ix", unique: true, using: :btree

  create_table "spots", force: :cascade do |t|
    t.string   "title",       limit: 255
    t.text     "description", limit: 65535
    t.string   "latitude",    limit: 255
    t.string   "longitude",   limit: 255
    t.string   "map_type",    limit: 255
    t.integer  "user_id",     limit: 4
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
    t.string   "slug",        limit: 255
    t.text     "sport_ids",   limit: 65535
    t.string   "ip",          limit: 255
  end

  add_index "spots", ["slug"], name: "index_spots_on_slug", unique: true, using: :btree

  create_table "sube_cards", force: :cascade do |t|
    t.string   "number",          limit: 255
    t.string   "alias",           limit: 255
    t.integer  "status",          limit: 4,   default: 0
    t.integer  "mkp_customer_id", limit: 4
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
  end

  add_index "sube_cards", ["mkp_customer_id"], name: "index_sube_cards_on_mkp_customer_id", using: :btree

  create_table "subscriptions", force: :cascade do |t|
    t.integer  "customer_id", limit: 4
    t.datetime "created_at",            null: false
    t.datetime "updated_at",            null: false
    t.integer  "service_id",  limit: 4
  end

  add_index "subscriptions", ["customer_id"], name: "index_subscriptions_on_customer_id", using: :btree

  create_table "suggestions", force: :cascade do |t|
    t.string   "term",       limit: 255
    t.integer  "score",      limit: 4
    t.text     "data",       limit: 65535
    t.string   "network",    limit: 255
    t.boolean  "active",                   default: false
    t.integer  "count",      limit: 4,     default: 1
    t.string   "updated_by", limit: 255
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
  end

  create_table "supplier_invoices", force: :cascade do |t|
    t.string   "external_id",            limit: 255
    t.datetime "external_date"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.integer  "store_id",               limit: 4
    t.integer  "shop_id",                limit: 4
    t.string   "invoice_status",         limit: 255
    t.string   "invoice_status_history", limit: 255
    t.string   "invoice_number",         limit: 255
    t.string   "shop_ids",               limit: 255
    t.string   "order_number",           limit: 255
  end

  create_table "supplier_stocks", force: :cascade do |t|
    t.integer  "store_id",       limit: 4
    t.integer  "supplier_id",    limit: 4
    t.float    "stock",          limit: 24
    t.float    "consumed_stock", limit: 24
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "suppliers", force: :cascade do |t|
    t.string   "name",         limit: 255
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.string   "slug",         limit: 255
    t.boolean  "manage_stock"
    t.integer  "store_id",     limit: 4
    t.string   "unit",         limit: 255
    t.integer  "shop_id",      limit: 4
  end

  create_table "system_communications", force: :cascade do |t|
    t.integer  "customer_id",    limit: 4
    t.string   "customer_type",  limit: 255
    t.string   "type",           limit: 255
    t.text     "data",           limit: 65535
    t.datetime "notified_at"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.string   "network",        limit: 255
    t.string   "customer_email", limit: 255
  end

  add_index "system_communications", ["customer_id", "customer_type"], name: "index_system_communications_on_customer_id_and_customer_type", using: :btree
  add_index "system_communications", ["id", "type"], name: "index_system_communications_on_id_and_type", using: :btree

  create_table "tdd_report_rows", force: :cascade do |t|
    t.integer  "tdd_report_id",    limit: 4
    t.string   "period",           limit: 255
    t.integer  "payments_qty",     limit: 4
    t.integer  "total_amount",     limit: 4
    t.integer  "member_id",        limit: 4
    t.string   "feedback_message", limit: 255
    t.string   "feedback_code",    limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "tdd_report_rows", ["tdd_report_id"], name: "index_tdd_report_rows_on_tdd_report_id", using: :btree

  create_table "tdd_reports", force: :cascade do |t|
    t.string   "card",        limit: 255
    t.string   "card_type",   limit: 255
    t.integer  "report_type", limit: 4
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "url",         limit: 255
  end

  create_table "third_party_codes", force: :cascade do |t|
    t.integer "store_id",        limit: 4
    t.string  "code",            limit: 255
    t.boolean "available",                   default: true
    t.integer "manufacturer_id", limit: 4
    t.boolean "used",                        default: false
  end

  create_table "users", force: :cascade do |t|
    t.string   "login",                   limit: 255,                null: false
    t.string   "email",                   limit: 255,   default: "", null: false
    t.string   "crypted_password",        limit: 255,                null: false
    t.string   "password_salt",           limit: 255,                null: false
    t.integer  "roles_mask",              limit: 4
    t.string   "persistence_token",       limit: 255,                null: false
    t.integer  "login_count",             limit: 4,     default: 0,  null: false
    t.datetime "last_request_at"
    t.datetime "last_login_at"
    t.datetime "current_login_at"
    t.string   "last_login_ip",           limit: 255
    t.string   "current_login_ip",        limit: 255
    t.string   "perishable_token",        limit: 255,   default: "", null: false
    t.string   "network",                 limit: 255
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "type",                    limit: 255
    t.integer  "braintree_customer_id",   limit: 4
    t.text     "braintree_data",          limit: 65535
    t.integer  "followers_count",         limit: 4,     default: 0
    t.integer  "mercadopago_customer_id", limit: 4
    t.text     "mercadopago_data",        limit: 65535
  end

  add_index "users", ["email"], name: "index_users_on_email", using: :btree
  add_index "users", ["last_request_at"], name: "index_users_on_last_request_at", using: :btree
  add_index "users", ["login"], name: "index_users_on_login", using: :btree
  add_index "users", ["perishable_token"], name: "index_users_on_perishable_token", using: :btree
  add_index "users", ["persistence_token"], name: "index_users_on_persistence_token", using: :btree
  add_index "users", ["type"], name: "index_users_on_type", using: :btree

  create_table "versions", force: :cascade do |t|
    t.string   "item_type",      limit: 191,        null: false
    t.integer  "item_id",        limit: 4,          null: false
    t.string   "event",          limit: 255,        null: false
    t.string   "whodunnit",      limit: 255
    t.text     "object",         limit: 4294967295
    t.datetime "created_at"
    t.text     "object_changes", limit: 4294967295
  end

  add_index "versions", ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id", using: :btree

  create_table "videos", force: :cascade do |t|
    t.string   "key",        limit: 255
    t.integer  "whatsup_id", limit: 4
    t.datetime "created_at"
  end

  create_table "vouchers", force: :cascade do |t|
    t.integer "product_id", limit: 4
    t.integer "supplier",   limit: 4
  end

  add_index "vouchers", ["product_id"], name: "index_vouchers_on_product_id", using: :btree

  create_table "weasel_events", force: :cascade do |t|
    t.integer  "actor_id",    limit: 4
    t.string   "actor_type",  limit: 255
    t.text     "action_data", limit: 65535
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "whatsups", force: :cascade do |t|
    t.string   "body",        limit: 255
    t.integer  "author_id",   limit: 4,     null: false
    t.integer  "target_id",   limit: 4,     null: false
    t.string   "target_type", limit: 255,   null: false
    t.datetime "created_at"
    t.text     "metadata",    limit: 65535
    t.string   "ip",          limit: 255
    t.datetime "updated_at"
  end

  add_index "whatsups", ["author_id"], name: "index_whatsups_on_author_id", using: :btree

  create_table "whitelist_configurations", force: :cascade do |t|
    t.integer  "store_id",                  limit: 4
    t.boolean  "active"
    t.string   "validation_strategy",       limit: 255
    t.string   "user_strategy",             limit: 255
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.string   "unauthorized_user_message", limit: 255
  end

  add_index "whitelist_configurations", ["store_id"], name: "index_whitelist_configurations_on_store_id", using: :btree

  create_table "withdrawal_branches", force: :cascade do |t|
    t.string   "id_centro_imposicion", limit: 255
    t.string   "sigla",                limit: 255
    t.string   "sucursal",             limit: 255
    t.string   "calle",                limit: 255
    t.string   "numero",               limit: 255
    t.string   "torre",                limit: 255
    t.string   "piso",                 limit: 255
    t.string   "depto",                limit: 255
    t.string   "localidad",            limit: 255
    t.string   "codigo_postal",        limit: 255
    t.string   "provincia",            limit: 255
    t.string   "telefono",             limit: 255
    t.string   "latitud",              limit: 255
    t.string   "longitud",             limit: 255
    t.string   "tipo_agencia",         limit: 255
    t.string   "horario_atencion",     limit: 255
    t.string   "sucursal_oca",         limit: 255
    t.text     "servicios",            limit: 65535
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
  end

  add_foreign_key "attempted_answers", "mkp_stores", column: "store_id"
  add_foreign_key "attempted_answers", "pioneer_admins"
  add_foreign_key "equifax_id_validator_configurations", "mkp_stores", column: "store_id"
  add_foreign_key "gateway_alternative_strategies", "mkp_stores", column: "store_id"
  add_foreign_key "installments", "services"
  add_foreign_key "insurance_configurations", "mkp_stores", column: "store_id"
  add_foreign_key "mkp_payment_credentials", "mkp_shops", column: "shop_id"
  add_foreign_key "mkp_payment_credentials", "mkp_stores", column: "store_id"
  add_foreign_key "mkp_pictures", "mkp_variants", column: "variant_id"
  add_foreign_key "pioneer_blacklists", "mkp_stores", column: "store_id"
  add_foreign_key "renaper_answers", "mkp_stores", column: "store_id"
  add_foreign_key "renaper_configurations", "mkp_stores", column: "store_id"
  add_foreign_key "renaper_failed_services", "mkp_stores", column: "store_id"
  add_foreign_key "services", "subscriptions"
  add_foreign_key "setting_coupons", "vouchers"
  add_foreign_key "sube_cards", "mkp_customers"

  create_view "lstble_sale_items", sql_definition: <<-SQL
      select `mkp_orders`.`id` AS `listable_id`,`mkp_orders`.`store_id` AS `store_id`,`mkp_orders`.`customer_id` AS `customer_id`,(`mkp_orders`.`customer_type` collate utf8_unicode_ci) AS `customer_type`,(`mkp_orders`.`id_cobis` collate utf8_unicode_ci) AS `id_cobis`,`mkp_orders`.`created_at` AS `created_at`,`mkp_orders`.`updated_at` AS `updated_at`,(cast('Mkp::Order' as char(10) charset utf8) collate utf8_unicode_ci) AS `listable_type` from `mkp_orders` union select `purchases`.`id` AS `listable_id`,`purchases`.`store_id` AS `store_id`,`purchases`.`customer_id` AS `customer_id`,(`purchases`.`customer_type` collate utf8_unicode_ci) AS `customer_type`,(`purchases`.`id_cobis` collate utf8_unicode_ci) AS `id_cobis`,`purchases`.`created_at` AS `created_at`,`purchases`.`updated_at` AS `updated_at`,(cast('Purchase' as char(8) charset utf8) collate utf8_unicode_ci) AS `listable_type` from `purchases`
  SQL
  create_view "mkp_last_shipment_statuses", sql_definition: <<-SQL
      select `mkp_shipments`.`id` AS `id`,`mkp_shipments`.`suborder_id` AS `suborder_id`,`mkp_shipments`.`status` AS `status` from `mkp_shipments` where (`mkp_shipments`.`suborder_id`,`mkp_shipments`.`created_at`) in (select `mkp_shipments`.`suborder_id` AS `id`,max(`mkp_shipments`.`created_at`) AS `max_date` from `mkp_shipments` group by `mkp_shipments`.`suborder_id`)
  SQL
  create_view "v_bna_mm_reservas", sql_definition: <<-SQL
      select (`mkp_order_items`.`created_at` - interval 3 hour) AS `created_at`,`mkp_bna_offices`.`token` AS `office`,`mkp_addresses`.`state` AS `state`,`mkp_shops`.`title` AS `shop_title`,`mkp_orders`.`id` AS `order_id`,`mkp_bna_info_packages`.`solicitude_id` AS `solicitude_id`,`mkp_order_items`.`updated_at` AS `updated_as`,replace(`mkp_guests`.`first_name`,',','') AS `first_name`,`mkp_guests`.`last_name` AS `last_name`,`mkp_guests`.`email` AS `email`,`mkp_guests`.`doc_number` AS `cuil`,`mkp_guests`.`telephone` AS `tel`,`mkp_suborders`.`title` AS `product_title`,(case when (`mkp_order_items`.`status` = 'approved') then 'aprobado' when (`mkp_order_items`.`status` = 'booked') then 'reservado' when (`mkp_order_items`.`status` = 'declined') then 'no_aprobado' when (`mkp_order_items`.`status` = 'delivered') then 'entregado' when (`mkp_order_items`.`status` = 'billed') then 'facturado' when (`mkp_order_items`.`status` = 'posted') then 'contabilizado' when (`mkp_order_items`.`status` = 'cancelled') then 'cancelado' when (`mkp_order_items`.`status` = 'pending_cancellation') then 'pendiente_cancelacion' else 'otros' end) AS `status`,format(`mkp_order_items`.`price`,2) AS `price` from (((((((`mkp_order_items` join `mkp_suborders` on((`mkp_suborders`.`id` = `mkp_order_items`.`suborder_id`))) join `mkp_orders` on((`mkp_orders`.`id` = `mkp_suborders`.`order_id`))) join `mkp_shops` on((`mkp_shops`.`id` = `mkp_suborders`.`shop_id`))) left join `mkp_bna_offices` on((`mkp_bna_offices`.`id` = `mkp_suborders`.`office_id`))) join `mkp_addresses` on((`mkp_addresses`.`addressable_id` = `mkp_suborders`.`shop_id`))) join `mkp_guests` on((`mkp_guests`.`id` = `mkp_orders`.`customer_id`))) join `mkp_bna_info_packages` on((`mkp_bna_info_packages`.`order_id` = `mkp_orders`.`id`))) where ((`mkp_orders`.`created_at` > '2020-10-01 00:00:00') and (`mkp_orders`.`store_id` = '43') and (`mkp_suborders`.`shop_id` not in ('1278','1553','1489')) and (`mkp_suborders`.`office_id` is not null) and (`mkp_addresses`.`addressable_type` = 'Mkp::Shop') and (`mkp_bna_info_packages`.`order_id` > 1)) group by `mkp_orders`.`id`
  SQL
  create_view "v_bna_mm_reservas_v2", sql_definition: <<-SQL
      select date_format((`mkp_orders`.`created_at` - interval 3 hour),'%Y-%m-%d %H:%i:%S') AS `created_at`,date_format((`mkp_orders`.`updated_at` - interval 3 hour),'%Y-%m-%d %H:%i:%S') AS `updated_at`,`mkp_bna_offices`.`token` AS `office`,`mkp_addresses`.`state` AS `state`,`mkp_shops`.`title` AS `shop_title`,`mkp_orders`.`id` AS `order_id`,`mkp_bna_info_packages`.`solicitude_id` AS `solicitude_id`,date_format((`mkp_order_items`.`updated_at` - interval 3 hour),'%Y-%m-%d %H:%i:%S') AS `updated_as`,replace(`mkp_guests`.`first_name`,',','') AS `first_name`,`mkp_guests`.`last_name` AS `last_name`,`mkp_guests`.`email` AS `email`,`mkp_guests`.`doc_number` AS `cuil`,`mkp_guests`.`telephone` AS `tel`,`mkp_bna_info_packages`.`program_type` AS `program_type`,`mkp_bna_info_packages`.`client_profile` AS `client_profile`,`mkp_suborders`.`title` AS `product_title`,`mkp_order_items`.`status` AS `status`,`mkp_order_items`.`price` AS `price` from (((((((`mkp_order_items` join `mkp_suborders` on((`mkp_suborders`.`id` = `mkp_order_items`.`suborder_id`))) join `mkp_orders` on((`mkp_orders`.`id` = `mkp_suborders`.`order_id`))) join `mkp_shops` on((`mkp_shops`.`id` = `mkp_suborders`.`shop_id`))) left join `mkp_bna_offices` on((`mkp_bna_offices`.`id` = `mkp_suborders`.`office_id`))) join `mkp_addresses` on((`mkp_addresses`.`addressable_id` = `mkp_suborders`.`shop_id`))) join `mkp_guests` on((`mkp_guests`.`id` = `mkp_orders`.`customer_id`))) join `mkp_bna_info_packages` on((`mkp_bna_info_packages`.`order_id` = `mkp_orders`.`id`))) where ((`mkp_orders`.`created_at` > '2020-10-01 00:00:00') and (`mkp_orders`.`store_id` = '43') and (`mkp_suborders`.`shop_id` not in ('1278','1553','1489')) and (`mkp_suborders`.`office_id` is not null) and (`mkp_addresses`.`addressable_type` = 'Mkp::Shop') and (`mkp_bna_info_packages`.`order_id` > 1)) group by `mkp_orders`.`id`
  SQL
  create_view "v_bna_mm_stock", sql_definition: <<-SQL
      select `mkp_variants`.`shop_id` AS `shop_id`,`mkp_shops`.`title` AS `shop_title`,`mkp_addresses`.`state` AS `state`,`mkp_products`.`id` AS `product_id`,`mkp_products`.`title` AS `product_title`,`mkp_variants`.`gp_sku` AS `sku`,`mkp_variants`.`visible` AS `visible`,sum(`mkp_variants`.`quantity`) AS `stock` from ((((`mkp_variants` join `mkp_products` on((`mkp_variants`.`product_id` = `mkp_products`.`id`))) join `mkp_shops` on((`mkp_variants`.`shop_id` = `mkp_shops`.`id`))) join `mkp_addresses` on((`mkp_addresses`.`addressable_id` = `mkp_shops`.`id`))) join `mkp_shop_stores` on((`mkp_shop_stores`.`shop_id` = `mkp_variants`.`shop_id`))) where ((`mkp_variants`.`deleted_at` is null) and (`mkp_variants`.`shop_id` not in ('1278','1553','1489')) and (`mkp_shop_stores`.`store_id` = 43) and (`mkp_products`.`deleted_at` is null) and (`mkp_addresses`.`addressable_type` = 'Mkp::Shop')) group by `mkp_variants`.`shop_id`,`mkp_shops`.`title`,`mkp_products`.`title`,`mkp_variants`.`gp_sku`,`mkp_variants`.`visible`
  SQL
  create_view "v_bna_orders", sql_definition: <<-SQL
      select (`mkp_orders`.`created_at` - interval 3 hour) AS `created_at`,`mkp_orders`.`id` AS `order_id`,`mkp_suborders`.`id` AS `suborder_id`,`mkp_shops`.`title` AS `shop_title`,`mkp_categories`.`name` AS `category`,`mkp_products`.`title` AS `item`,`mkp_order_items`.`quantity` AS `quantity`,`mkp_payments`.`status` AS `payment_status`,`mkp_payments`.`installments` AS `payment_installments`,substring_index(substring_index(`mkp_shipments`.`destination_address`,':state: ',-(1)),':country: ',1) AS `state`,`mkp_order_items`.`status` AS `shipment_status`,format(`mkp_shipments`.`charged_amount`,2) AS `shimpent_charged_amount`,format(`mkp_order_items`.`price`,2) AS `order_item_price`,format(`mkp_order_items`.`sale_price`,2) AS `order_item_sale_price`,format(`mkp_payments`.`collected_amount`,2) AS `payment_collected_amount` from ((((((((`mkp_orders` join `mkp_stores` on((`mkp_stores`.`id` = `mkp_orders`.`store_id`))) join `mkp_payments` on((`mkp_payments`.`sale_item_id` = `mkp_orders`.`id`))) join `mkp_suborders` on((`mkp_suborders`.`order_id` = `mkp_orders`.`id`))) join `mkp_shops` on((`mkp_shops`.`id` = `mkp_suborders`.`shop_id`))) join `mkp_order_items` on((`mkp_order_items`.`suborder_id` = `mkp_suborders`.`id`))) join `mkp_shipments` on((`mkp_shipments`.`suborder_id` = `mkp_suborders`.`id`))) join `mkp_products` on((`mkp_products`.`id` = `mkp_order_items`.`product_id`))) join `mkp_categories` on((`mkp_categories`.`id` = `mkp_products`.`category_id`))) where ((`mkp_orders`.`created_at` > '2020-12-01 03:00:00') and (`mkp_stores`.`id` = '41') and (`mkp_payments`.`sale_item_type` = 'Mkp::Order') and (`mkp_payments`.`status` in ('cancelled','refunded','collected','pending')) and (`mkp_order_items`.`status` in ('shipped','available','cancelled','delivered','unfulfilled')))
  SQL
  create_view "v_bna_orders_v2", sql_definition: <<-SQL
      select (`mkp_orders`.`created_at` - interval 3 hour) AS `created_at`,(`mkp_orders`.`updated_at` - interval 3 hour) AS `updated_at`,`mkp_orders`.`id` AS `order_id`,`mkp_suborders`.`id` AS `suborder_id`,`mkp_shops`.`title` AS `shop_title`,`mkp_categories`.`name` AS `category`,`mkp_products`.`title` AS `item`,`mkp_order_items`.`quantity` AS `quantity`,`mkp_payments`.`status` AS `payment_status`,(case when (`bines`.`brand` = 0) then 'AMEX' when (`bines`.`brand` = 1) then 'CABAL' when (`bines`.`brand` = 2) then 'CABAL DÉBITO' when (`bines`.`brand` = 3) then 'CREDIMAS' when (`bines`.`brand` = 4) then 'DINERS' when (`bines`.`brand` = 5) then 'MAESTRO' when (`bines`.`brand` = 6) then 'MASTERCARD' when (`bines`.`brand` = 7) then 'NARANJA' when (`bines`.`brand` = 8) then 'VISA' when (`bines`.`brand` = 9) then 'VISA DÉBITO' when (`bines`.`brand` = 10) then 'VISA RECARGABLE' when (`bines`.`brand` = 11) then 'MASTERCARD DEBITO' when (`bines`.`brand` = 10) then 'NATIVA' end) AS `payment_brand`,`mkp_payments`.`installments` AS `payment_installments`,concat(`mkp_guests`.`first_name`,' ',`mkp_guests`.`last_name`) AS `customer_name`,`mkp_guests`.`email` AS `customer_email`,`mkp_payments`.`document_number` AS `payment_dni`,replace(substring_index(substring_index(`mkp_shipments`.`destination_address`,':state: ',-(1)),':country: ',1),'\n                        ','') AS `state`,`mkp_order_items`.`status` AS `shipment_status`,format(`mkp_shipments`.`charged_amount`,2) AS `shimpent_charged_amount`,if((`insurance_tokens`.`amount` is null),'No','Si') AS `insurance_status`,ifnull(`insurance_tokens`.`amount`,'-') AS `insurance_ammount`,ifnull(`insurance_tokens`.`token`,'-') AS `insurance_token`,`mkp_order_items`.`price` AS `order_item_price`,`mkp_order_items`.`sale_price` AS `order_item_sale_price`,`mkp_payments`.`collected_amount` AS `payment_collected_amount`,replace(replace(substring_index(substring_index(`mkp_payments`.`gateway_data`,'token: ',-(1)),'customer:',1),'\'',''),'\n                        ','') AS `id_decidir` from (((((((((((`mkp_orders` join `mkp_stores` on((`mkp_stores`.`id` = `mkp_orders`.`store_id`))) join `mkp_payments` on((`mkp_payments`.`sale_item_id` = `mkp_orders`.`id`))) join `mkp_suborders` on((`mkp_suborders`.`order_id` = `mkp_orders`.`id`))) join `mkp_shops` on((`mkp_shops`.`id` = `mkp_suborders`.`shop_id`))) join `mkp_order_items` on((`mkp_order_items`.`suborder_id` = `mkp_suborders`.`id`))) join `mkp_shipments` on((`mkp_shipments`.`suborder_id` = `mkp_suborders`.`id`))) join `mkp_products` on((`mkp_products`.`id` = `mkp_order_items`.`product_id`))) join `mkp_categories` on((`mkp_categories`.`id` = `mkp_products`.`category_id`))) join `mkp_guests` on((`mkp_guests`.`id` = `mkp_orders`.`customer_id`))) join `bines` on((`bines`.`number` = replace(replace(substring_index(substring_index(`mkp_payments`.`gateway_data`,'bin: ',-(1)),'amount:',1),'\'',''),'\n                ','')))) left join `insurance_tokens` on((`insurance_tokens`.`order_id` = `mkp_orders`.`id`))) where ((`mkp_orders`.`created_at` > '2020-12-01 03:00:00') and (`mkp_stores`.`id` = '41') and (`mkp_payments`.`sale_item_type` = 'Mkp::Order') and (`mkp_payments`.`status` in ('cancelled','refunded','collected','pending')) and (`mkp_order_items`.`status` in ('shipped','available','cancelled','delivered','unfulfilled')))
  SQL
  create_view "v_bna_stock", sql_definition: <<-SQL
      select `mkp_variants`.`shop_id` AS `shop_id`,`mkp_shops`.`title` AS `shop_title`,`mkp_categories`.`name` AS `categorie`,`mkp_products`.`id` AS `product_id`,`mkp_products`.`title` AS `product_title`,`mkp_variants`.`gp_sku` AS `sku`,`mkp_variants`.`visible` AS `visible`,sum(`mkp_variants`.`quantity`) AS `stock` from ((((`mkp_variants` join `mkp_products` on((`mkp_variants`.`product_id` = `mkp_products`.`id`))) join `mkp_shops` on((`mkp_variants`.`shop_id` = `mkp_shops`.`id`))) join `mkp_shop_stores` on((`mkp_shop_stores`.`shop_id` = `mkp_variants`.`shop_id`))) join `mkp_categories` on((`mkp_categories`.`id` = `mkp_products`.`category_id`))) where ((`mkp_variants`.`deleted_at` is null) and (`mkp_variants`.`shop_id` not in ('1278','1553','1489')) and (`mkp_shop_stores`.`store_id` = 41) and (`mkp_products`.`deleted_at` is null)) group by `mkp_variants`.`shop_id`,`mkp_shops`.`title`,`mkp_products`.`title`,`mkp_variants`.`gp_sku`,`mkp_variants`.`visible`
  SQL
end

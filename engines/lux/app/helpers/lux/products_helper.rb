module Lux
  module <PERSON>Helper
    def category_ancestry(category)
      categories = []
      while(category.present?) do
        categories << category
        category = category.parent
      end

      categories.reverse
    end

    def product_available_info(product)
      if product.available_on.nil?
        "draft version"
      elsif product.available_on.future?
        "in " + time_ago_in_words(product.available_on) + " (#{product.available_on.strftime('%d %b')})"
      elsif !product.deleted_at.nil? && !product.deleted_at.future?
        "deleted (#{product.deleted_at.strftime('%d %b')})"
      else
        "yes, since (#{product.available_on.strftime('%d %b %y')})"
      end
    end

    def product_variants_display
      if @product.variants.present? && @product.variants.select{|variant| variant.visible}.count > 1
        true
      else
        false
      end
    end

    def get_shop_pro_athletes(product)
      product.shop.owner.pro_athletes
    rescue
      []
    end
  end
end

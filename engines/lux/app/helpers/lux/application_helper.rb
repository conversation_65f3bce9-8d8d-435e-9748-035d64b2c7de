module Lux
  module <PERSON>Helper

    def body_id
      parts_of_path = @active_template.virtual_path.split('/')
      how_many = parts_of_path.size - 1
      last_parts_of_path = parts_of_path[-how_many, how_many]
      last_parts_of_path.join('-')
    end

    def orders_current_page? current_controller
      current_controller == "lux/orders"
    end

    def change_shop_path(shop, controller=nil)
      shop_products_path(shop, network: shop.network.downcase,stay_on: controller)
    end

    def link_to_add_fields(name, f, association)
      new_object = f.object.send(association).klass.new
      id = new_object.object_id
      fields = f.fields_for(association, new_object, child_index: id) do |builder|
        render('lux/products/partials/packages_field', f: builder)
      end
      link_to(name, '#',class: "add_fields button tiny success", data: {id: id, fields: fields.gsub("\n", "")})
    end

    def url_for(options = nil)
      if options.is_a?(Hash) && options.key?(:controller) && @network.present?
        if options[:network].blank? && /^lux\// =~ options[:controller]
          options[:network] = @network.downcase
        end
      end

      super options
    end
  end
end

module Lux
  module OrderItemsHelper
    extend self

    def order_item_variant(item, variant, to_fulfill = false)
      content_tag :div, class: "variant columns #{to_fulfill ? 'small-5' : 'small-6'}" do
        concat image_tag variant.picture.url(:st), size: '48x48' if variant.picture
        concat content_tag :div, item.title, class: 'title'
        concat content_tag :div, variant_properties(variant), class: 'properties'
      end
    end

    def order_item_skus(variant)
      main_sku = variant.sku || variant.gp_sku
      content_tag :div, class: 'skus small-2 columns' do
        concat content_tag :div, main_sku
        if variant.sku.present?
          concat content_tag :div, variant.gp_sku, class: "extra"
        end
      end
    end

    def order_item_quantity_to_fulfill(item)
      content_tag :div, class: 'quantity-fulfill small-3 columns' do
        concat content_tag :span, t('.quantity_html', number:item.quantity)
        concat content_tag :span, '', class: 'remove-item'
      end
    end

    def order_item_quantity(item)
      content_tag :div, class: 'quantity small-2 columns' do
        if item.on_sale?
          concat content_tag :span, "#{number_to_currency(item.price, precision: 1)} / ", class: "original-price"
        end
        concat content_tag :span, number_to_currency(item.unit_price_charged, precision: 1), class: "charged #{'on-sale' if item.on_sale?}"
        concat ' x '
        concat content_tag :span, item.quantity
      end
    end

    def order_item_subtotal(item)
      content_tag :div, number_to_currency(item.total_without_points, precision: 1), class: 'subtotal small-2 columns'
    end

    def order_item_subtotal_points(item)
      if item.points > 0
        content_tag :div, "#{item.points} puntos", class: 'subtotal small-2 columns'
      end
    end

    private

    def variant_properties(variant)
      variant.properties.map do |k, v|
        if k == :noproperty
          "#{t('.properties.' + k.to_s)}"
        else
          "#{t('.properties.' + k.to_s)}#{v.is_a?(Hash) ? v[:name] : v}"
        end
      end.join(' / ')
    end

  end
end

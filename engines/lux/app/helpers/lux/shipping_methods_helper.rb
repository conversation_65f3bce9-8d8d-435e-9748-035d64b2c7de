module Lux
  module ShippingMethodsHelper
    def display_price(shipping_method)
      if shipping_method.express? || shipping_method.fixed? && shipping_method.states.blank?
        number_to_currency(shipping_method.price, precision: 2)
      end
    end

    def load_shipping_methods
      methods = load_from_network
      filter_express(methods)
    end

    def load_states
      case states = GeoConstants::States.send("for_#{@network.downcase}_network")
        when <PERSON><PERSON><PERSON> then states
        when <PERSON><PERSON> then states.values
      end
    end

    protected
    def load_from_network
      Network[@network].shipping_methods
    end

    def filter_express(methods)
      return methods if not @network == 'AR'
      return methods if @shop.goodpeople?
      methods - ['Express']
    end
  end
end

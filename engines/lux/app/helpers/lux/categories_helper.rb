module Lux
  module Categories<PERSON><PERSON><PERSON>
    def categories_options_for_select(categories)
      ordered_categories = Mkp::Category.sort_by_ancestry(categories)
      return options_for_select(ordered_categories.map do |c|
        [c.name, c.id, { 'data-depth' => c.depth }]
      end)
    end

    def filter_options_for_select(collection)
      return options_for_select(collection.map do |c|
        [c[:name], c[:id]]
      end)
    end

    def options_for_manufacturers_select(manufacturers)
      return options_for_select(manufacturers.map do |c|
        [c.name, c.id]
      end)
    end
  end
end

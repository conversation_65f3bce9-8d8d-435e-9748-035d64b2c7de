module Lux
  module LabelGatewaysHelper
    ###### SUBORDER SHOW STUFF ####
    def can_print_label?(label) # Has a gateway
      label.gateway.present?
    end

    def has_gateway_for_labels?(shipment)
      return @has_gateway_for_labels unless @has_gateway_for_labels.nil?
      @has_gateway_for_labels = if shipment.present?
        available_label_gateways(shipment).present? || shipment.extra_info.key?(:gateway_class)
      else
        false
      end
    end

    def available_label_gateways(shipment)
      @available_label_gateways ||= begin
        gateways = Network[shipment.order.network].label_gateways.map do |gateway_name|
          "Gateways::Labels::#{gateway_name}".constantize
        end
        gateways << Gateways::Labels::Lion
        gateways
      rescue
        [Gateways::Labels::Lion]
      end
    end

    def customer_choose_label?(shipment)
      return @customer_choose_label unless @customer_choose_label.nil?
      @customer_choose_label = if shipment
        info = shipment.extra_info[:gateway_info]
        if has_gateway_for_labels?(shipment) && info
          available_label_gateways(shipment).map(&:mandatory_keys).detect do |keys|
            keys.all? { |key| info.key?(key) }
          end.present?
        else
          false
        end
      else
        false
      end
    end

    # This method is a question and also changes internal state of the helper
    # On some partials we are accessing @on_demand_labels_config after asking allowed_to_purchase_labels_on_demand?

    # To the one who did this:
    # You're not a bad person.
    # You're a very good person, who bad things have happened to.
    # Besides, the world isn't split into good people and Death Eaters.
    # We've all got both light and dark inside us. What matters is the part we choose to act on.
    # That's who we really are.
    #
    # - Sirius Black, Google IT Leader, Scrum Master level over 9000
    def allowed_to_purchase_labels_on_demand?(shop)
      @allowed_to_purchase_labels ||= on_demand_labels_config(shop).present?
    end

    def on_demand_labels_config(shop)

      @on_demand_labels_config ||= begin
        carriers = %w(lion atene andreani motonorte krabpack).map(&:classify)
        result = carriers.each_with_object({}){|k,h| h[k] = {}}
        configs = shop.setting.on_demand_labels_config

        if configs.present?
          configs.each_with_object(result) do |(k, v), hash|
            hash[k.to_s.classify] = v
          end
        else
          result
        end
      end
    end

    def preference(labels, state = nil)
      store = @suborder.order.store
      destination_zip = state.zip
      carrier = store.get_gateway_for_labels(destination_zip, @suborder.get_suborder_weight, @suborder.shop_id)
      [carrier, labels[carrier]]
    end

    ######## GATEWAY INFO DISPLAY ########
    def label_gateway_service_name(gateway)
      if gateway.respond_to?(:title)
        gateway.title
      else
        gateway.class.name.demodulize.titleize
      end
    end

    def label_gateway_information(gateway)
      if gateway.origin_data then
        content_tag :div, class: "detail" do
          action_text = t("#{locale_key(gateway)}.operation.#{gateway.operation_id}.action")
          concat content_tag :div, action_text, class: 'title'
          concat content_tag :div, origin_address(gateway.origin_data, gateway.contact_name), class: 'address'
        end
      end
    end

    def origin_address(origin, contact_name = nil)
      output = ["#{origin[:street]} #{origin[:street_number]} #{origin[:floor]} #{origin[:apartment]}".strip]
      output << [origin[:zip_code], origin[:city], origin[:state]].compact.join(' - ').strip
      if contact_name
        contact_tag = content_tag :span, t(".contact_name_html", name: contact_name), class: 'contact'
        output << contact_tag
      end
      output.join('<br>').html_safe
    end

    def locale_key(gateway = nil)
      if gateway
        "lux.gateways.labels.#{gateway.class.name.demodulize.underscore}"
      else
        "lux.gateways.labels"
      end
    end

    def label_gateway_package(gateway, shipment)
      package = gateway.get_package(shipment)
      content_tag :div, class: "package" do
        package_size = t("#{locale_key}.package.size",
                          height: package[:height],
                          width: package[:width],
                          length: package[:length])
        package_weight = t("#{locale_key}.package.weight_html", weight: package[:weight])
        concat content_tag :span, t("#{locale_key}.package.title"), class: 'title'
        concat content_tag :span, package_size, class: 'size'
        concat content_tag :span, package_weight, class: 'weight'
      end
    end

    def label_krabpack_package(shipment)
      package = Mkp::Shipping::Packager.new(shipment.items).get_package_values(length: :meters, mass: :kilograms)
      content_tag :div, class: "package" do
        package_size = t("#{locale_key}.package.size",
                          height: package[:height],
                          width: package[:width],
                          length: package[:length])
        package_weight = t("#{locale_key}.package.weight_html", weight: package[:weight])
        concat content_tag :span, t("#{locale_key}.package.title"), class: 'title'
        concat content_tag :span, package_size, class: 'size'
        concat tag :br
        concat content_tag :span, package_weight, class: 'weight'
      end
    end
  end
end

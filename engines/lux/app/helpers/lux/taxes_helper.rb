module Lux
  module TaxesHelper
    def self.us_states(tax_rates)
      keys = tax_rates.keys
      hash = Mkp::OrdersHelper.us_states

      keys.each do |key|
        hash.except!(hash.key(key)) if hash.value?(key)
      end

      us_states = []
      us_states = hash.select{ |key, value| us_states << [key, value] }
      us_states
    end

    def self.state_name(state)
      Mkp::OrdersHelper.us_states.key(state)
    end
  end
end

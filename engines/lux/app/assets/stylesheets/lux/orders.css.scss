// Based a lot on v5 design mixins
#orders-list{
  margin-bottom: $grid-gutter * 2;
  @include outer-container;
  .order{
    @include container;
    position: relative;
    border:1px solid $lightest_gray;
    margin-bottom: $grid-gutter;
    > .header{
      @include span(1 of 1, $gutter: false);
      text-align: center;
      font-size: 0.9em;
      border-bottom: 2px solid $gray;
      background-color: $lightest_gray;
      .status{
        position:absolute;
        top: $grid-gutter/2;
        left: $grid-gutter/2;
        border-radius: $grid-gutter/2;
        font-weight: 800;
        padding: 0 $grid-gutter/2;
        font-size: 0.9em;
      }
      .creation_date{
        position:absolute;
        top: $grid-gutter/2;
        right: $grid-gutter/2;
        font-size: 0.8em;
        font-weight: 800;
      }
    }
    > .content{
      @include span(5 of 8, $gutter: false);
      // border-right: 1px solid $lightest_gray;
      @media #{$before-large} {
        @include span(4 of 6, $gutter: false);
      }
      > #suborders-to-fulfill {
        .suborder{
          @include container;
          padding: emCalc($grid-gutter/3);
          border-bottom: 1px solid $lightest_gray;
          &:last-child{
            border-bottom: 0px none;
          }
          position: relative;
          .shop{
            @include container;
            position: relative;
            .logo{
              float:left;
              margin-right: 10px;
              height:24px;
              img{
                width:24px;
                border:0px none;
              }
            }
            .title{
              font-size: 0.9em;
              font-weight: 800;
            }
            .id{
              position: absolute;
              top: 2px;
              right: 0px;
              font-size: 0.8em;
              font-style: italic;
              a {
                color: $blue;
              }
            }
          }
          .products{
            padding-left: 42px;
            .product {
              @include container;
              .pic {
                @include span(1 of 10, $gutter: $grid-gutter/2);
                @media #{$before-large} {
                  @include span(2 of 12);
                }
                img{
                  width: auto;
                  height: auto;
                  border:0px none;
                }
              }
              .info{
                @include span(9 of 10, $gutter: $grid-gutter/2);
                @include omega;
                font-size: 0.8em;
                @media #{$before-large} {
                  @include span(10 of 12);
                  @include omega;
                }
                .title{
                  .name{
                    font-weight: 800;
                  }
                  .by{
                    padding-left: 5px;
                  }
                }
                .data{
                  .variant_properties, .purchase_prices{
                    @include container;
                  }
                  .property, .sale_price, .price, .coupon_discount{
                    @include span(1 of 3, $gutter: false);
                    @media #{$before-large} {
                      @include span(1 of 2, $gutter: false);
                    }
                  }
                  .property{
                    > span, > strong {
                      margin-left: $grid-gutter/4;
                    }
                    .color{
                      display: inline-block;
                      height: 12px;
                      width: 12px;
                      border-radius: 3px;
                      &.white{
                        border:1px solid $lightest_gray;
                      }
                    }
                  }
                  .sale_price, .coupon_discount {
                    font-weight: 800;
                    color: $red;
                  }
                  .price{
                    font-weight: 800;
                    &.with_sale{
                      font-weight: 400;
                      font-style: italic;
                      text-decoration: line-through;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    > .side_info{
      @include span(3 of 8, $gutter: false);
      @include omega;
      border-left: 1px solid $lightest_gray;
      @media #{$before-large} {
        @include span(2 of 6, $gutter: false);
      }
      > div {
        @include container;
        padding: emCalc($grid-gutter/3);
        border-bottom: 1px solid $lightest_gray;
        &:last-child{
          border-bottom: 0px none;
        }
      }
      .customer{
        .name{
          font-size: 0.9em;
          .login{
            font-size: 0.9em;
            margin-left: emCalc($grid-gutter/4);
            a {
              color: $blue;
            }
          }
        }
        .email{
          font-size: 0.8em;
        }
        .phone_numbers{
          font-size: 0.8em;
        }
      }
      .payment{
        .title{
          font-size: 0.9em;
        }
        .external_reference{
          font-size: 0.7em;
        }
        .status{
          margin-top: 2px;
          font-size: 0.8em;
          .state{
            border-radius: $grid-gutter/2;
            font-weight: 800;
            padding: 0 $grid-gutter/2;
            font-size: 0.9em;
            &.approved, &.collected{
              background-color: $green;
              color: $white;
            }
            &.refunded{
              background-color: $gray;
              color: $lightest_gray;
            }
          }
          .date_approved {
            color: $green;
            font-weight: 800;
            margin-left: $grid-gutter/4;
          }
        }
        .data{
          font-size: 0.8em;
          .installments{
            margin-left: $grid-gutter/4;
          }
        }
      }
      .delivery {
        .type {
          font-size: 0.9em;
          .service{
            font-size: 0.9em;
            margin-left: emCalc($grid-gutter/4);
          }
        }
        .shipment{
          font-size: 0.8em;
          .price {
            .free {
              font-style: italic;
              text-decoration: line-through;
            }
            .bonification{
              margin-left: $grid-gutter/4;
              color: $green;
              font-weight: 800;
            }
          }
          .tracking{
            .status{
              border-radius: $grid-gutter/2;
              font-weight: 800;
              padding: 0 $grid-gutter/2;
              font-size: 0.9em;
              &.shipped{
                background-color: $green;
                color: $white;
              }
            }
            .code{
              font-size: 0.9em;
              margin-left: emCalc($grid-gutter/4);
              a {
                color: $blue;
              }
            }
          }
        }
      }
    }
    > .more_details{
      @include span(1 of 1, $gutter: false);
      text-align: center;
      font-size: 0.8em;
      padding: emCalc($grid-gutter/3) 0;
      border-top: 1px solid $lightest_gray;
    }
  }
}

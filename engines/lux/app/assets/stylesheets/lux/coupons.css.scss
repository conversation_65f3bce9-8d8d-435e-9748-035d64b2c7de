#coupons-index {
  .goodpeople-app {
    input[type='text'] {
      margin-bottom: 0.25em;
      height: 2.5625em;
      width: 56.5em;
    }
    span {
      display: block;
      margin-bottom: 1.65em;
      font-size: 0.75em;
    }
    a {
      font-weight: bold;
    }
  }
}

#coupon-list {
  .gp-simple-panel{
    margin: 0 auto emCalc(10px);
    padding: emCalc(10px);
    .product-title {
      .id {
        margin-right: emCalc(16px);
      }
    }
  }
}

#coupons-new{
  .percent{
    display: none;
  }
  .cancel{
    position: relative;
    top: 1px;
  }
  .restrictions {
    .restriction-label {
      font-weight: normal;
      font-size: emCalc(12px);
      display: inline;
    }
    .radio {
      font-weight: normal;
      font-size: emCalc(10px);
    }
  }
  .apply-on-sale{
    font-size: 0.8575em;
    .checkbox{
      margin-right: 0.4em;
      &:before{
        margin-top: 2px;
      }
    }
  }
  .select2-choices {
    height: emCalc(40px);
    padding: 0.55em 0.75em;
    &:focus {
      border-color: $gris;
      box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
    }
  }
  .select2-container, .select2-container-multi {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    margin-bottom: 1.25em;
    .select2-choices {
      border-radius: 3px 3px 3px 3px;
      outline: medium none;
      background-color: white;
      border: 1px solid #CCCCCC;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      background-image: none;
      color: rgba(0, 0, 0, 0.75);
      cursor: text;
      padding: 0.45em 0.75em 0.25em;
      &:focus{
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
  }
}
#coupons-show{
  .status{
    position: absolute;
    right: 16px;
    padding: 10px;
    margin-left: 30px;
    border-radius: 5px;
    color: white;
    text-decoration: none;
    font-size: emCalc(12px);
    &.deleted{
      background-color: $rojo;
    }
    &.inactive{
      background-color: orange;
    }
    &.active{
      background-color: green;
    }
  }
  .back{
    color: $rojo;
    font-size: emCalc(14px);
  }
}

#coupons-index{
  .search input{
    height: 2.5625em;
  }
  .new-coupon{
    text-align: right;
    top: -7px;
  }
  .delete-form{
    display: inline;
    top: -1px;
    position: relative;
    div{
      display: inline;
    }
  }
  #coupon-list{
    .status{
      position: absolute;
      right: 16px;
      padding: 10px;
      margin-left: 30px;
      border-radius: 5px;
      color: white;
      text-decoration: none;
      font-size: emCalc(12px);
      &.deleted{
        background-color: $rojo;
      }
      &.inactive{
        background-color: orange;
      }
      &.active{
        background-color: green;
      }
    }
    .coupons-identifiers{
      font-weight: bold;
    }
    .coupon-description{
      font-size: 0.9em;
      .item{
        display: inline;
        padding-right: 20px;
      }
    }
    .links{
      a{
        color: red;
        padding-right: 50px;
      }
    }
  }
  .digg_pagination{
    text-align: center;
    a{
      cursor: pointer;
    }
    a:hover{
      font-weight:bold;
      color: rgb(86, 86, 86);
      text-decoration: none;
    }
    .current{
      color: #F6F5F7;
    }
  }
}

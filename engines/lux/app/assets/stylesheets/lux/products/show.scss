#products-show{
  .section{
    h2, h3, h4 {
      color:$gris;
      border-bottom: 1px solid darken($gris-claro, 10%);
      padding: 0.3em 0.8em;
      background-color: lighten($gris-claro, 3%);
      border-radius: 5px 5px 0 0;
    }
    h5 {
      color:$gris;
      border-bottom: 1px solid darken($gris-claro, 10%);
      padding: 0.3em 0em;
    }
  }
  .with_explanation{
    margin-bottom: emCalc(4px);
  }
  .field{
    margin-bottom: 14px;
  }
  #gender-options{
    margin-bottom: 1.25em;
    input {
      display:none;
    }
    .gender-container{
      margin-bottom: 0.4em;
    }
    .gender{
      margin-left: 5px;
      display:inline-block;
      font-size: 0.875em;
      color: rgba(0, 0, 0, 0.75);
    }
    .custom.checkbox{
      width:18px;
      height:18px;
      border-radius: 2px;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      border: 1px solid #CCCCCC;
      &.checked:before{
        margin-left: -4px;
      }
    }
  }
  #pictures-container{
    &.loading {
      .loader {
        display: block;
      }
    }
    .loader {
      display: none;
      z-index: 999;
      content: ' ';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(#fff, 0.8);
    }
    .picture {
      position: relative;
      margin-bottom: emCalc(10px);
      img {
        border-radius: 3px;
        border: 2px solid $gris-claro;
      }
      .color-hex-selector {
        margin: 0;
        .select2-choice {
          height: 2.4em;
          line-height: 2.3em;
          .select2-arrow b {
            background-position: 0 0.3em;
          }
        }
      }
      .color {
        height:emCalc(36px);
        width:emCalc(36px);
        margin-right: 0.5em;
        border-radius: 3px;
        border: 2px solid $gris-claro;
        box-shadow: 1px 1px 3px 0px rgba(0, 0, 0, 0.5) inset;
      }
    }
    h5 {
      border: 0 none;
    }
  }
  .variants-container{
    .color-container{
      padding-top: emCalc(14px);
      padding-bottom: emCalc(14px);
      border-bottom: 1px solid $gris-claro;
      .title{
        padding-left: 0.75em;
      }
      input{
        margin-bottom: emCalc(5px);
      }
      p {
        margin-bottom: 0;
      }
    }
    .property {
      margin-right: emCalc(6px);
      padding-right: emCalc(6px);
      border-right: 1px solid #dddddd;
    }
    .color{
      height:emCalc(36px);
      width:emCalc(36px);
      margin-right: 0.5em;
      border-radius: 3px;
      border: 2px solid $gris-claro;
      box-shadow: 1px 1px 3px 0px rgba(0, 0, 0, 0.5) inset;
    }
    h5{
      display: inline-block;
      border:0px none;
    }
    .size-container{
      margin-top: emCalc(5px);
      > .columns{
        padding-left: 0.75em;
      }
      input{
        margin-bottom: emCalc(5px);
      }
    }
  }
  input, .select2-choices, textarea{
    height: emCalc(40px);
    padding: 0.55em 0.75em;
    &:focus {
      border-color: $gris;
      box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
    }
  }
  .prefix, .postfix{
    height: 3.2em;
    font-size: emCalc(11px);
    font-weight: 700;
    color:$gris;
  }
  .prefix{
    border-radius: 3px 0 0 3px;
  }

  .postfix{
    border-radius: 0 3px 3px 0;
  }
  .select2-container, .select2-container-multi {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    margin-bottom: 1.25em;
    .select2-choices {
      border-radius: 3px 3px 3px 3px;
      outline: medium none;
      background-color: white;
      border: 1px solid #CCCCCC;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      background-image: none;
      color: rgba(0, 0, 0, 0.75);
      cursor: text;
      padding: 0.45em 0.75em 0.25em;
      &:focus{
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
  }
  .product-actions button{
    padding: 9px 46px;
  }
  .price{
    margin-top: 22px;
  }
  .dropdown{
    border-radius: 3px;
  }
  #price{
    .input-text{
      height: 2.425em;
      font-size: 1.1em;
      font-weight: 700;
    }
  }
  .product-actions{
    margin-bottom: 50px;
  }
  #product_description{
    height: 84px;
  }
  .currency{
    float: left !important;
  }
  #overlay{
    opacity: .2;
  }
  .add-photos{
    margin-bottom: 20px;
  }
  .variant {
    input[type="text"] {
      width: 70px;
      display: inline;
    }
  }
}

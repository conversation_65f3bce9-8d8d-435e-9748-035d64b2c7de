// Goodpeople General Variables & Foundation Variables Overrides
@import 'globals/colors';
@import 'globals/variables';
@import 'mkp/globals/variables';
@import 'globals/base_application'; // This File include the V5 Stuff Already

// Libs
@import 'foundation/components/sub-nav';
@import 'foundation/components/custom-forms';
@import 'foundation/components/switch';
@import 'foundation/components/alert-boxes';
@import 'foundation/components/section';
@import 'foundation/components/tables';
@import 'foundation/components/labels';
@import 'foundation/components/progress-bars';
@import 'select2';
@import 'selectize.default';
@import 'font-awesome';
@import 'foundation-datepicker';
@import 'spectrum';

// General Layout
@import 'social/globals/layout';
@import 'social/profiles/media_stats';

// Modules and Partials
@import 'layout';
@import './partials/header';
@import 'partials/alerts';
@import 'partials/menu';
@import 'partials/account_submenu';
@import 'partials/shop_title';
@import './v2/partials/shop_title'; //  NEW STUFF FOR THE NEW DISPLAYS
@import 'partials/banners/banner';
@import 'partials/banners/banner_templates';
@import 'social/partials/file_uploader';

// Page Specific
@import 'products';
@import 'products/show';
@import 'product_imports/new';
@import 'product_imports/progress';
@import 'suborders/label_imports.scss';
@import 'warehouses';
@import 'suborders';
@import './v2/views/suborders'; //  NEW STUFF FOR THE NEW DISPLAYS
@import 'suborder_filter';
@import 'orders';
@import 'carts';
@import 'coupons';
@import 'questions';
@import 'merchants';
@import 'shipping_methods';
@import 'facebook_app';
@import 'taxes';
@import 'wide_discount';
@import 'integrations';
@import 'integration/product';


.form-error {
  color: $red;
}
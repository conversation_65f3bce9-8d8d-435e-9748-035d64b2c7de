#wide_discount-edit{
  .wide-sale{
    margin-bottom: 9.7em;
  }
  .apply-to-all-checkbox {
    margin-bottom: 0.8em;
    label {
      font-size: 0.9em;
      text-align: right;
    }
    input {
      float: right;
      margin: 0.35em 0 0.4em 10px;
    }
  }
  #shop_discount_ends_at{
    margin-top: 1em;
  }
  .edit-wide-sale {
    .restrictions {
      .restriction-label {
        font-weight: normal;
        font-size: emCalc(12px);
        display: inline;
      }
      .radio {
        font-weight: normal;
        font-size: emCalc(10px);
      }
    }
    .select2-choices {
      height: emCalc(40px);
      padding: 0.55em 0.75em;
      &:focus {
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
    .select2-container, .select2-container-multi {
      box-shadow: none;
      display: block;
      font-size: 0.875em;
      margin-top: 0;
      padding: 0;
      position: relative;
      top: 0;
      width: 100%;
      margin-bottom: 1.25em;
      .select2-choices {
        border-radius: 3px 3px 3px 3px;
        outline: medium none;
        background-color: white;
        border: 1px solid #CCCCCC;
        box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
        background-image: none;
        color: rgba(0, 0, 0, 0.75);
        cursor: text;
        padding: 0.45em 0.75em 0.25em;
        &:focus{
          border-color: $gris;
          box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
  .products-list-pagination {
    font-size: 12px;
    margin-right: 10px;
    height: 10px;
    li {
      cursor: pointer;
      float: left;
      margin-right: 10px;
      &.selected {
        font-weight: bold;
        color: red;
      }
    }
  }
  #products-container {
    #affected-products-amount {
      padding-top: 20px;
      padding-bottom: 20px;
      font-style: bold;
    }
  }
}

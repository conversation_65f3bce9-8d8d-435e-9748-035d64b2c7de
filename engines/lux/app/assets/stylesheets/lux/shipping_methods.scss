.section-auto-sample-accordion {
  @include section-container($section-type:accordion);
  & > section { @include section($section-type:accordion, $title-selector:".title-sample", $content-selector:".content-sample"); }
}

#shipping_methods-index, #shipping_methods-edit{
  .states{
    margin-top: 1em;
    font-size: 0.8em;
    .state{
      font-size: 0.9em;
      margin-top: 0.8em;
      margin-right: 1em;
      text-align: right;
    }
  }

  .delete{
    display: inline;
    div{
      display: inline;
    }
  }

  .new-service-form{
    display: none;
  }
  .services-attrs > .row{
    display: none;
  }
  .easypost-service{
    .label{
      margin-bottom: 1.5em;
    }
    .carrier{
      text-align: center;
      margin-bottom: 0.9em;
      .carrier-logo-container{
        height: 4em;
      }
    }
    .speeds{
      margin-bottom: 1.5em;
    }
    .carrier-warn{
      p{
        padding: 0 3.8em;
        margin-bottom: 2em;
      }
    }
  }
  .international-speeds{
    display: none;
  }
  .warehouse-input{
    margin-left: 0px;
  }
  .methods{
    margin-bottom: 1.5em;
  }
  .fixed-service, .express-service{
    .prefix{
      background-color: #eee;
      border: none;
      color: #aaa;
    }
  }
  .fixed-service{
    .checkbox-container{
      margin-bottom: 1em;
      label{
        font-weight: lighter;
        font-size: 0.8em;
      }
    }
  }
  .international-speeds, .domestic-speeds{
    margin-left: 17px;
    font-size: 0.8em;
    label{
      font-weight: 500;
    }
  }
  .speeds label, .carriers label{
    font-size: 0.90em;
    margin-bottom: 0.2em;
  }
  .shipping-methods-list{
    margin-bottom: 3em;
    img {
      margin-right: 10px;
      &:last-child{
        margin-right: 0;
      }
    }
    .th-actions{
      width: 158px;
    }
  }
}

.shipping-methods{
  .select2-container, .select2-container-multi {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    margin-bottom: 1.25em;
    .select2-choices {
      border-radius: 3px 3px 3px 3px;
      outline: medium none;
      background-color: white;
      border: 1px solid #CCCCCC;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      background-image: none;
      color: rgba(0, 0, 0, 0.75);
      cursor: text;
      padding: 0.45em 0.75em 0.25em;
      &:focus{
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
  }
}
#shipping_methods-edit{
  .new-service-form{
    display: inherit;
  }
}

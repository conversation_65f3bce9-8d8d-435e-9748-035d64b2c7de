body {
  background-image: none;
  padding-bottom: 30px;
}

h3 {
  .subheader{
    font-size: 0.8em;
  }
}

.cover_image {
  width: 100%;
  height: 205px;
  margin:1em 0;
}

.arrow-up {
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid $rojo;
}

#actions-section{
  input{
    margin: 0 0;
  }
}
.general-actions{
  text-align: right;
  a {
    margin-right: 0px;
    margin-left: emCalc(5px);
  }
}

.integration-actions{
  text-align: center;
  a {
    margin-right: 0.5em;
    margin-left: 0px;
  }
}

#topnav-container{
  background-color: $blanco;
  border-bottom: 1px solid lighten($gris, 30%);
  margin-bottom: emCalc(10px);
  h2, h3{
    margin-bottom: 0px;
    margin-top: 0.5em;
    border-bottom: 1px solid $gris-claro;
    padding-bottom: 0.5em;
    span {
      font-weight: 400;
    }
  }
}

#submenu {
  margin-top: - emCalc(10px);
  margin-bottom: emCalc(10px);
  .marketing-menu {
    .columns {
      background-color: $gris-claro;
      padding:5px 0px;
      font-size: 0.85em;
    }
  }
  a {
    color: $gris-oscuro;
  }
}

.topnav {
  font-size: emCalc(13px);
  text-transform: uppercase;
  position: relative;
  .topnav-item {
    float: left;
    width: 20%;
  }
  .section {
    position: relative;
    text-align: center;
    border-top:3px solid transparent;
    border-bottom:3px solid $gris-oscuro;
    padding: emCalc(11px) emCalc(15px) emCalc(15px);
    .arrow-up{
      position:absolute;
      margin: 0px auto;
      bottom:0;
      left:0;
      right:0;
    }
  }
  .active {
    .section {
      border-bottom:3px solid $rojo;
      a {
        font-weight: 700;
        color: $rojo;
      }
    }
  }
}

.gp-simple-panel{
  position: relative;
  .panel-active{
    background-color: lighten($gris, 30%);
    position:absolute;
    width:100%;
    height:emCalc(7px);
    border-radius: 3px 3px 0 0;
    top:0px;
    left:0px;
  }
}

.subheader {
  a {
    color: $rojo;
    &:hover{
      text-decoration: underline;
    }
  }
}

label{
  display:block;
  color: $gris-oscuro;
  cursor: pointer;
  font-size: emCalc(15px);
  font-weight: 700;
  &.with_explanation{
    margin-bottom: 0px;
  }
}

.explanation{
  font-size: emCalc(12px);
  color: $gris;
  margin-bottom: emCalc(8px);
}

.general-filters {
  dl {
    font-size: 0.85em;
    dt{
      color: darken($gris, 20%);
      font-weight: 700;
    }
    dd {
      a {
        color: lighten($gris, 15%);
      }
      &.active {
        a {
          background-color: transparent;
          color: $rojo;
        }
      }
    }
  }
}

// STATUS COLORS
.pending, .in_process, .ready_to_pick_up {
  background-color: $yellow;
  border-color: darken($yellow, 20%);
  color: darken($yellow, 30%);
}
.rejected, .cancelled {
  background-color: $light-red;
  border-color: darken($light-red, 20%);
  color: darken($light-red, 50%);
}
// SHIPMENT SPECIFIC
.shipped {
  background-color: $blue;
  border-color: darken($blue, 20%);
  color: darken($blue, 50%);
}
.delivered {
  background-color: $lighter-green;
  border-color: darken($lighter-green, 20%);
  color: darken($lighter-green, 30%);
}
// PAYMENT SPECIFIC
.collected {
  background-color: $lighter-green;
  border-color: darken($lighter-green, 20%);
  color: darken($lighter-green, 30%);
}
.refunded {
  background-color: #b8b9b9;
  border-color: darken(#b8b9b9, 20%);
  color: darken(#b8b9b9, 30%);
}

.marketing-menu{
  text-align: center;
}

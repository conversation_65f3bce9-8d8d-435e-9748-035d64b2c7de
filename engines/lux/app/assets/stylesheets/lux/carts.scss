#carts-index{
  .gp-simple-panel{
    margin: 0 auto emCalc(10px);
    padding: emCalc(10px);
  }
  .search input{
    height: 2.5625em;
  }
  #cart-list{
    .buyer_data{
      font-size: emCalc(14px);
      color: $gris;
      font-weight: 700;
      span {
        color: lighten($gris, 15%);
        font-weight: 400;
        margin-left: 1em;
        &:first-child{
          margin-left: 0em;
        }
      }
    }
    .cart-description{
      font-size: emCalc(12px);
      h3{
        font-size: emCalc(21px);
        margin: 0px;
        a {
          color:$gris;
        }
      }
      .general_data {
        span{
          color: lighten($gris, 15%);
          font-weight: 400;
          margin-right: 1em;
          &:last-child{
            margin-right: 0em;
          }
        }
      }
    }
    .total-price{
      position:absolute;
      bottom:0px;
      right:0px;
      font-size: emCalc(14px);
      text-align: right;
      font-weight: 700;
      color: $gris;
    }
    .type{
      position: absolute;
      right: 36px;
      top: 39px;
      font-size: 13px;
    }
    .status{
      position: absolute;
      right: 16px;
      font-size: 0.8em;
      padding: emCalc(4px) emCalc(21px);
      margin-left: 30px;
      text-decoration: none;
      border-width: 1px;
      border-style: solid;
      border-radius: 3px;
      font-weight: 700;
      &.active{
        background-color: #f5e703;
        border-color: darken(#f5e703, 20%);
        color: darken(#f5e703, 30%);
      }
    }
    .action_links{
      a{
        color: $rojo;
        font-size: emCalc(14px);
      }
    }
  }
  .digg_pagination{
    text-align: center;
    a{
      cursor: pointer;
    }
    a:hover{
      font-weight:bold;
      color: rgb(86, 86, 86);
      text-decoration: none;
    }
    .current{
      color: #F6F5F7;
    }
  }
}

#carts-show{
  .gp-simple-panel{
    margin: 0 auto emCalc(20px);
    padding: emCalc(20px);
  }
  .user-id{
    color: red;
    font-size: .9em;
  }
  .order-id, .id{
    font-size: .9em;
  }
  .subheader{
    font-size: 0.8em;
    .subtitle{
      display: inline;
      font-weight: bold;
    }
  }
  .order-description{
    span{
      display: inline;
      padding-right: emCalc(20px);
    }
  }
  .total-price{
    font-size: 0.9em;
  }
  .product-list{
    font-size: 0.8em;
  }
  .color{
    width: 21px;
    height:21px;
    position:absolute;
    border-radius: 3px;
    right: -23px;
    top:0px;
  }
  .on_sale{
    color: $rojo;
  }
  .updated{
    margin-left: 18px;
  }
  .states{
    text-align: center;
  }
  .state{
    font-size: 0.8em;
    padding: 0.25em 1.3125em;
    text-decoration: none;
    border-width: 1px;
    border-style: solid;
    border-radius: 3px;
    font-weight: 700;
  }
}

#carts-index, #carts-show{
    // COLORS
  .initialized {
    background-color: #f5e703;
    border-color: darken(#f5e703, 20%);
    color: darken(#f5e703, 30%);
  }
  .address {
    background-color: #0687d1;
    border-color: darken(#0687d1, 20%);
    color: lighten(#0687d1, 60%);
  }
  .shipping_method {
    background-color: #95d708;
    border-color: darken(#95d708, 20%);
    color: darken(#95d708, 30%);
  }
  .payment {
    background-color: #f5b405;
    border-color: darken(#f5b405, 20%);
    color: darken(#f5b405, 30%);
  }
  .review {
    background-color: #4fe3fe;
    border-color: darken(#4fe3fe, 20%);
    color: darken(#4fe3fe, 50%);
  }
  .awaiting_confirm {
    background-color: #fe4f53;
    border-color: darken(#fe4f53, 20%);
    color: darken(#fe4f53, 50%);
  }
}

.add-tax-rate {
  .select2-container .select2-choice {
    border-radius: 3px 3px 3px 3px;
    outline: medium none;
    background-color: white;
    border: 1px solid #CCCCCC;
    box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
    background-image: none;
    color: rgba(0, 0, 0, 0.75);
    cursor: text;
    padding: 0.45em 0.75em 0.25em;
    height: 39px;
  }
  .select2-container {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    margin-bottom: 1.25em;
  }
  #us_states {
    width: 100%;
  }
}

.tax-rates {
  table {
    width: 90%;
    th, td {
      width: 25%;
      text-align: left;
      vertical-align: middle;
    }
  }
  .states-header {
    height: 40px;
    th {
      font-weight: bold;
    }
  }
  .state {
    height: 50px;
    border-bottom: 1px solid #c9c9c9;
  }
  .state-tax-rate {
    input[type = 'text'] {
      position: relative;
      top: 10px;
      margin-bottom: 0em;
      width: 50%;
      height: 30px;
    }
    span {
      position: relative;
      top: -15px;
      left: 105px;
      font-size: 0.9em;
    }
  }
  input[type = 'checkbox'] {
    margin: 0em;
  }
  .delete-tax-rate {
    margin-left: 2em;
    cursor: pointer;
    &:hover {
      font-weight: bold;
    }
  }
}

.setup-tax-rate {
  margin-top: 1em;
  font-size: 1em;
}

.save-tax-rates {
  margin-top: 1em;
  margin-left: 1em;
}

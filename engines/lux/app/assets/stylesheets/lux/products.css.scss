#product-list {
  .gp-simple-panel{
    margin: 0 auto emCalc(10px);
    padding: emCalc(10px);
    .product-title {
      .id {
        margin-right: emCalc(16px);
      }
    }
  }
}

#products-index{
  .pagination{
    .current{
      color: $rojo;
    }
  }
  .search input{
    height: 2.5625em;
  }
  #product-data {
    @media #{$til-large} {
      text-align: left;
      line-height: 1.8em;
    }
    @media #{$after-large} {
      text-align: center;
      line-height: 3.2em;
    }
    span {
      font-size: 0.8em;
      color: $gris;
      margin-right: 1em;
      font-weight: 700;
    }
  }
  #sale-data {
    @media #{$til-large} {
      text-align: left;
      line-height: 1.8em;
    }
    @media #{$after-large} {
      text-align: right;
      line-height: 3.2em;
    }
    span {
      font-size: 0.8em;
      color: $gris;
      margin-right: 1em;
      font-weight: 700;
      &.selled_date{
      color: lighten($gris, 20%);
      font-weight: 400;
      }
      &.zero{
        color: lighten($gris, 20%);
      }
      &:last-child {
        margin-right: 0em;
      }
    }
  }
}

#products-new, #products-edit {
  #product-form{
    .section{
      h2, h3, h4 {
        color:$gris;
        border-bottom: 1px solid darken($gris-claro, 10%);
        padding: 0.3em 0.8em;
        background-color: lighten($gris-claro, 3%);
        border-radius: 5px 5px 0 0;
      }
      h5 {
        color:$gris;
        border-bottom: 1px solid darken($gris-claro, 10%);
        padding: 0.3em 0em;
      }
    }
    #gender-options{
      margin-bottom: 1.25em;
      input {
        display:none;
      }
      .gender-container{
        margin-bottom: 0.4em;
      }
      .gender{
        @extend %checkbox_label;
      }
      .custom.checkbox{
        @extend %checkbox;
      }
    }
    #pictures-container{
      #picture {
        margin-bottom: emCalc(10px);
        position: relative;
        img {
          border-radius: 3px;
          border: 2px solid $gris-claro;
        }
        .delete-btn {
          position: absolute;
          top: 2px;
          right: 15px;
          width: 16px;
          height: 16px;

          background-color: $blanco-oscuro;
          img {
            width: 12px;
            height: 12px;
            position: absolute;
            top: 2px;
            left: 2px;
          }
        }
        .delete-btn-hovered {
          background-color: $gris-claro;
        }
      }
    }
    .sports-tree{
      margin-bottom: 1.25em;
      .error{
        display: none;
      }
    }
    .categories{
      margin-bottom: 1.25em;
      .error{
        display: none;
      }
      .custom{
        margin-bottom: 0;
      }
    }
    .variants-form{
      .variant-color-selector {
        .color-hex-preview, .prefix {
          cursor: pointer;
        }
      }
      .select2-container{
        margin-bottom: 0.2em !important;
      }
      .warning{
        color: $rojo;
      }
      .color-container{
        padding-bottom: emCalc(10px);
        margin-bottom: emCalc(10px);
        .title {
          padding-left: 0.75em;
        }
        input {
          margin: 0;
        }
        .delete-color, .delete-size {
          cursor: pointer;
          font-size: 12px;
          float: right;
          line-height: 30px;
          &.delete-color {
            font-weight: bold;
          }
        }
      }
      .manage-stock{
        margin-top: 0.8em
      }
      h5 {
        border: 0 none;
      }
      .property-value{
        margin-top: emCalc(5px);
        > .columns{
          padding-left: 0.75em;
        }
        input{
          margin-bottom: emCalc(5px);
        }
      }
      .stock, .display-options{
        margin-bottom: 0.5em;
        .custom.checkbox {
          @extend %checkbox;
        }
        span, label {
          @extend %checkbox_label;
        }
      }
      .display-options{
        .custom.checkbox {
          margin-right: 0.4em;
        }
      }
      .stock{
        .custom.checkbox {
          margin-right: 0.4em;
        }
      }
    }
  }
  .white-popup {
    position: relative;
    background: #FFF;
    padding: 20px;
    width: auto;
    max-width: 900px;
    margin: 20px auto;
  }
  .table-container{
    table {
      margin-bottom: 2.95em;
      margin-left: auto;
      margin-right: auto;
      input{
        width: 4.9em !important;
        text-align: right;
        margin: 0 0 0.55em 0 !important;
      }
      td{
        vertical-align: middle;
      }
      tbody tr{
        border-bottom: 1px solid #dddddd;
      }
    }
    table, th{
      text-align: center !important;
    }
    h1{
      margin-bottom: 1em;
    }
    hr{
      border-top: 1px solid #eee;
    }
    .submit{
      text-align: right;
    }
  }
  .property-container{
    background-color: aliceblue;
    padding: 16px;
    margin: 10px;
    border: 1px solid lightblue;
    border-radius: 4px;
    hr{
      border-bottom: 1px solid $gris-claro;
      border-top: 0px
    }
    .right{
      text-align: right;
    }
    .alert{
      color: $rojo;
    }
    .success{
      color: green;
    }
    .delete-property-value{
      cursor: pointer;
      font-size: 12px;
      line-height: 30px;
      &.delete-color {
        font-weight: bold;
      }
    }
  }
  input, .select2-choices, textarea{
    font-size: 0.68em;
    &:focus {
      border-color: $gris;
      box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
    }
  }
  .prefix, .postfix{
    height: 3.25em;
    font-size: emCalc(11px);
    font-weight: 700;
    color: $gris;
    line-height: 3em;
  }
  .prefix{
    border-radius: 3px 0 0 3px;
  }

  .postfix{
    border-radius: 0 3px 3px 0;
  }
  .select2-container, .select2-container-multi {
    box-shadow: none;
    display: block;
    font-size: 0.875em;
    margin-top: 0;
    margin-bottom: 1em;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    .select2-choices {
      border-radius: 3px 3px 3px 3px;
      outline: medium none;
      background-color: white;
      border: 1px solid #CCCCCC;
      box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
      background-image: none;
      color: rgba(0, 0, 0, 0.75);
      cursor: text;
      padding: 0.45em 0.75em 0.25em;
      &:focus{
        border-color: $gris;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);
      }
    }
  }
  .price {
    margin-top: 22px;
  }
  .dropdown {
    border-radius: 3px;
  }
  #price{
    input.input-text{
      height: 2.425em;
      font-size: 1.1em;
      font-weight: 700;
    }
  }
  .product-actions{
    margin-bottom: 50px;
  }
  #product_description{
    height: 84px;
  }
  .currency{
    font-weight: lighter;
    line-height: 3.2em;
  }
  #overlay{
    opacity: .2;
  }
  .add-photos{
    margin-bottom: 20px;
  }
  .variant {
    input[type="text"] {
      width: 70px;
      display: inline;
    }
  }
  .shipping{
    .dropdown{
      height: 2.5em;
      .current{
        line-height: 2.4em;
      }
      .selector{
        height: 2.5em;
      }
    }
  }
}

%checkbox {
  width:18px;
  height:18px;
  border-radius: 2px;
  box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
  border: 1px solid #CCCCCC;
  &.checked:before{
    margin-left: -4px;
  }
}
%checkbox_label {
  display:inline-block;
  font-size: 0.875em;
  color: rgba(0, 0, 0, 0.75);
}

.products-list {
  position: relative;
  &.empty {
    min-height: 300px;
    .empty-msg { display: block; }
    .products-list-list-wrapper { display: none; }
    &.loading {
      .empty-msg { display: none; }
    }
  }
  &.loading {
    min-height: 300px;
    .loading-msg { display: block; }
    .products-list-list-wrapper { display: none; }
  }
  .refinements {
    margin: 0.5em 0;
  }
  .products-list-msg {
    display: none;
    text-align: center;
  }
  .products-list-list-wrapper {
    font-size: 14px;
    table {
      margin: 0;
    }
    td, th {
      text-align: center;
      vertical-align: middle;
    }
  }
  .products-list-pagination {
    font-size: 12px;
    margin-right: 10px;
    height: 10px;
    li {
      cursor: pointer;
      float: left;
      margin-right: 10px;
      &.selected {
        font-weight: bold;
        color: red;
      }
    }
  }
  .products-list-item {
    &:hover {
      background-color: #f5f5f5;
    }
    .list-item-thumb {
      position: relative;
      img {
        width: 100%;
      }
      .i-integration {
        position: absolute;
        top: 5px;
        left: 5px;
        width: 35px;
      }
    }
    .list-item-actions {
      font-size: 1.6em;
      i{
        font-size: .8em;
      }
      a:hover {
        color: $rojo;
      }
    }
    .visible{
      font-size: 1.4em;
      .fa-check {
        color: $green;
      }
      .fa-close {
        color: $red;
      }
    }
    .list-item-sports {
      &.editing {
        &:before {
          content: 'Esc + Return to Save';
          position: absolute;
          left: 0; bottom: 101%;
          color: $gris;
          font-size: 11px;
          font-weight: bold;
        }
      }
    }
  }
  .editable {
    position: relative;
    &.not-editing {
      &:hover {
        cursor: pointer;
        color: $rojo;
        background-color: #eee;
      }
    }
    &.editing {
      text-align: left;
      padding-right: 19px;
      .close {
        position: absolute;
        right: 1px; top: 32%;
        color: $gris;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        &:hover {
          color: $rojo;
        }
      }
    }
    input {
      margin: 0;
    }
    .selectize-control {
      min-width: 150px;
      .selectize-input {
        input {
          height: auto;
          display: inherit;
        }
      }
    }
  }
  .orderable {
    position: relative;
    cursor: pointer;
    &:hover {
      background-color: #eee;
    }
    &.ordering {
      padding-right: 20px;
      color: white;
      background-color: $gris;
    }
    &[data-order-direction="desc"]:after {
      content: '';
      position: absolute;
      top: 50%; right: 7px;
      margin-top: -2px;
      @include triangle(8px, white, down);
    }
    &[data-order-direction="asc"]:after {
      content: '';
      position: absolute;
      top: 50%; right: 7px;
      margin-top: -7px;
      @include triangle(8px, white, up);
    }
  }
}

#stock-table{
  .stock{
    font-size: 13px;
    padding-top: 8px;
  }
  &>div{
    border-bottom: 1px solid #c5c5c5;
    margin-bottom: 21px;
  }
  h4{
    color: #565656;
    border-bottom: 1px solid #c9c9c9;
    padding: 0.3em 0.8em;
    background-color: #eaeaea;
    border-radius: 5px 5px 0 0;
    font-weight: 300;
  }
}

#integrations-index {
  #remove-integration-modal {
    @include reveal-bg;
    @include reveal-modal-style;
    @include reveal-modal-base($base-style: true, $width: 50%);
    @include gp-panel;
    top: 65px;
    padding: 0;
    border: 0;
    .close {
      cursor: pointer;
      float: right;
      height: 69px;
      width: 69px;
      text-align: center;
      line-height: 65px;
      font-size: 28px;
      font-weight: bold;
      overflow: auto;
      &:hover{
        color: $red;
      }
    }
    .modal-content {
      padding: 1em;

      .body {
        font-size: 0.9em;
      }
      .message {
        font-weight: bold;
      }
      .options {
        padding-left: 10px;
        .option {
          label {
            display: inline;
            margin-left: 0.5em;
          }
        }
      }
      .actions{
        text-align: right;
      }
    }
  }
  .integrations {
    position: relative;

    .deleted {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.4);
      z-index: 1000;

      .message {
        margin: 0 auto;
        background-color: $white;
        width: 30%;
        margin-top: 30%;
        text-align: center;
        padding: 1em;
        border: 3px solid $dark-gray;
      }
    }

    .integration-wrapper {
      padding: 1em;
    }
    .authorized {
      font-weight: 600;
      font-size: 0.7em;
      color: $verde;
    }
    .actions{
      margin-top: 10px;
    }
    label{
      font-weight: 400;
      &.inline{
        padding: $form-spacing / 2 - emCalc($input-border-width * 2) 0;
      }
      &.missing{
        font-weight:600;
        color: $red;
      }
    }

    #permissions {
      .custom.checkbox{
        width:18px;
        height:18px;
        border-radius: 2px;
        box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
        border: 1px solid #CCCCCC;
        &.checked:before{
          margin-left: -4px;
        }
      }
      span.label{
        font-size: 0.8em;
        margin-left: 10px;
      }

      %checkbox {
        width:18px;
        height:18px;
        border-radius: 2px;
        box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
        border: 1px solid #CCCCCC;
        &.checked:before{
          margin-left: -4px;
        }
      }
      %checkbox_label {
        display:inline-block;
        font-size: 0.875em;
        color: rgba(0, 0, 0, 0.75);
      }
      ul {
        padding-top: 0.8em;
        line-height: 2em;
        list-style: inside none circle;
      }
      li{
         font-size: 0.9375rem;
      }
      h2{
        border-bottom: 1px solid #CCC;
        font-weight: normal;
        padding-bottom: 0.4em;
      }
      .permission{
        font-size: 0.875em;
        margin-bottom: 0.6em;
        .title{
          font-weight:bold;
        }
      }
    }

    #detail{
      label.inline{
        margin-bottom:0;
      }
      .associated_account{
        width: 60%;
      }
      .missing_store_id{
        font-size: 0.9375rem;
        padding: 0.2em 0.6em;
        color: $red;
        border: 1px solid lighten($red, 15%);
        border-radius: 3px;
        margin-bottom:1em;
      }
    }

    #defaults {
      #sports-select {
        width: 100%;
      }
      .select2-container {
        width: 100%;
      }
      p {
         font-size: 0.9375rem;
      }
    }

    #actions {
      padding:1em 0px 0px;
    }

    .message {
      font-size: 0.9375rem;
      padding: 0.2em 0.6em;
      border-radius: 3px;
      margin-bottom:.5em;
      &.alert {
        color: $red;
        border: 1px solid lighten($red, 15%);
      }
    }
  }

}

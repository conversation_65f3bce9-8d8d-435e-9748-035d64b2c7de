#integration-products-fetch{
  .products-block {
    .product {
      margin-bottom:1em;
      padding-top: 1em;
      border-top:1px dashed $gris-claro;
      &:first-child {
        padding-top:0;
        border-top:0px none;
      }
    }
  }
  h2{
    font-size: 1.2em;
  }
  .title {
    font-weight: 400;
    border-bottom: 1px solid $gris-claro;
    padding: 0 0 0.8em;
    margin-bottom:0.8em;
  }
  .line{
    margin-bottom: 0.3em;
    &> span {
      margin-left:1em;
      border-left:1px solid $gris-claro;
      padding-left:1em;
    }
    &> :first-child{
      margin-left:0em;
      border-left:0px none;
      padding-left:0em;
    }
  }
  .label {
    margin-left: 5px;
    font-size: 0.9em;
    font-weight: 600;
  }
  .product_title{
    font-weight: 600;
  }
  .id, .price, .manufacturer, .category{
    font-size: 0.8em;
  }
  .category{
    text-transform: capitalize;
  }
  .description{
    max-height:80px;
    overflow-y: auto;
    font-size: 0.9em;
    padding:0 0.5em;
    border:1px solid $gris-claro;
    border-radius: 3px;
    p{
      margin-bottom: 0px;
    }
  }
  .action_required{
      border-top:1px solid $rojo;
      border-bottom:1px solid $rojo;
      color:darken($rojo, 30%);
      padding:0.6em 0;
      background: repeating-linear-gradient(
        -55deg,
        lighten($rojo, 43%),
        lighten($rojo, 43%) 10px,
        lighten($rojo, 45%) 10px,
        lighten($rojo, 45%) 20px
      );
  }
  .variants{
    padding:0.5em 0;
    ul{ margin-bottom:0px; }
    li{
      display: inline-block;
      padding:0.3em 1em;
      margin-bottom: 0.3em;
      border:1px solid $gris-claro;
      border-radius: 3px;
      margin-right:20px;
      &:last-child{
        margin-right: 0px
      }
    }
  }
  .quantity{
    margin-left:1em;
    border-left:1px solid $gris-claro;
    padding-left:1em;
  }
  .pictures {
    ul{ margin-bottom:0px; }
    li {
      display: inline;
      margin-right:20px;
      &:last-child{
        margin-right: 0px
      }
      img {
        border:1px solid $gris-claro;
        border-radius: 3px;
        padding:2px;
      }
    }
  }
}

.section-container.auto > section > .title a, .section-container.auto > .section > .title a{
  font-size: 1.2em;
}

.pickup{
  display: none;
}

.shipping-box{
  @include gp-checkout-box;
  line-height: 23px;
  padding-top: 19px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 300;
  padding: 10px;
  margin-bottom: 12px;
  overflow-y: auto;

  .actions {
    margin-top: 5px;
    form {
      float: left;
      margin-right: 5px;
    }
  }
}

.warehouses{
  margin-bottom: 23px;
  .row{
    margin-left: 0;
    margin-right: 0;
  }
}
.new-warehouse{
  .select2-container .select2-choice {
  border-radius: 3px 3px 3px 3px;
  outline: medium none;
  background-color: white;
  border: 1px solid #CCCCCC;
  box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.1) inset;
  background-image: none;
  color: rgba(0, 0, 0, 0.75);
  cursor: text;
  padding: 0.45em 0.75em 0.25em;
  height: 39px;
  }

  .select2-container {
  box-shadow: none;
  display: block;
  font-size: 0.875em;
  margin-top: 0;
  padding: 0;
  position: relative;
  top: 0;
  width: 100%;
  margin-bottom: 1.25em;
  }
}
#warehouse_pickup{
  margin-right: 0.6em;
}

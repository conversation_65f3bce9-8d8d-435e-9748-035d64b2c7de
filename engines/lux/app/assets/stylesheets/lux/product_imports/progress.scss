.product-imports-item {
  position: relative;
  text-align: center;
  .product-imports-progress {
    @include progress-container;
    &[data-status="error"] .meter { @include progress-meter($bg:$progress-meter-alert-color); }
    &[data-status="complete"] .meter { @include progress-meter($bg:$progress-meter-success-color); }
    &[data-status="in_progress"] .meter { @include progress-meter($bg:$progress-meter-secondary-color); }
    .meter {
      @include progress-meter;
      @include radius($global-radius - 1);
    }
  }
  .product-imports-item-status {
    @include label-base;
    @include label-size;
    @include label-style(false, true);
    &[data-status="error"]       { @include label-style($alert-color); }
    &[data-status="complete"]    { @include label-style($success-color); }
    &[data-status="in_progress"] { @include label-style($secondary-color); }
  }
  .product-imports-item-errors {
    overflow: auto;
    text-align: left;
    fieldset {
      padding: 0.5em 1em 1em;
    }
    p {
      margin: 0;
    }
    table {
      td.error {
        background-color: rgba($alert-color, .2)
      }
    }
  }
}

.back-to-products, .back-to-import-products {
  margin-top: 0.6em;
  display: none;
}
@import 'v5/globals/components/form';

#suborders-index, #suborders-show {
  .payment_status {
    font-size: 0.8em;
    padding: .4em 1em;
    border-radius: 3px;
    font-weight: 700;
    white-space: nowrap;
  }

  .service-detail {
    border: 1px solid #bdbdbd;
    margin-bottom: 10px;
    padding-top: 5px;
    padding-bottom: 5px;
    cursor: pointer;
    &.selected{
      border-color: #66c17b;
    }
  }

  .shipment_status {
    font-size: 0.8em;
    padding: .4em 1em;
    border-radius: 3px;
    font-weight: 700;
    white-space: nowrap;
    &.unfulfilled {
      border: 0px none;
      background-color: transparent;
    }
  }
}

#suborders-index{
  .gp-simple-panel{
    margin: 0 auto emCalc(10px);
    padding: emCalc(10px);
  }
  .search input{
    height: 2.5625em;
  }
  #suborders-list{
    > table {
      th, td {
        text-align:center;
         &.details {
          text-align:left;
        }
      }
      td {
        font-size: 0.8em;
        &.details {
          h3 {
            font-size: 1em;
          }
          .name{
            line-height: 1.4em;
            margin-bottom: $grid-gutter/4;
          }
          span{
            color: $gray;
            padding-right: $grid-gutter;
            &:last-child{
              padding-right: none;
            }
          }
        }
      }
    }
    a {
      cursor: pointer;
      color: $blue;
      &:hover{
        text-decoration: underline;
      }
    }
  }
  .digg_pagination{
    text-align: center;
    a{
      cursor: pointer;
    }
    a:hover{
      font-weight:bold;
      color: rgb(86, 86, 86);
      text-decoration: none;
    }
    .current{
      color: #F6F5F7;
    }
  }
}

#suborders-show{
  .suborder{
    font-size: 14px;
    h1, h2, h3 {
      font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    }
    h2 {
      font-size: 1.6em;
      border-bottom: 1px solid $lighter-gray;
      padding-bottom: 0.2em;
      line-height: 1.4em;
      @include ellipsis;
    }
    h3{
      font-size: 1.2em;
      font-weight: 300;
      line-height: 1.6em;
      margin-bottom: 0;
    }
    .details{
      color: $gray;
    }
    .customer{
      margin-top: 0.3em;
      text-align: center;
      .avatar{
        &.show-fallback{
          img{
            display:none;
          }
          .fallback {
            display: inline-block;
          }
        }
        img, .fallback {
          width: 64px;
          height: 64px;
          border-radius: 32px;
        }
        .fallback{
          display:none;
          span{
            color: $white;
            font-weight: bold;
            font-size: 2em;
            line-height: 2.2em;
            padding-left: 0.15em;
          }
        }
      }
      .email{
        color: $gray;
      }
    }
    .creation_date{
      color: $dark-gray;
      text-align: center;
      font-size: 0.9em;
      margin-top: 1em;
    }
    .destination_address{
      padding-top: 0.5em;
      margin-top: 1em;
      border-top: 1px solid $lightest-gray;
      .phone{
        font-size: 0.9em;
        font-style: italic;
        margin-top: 0.4em;
        padding-top: 0.4em;
        border-top: 1px dashed $lightest-gray;
      }
    }
    .payment{
      text-align: center;
      margin-top: 1em;
      padding-bottom: 0.2em;
      border-top: 1px solid $lightest-gray;
      .subtotal{
        font-size: 1.6em;
      }
      .taxes{
        margin-top: -0.4em;
        font-size: 0.9em;
        color: $gray;
      }
      .details{
        padding-top: 0.8em;
      }
    }
    .unfulfilled, .shipments {
      h3{
        color: $dark-gray;
        font-size: 1em;
        font-weight: 600;
        border-bottom: 1px solid $lighter-gray;
        padding-bottom: 0.4em;
      }
      h5{
        color: $dark-gray;
        font-size: .9em;
        font-weight: 600;
      }
      .actions, .downloads {
        text-align: right;
        padding-top: .5em;
        padding-bottom: .8em;
        @media #{$til-medium} {
          text-align: center;
        }
        > span, a, .message {
          margin-right: 1em;
          &:last-child{
            margin-right: 0;
          }
        }
        > span, a {
          @include transition(all .15s linear);
          border: 1px solid lighten($blue, 10%);
          color: lighten($blue, 10%);
          cursor: pointer;
          font-weight: 600;
          text-align: center;
          font-size: 0.8em;
          text-transform: uppercase;
          padding: .5em 1.2em;
          border-radius: 3px;
          white-space: nowrap;
          &:hover{
            background-color: lighten($blue, 10%);
            color: $white;
          }
        }
        > .message {
          display:inline-block;
          font-size: 0.9em;
          color: $gray;
          margin-bottom: 0.8em;
        }
      }
      input {
        @include form-input
      }
      .input-group{
        display: inline-table;
        vertical-align: middle;
        position: relative;
        border-collapse: separate;
        > span {
          width: auto;
          background-color: $lightest-gray;
          color: $gray;
          border: 1px solid $lighter-gray;
          text-align: center;
          vertical-align: middle;
          padding: .4em .8em;
          display: table-cell;
        }
        input {
          border-left: 0px none;
          display: table-cell;
          float: left;
          margin-bottom: 0;
          position: relative;
        }
      }
      .selectize-input{
        border-color:$lighter-gray;
        box-shadow: none;
        padding: 0.55em;
        border-radius: 0px;
      }
    }
    .unfulfilled{
      .fulfillment{
        .options{
          text-align: center;
          padding: .8em 0;
          .columns{
            @media #{$til-medium} {
              margin-bottom: 1em;
              text-align: left;
              &:last-child{
                margin-bottom: 0em;
              }
            }
          }
          .columns > span {
            @media #{$til-medium} {
              border-bottom: 0px none;
            }
            @include transition(all .15s linear);
            border-bottom: 1px solid $white;
            color: $gray;
            cursor: pointer;
            font-weight: 600;
            text-align: center;
            font-size: 0.8em;
            text-transform: uppercase;
            padding: .5em 1.2em;
            white-space: nowrap;
            margin-right: 1em;
            i {
              margin-right: .6em;
            }
            &:hover{
              border-bottom-color: $lighter-gray;
            }
            &.selected{
              border-bottom-color: $lighter-green;
              color: $lighter-green;
              &:hover{
                border-bottom-color: $lighter-green;
                color: $lighter-green;
              }
            }
          }
        }
        .prepaid, .other{
          margin:1em 0 .6em;
        }
        .prepaid {
          .carrier{
            text-align: center;
            h4{
              font-size: 1.2em;
              margin-bottom: 0px;
            }
            .service{
              font-size: .9em;
              color:$gray;
            }
          }
          .message{
            font-size: .9em;
            color: $light-red;
            border: 1px solid lighten($light-red, 15%);
            padding: .5em 1em;
          }
          .service-detail{
            @include container;
            .icon {
              @include span(1 of 12, $gutter: $grid-gutter/2);
              text-align:center;
            }
            .information {
              @include span(11 of 12, $gutter: $grid-gutter/2);
              @include omega;
              vertical-align: top;
              h3 {
                margin-top:0px;
                margin-bottom:$grid-gutter/2;
              }
              .detail, .package{
                font-size:.9em;
                color: $gray;
                > span {
                  margin-right: $grid-gutter/2;
                  &:last-child{
                    margin-right: 0;
                  }
                }
                .title{
                  font-weight:bold;
                }
              }
              .detail{
                margin-bottom: $grid-gutter/2;
                .title{
                  margin-bottom:5px;
                }
                .address{
                  margin-left: $grid-gutter/2;
                  padding-left: $grid-gutter/2;
                  border-left: 2px solid $light-gray;
                }
              }
            }
          }
        }
        .other {
        }
        .notification{
          color: $gray;
          i {
            margin-right:.3em;
            font-size: 1.4em;
          }
          span {
            font-size: .9em;
          }
          padding-bottom: .5em;
          margin-bottom: .5em;
          border-bottom: 1px dashed $light-gray;
        }
        .actions{
          .cancel-it{
            cursor:pointer;
            color: $light-red;
            &:hover{
              text-decoration: underline;
            }
          }
          .confirm-button{

          }
          .disabled{
            cursor: default;
            color: $light-gray;
            &:hover{
              text-decoration: none;
            }
            &.confirm-button{
              color: $white;
              background: $light-gray;
              border-color: $light-gray;
              &:hover{
                background: $light-gray;
                border-color: $light-gray;
              }
            }
          }
        }
      }
      .actions{
        .awaiting_payment, .voided_payment{
          color: $light-red;
          font-size: .9em;
          font-style: italic;
        }
        .voided_payment{
          font-style: normal;
          font-weight: 600;
        }
      }
    }
    .shipment{
      @include container;
      .deliveries{
        @include container;
        .delivery{
          .actions, .label{
            @include span(12 of 12);
            @include omega;
          }
        }
      }
      .message{
        i{
          margin-right: 0.5em;
          font-size: 1.2em;
        }
        &.unknown{
          color: $light-red;
          display: block;
          margin-right: 0px;
          text-align: center;
        }
      }
      .actions {
        > span, a {
          border: 1px solid $lighter-gray;
          color: $gray;
          &:hover{
            border: 1px solid $gray;
            background-color: $gray;
            color: $white;
          }
        }
      }
      .label{
        .info {
          @include span(8 of 12);
          @media #{$til-medium} {
            margin-top: .5em;
            margin-bottom: .5em
          }
          position:relative;
          &.with-icon{
            padding-left: 38px;
          }
          img{
            position:absolute;
            left: 0px;
            top: 2px;
          }
          h4{
            margin: 0;
          }
        }
        .status {
          @include span(4 of 12);
          @include omega;
          text-align: right;
          line-height: 2.5em;
          > span {
            font-size: 0.8em;
            font-weight: 700;
            padding: .4em 1em;
            border-radius: 3px;
            text-transform: uppercase;
            &.awaiting_payment, &.ready_to_pick, &.new{
              @include colored-status($yellow);
            }
            &.picking_list, &.packing_slip, &.ready_to_ship, &.shipped, &.delivered{
              @include colored-status($lighter-green);
            }
            &.not_delivered, &.on_hold, &.cancelled {
              @include colored-status($light-red, 50%);
            }
          }
        }
        .tracking{
          font-size: 0.9em;
          color: $gray;
          > span {
            margin-right: .8em;
            &:last-child{
              margin-right: 0;
            }
          }
          a {
            color: $blue;
            font-weight: 600;
            &:hover{
              text-decoration: underline;
              color: $blue;
            }
            &:visited{
              color: darken($blue, 20%);
            }
          }
        }
        .actions{
          @include span(12 of 12);
          @include omega;
          margin-top: 1em;
          a {
            &.disabled{
              cursor: default;
              border: 1px solid $lighter-gray;
              color: $lighter-gray;
              &:hover{
                color: $lighter-gray;
                background-color:inherit;
              }
            }
          }
          .cancel{
            border: 1px solid $light-red;
            color: $light-red;
            &:hover{
              background-color: $light-red;
              color: $white;
            }
          }
          .deliver{
            border: 1px solid $light-green;
            color: $light-green;
            &:hover{
              background-color: $light-green;
              color: $white;
            }
          }
          .messages{
            margin-top: 20px;
            color: #ff0000;
            font-size: .9em;
            font-style: italic;
          }
        }
        .downloads{
          @include span(12 of 12);
          @include omega;
          text-align: left;
          padding-bottom: 0px;
          &:last-child{
            > span, a {
              border: 1px solid $lighter-gray;
              color: $gray;
              font-size: 0.7em;
              padding: 0.3em 0.8em;
              &:hover{
                border: 1px solid $gray;
                background-color: $gray;
                color: $white;
              }
            }
          }
          @media #{$til-medium} {
            text-align: center;
          }
        }
      }
    }
  }
}

.items{
  @include container;
  article{
    @include span(12 of 12);
    @include omega;
    border-bottom: 1px solid $lightest-gray;
    &:last-child{
      border-bottom: 0px none;
    }
    .variant{
      position:relative;
      padding: 0.6em 0 0.6em 5.5em;
      img{
        position:absolute;
        top: 0.2em;
        left: 1.2em;
      }
      .title{
        @include ellipsis;
      }
      .properties{
        color: $gray;
        font-size: .9em
      }
    }
    .quantity, .subtotal, .skus, .quantity-fulfill{
      text-align: center;
      vertical-align: middle;
      padding-top: 0.6em;
      line-height: 2em;
      font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    }
    .quantity{
      line-height: 2.4em;
      color: $gray;
      span{
        padding: 0 .2em;
      }
      .original-price{
        font-size: .8em;
        text-decoration: line-through;
      }
      .on-sale{
        color: $red;
        font-weight: 600;
      }
    }
    .subtotal{
      color: $gray;
      font-size: 1.2em
    }
    .skus{
      @media #{$til-medium} {
       display:none;
      }
      color: $gray;
      line-height: 1.5em;
      .extra{
        color: $light-gray;
        font-size: .8em;
        line-height: 1.1em;
      }
    }
    .quantity-fulfill{
      text-align: right;
      line-height: 1.5em;
      @media #{$til-medium} {
        padding-top: 1.5em;
      }
      > span{
        color: $gray;
        margin-top: 0.5em;
      }
      .remove-item{
        display:none;
        @media #{$til-medium} {
         display:none;
        }
        @include transition(all .15s linear);
        @extend .i-close;
        cursor: pointer;
        font-size: 0.8em;
        float: right;
        margin-right: 0.5em;
        margin-top: 0.5em;
        border: 2px solid $white;
        border-radius: 2em;
        padding-left: 0.5em;
        padding-right: 0.5em;
        &:hover{
          color: $light-red;
          border-color: $light-red;
        }
      }
    }
  }
}
/* New style for css select... not more css legacy for shopadmin please*/
select {
  padding: 5px 35px 5px 5px;
  font-family: inherit;
  width: 170px;
  font-size: 0.8em;
  border: 1px solid #cccccc;
  background: #FFFFFF;
  padding: 0.825em;
  height: 3.2875em;
}
/* CAUTION: Internet Explorer hackery ahead */
select::-ms-expand {
  display: none; /* Remove default arrow in Internet Explorer 10 and 11 */
}
/* Target Internet Explorer 9 to undo the custom arrow */
@media screen and (min-width:0\0) {
  select {
    background: none\9;
    padding: 5px\9;
  }
}

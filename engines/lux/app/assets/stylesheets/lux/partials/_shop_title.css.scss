.shop-selector {
  position: relative;
  display: inline-block;
  margin-top: 0.5em;
  padding: .5 .5em;
  font-size: 0.9em;
  font-weight: bold;
  line-height: 1.5em;
  text-decoration: none;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
  cursor: pointer;
  @include gp-button-color-default;
  span {
    margin-left: 0.5em;
  }
  .select2-container{
    .select2-choice {
      border: 0px none;
      background:transparent;
      .select2-arrow {
        border-left: 0px none;
        border-radius: 0px;
        background:transparent;
      }
    }
  }
}

.managed-shops {
  margin: 0;
  padding: 0.3em;
}

.shops {
  position: absolute;
  top: 3em;
  left: 0em;
  border: 1px solid #bcbcbc;
  border-radius: 4px;
  z-index: 100;
  display: none;
  a {
    display: block;
    padding: 0.9em 2em;
    background-color: $gris-claro;
    border-bottom: 1px solid #bcbcbc;
    @include linear-gradient(#fff, #eee 98%);
    &:hover {
      @include linear-gradient(#fbfbfb, #e6e6e6 98%);
    }
    &:first-child {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }
    &:last-child {
      border-bottom: none;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}

#question-list {
  .gp-simple-panel{
    margin: 0 auto emCalc(10px);
    padding: emCalc(10px);
    .product-title {
      .id {
        margin-right: emCalc(16px);
      }
    }
  }
}

#questions-index{
  #query{
    margin-bottom: 0px;
  }
  .search input{
    height: 2.5625em;
  }
  .list{
    background-color: #807F80;
    li {
      list-style: none;
      background-color: #F6F5F7;
    }
    .links{
      a{
        color: red;
        padding-right: 50px;
      }
    }
    .user-id{
      color: red;
      font-weight: bold;
    }
  }
  .digg_pagination{
    text-align: center;
    a{
      cursor: pointer;
    }
    a:hover{
      font-weight:bold;
      color: rgb(86, 86, 86);
      text-decoration: none;
    }
    .current{
      color: #F6F5F7;
    }
  }
  .question-modal {
    @include reveal-bg;
    @include reveal-modal-style;
    @media #{$til-medium} {
      @include reveal-modal-base($width: 80%);
    }
    @media #{$from-medium} {
      @include reveal-modal-base($width: 39%);
    }
    top: 250px;
  }
  .question-close {
    @include reveal-close;
  }
  .question-section{
    background-color: #D81512;
    color: white;
    padding: 1.25em;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  .question-show .login, .answer-section .login, .question-section .login{
    font-weight: bold;
    display: inline;
  }

  .answer-section {
    padding: 1.25em;
    border: solid 1px #d9d9d9;
    border-radius: 5px;

    .answer-content {
      display: inline-block;
      font-size: 1em;
      margin: 0 0 0 5px;
    }

    #edition-links {
      .update-answer, .restore-answer-content {
        margin-right: 5px;
        display: none;
      }

      .edit-answer {
        margin: 5px 0 -5px 0;
      }
    }

    textarea {
      display: inline-block;
      margin: 10px 0 10px 0;
      resize: vertical;
    }
  }

  input{
    border-radius: 5px;
  }
  .close-reveal-modal{
    cursor: pointer;
  }
  .back{
    color: red;
    font-size: 0.9em;
    padding-top: 0.5em;
  }
}

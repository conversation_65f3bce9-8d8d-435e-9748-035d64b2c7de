// Based a lot on v5 design mixins
#suborders-index{
  .general-filters{
    margin-bottom: $grid-gutter * 2;
    @include outer-container;

    .suborder-filters {
      @include container;
      position: relative;


      .content{
        font-size: 0.9em;
        @include span(4 of 5, $gutter: false);
        > div{
          @include span(1 of 4, $gutter: false);
          &:nth-of-type(4n) {
            @include omega;
          }
          @media #{$before-large} {
            @include span(1 of 2);
            &:nth-of-type(2n) {
              @include omega;
            }
          }
        }
      }
      .actions{
        @include span(1 of 5, $gutter: false);
        @include omega;
        text-align: right;
        input{
          &:last-child{
            margin-left:$grid-gutter/4;
          }
        }
      }
      input{
        width: auto;
        display: inline-block;
      }
    }

  }
}

// .suborder-filters {
//   form {
//     display: inline-block;

//     input[type=text] {
//       display: inline-block;
//       margin-right: 15px;
//       width: 125px;
//       height: 25px;
//       padding: 3px;
//     }

//     select {
//       width: 125px;
//       margin-right: 15px;
//       float: left;
//     }

//     span {
//       margin-right: 10px;
//     }
//   }

//   a {
//     margin-left: 15px;
//   }
// }

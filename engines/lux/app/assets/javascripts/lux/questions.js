$(function() {
  $('.reveal-question-link[read_path]').click(function() {
    $.ajax({
      url: $(this).attr('read_path'),
      type: "PUT"
    })
  })

  $('#questions-index').on('click', '.edit-answer', function(ev) {
    ev.preventDefault()
    var answerId = $(this).data('answer-id')
    var originalAnswer = $("#answer_"+answerId).text()
    var textContainer = '<textarea name="answer[description]">' + originalAnswer + '</textarea>'

    $(".answer-section .login").after(textContainer)

    toggleEditMode()
    return false
  })

  $('#questions-index').on('click', '.restore-answer-content', function() {
    $("textarea[name='answer[description]']").remove()

    toggleEditMode()
    return false
  })

  $('#questions-index').on('click', '.update-answer', function() {
    var updatedAnswer = $("textarea[name='answer[description]']").val()
    var answerId = $(this).data('answer-id')

    $.ajax({
      url: $("#answer_"+answerId).data('answer-path'),
      type: "PUT",
      data: {
        description: updatedAnswer
      },
      success: function(data, statusCode, xhr) {
        $("#answer_"+answerId).text(updatedAnswer)
        $('.restore-answer-content').trigger("click")
        toggleEditMode()
        $(".question-modal").foundation("reveal", "close")
      }
    })
  })

  function toggleEditMode() {
    var editionLinksSelectors = [
      '.answer-content',
      '.edit-answer',
      '.update-answer',
      '.restore-answer-content'
    ];

    $.each(editionLinksSelectors, function(i, v) {
      $(v).toggle();
    });
  }

  $('.new-answer-form').each(function(index) {
    $(this).submit(function(event) {
      event.preventDefault();

      var that = this;

      $.ajax({
        url: $(this).attr('action'),
        type: "POST",
        data: { description: $(this).find('#description').val() }
      })
    })
  })
})

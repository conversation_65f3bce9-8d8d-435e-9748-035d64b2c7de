$(function() {
  if( !gp.controller.shipping_methods || !( gp.action.index || gp.action.edit) ) return

  function initCountriesSelector(){
    var countries_opts = ''

    _.each(GEO_CONSTANTS.countries, function(name, code){
      countries_opts += "<option value='"+code+"'>"+name+'</option>'
    })

    $('#zone_countries').append(countries_opts)

    var $countriesSelector = $('#zone_countries')

    $countriesSelector.select2()
    $countriesSelector.select2('val', $countriesSelector.data('values'))
  }

  var $customsSignerName
  function enableCustomsSignerName(){
    $customsSignerName = $customsSignerName || $('.customs-signer-name')
    $customsSignerName.find('input').attr('disabled', false)
    $customsSignerName.show()
  }
  function disableCustomsSignerName(){
    $customsSignerName = $customsSignerName || $('.customs-signer-name')
    $customsSignerName.find('input').attr('disabled', true)
    $customsSignerName.hide()
  }

  function enableCountrySelector(){
    var $countriesContainer = $('.countries')
    $countriesContainer.show()
    if( isEasypostChoosen && !isDomesticEasypostChoosen() ){
      $('#zone_countries').select2('val', '')
    }
  }

  function disableCountrySelector(val){
    var $countriesContainer = $('.countries')
    $countriesContainer.hide()
    $('#zone_countries').select2('val', val)
  }

  function enableTitleField(){
    $('.new-service-form .title').show()
    $('.new-service-form .title input').prop('disabled', false)
  }

  function disableTitleField(){
    $('.new-service-form .title').hide()
    $('.new-service-form .title input').prop('disabled', true)
  }

  function loadServiceSelector(){
    $('.methods input').change(function(e){
      var serviceName = $(e.currentTarget).attr('id')
      classService = '.' + serviceName +'-service'
      $('.services-attrs > .row').hide()
      _.each($('.services-attrs input'), function(input){
        $(input).prop('disabled', true)
      })
      $(classService).slideDown()
      $(classService + ' input').prop('disabled', false)
      $(classService + ' .checkbox').removeClass('disabled')
      if(serviceName == 'fixed'){
        enableTitleField()
        disableCountrySelector(gp.network)
        loadFixedOptions()
      }else if( serviceName == 'oca'){
        enableTitleField()
        enableCountrySelector()
        disableFixedStatesPrices()
      }else if( serviceName == 'express' ) {
        enableTitleField()
        disableCountrySelector(gp.network)
      }else{
        disableTitleField()
         if($('#international:enabled:checked').length > 0){
          enableCountrySelector()
        }else{
          disableCountrySelector(gp.network)
        }
      }
    })

    if( $('.methods .radio.checked').length > 0 ){
      $radioToSelect = $('.methods .radio.checked')
    }
  }

  function isEasypostChoosen(){
    return $('#easypost:checked').length > 0
  }

  function isInternationalEasypostChoosen(){
    return $('#international:checked').length > 0
  }

  function isDomesticEasypostChoosen(){
    return $('#domestic:checked').length > 0
  }

  function performValidations(){
    $('form#new_mkp_zone').on('submit', function(e){

      function stopSubmit(){
        e.stopPropagation()
        e.preventDefault()
        return false
      }

      function atLeastOneUSACarrier(){
        if($('.easypost-service .carriers input:checked:enabled').length < 1) return false
        return true
      }

      function atLeastOneInternationalSpeed(){
        if($('.easypost-service .international-speeds input:checked').length < 1) return false
        return true
      }

      function atLeastOneDomesticSpeed(){
        if($('.easypost-service .domestic-speeds input:checked').length < 1) return false
        return true
      }

      var $inputs_required = $('.new-service-form form input.required:enabled')
      var halt = false

      _.each($inputs_required, function(input){
        $input = $(input)
        if($input.val() == ''){
          $input.addClass('error')
          halt = true
          $input.click(function(){
            $input.removeClass('error')
          })
        }else{
          $input.removeClass('error')
        }
      })

      if(halt){
        alert(I18n.t('javascripts.mkp.checkout.fields_alert'))
        return stopSubmit()
      }

      if(isEasypostChoosen()){
        if(!atLeastOneUSACarrier()){
          alert('At least one carrier must be checked')
          return stopSubmit()
        }
        if(isInternationalEasypostChoosen()){
          if (!atLeastOneInternationalSpeed()){
            alert('At least one international speed must be checked')
            return stopSubmit()
          }
        }else if(isDomesticEasypostChoosen()){
          if(!atLeastOneDomesticSpeed()){
            alert('At least one domestic speed must be checked')
            return stopSubmit()
          }
        }else{
          alert('Choose Domestic or International')
          return stopSubmit()
        }
      }
    })
  }

  function initNewServiceForm(){
    $('.new-service-button').click(function(e){
      $('.new-service-form').slideDown()
    })
  }

  function loadDomesticEasypostSelector(){
    var $countriesSelector = $('#zone_countries')
    var $domestic = $('#domestic')
    $domestic.change(function(){
      if ( $domestic.is(':checked') ){
        $('.domestic-speeds').slideDown()
        $('.domestic-speeds input').prop('disabled', false)
        $('.domestic-speeds .checkbox').removeClass('disabled')

        var countriesWithUSA = $countriesSelector.select2('val')
        countriesWithUSA.push('US')
        $countriesSelector.select2('val', _.uniq(countriesWithUSA))

      }else{
        $('.domestic-speeds input').prop('disabled', true)
        $('.domestic-speeds').hide()

        var countriesWithoutUSA = _.without($countriesSelector.select2('val'), 'US')
        $countriesSelector.select2('val', countriesWithoutUSA)
      }
    })
  }

  function enableInternationalEasypostOptions(){
    var $internationalSpeedsContainer = $('.international-speeds')
    $internationalSpeedsContainer.slideDown()
    $internationalSpeedsContainer.find('input').prop('disabled', false)
    $internationalSpeedsContainer.find('checkbox').removeClass('disabled')
  }

  function disableInternationalEasypostOptions(){
    var $internationalSpeedsContainer = $('.international-speeds')
    $internationalSpeedsContainer.find('input').prop('disabled', true)
    $internationalSpeedsContainer.hide()
  }

  function loadInternationalEasypostSelector(){
    var $international = $('#international')
    $international.change(function(){
      if ( $international.is(':checked') ){
        enableInternationalEasypostOptions()
        enableCountrySelector()
        enableCustomsSignerName()
      }else{
        disableInternationalEasypostOptions()
        disableCustomsSignerName()
        disableCountrySelector('US')
      }
    })
    if ( $international.is(':checked') ){
      enableInternationalEasypostOptions()
      enableCountrySelector()
      enableCustomsSignerName()
    }else{
      disableInternationalEasypostOptions()
      disableCountrySelector('US')
      disableCustomsSignerName()
    }
  }

  function enableFixedStatesPrices(){
    $('.fixed-service .states').show()
    $('.fixed-service .states input').prop('disabled', false)
  }

  function disableFixedStatesPrices(){
    $('.fixed-service .states').hide()
    $('.fixed-service .states input').prop('disabled', true)
  }

  function disableFixedPriceInput(){
    $('.fixed-service .price').hide()
    $('.fixed-service .price input').prop('disabled', true)
  }

  function enableFixedPriceInput(){
    $('.fixed-service .price').show()
    $('.fixed-service .price input').prop('disabled', false)
  }

  function loadFixedOptions(){
    $('#states_prices_checkbox').change(function(){
      if($('#states_prices_checkbox:checked').length > 0){
        enableFixedStatesPrices()
        disableFixedPriceInput()
      }else{
        disableFixedStatesPrices()
        enableFixedPriceInput()
      }
    })
    if ($('#states_prices_checkbox:checked').length > 0){
      enableFixedStatesPrices()
      disableFixedPriceInput()
    }else{
      disableFixedStatesPrices()
      enableFixedPriceInput()
    }
  }

  function loadServiceWhenEditing(){
    if (typeof $radioToSelect != 'undefined'){
      $radioToSelect.click()
    }else{
      $('.methods .radio:first').click()
    }
    var $countriesSelector =  $('#zone_countries')
    var countries_chose = $('.countries-editing').html()
    if(countries_chose.length > 2){
      selected = []
      _.each(GEO_CONSTANTS.countries, function(name, code){
        if(countries_chose.search(code) > 1){
          selected.push(code)
        }
      })
      if(selected.length > 0){
        $countriesSelector.select2('val', selected)
      }
    }else{
      if(gp.network == 'US'){
        $countriesSelector.select2('val', 'US')
      }else if(gp.network == 'AR'){
        $countriesSelector.select2('val', 'AR')
      }else if(gp.network == 'CL'){
        $countriesSelector.select2('val', 'CL')
      }
    }
  }

  function loadWorkaroundForLinkOnFoundationTabs(){
    $('.go-to-services').click(function(e){
      var url = $(e.currentTarget).attr('href')
      window.location.href = url
    })
  }

  initNewServiceForm()
  initCountriesSelector()
  loadServiceSelector()
  loadDomesticEasypostSelector()
  loadInternationalEasypostSelector()
  performValidations()
  loadFixedOptions()
  loadServiceWhenEditing()
  loadWorkaroundForLinkOnFoundationTabs()
});

;(function(){
  gp.Lux.ancestryMixin = {
    // Get root models (models that doesn't have ancestors)
    roots: function(){
      return this.where({ancestry: null})
    },

    // Function to cache ancestors ids in the model as an array
    // Only accepts a valid model instance for performance reasons
    ancestorsIdsOf: function(model){
      if( !model._ancestry ) {
        var ancestry = model.get('ancestry')
        if( ancestry && ancestry.length ){
          ancestry = _.map(ancestry.split('/'), function(id){ return +id })
        } else {
          ancestry = []
        }
        model._ancestry = ancestry
      }
      return model._ancestry
    },

    fatherIdOf: function(model){
      return _.last(this.ancestorsIdsOf(model)) || null
    },

    // Ancestors of model, model can be either a record or an id
    ancestorsOf: function(model){
      var that = this
      model = this.get(model)
      if( !model ) return null
      return _.map(this.ancestorsIdsOf(model), function(id){
        return that.get(id)
      })
    },

    // Children of model, model can be either a record or an id
    childrenOf: function(model){
      var that = this
      model = this.get(model)
      if( !model ) return null
      var id = +model.id
      return this.filter(function(m){
        return that.fatherIdOf(m) === +model.id
      })
    },

    // Descendants of model, model can be either a record or an id
    descendantsOf: function(model){
      var that = this
      model = this.get(model)
      if( !model ) return null
      return this.filter(function(m){
        return _.contains(that.ancestorsIdsOf(m), model.id)
      })
    },

    // Return an arrays with the descendants of model and itself
    subtreeOf: function(model){
      var that = this
      model = this.get(model)
      if( !model ) return null
      var descendants = this.descendantsOf(model)
      descendants.unshift(model)
      return descendants
    },

    // Return an arrays with the descendants of model and itself
    siblingsOf: function(model){
      var that = this
      model = this.get(model)
      if( !model ) return null
      var fatherId = this.fatherIdOf(model)
      return this.filter(function(m){
        return fatherId === that.fatherIdOf(m)
      })
    }
  }
})()
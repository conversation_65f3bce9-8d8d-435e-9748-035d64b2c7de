gp.Lux.Product = gp.App.Model.extend({
  paramRoot: 'product',

  url: function(){
    var shop_id = this.collection.params.shop_id
    if( !shop_id ) shop_id = $('meta[name="shop_id"]').attr('content')
    return '/manage/ajax/products/'+this.id+gp.Helpers.url.toParams({
      shop_id: shop_id,
      network: gp.network
    })
  },

  parse: function(r){
    return (r && r.product) ? r.product : r
  },

  getCategoryId: function(){
    return +this.get('category_id')
  },

  getCategoryName: function(){
    var category = gp.categories.get(this.getCategoryId())
    return category ? category.get('name') : ''
  },

  getManufacturerName: function(){
    var mfr = gp.manufacturers.get(this.get('manufacturer_id'))
    return mfr ? mfr.get('name') : ''
  },

  getSportsNames: function(){
    var sports = this.get('sports_ids') || []
    sports = _.map(sports, function(id){ return gp.sports.get(id).get('name') })
    return sports.length ? sports.join(', ') : '-'
  }
})

gp.Lux.Products = gp.App.Collection.extend({
  model: gp.Lux.Product,
  url: function(){
    return '/manage/ajax/products'+gp.Helpers.url.toParams(this.params)
  },

  params: {},
  info: {},

  parse: function(r){
    this.setParams(r.params, true)
    this.info = r.info
    return r.products
  },

  fetch: function(options){
    options = _.extend({ reset: true }, options)
    return gp.App.Collection.prototype.fetch.call(this, options)
  },

  setParams: function(params, override){
    if( !params && params !== undefined ){
      this.params = {}
      this.trigger('params:change')
    } else {
      if( override ){
        this.params = params
      } else {
        _.extend(gp.products.params, params)
      }
      this.trigger('params:change')
    }
    return this
  },

  setCategoryId: function(category_id, options){
    this.setParams({ category_id: category_id, current_page: 1})
    options = _.extend({ reset: true }, options)
    return this.fetch.call(this, options)
  },

  setRefinement: function(refinement){
    this.setParams({ refinement: refinement, current_page: 1 })
    return this
  },

  fetchPage: function(page, options){
    this.setParams({ current_page: +page })
    options = _.extend({ reset: true }, options)
    return this.fetch.call(this, options)
  },

  fetchNextPage: function(options){
    var page = +(this.params.current_page) + 1
    return page <= this.info.total_pages ? this.fetchPage(page, options) : null
  },

  fetchPrevPage: function(options){
    var page = +(this.params.current_page) - 1
    return page >= 1 ? this.fetchPage(page, options) : null
  },

  search: function(query, options){
    this.setParams({ query: query, current_page: 1 })
    options = _.extend({ reset: true }, options)
    return this.fetch.call(this, options)
  }
})
$(document).ready( function(){
  $("#select_all").css("margin-bottom", "1px");
  $("#select_all").click(function(){
    if ($(this).is(':checked')){
      $(".checkbox-visibility").each(function(){
        $(this).prop('checked', true);
      });
    }else{
      $(".checkbox-visibility").each(function(){
        $(this).prop('checked', false);
      });
    }
  });

  $(".enable_all").click(function(e){
    shopId = parseInt($(this).attr("data-shop"));
    productIds = [];
    $(".checkbox-visibility").each(function(){
      if($(this).is(':checked')){
        id = $(this).attr('id');
        productIds.push(id);
      }
    });
    enableAll(shopId, productIds);
  }); 

  $(".disabled_all").click(function(e){
    shopId = parseInt($(this).attr("data-shop"));
    productIds = [];
    $(".checkbox-visibility").each(function(){
      if($(this).is(':checked')){
        id = $(this).attr('id');
        productIds.push(id);
      }
    });
    disabledAll(shopId, productIds);
  }); 

  function enableAll(shopId, productIds) {
    $.ajax({
      url: "/manage/shops/" + shopId + "/products/enable_all",
      type: 'post',
      data:{product_ids: productIds},
      async: true
    }).done(function(){
      location.reload();
      console.log("productos habilitados" + productIds);
    }).fail(function(){
      console.log("error");
    })
  }  

  function disabledAll(shopId, productIds) {
    $.ajax({
      url: "/manage/shops/" + shopId + "/products/disabled_all",
      type: 'post',
      data:{product_ids: productIds},
      async: true
    }).done(function(){
      location.reload();
      console.log("productos deshabilitados" + productIds);
    }).fail(function(){
      console.log("error");
    })
  }  
});
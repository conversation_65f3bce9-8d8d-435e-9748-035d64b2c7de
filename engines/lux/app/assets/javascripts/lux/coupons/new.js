$(function(){
  if( !gp.controller.coupons || !gp.action['new'] ) return

  function initCouponType() {
    $('#coupon_policy').change(function(){
      if ($(this).val() === "percent"){
        $('.amount').css('display', 'none')
        $('.percent').css('display', 'inherit')
        $('.amount input').removeClass('required')
        $('.percent input').addClass('required')
      }else{
        $('.amount').css('display', 'inherit')
        $('.percent').css('display', 'none')
        $('.percent input').removeClass('required')
        $('.amount input').addClass('required')
      }
    })
  }

  function initDateFields() {
    $('#starts_at').fdatepicker({format: 'yyyy/mm/dd'})
    $('#expires_at').fdatepicker({format: 'yyyy/mm/dd'})
  }

  function initRestrictions() {
    var categoriesSelector = initCategoriesRestrictionSelector()
    initRestriction('categories', categoriesSelector)

    var manufacturersSelector = initManufacturersRestrictionSelector()
    initRestriction('manufacturers', manufacturersSelector)

    var usersSelector = initUsersRestrictionSelector()
    initRestriction('users', usersSelector)
  }

  function initCategoriesRestrictionSelector() {
    var categoriesSelector = $('#categories-restriction')
    categoriesSelector.select2({formatResult: formatCategoryForSelector})
    categoriesSelector.select2('enable', false)

    return categoriesSelector
  }

  function initManufacturersRestrictionSelector() {
    var manufacturersSelector = $('#manufacturers-restriction')
    manufacturersSelector.select2()
    manufacturersSelector.select2('enable', false)

    return manufacturersSelector
  }

  function initUsersRestrictionSelector() {
    var usersSelector = $('#users-restriction')
    var restrictionUsersUrl = usersSelector.data('restriction_users_url')

    usersSelector.select2({
      placeholder: I18n.t('javascripts.lux.coupons.new.user_restriction_placeholder'),
      multiple: true,
      minimumInputLength: 2,
      ajax: {
        url: restrictionUsersUrl,
        data: function(term) { return {email: term} },
        results: function (data, page) {
          return {results: data}
        }
      }
    })
    usersSelector.select2('enable', false)

    return usersSelector
  }

  function initRestriction(name, selector) {
    var actionRadioButtons = $('input[name=' + name + '_restriction_action]')
    actionRadioButtons.siblings('.radio').click(function(e) {
      selector.select2('enable', true)
      var radio = $(this).siblings('input')
      var action = radio.val()
      var selectorPrevName = selector.attr('name')
      var selectorNewName = selectorPrevName.replace('included_' + name, action).replace('excluded_' + name, action)
      selector.attr('name', selectorNewName)
    })
  }

  var formatCategoryForSelector = function(category) {
    var originalOption = category.element
    var depth = $(originalOption).data('depth')
    var indent = Array(depth*4).join('&nbsp;')
    var name = indent + category.text

    return name;
  }

  function initSaveButton() {
    $('#save').click(function(e){
      e.preventDefault()
      var inputs_required = $('input.required, textarea')
      var halt = false
      _.each(inputs_required, function(input){
        input = $(input)
        if(input.val() == ""){
          input.addClass('error')
          halt = true
          input.click(function(){
            input.removeClass('error')
          })
        }else{
          input.removeClass('error')
        }
      })

      if(halt){
        alert(I18n.t('javascripts.lux.coupons.new.fields_not_required'))
        return
      }

      $('form').submit()
    })
  }

  initCouponType()
  initDateFields()
  initRestrictions()
  initSaveButton()
});

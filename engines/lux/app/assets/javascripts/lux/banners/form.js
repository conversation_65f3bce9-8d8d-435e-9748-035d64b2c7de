;if( gp.controller.banners && (gp.action['new'] || gp.action.edit) ) $(function(){
  function initDateFields() {
    $('#starts_on').fdatepicker({format: 'yyyy/mm/dd'})
    $('#ends_on').fdatepicker({format: 'yyyy/mm/dd'})
  }

  function saveAsync(field){
    $.ajax({
       url: $('form').last().attr('action'),
       type: 'PUT',
       data: $("form").serialize(),
       dataType: 'json',
       beforeSend: function(){
         $(field).addClass('autosave')
       },
       complete: function(){
         $(field).removeClass('autosave')
       }
    })
  }

  function resetTextPosition(){
    $('.ui-draggable').attr('style', '')
    $('#_banner_shopposition').val('')
  }

  function initAsyncAutoSave(){
    $('form input:not(:file,:checkbox), form textarea').change(function(e){
      saveAsync(e.currentTarget)
    })
  }

  function initLiveWebHeadline(){
    $headline = $('#banner_shop_web_headline')
    $web_headline_preview = $('.desktop-content .banner-content-text .headline')
    $web_headline_preview.text($headline.val())
    $headline.on('keyup', function(event) {
      var stt = $(this).val()
      $web_headline_preview.text(stt)
    })
  }

  function initLiveWebDescription(){
    $description = $('#banner_shop_web_description')
    $web_description_preview = $('.desktop-content .banner-content-text .description')
    $web_description_preview.text($description.val())
    $description.on('keyup', function(event) {
      var stt = $(this).val()
      $web_description_preview.text(stt)
    })
  }

  function initLiveMobileHeadline(){
    $headline = $('#banner_shop_mobile_headline')
    $mobile_headline_preview = $('.mobile-content .banner-content-text .headline')
    $mobile_headline_preview.text($headline.val())
    $headline.on('keyup', function(event) {
      var stt = $(this).val()
      $mobile_headline_preview.text(stt)
    })
  }

  function initLiveMobileDescription(){
    $description = $('#banner_shop_mobile_description')
    $mobile_description_preview = $('.mobile-content .banner-content-text .description')
    $mobile_description_preview.text($description.val())
    $description.on('keyup', function(event) {
      var stt = $(this).val()
      $mobile_description_preview.text(stt)
    })
  }


  function initUploadRedirect(){
    $('form input:file, form select ').change(function(e){
      var input = $("<input>").attr({'type':'hidden','name':'not_redirect'})
      $(input).appendTo($(this).closest('form'))
      var heightPosition = $(window).scrollTop() + $(window).height() / 2
      $(this).closest('form').submit()
    })
  }

  function initTemplates(){
    var templates = $('form input:radio').map(function(a, b){ return b.value}).toArray().join(' ')
    $('form input:radio').change(function(e){
      $('.banner').removeClass(templates)
      $('.banner').addClass(e.currentTarget.value)
      if($('.banner-uploader-desktop .banner').hasClass('paint')){
        $('.desktop-content .banner-content-text').draggable( "option", "axis", "y" );
      }else{
        $('.desktop-content .banner-content-text').draggable( "option", "axis", "" );
      }
    })
  }

  function initTemplateRadioButton(){
    var $template_chose = $('form input:radio:checked')
    if ($template_chose.size()){
      $template_chose.change()
    }else{
      $('form input:radio').first().click()
    }
    $('form input:radio').change(function(e){
      resetTextPosition()
    })
  }

  function initSaveAsADraftButton(){
    $saveAsDraftButton = $('#save-as-draft')
    $saveAsDraftButton.click(function(e){
      e.preventDefault()
      $draftInput = $('#banner_shop_draft')
      $form = $('.shop-banner form')
      $draftInput.val('true')
      $form.submit()
    })
  }

  function initPublishButton(){
    $publishButton = $('#save')
    $publishButton.click(function(e){
      e.preventDefault()

      var inputs_required = $('input.required, textarea.required')
      var halt = false
      _.each(inputs_required, function(input){
        input = $(input)
        if(input.val() == ""){
          input.addClass('error')
          halt = true
          input.click(function(){
            input.removeClass('error')
          })
        }else{
          input.removeClass('error')
        }
      })

      if(halt){
        alert('Please, fill all required fields.')
        return
      }

      $draftInput = $('#banner_shop_draft')
      $draftInput.val('false')

      $('.shop-banner form').submit()
    })
  }

  function initBannerContentDraggable(){
    $('.banner a').removeClass('full-link')
    $('.desktop-content .banner-content-text').draggable({stop: function(event, ui){
      var left = ((parseInt($('.ui-draggable').css('left')) / parseInt($(this).parent().width())) * 100 ) +"%"
      var top  = ((parseInt($('.ui-draggable').css('top')) / parseInt($(this).parent().parent().height())) * 100 ) +"%"
      $('#_banner_shopposition').val('left:'+left+'; top:'+top)
    }})
  }

  initDateFields()
  initUploadRedirect()
  initBannerContentDraggable()
  initTemplates()
  initTemplateRadioButton()
  initLiveWebHeadline()
  initLiveWebDescription()
  initLiveMobileHeadline()
  initLiveMobileDescription()
  initAsyncAutoSave()
  initSaveAsADraftButton()
  initPublishButton()
});

//= require_self
//= require app/locale
//
;(function(){
  var root = this

  root.$window = $(window)
  root.$document = $(document)
  root.$body = $('body')
  root.$html = $('html')

  var gp = root.gp = root.gp || {}
  _.extend(gp, {
    Lux: {
      Helpers: {},
      Views: {}
    },
    controller: {},
    action: {},
    debug: false,
    user: false,
    network: null,
    mobile: root.$html.hasClass('mobile')
  })
  gp.pubSub = _.extend({}, Backbone.Events)
  gp.lux = gp.lux || {}

  // View if the server is on development
  if( $('meta[name="environment"][content="development"]').length
  ||  $('meta[name="environment"][content="test"]').length ) {
    gp.debug = true
  }

  // View if the server is on staging server
  if( $('meta[name="context"][content="staging"]').length ) {
    gp.onStaging = true
  }

  // Check if the user is logged in
  if( $('meta[name="user"]').length ) gp.user = true

  // Set Network
  var $network = $('meta[name="network"]')
  if( $network.length ){
    gp.network = $network.attr('content')
  }

  // Set controller and action variables.
  var method = $('body').attr('id').split('-')
  gp.controller[method[0]] = true
  gp.action[method[1]] = true

  // Send token on every ajax call that is not GET
  gp.CSRFtoken = $('meta[name="csrf-token"]').attr('content')

  if( typeof gp.CSRFtoken !== 'string' || !gp.CSRFtoken.length ) {
    gp.CSRFtoken = null
  } else {
    $.ajaxPrefilter(function(options, originalOptions, jqXHR) {
      jqXHR.setRequestHeader('X-CSRF-Token', gp.CSRFtoken)
    })
  }
})()

$(function(){
  if( window.FastClick ) FastClick.attach(document.body)
})

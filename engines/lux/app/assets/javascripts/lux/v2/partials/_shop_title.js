$(function() {
  $('.managed-shops').selectize({
    render: {
      option: function (data, escape) {
        return "<div data-value=" + data.value + " data-shop-url='" + data.shopUrl + "'>" + data.text + "</div>"
      }
    },
    onChange: function(value) {
      if( value !== "" && this.getOption(value).length === 1 ){
        window.location = this.getOption(value).data('shop-url')
      }
    },
    allowEmptyOption: true,
    create: false
  })
})

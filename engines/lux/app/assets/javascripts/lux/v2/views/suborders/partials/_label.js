gp.Lux.Views['order/show/label'] = Backbone.View.extend({
  events: {
    'click .actions .cancel, .actions .deliver':  'sendAction'
  },

  initialize: function(){
    _.bindAll(
      this,
      'actionInit',
      'actionDone'
    )

    this.$actionButtons = this.$el.find('.actions a')

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'label:action:init': this.actionInit,
      'label:action:done': this.actionDone
    })
  },

  sendAction: function(evt){
    var $button = $(evt.currentTarget)
    var that = this,
        url = $button.data('endpoint')

    evt.preventDefault()
    evt.stopPropagation()
    gp.pubSub.trigger('label:action:init')
    $.ajax({
        url:      url,
        dataType: 'json',
        type:     'post',
        cache:    false
    }).done(function(response){
      if( response.status === 'success' ){
        window.location.reload(true)
      }
    }).fail(function(){
      gp.pubSub.trigger('label:action:done')
    })
    return false
  },

  actionInit: function(){
    this.undelegateEvents()
    this.$actionButtons.addClass('disabled')
  },

  actionDone:  function(){
    this.$actionButtons.removeClass('disabled')
    this.delegateEvents()
  },


})

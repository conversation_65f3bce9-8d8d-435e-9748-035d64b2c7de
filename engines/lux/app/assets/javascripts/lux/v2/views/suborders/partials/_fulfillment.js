gp.Lux.Views['order/show/fulfillment'] = Backbone.View.extend({
  className: "fulfillment",
  events: {
    'click .actions .cancel-it':      'cancel',
    'click .actions .confirm-button': 'submit'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'submitInit',
      'submitSuccess',
      'submitFail'
    )

    this.items = options.item_ids
    this.suborder = options.suborder_id
    gp.pubSub.trigger('order:fulfillment:init')
  },

  cancel: function(){
    gp.pubSub.trigger('order:fulfillment:cancelled')
  },

  submit: function(){
    var that = this,
        data = this.dataToSubmit()
    //if(!this.shipmentIsSelected(data)) return
    if(!this.priceIsNotSet(data)){
      alert('El campo precio es obligatorio')
      return;
    };
    this.trigger('submit:init')
    $.ajax({
        url: '/manage/ajax/labels',
        data: data,
        type: 'post',
        cache: false
    }).done(function(data){
      that.trigger('submit:success', data)
    }).fail(function(data){
      that.trigger('submit:fail')
      alert(data.responseJSON.message)
    })
  },

  render: function(){
    this.getLabelForm()
    return this;
  },

  getLabelForm: function(){
    var that = this
    $.ajax({
        url: '/manage/ajax/labels/new',
        data: {
          suborder_id: this.suborder,
          item_ids: this.items
        },
        type:'post',
        cache: false
    }).done(function(data){
      that.$el.html(data)
      that.ready()
      that.$el.find('.service-detail').click(function(ev){
        that.$el.find('.service-detail').removeClass('selected')
        $(ev.currentTarget).addClass('selected')
        var gateway = $(ev.currentTarget).data('gateway')
        that.$el.find("input[name='prepaid[gateway]']").val(gateway)
      })
      that.$el.find('.service-detail[data-gateway="Lion"]').click()
    }).fail(function(){
      gp.pubSub.trigger('order:fulfillment:init:fail')
    })
  },

  ready: function(){
    var that = this
    this.$options = this.$el.find('.options .columns > span')
    this.$optionsSetup = this.$el.find('.options-setup > div')
    this.$optionsSetup.hide().removeClass('hidden')
    this.initializeOptionsSetup()

    _chooseOption = function(evt){
      var $this = $(this)
      if( $this.data('option') !== that.selected_option) {
        _unchooseActiveOption()
        that.selected_option = $this.data('option')
        that.$optionsSetup.filter('.'+that.selected_option).show()
        $this.addClass('selected')
        $this.find('i').swapClass('i-radio-off', 'i-radio-on')
      }
    }
    _unchooseActiveOption = function(){
      if( typeof that.selected_option !== 'undefined' ){
        var $selectedOption = that.$options.filter('.selected')
        that.$optionsSetup.filter('.'+that.selected_option).hide()
        $selectedOption.removeClass('selected')
        $selectedOption.find('i').swapClass('i-radio-on', 'i-radio-off')
      }
    }

    this.$options.each(function(i, option){
      $(option).on('click', _chooseOption)
    })
    this.$options.first().trigger('click')

    // Local Events
    this.on('submit:init', this.submitInit)
    this.on('submit:success', this.submitSuccess)
    this.on('submit:fail', this.submitFail)
  },

  shipmentIsSelected: function (data) {
    if(data.option!="prepaid")return true;
    return (data.prepaid.gateway != "")
  },

  priceIsNotSet: function (data) {
    if ( data.option != "other" )  return true;
    value = parseFloat(data.other.price)
    return value > 0
  },

  initializeOptionsSetup: function(){
    var $selects = this.$optionsSetup.find('select')
    if( $selects.length > 0 ){
      $selects.selectize({
        persist: false,
        selectOnTab: true,
        create: true,
        createOnBlur: true,
        render: {
          option_create: function(data, escape) {
            return '<div class="create">'+I18n.t('lux.v2.js.suborders.fulfillment.other_courier')+'<strong>' + escape(data.input) + '</strong>&hellip;</div>';
          }
        }
      })
    }
  },

  dataToSubmit: function(){
    var shipmentDate = ''
    if(document.getElementById("prepaid_shipment_from_date_3i")) {
      var day = document.getElementById("prepaid_shipment_from_date_3i");
      var month = document.getElementById("prepaid_shipment_from_date_2i");
      var year = document.getElementById("prepaid_shipment_from_date_1i");
      var hour = document.getElementById("prepaid_shipment_from_date_4i");
      var minutes = document.getElementById("prepaid_shipment_from_date_5i");
      var fromDate = day.options[day.selectedIndex].value + '/' + month.options[month.selectedIndex].value + '/' + year.options[year.selectedIndex].value + ' ' + hour.options[hour.selectedIndex].value + ':' + minutes.options[minutes.selectedIndex].value + ':00';
      var day = document.getElementById("prepaid_shipment_to_date_3i");
      var month = document.getElementById("prepaid_shipment_to_date_2i");
      var year = document.getElementById("prepaid_shipment_to_date_1i");
      var hour = document.getElementById("prepaid_shipment_to_date_4i");
      var minutes = document.getElementById("prepaid_shipment_to_date_5i");
      var toDate = day.options[day.selectedIndex].value + '/' + month.options[month.selectedIndex].value + '/' + year.options[year.selectedIndex].value + ' ' + hour.options[hour.selectedIndex].value + ':' + minutes.options[minutes.selectedIndex].value + ':00';
      var shipmentDate = fromDate + ', ' + toDate
    }

    var $optionSetup = this.$optionsSetup.filter('.'+this.selected_option)
    return _.extend({
      suborder_id: this.suborder,
      item_ids: this.items,
      option: this.selected_option,
      shipment_date: shipmentDate
    }, $optionSetup.find('form').formParams())
  },

  submitInit: function(){
    this.enableForm(false)
  },

  submitSuccess: function(response){
    if( response.status === 'success' ){
      window.location.reload(true)
    }
  },

  submitFail: function(){
    this.enableForm(true)
  },

  enableForm: function(enable){
    var $form = this.$optionsSetup.filter('.'+this.selected_option).find('form')
    var $cancelBtn = this.$el.find('.actions .cancel-it')
    var $submitBtn = this.$el.find('.actions .confirm-button')
    if( enable ){
      $cancelBtn.removeClass('disabled')
      $submitBtn.removeClass('disabled')
      $form.find('input, select').removeAttr('disabled')
      this.delegateEvents()
    } else {
      this.undelegateEvents()
      $cancelBtn.addClass('disabled')
      $submitBtn.addClass('disabled')
      $form.find('input, select').attr('disabled', 'disabled')
    }
  }

})

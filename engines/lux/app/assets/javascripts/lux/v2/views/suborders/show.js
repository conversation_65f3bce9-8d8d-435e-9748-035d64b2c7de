;if( gp.controller.suborders && gp.action.show ) (function(){

  gp.Lux.Views['order/show'] = Backbone.View.extend({
    initialize: function(){
      var $self = this.$el
      this.set_order_object()

      // Initialize the Unfulfilled Items Component
      var $items_to_fulfill = $self.find('main .unfulfilled')
      if( $items_to_fulfill.length > 0 ) {
        var unfulfilled_items = new gp.Lux.Views['order/show/unfulfilled_items']({
          el: $items_to_fulfill
        })
      }

      // Initialize each Label Components
      var $labels = $self.find('main .shipment .delivery .label')
      _($labels).each(function(label){
        new gp.Lux.Views['order/show/label']({
          el: label
        })
      })

    },

    set_order_object: function(options){
      gp.lux.order = options || {}
    }

  })

  new gp.Lux.Views['order/show']({
    el: $('#suborders-show .suborder')
  })
})()

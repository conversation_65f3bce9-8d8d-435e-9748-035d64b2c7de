;if( gp.controller.suborders && gp.action.show ) (function(){
  gp.Lux.Views['order/show/unfulfilled_items'] = Backbone.View.extend({
    events: {
      'click .actions .fulfill-items':   'displayFulfillmentFlow'
    },

    initialize: function(options){
      var $self = this.$el
      this.suborder_id = $self.data('soid')
      this.items = $self.find('> .items')
      this.actionsRow = $self.find('> .row')

      // Pub/Sub Events (need to explicit bind the "this")
      gp.pubSub.on({
        'order:fulfillment:init': this.hideElements,
        'order:fulfillment:cancelled order:fulfillment:init:fail': this.fulfillmentOver
      }, this)
    },

    displayFulfillmentFlow: function(evt){
      var item_ids = $(evt.target).data('ids')
      this.fulfillmentFlow = new gp.Lux.Views['order/show/fulfillment']({
        item_ids: item_ids,
        suborder_id: this.suborder_id
      })
      wea = document.getElementById($(evt.target).data('shipment-id')).getElementsByClassName('unfulfilled')[0]
      wea.append( this.fulfillmentFlow.render().el )
    },

    hideElements: function(){
      this.items.hide()
      this.actionsRow.hide()
    },

    fulfillmentOver: function(){
      this.fulfillmentFlow.remove()
      this.displayElements()
    },

    displayElements: function(){
      this.items.show()
      this.actionsRow.show()
    }

  })
})()

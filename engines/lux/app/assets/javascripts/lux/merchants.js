$(function(){
    if( !gp.controller.merchants || !gp.action.index ) return

    function initUsersSelector() {
      var usersSelector = $('#users')
      var restrictionUsersUrl = usersSelector.data('users_url')

      usersSelector.select2({
        placeholder: 'Specify an email address...',
        multiple: true,
        minimumInputLength: 2,
        maximumSelectionSize: 1,
        ajax: {
          url: restrictionUsersUrl,
          data: function(term) { return {email: term} },
          results: function (data, page) {
            return {results: data}
          }
        }
      })

      return usersSelector
    }

    $('.remove').click(function(){
      $(this).closest('form').submit()
    })

    $('#add-administrator').submit(function(e){
      if(usersSelector.select2('val').length < 1){
        e.preventDefault()
        return false
      }
    })

    var usersSelector = initUsersSelector()

});

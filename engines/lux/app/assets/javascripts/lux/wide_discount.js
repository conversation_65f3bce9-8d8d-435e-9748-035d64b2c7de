$(function(){
  if( !gp.controller.wide_discount ) return

  var ProductsListPaginator = gp.App.View.extend({
    className: 'products-list-pagination',

    events: {
      'click [data-page]': 'showPage',
      'click .prev': 'showPrevPage',
      'click .next': 'showNextPage'
    },

    initialize: function(options) {
      _.bindAll(
        this,
        'reset',
        'showNextPage',
        'showPrevPage',
        'showPage'
      )
      this.reset()
    },
    reset: function() {
      var that = this, c = this.collection
      var total = +c.info.total_pages
      var total_entries = +c.info.total_entries
      if( _.isNumber(total) && total > 1 ) {
        var links = []
        var page = +c.params.current_page
        var max_show = 9
        var addPage = function(p) {
          if( _.isArray(p) ) _.each(p, addPage)
          if( p === 0 ) {
            links.push('<li><span>...</span></li>')
          } else if( p >= 1 && p <= total ) {
            var className = page === p ? ' class="selected"' : ''
            links.push('<li'+className+' data-page="'+p+'">'+p+'</li>')
          }
        }
        if( page > 1 ) links.push('<li class="prev">&laquo;</li>')
        if( total > max_show ) {
          var half = Math.floor(max_show / 2)
          var start = page - half > 0 ? page - half : 1
          pages = _.range( start, start + max_show + 1 )
          while( _.last(pages) > total ) {
            pages.splice(-1, 1)
            var val = pages[0] - 1
            pages.splice(0, 0, val)
          }
          if( _.first(pages) != 1 ) {
            pages.splice(0, 2, 1, 0)
          }
          if( _.last(pages) != total ) {
            pages.splice(-2, 2, 0, total)
          }
          addPage(pages)
        } else {
          _.times(total, function(i){
            addPage( i + 1 )
          })
        }
        if( page < total ) links.push('<li class="next">&raquo;</li>')
        links = links.join('')
        this.$el.html(links)
        this.show()
      } else {
        this.$el.html('')
        this.hide()
      }
      $("#affected-products-amount").html(total_entries + " possibly affected products")
      return this
    },
    showPage: function(el) {
      var $el = $(el.currentTarget)
      new WideDiscountView().showProducts($el.text())
    },
    showNextPage: function() {
      var page = this.collection.params.current_page + 1
      new WideDiscountView().showProducts(page)
    },
    showPrevPage: function() {
      var page = this.collection.params.current_page - 1
      new WideDiscountView().showProducts(page)
    }
  })

  var WideDiscountView = gp.App.View.extend({
    className: 'wide-discount-pagination',

    events: {
      'change .restriction-select': 'getProductsFiltered'
    },

    initialize: function(options) {
      _.bindAll(
        this,
        'getProductsFiltered',
        'showProducts'
      )
      gp.products = new gp.Lux.Products()
    },
    getProductsFiltered: function(el) {
      this.showProducts()
    },
    showProducts: function(page){
      var $params = getFilterParams(page)

      gp.products = new gp.Lux.Products()
      if ($params) {
        gp.products.setParams($params, true)
          .fetch({
            success: successCallback
          })
      } else {
        $("#products-container").empty()
      }
    }
  })

  $('#mkp_shop_setting_discount_starts_at').fdatepicker({ format: 'yyyy-mm-dd' })
  $('#mkp_shop_setting_discount_ends_at').fdatepicker({ format: 'yyyy-mm-dd' })

  function getFilterParams(page) {
    var $categories = $('#categories-restriction')
    var $manufacturers = $('#manufacturers-restriction')
    var $shop_id = $('meta[name="shop_id"]').attr('content')

    if (!$categories.val() && !$manufacturers.val()) {
      return null
    }

    params = new Object()
    params['network'] = gp.network
    if ($shop_id) {
      params['shop_id'] = $shop_id
    }
    if (page) {
      params['current_page'] = page
    }
    if ($categories.val()) {
      params['category_ids'] = $categories.val().toString()
    }
    if ($manufacturers.val()) {
      params['manufacturer_ids'] = $manufacturers.val().toString()
    }
    return params
  }

  function initFormValidation(){
    $document.on('submit', '.edit-wide-sale', function(e){
      var $form = $(e.currentTarget)
      var $inputsRequired = $form.find('[required]')
      var halt = false

      $inputsRequired.each(function(i, el){
        var $el = $(el)
        if( !$el.val() ){
          halt = true
          $el.addClass('error')
          $el.one('click', function(){ $el.removeClass('error') })
        } else {
          $el.removeClass('error')
        }
      })

      if( halt ){
        e.stopPropagation()
        e.preventDefault()
        alert('Please, fill all required fields.')
        return
      }
    })
  }

  function initCategoriesRestrictionSelector() {
    var categoriesSelector = $("#categories-restriction")
    categoriesSelector.select2()

    return categoriesSelector
  }

  function initManufacturersRestrictionSelector() {
    var manufacturersSelector = $("#manufacturers-restriction")
    manufacturersSelector.select2()

    return manufacturersSelector
  }

  // Usamos la validacion de HTML5 excepto que el browser no la soporte
  if( !Modernizr.input.required ){
    initFormValidation()
  }


  var wideDiscountView = new WideDiscountView({
    el: $('.'+WideDiscountView.prototype.className)
  })


  function initRestrictionSelectors() {
    initCategoriesRestrictionSelector()
    initManufacturersRestrictionSelector()
  }

  initRestrictionSelectors()

  function successCallback(data) {
    $("#products-container").empty()

    var $template = _.template($('#products-results').html())

    var $data = getCompleteData(gp.products)
    $("#products-container").html( $template({
        products: $data
    }))
    var options = data[0]
    new ProductsListPaginator({
      el: this.$('.'+ProductsListPaginator.prototype.className),
      collection: options
    })
  }
  function getCompleteData(products) {
    var data = new Array()
    products.each(function(product){
      var item = product.toJSON()
      _.extend(item, {
        category_id: product.getCategoryId(),
        category_name: product.getCategoryName(),
        manufacturer_name: product.getManufacturerName()
      })
      data.push(item)
    })
    return data
  }
})

$(function(){
  if (!gp.controller.taxes || !gp.action.index) return

  $('#us_states').select2()

  // Add Tax Rate
  $('.add-rate').on('click', function(){
    var name = $('#us_states option:selected').text()
    var abbreviation = $('#us_states').val()

    $('#us_states option:selected').remove()
    $('.select2-chosen').text($('#us_states option:selected').text())
    $('.tax-rates table').append("\
      <tr class='state'>\
        <td>\
          " + name + "\
        </td>\
        <td class='state-tax-rate'>\
          <input id='_tax_rates_" + abbreviation + "rate' name='[tax_rates][" + abbreviation + "]rate' type='text' value='0.00'>\
          <span>%</span>\
        </td>\
        <td>\
          <input id='_tax_rates_" + abbreviation + "apply_to_shipping' name='[tax_rates][" + abbreviation + "]apply_to_shipping' type='checkbox' value='true'>\
        </td>\
        <td>\
          <span class='delete-tax-rate'>x</span>\
        </td>\
      </tr>")

      $('.setup-tax-rate').hide()
  })

  // Remove tax rate
  $document.on('click', '.delete-tax-rate', function(){
    $(this).parents('.state').remove()
  })
})

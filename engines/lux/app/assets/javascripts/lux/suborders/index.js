$(function(){
  if (!gp.controller.suborders || !gp.action.index) return

  var $startDate = $('#start_date')
  var $endDate = $('#end_date')

  $startDate.fdatepicker({ format: 'dd-mm-yyyy' })
  $endDate.fdatepicker({ format: 'dd-mm-yyyy' })

  var $form = $('form#filter')
  var $filterBtn = $('#filter-btn')
  var $exportBtn = $('#export-btn')

  $filterBtn.on( 'click', function(){
    $form.find('#format').remove()
    _submitForm()
  })
  $exportBtn.on( 'click', function(){
    var $format_field = $form.find('#format')
    if ($format_field.length > 0) {
      $format_field.val('csv')
    } else {
      $form.append('<input id="format" type="hidden" value="csv" name="format">')
    }
    _submitForm()
    $form.find('#format').remove()
  })

  function _submitForm() {
    $form.submit()
  }

})

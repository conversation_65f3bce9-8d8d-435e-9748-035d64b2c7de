 gp.Lux.AddItemView = gp.App.View.extend({
  events: {
    'click a.add-new': 'addItemClick',
    'change #order_item_variant_id': 'changeSelectedItem'
  },

  initialize: function() {
    _.bindAll(
      this,
      'addItemClick',
      'changeSelectedItem',
      'showItemStock',
      'addItemSubmit'
    )
    var $select = this.$el.find('#order_item_variant_id')
    var $form = this.$el.find('#add-item-form form')

    this.$quantityField = $form.find('#order_item_quantity')
    this.$currentStock = this.$el.find('.current-stock')
    this.$formErrors = this.$el.find('.form-errors')

    $select.select2()
    $select.trigger('change')

    $form.on('submit', this.addItemSubmit)
  },

  addItemClick: function(evt) {
    evt.preventDefault()
    this.$el.find('.order-item-form').toggle()
  },

  changeSelectedItem: function(evt) {
    var that = this
    var $select = $(evt.currentTarget)
    var variantId = $select.val()

    this.$formErrors.hide()

    $.ajax({
      url: '/manage/ajax/item_stock',
      data: { variant_id: variantId }
    }).done(function(data){
      var itemStock = data.response.stock
      that.showItemStock(itemStock)
    })
  },

  showItemStock: function(itemStock) {
    this.$currentStock.data('quantity', itemStock)
    this.$currentStock.text('Stock: ' + itemStock)
  },

  addItemSubmit: function(evt) {
    if (parseInt(this.$quantityField.val()) > this.$currentStock.data('quantity')) {
      this.$formErrors.show()
      return false
    }
  }
})


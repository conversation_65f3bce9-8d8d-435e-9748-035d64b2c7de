$(function(){
  if( !gp.controller.products || !gp.action.edit ) return

  var ProductsForm = new gp.Lux.ProductsFormView({ el:'.product-form' })
  ProductsForm.setup()

  $('#product-form').submit(function(ev) {
    var $form = $(ev.currentTarget)
    var $selectedGenders = $form.find('#gender-options input[type=checkbox]:checked')
    if ($selectedGenders.length == 0 ) {
      $form.append("<input type='checkbox' name='product[gender_ids][]' value='' checked />")
    }
  })

  $('#pictures-container').sortable({
      update: function( event, ui ) {
        var product_id = this.dataset.id;
        var pics = [];
        $(this.children).each(function(_,pic){ pics.push({id: pic.dataset.pic_id}) });
        var url =  "/manage/ajax/products/"+product_id+"/sort_pictures";
        $.ajax({
          url: url,
          data: {pics: pics},
          type: 'put',
          cache: false
        })
        .done(function(data) {})
        .fail(function(data) {});
      }
    });
  $('#pictures-container').disableSelection();
});

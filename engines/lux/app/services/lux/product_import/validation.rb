module Lux
  module ProductImport
    class Validation
      def self.of(product_attrs, variants_attrs, row, shop, event_to_validate)
        event_to_validate ||= :create

        if event_to_validate == :update
          product = Product.find(product_attrs.fetch(:id))
          product.assign_attributes(product_attrs.except(:id, :variants_attributes))
          product.valid?(event_to_validate) if product.changed?

          variant = Mkp::Variant.find(variants_attrs.fetch(:id))
          variant.assign_attributes(variants_attrs.except(:id))
          variant.changed? && variant.valid?(event_to_validate)

          variant.valid?(event_to_validate) if variant.changed?
        else
          product_attrs = product_attrs.select { |k, v| (k != :variants_attributes ||  k != :packages_attributes) }.except(:id)
          product = shop.products.build(product_attrs)

          # Add dummy product_id to pass validation.
          variants_attrs = variants_attrs.map { |v| v.merge(product_id: Mkp::Product.order(created_at: :desc).limit(10).pluck(:id).sample) }
          variants = variants_attrs.map do |variant|
            Mkp::Variant.new(variant.except(:id))
          end
          product.valid?(event_to_validate)
          variants.map do |variant|
            variant.valid?(event_to_validate)
          end
        end
        if event_to_validate == :create # Avoid check this when updating

          if row[:category].blank? && product_attrs[:category_id].blank?
            index = product.errors[:category].index("can't be blank")
            product.errors[:category].delete_at(index)
            product.errors.add(:category_id, 'is not a valid value')
          end
          if row[:manufacturer].blank? || product.manufacturer.blank?
            product.errors.add(:manufacturer_id, 'is not a valid value')
          end
          if row[:property_names].blank? && variants_attrs[:properties][:color].blank?

          end
        end
        variants.map do |variant|
          variant.errors.delete(:gp_sku)
          variant
        end
        product.errors.messages.merge(variants.map{ |v| v.errors.messages }.reduce({}, :merge) )
      end
    end
  end
end

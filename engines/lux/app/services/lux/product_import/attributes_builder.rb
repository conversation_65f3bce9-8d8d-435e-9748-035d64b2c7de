module Lux
  module ProductImport
    class AttributesBuilder
      attr_reader :errors

      def initialize(mapper, shop)
        @mapper = mapper
        @shop   = shop
        @cached_products_attrs = {}
        @errors = {}
      end

      def add(row)
        attrs = add_for_create(row)
        return false unless attrs
        old_variants = attrs[:product][:variants_attributes]
        old_package = attrs[:product][:package_attributes]

        attrs[:product].except(:variants_attributes)

        @errors = Validation.of(attrs[:product].except(:variants_attributes), attrs[:variants], row, @shop, row[:id].present? && :update)
        variants = old_variants ? (old_variants + attrs[:variants]) : attrs[:variants]
        package = old_package ? (old_package + attrs[:packages]) : attrs[:packages]
        associate_variants_and_product_attributes(attrs[:product], variants)
        associate_packages_and_product_attributes(attrs[:product], package)
      end

      def build
        @cached_products_attrs.values
      end

      def valid?
        @errors.empty?
      end

      private

      def add_for_create(row)
        attrs = {}
        attrs[:product] = @cached_products_attrs.fetch(row[:title]) do |title|
          @cached_products_attrs[title] = @mapper.to_product_attributes(row)
        end
        attrs[:variants] = @mapper.to_variants_attributes(row)
        attrs[:packages] = @mapper.to_packages_attributes(row)
        attrs
      end

      def associate_variants_and_product_attributes(product_attrs, variants_attrs)
        product_attrs[:variants_attributes] = variants_attrs
      end

      def associate_packages_and_product_attributes(product_attrs, packages_attrs)
        product_attrs[:packages_attributes] ||= packages_attrs
      end
    end
  end
end

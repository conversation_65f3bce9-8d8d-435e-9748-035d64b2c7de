module Lux
  class ChangeVisibility
    def initialize(product_ids:, visibility:)
      @product_ids = product_ids
      @visibility = visibility
    end

    def perform
      results = []
      products = Mkp::Product.where(id: @product_ids)
      products.each do |product|
        has_integration = product.has_any_shop_integration?
        case @visibility
        when 'show'
          if has_integration
            integration = product.external_object.integration
            integration.sync_visibility(product.external_object)
            product.update(available_on: Time.current - 1.day)
            update_visibility_attributes(product, true)
            if product.valid?
              update_visibility_attributes(product, true)
              results << OpenStruct.new(
                              {
                                success?: true, payload: {notice: I18n.t('lux.controllers.integration_product_show', integration_name: integration.type.demodulize, product_title: product.title)}
                              }
                            )
            else
              update_visibility_attributes(product, false)
              results <<  OpenStruct.new(
                              {
                                success?: false, payload: {error: "#{product.title}: #{product.errors.full_messages.to_sentence}"}
                              }
                            )
            end
          else
            product.update(available_on: Time.current - 1.day)
            if product.valid?
              product.variants.each(&:show)
              results << OpenStruct.new(
                                {
                                  success?: true, payload: {notice: I18n.t('lux.controllers.product_show', product_title: product.title)}
                                }
                              )
            else
              results << OpenStruct.new(
                              {
                                success?: false, payload: {error: I18n.t('lux.controllers.product_without_integration', product_title: product.title)}
                              }
                            )
            end
          end
        when 'hide'
          product.update(available_on: nil)
          product.variants.each(&:hide)
          if has_integration
            update_visibility_attributes(product, false)
            results << OpenStruct.new(
                            {
                              success?: true, payload: {notice: I18n.t('lux.controllers.product_hide', product_title: product.title)}
                            }
                          )
          else
            results << OpenStruct.new(
                            {
                              success?: false, payload: {error: I18n.t('lux.controllers.product_without_integration', product_title: product.title)}
                            }
                          )
          end
        end
      end
      return results
    end

    private
    def update_visibility_attributes(product, visibility)
      product.external_object.update(display: visibility)
      product.variants.flat_map(&:external_objects).each do |external_object|
        external_object.update(display: visibility)
      end
    end
  end
end
module Lux
  module ProductExporter
    extend self

    HEADER = ['slug',
              'ID',
              'SKU',
              'AV SKU',
              'EAN_CODE',
              'Title',
              'Description',
              'Category',
              'Category Full Path',
              'Property Name 1',
              'Property Value 1',
              'Property Name 2',
              'Property Value 2',
              'Property Name 3',
              'Property Value 3',
              'Manufacturer',
              'Price',
              'Price Without Taxes',
              'Sale Price',
              'Sale Price Without Taxes',
              'Available On',
              'Height',
              'Length',
              'Width',
              'Length Unit',
              'Weight',
              'Mass Unit',
              'Iva',
              'Current Cost',
              'Stock',
              'Variants Visibility',
              'Shop Visibility',
              'Shop Title',
              'Created At'].freeze

    def perform(products)
      CSV.generate do |csv|
        csv << build_header
        variants = Mkp::Variant.where(product_id: products).includes(product: %i[category]).order('mkp_variants.product_id desc')
        @lists_manufacturer = Mkp::Manufacturer.pluck(:id, :name).to_h
        variants.each_with_index do |variant, index|
          are_product_attributes_nedeed = index == 0 ? true : variant.product_id != variants[index - 1].product_id
          if index == 0 || variant.product_id != variants[index - 1].product_id
            @product = variant.product
            @dimensions = @product.packages.select("SUM(mkp_packages.width) AS width, SUM(mkp_packages.length) AS length, SUM(mkp_packages.height) AS height, SUM(mkp_packages.weight) AS weight").first
          end
          csv << build_content_row(variant, are_product_attributes_nedeed.presence)
        end
      end
    end

    private

    def build_header
      HEADER
    end

    def build_content_row(variant, are_product_attributes_nedeed)
      [
        @product.slug,
        @product.id,
        sanatize(variant.sku),
        variant.gp_sku,
        variant.try(:ean_code),
        are_product_attributes_nedeed && sanatize(@product.title).gsub(/[\r\n]?/, ''),
        are_product_attributes_nedeed && sanatize(@product.description.gsub(/[\r\n]?/, '').tr('"', "'")),
        are_product_attributes_nedeed && sanatize(@product.category.try(:name)),
        are_product_attributes_nedeed && sanatize(@product.category.try(:full_path_name)),
        variant.property_name(1),
        sanatize(variant.property_value(1)),
        variant.property_name(2),
        parse_number(variant.property_value(2)),
        variant.property_name(3),
        parse_number(variant.property_value(3)),
        are_product_attributes_nedeed && @lists_manufacturer[@product.manufacturer_id],
        are_product_attributes_nedeed && @product.regular_price.to_f,
        are_product_attributes_nedeed && @product.regular_price_without_taxes.to_f,
        are_product_attributes_nedeed && @product.sale_price.try(:to_f),
        are_product_attributes_nedeed && @product.sale_price_without_taxes.try(:to_f),
        are_product_attributes_nedeed && @product.available_on.try(:strftime, '%m/%d/%Y'),
        data_or_dd(are_product_attributes_nedeed && @dimensions[:height]),
        data_or_dd(are_product_attributes_nedeed && @dimensions[:length]),
        data_or_dd(are_product_attributes_nedeed && @dimensions[:width]),
        data_or_dd(are_product_attributes_nedeed && @product.length_unit),
        data_or_dd(are_product_attributes_nedeed && @dimensions[:weight]),
        data_or_dd(are_product_attributes_nedeed && @product.mass_unit),
        are_product_attributes_nedeed && @product.iva,
        variant.current_cost.to_f,
        variant.quantity,
        @product.available? && variant.quantity > 0 ? "visible" : "no visible",
        variant.shop.visible? ? "visible" : "--",
        data_or_dd(variant.shop.title.to_s),
        variant.created_at.to_date
      ]
    end

    def sanatize(field)
      field.gsub(/[^A-Za-z0-9_.\- ]/, '') if field.present?
    end

    def parse_number(number)
      number.tr(',', '.') if number.present?
    end

    def data_or_dd(data)
      data.blank? || data == 0 ? '--' : data
    end
  end
end

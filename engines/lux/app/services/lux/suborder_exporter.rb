module Lux
  class SuborderExporter < Reporting::Exporter
    attr_reader :suborders
    GATEWAYS = %w[decidir decidir_distributed mercadopago mercadopago_ticket firstdata
                  firstdata_distributed visa_puntos todopago todopago_decidir system_points
                  tarjeta_digital].freeze

    MAPPED_GATEWAY_STORES = {
      'decidir' => %w[Decidir AvenidaDecidir],
      'decidir_distributed' => %w[AvenidaDecidirDistributed DecidirDistributed DecidirDistributedMacro],
      'modo_distributed' => %w[AvenidaModoDistributed],
      'empty' => %w[Empty],
      'firstdata' => %w[FirstData],
      'firstdata_distributed' => %w[FirstDataDistributed],
      'mercadopago' => %w[Mercadopago],
      'mercadopago_ticket' => %w[Mercadopago],
      'system_points' => %w[SystemPoint],
      'tarjeta_digital' => %w[Tarjetadigital],
      'todopago' => %w[Todopago],
      'visa_puntos' => %w[VisaPuntos],
      'bogus' => %w[Bogus]
    }.freeze

    def initialize(options = {}) # rubocop:disable Metrics/MethodLength
      @suborders = options[:suborders]
      @shop = options[:shop]
      @header = %w[Fecha_creacion_orden Fecha_pago Orden_# Orden_pública_# Suborden_# Fabricante
                   Cantidad GP_SKU SKU_externo Producto_nombre Variantes Apellido Nombre Email
                   DNI Dirección Código_postal Telefono Ciudad Provincia
                   Nombre_factura Documento_factura Email_factura Info_factura
                   Codigo_postal_factura Celular_factura Ciudad_factura Provincia_factura
                   Suborden_total
                   Precio_producto Precio_venta Cupon_nombre Cupon_descuento Nombre_completo_envío
                   Medio_de_envío Número_de_seguimiento Monto_cobrado Tipo_del_envio
                   Estado_fecha_actualizacion_tipo_de_envío Estado_del_envio
                   Estado_fecha_actualizacion_envio Estado_del_producto
                   Estado_fecha_actualizacion_producto Liquidado ID_Cobis Total_puntos
                   Total_dinero Total_con_tasas Cuotas Relacion_de_puntos Equivalencia_puntos_-_pesos IVA
                   Relacion_de_puntos_sin_IVA Equivalencia_puntos_sin_IVA_-_pesos TIPO_DOC_PAGO DOC_PAGO NOMBRE_Y_APELLIDO_TARJETA NUMEROS_TARJETA
                   BIN_TARJETA CUPON COD_AUT]
    end

    def filename
      "#{@shop.title.downcase}_suborders_#{Time.zone.now.strftime('%Y-%m-%d_%H%M%S')}"
    end

    def get_gateway(shop)
      shop.stores.map { |x| x.gateway.split(',') }.flatten.uniq
    end

    def stream
      Enumerator.new do |result|
        header = process_headers
        result << header

        yielder do |row|
          result << csv_row(row)
        end
      end
    end

    private

    def process_headers
      payment_method = get_gateway(@shop)
      payment_method.each { |gateway_header| @header << gateway_header }
      csv_header
    end

    def csv_header
      CSV::Row.new(@header, @header, true).to_s
    end

    def csv_row(values)
      values_size = values.size
      header_size = @header.size
      (header_size - values_size).times { values << ' - ' } if values_size < header_size
      CSV::Row.new(@header, values).to_s
    end

    def yielder
      suborders.each do |suborder|
        suborder.items.each do |item|
          yield row_data(item)
        end
      end
    end

    def partial_operation(partial_suborder)
      if partial_suborder.shipments.present?
        partial_suborder.shipments.last.shipment_kind
      else
        'delivery'
      end
    end

    def partial_shipment(partial_suborder)
      if partial_suborder.shipments.present?
        partial_suborder.shipments.map(&:status).last
      else
        'unfulfilled'
      end
    end

    def shipment_date(suborder)
      date = if suborder.shipments.present?
               suborder.shipment.updated_at
             else
               suborder.updated_at
             end
      date_format(date)
    end

    def row_data(item) # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      suborder = item.suborder
      partial_suborder = Mkp::Suborder.find(item.suborder_id)
      shipment = partial_shipment(partial_suborder)
      operation_type = partial_operation(partial_suborder)
      operation_value = exists_operation?(operation_type)
      payer_identification = suborder.payment&.gateway_data&.dig(:payer,:identification)
      bin_identification = suborder.payment&.gateway_data&.[]('gateway')&.[]('bin') || suborder.payment&.gateway_data&.[]('bin')
      cupon_identifications = suborder.payment&.gateway_data&.[]('gateway')&.[]('sub_payments') ||  suborder.payment&.gateway_data&.[]('sub_payments')

      row_data = [
        date_format(suborder.order.created_at),
        suborder_payment_collection_date(suborder),
        suborder.order.id,
        suborder.public_id,
        suborder.id,
        item.product.manufacturer.name,
        item.quantity,
        item.variant.gp_sku,
        external_sku(item),
        item.product.title,
        item.variant.name,
        last_name(suborder),
        first_name(suborder),
        email(suborder),
        dni(suborder),
        address(suborder),
        zip_code(suborder),
        phone(suborder),
        city(suborder),
        state(suborder),
        billing_address(suborder).try(:name) || '-',
        billing_address(suborder).try(:doc_number) || '-',
        billing_address(suborder).try(:email) || '-',
        billing_address_info(suborder),
        billing_address(suborder).try(:postal_code) || '-',
        billing_address(suborder).try(:phone) || '-',
        billing_address(suborder).try(:city) || '-',
        billing_address(suborder).try(:state) || '-',
        suborder_total(suborder),
        order_item_price(item),
        order_item_sale_price(item),
        suborder.order.coupon_code,
        coupon_discount(suborder),
        destination_address(suborder),
        shipping_method(suborder),
        shipment_tracking_id(suborder),
        charged_amount(suborder), # monto cobrado
        operation_method(operation_value, operation_type), #Tipo de Envio
        operation_date(operation_value, partial_suborder, suborder), #Fecha de tipo de envio
        I18n.t("pioneer.orders.#{shipment}"),
        shipment_date(suborder),
        I18n.t("lux.suborders.partials.suborder.order_status.#{status_product(operation_type, shipment)}"), # rubocop:disable Layout/LineLength
        shipment_kind_date(partial_suborder),
        suborder.order.jubilo_liquided ? 'Yes' : 'No',
        get_id_cobis(suborder),
        item.points,
        get_total_consumed_money(suborder),
        get_total_with_interests(suborder),
        get_installments(suborder),
        get_points_relation_with_iva(item),
        get_points_money_value(item),
        get_iva(item),
        points_relation_without_iva(item),
        points_without_iva_money_value(item),
        payer_identification.try(:dig,:type) || '-',
        payer_identification.try(:dig,:number) || '-',
        payer_identification.try(:dig,:name_payment) || '-',
        payer_identification.try(:dig,:card_last_four_digits) || '-',
        bin_identification || '-',
        cupon_identifications&.dig(0, 'ticket')&.to_s || '-',
        cupon_identifications&.dig(0, 'card_authorization_code')&.to_s || '-'
      ]

      gateways = payment_method_gateway(@header, @shop)
      payments_data = find_payment_method((item.payments + item.order.payments).uniq)
      gateways.each do |x|
        row_data << if payments_data.key?(x)
                      payments_data[x].join('/')
                    else
                      '-'
                    end
      end
      row_data
    end

    def operation_date(value, partial_suborder, suborder)
      date = value ? partial_suborder.shipments.last.created_at : suborder.created_at
      date_format(date)
    end

    def operation_method(value, operation_type)
      if value
        I18n.t("lux.suborders.partials.suborder.operation.#{logistic_type(operation_type)}")
      else
        'Normal'
      end
    end

    def find_payment_method(payments) #  rubocop:disable Metrics/MethodLength
      method = {}
      payments.each do |payment|
        mapped_values = MAPPED_GATEWAY_STORES.select do |x, v|
          x if v.include?(payment.gateway)
        end .keys.first
        if method.key?(mapped_values)
          method[mapped_values] << payment.get_external_id
        else
          method[mapped_values] = [payment.get_external_id]
        end
      end

      method
    end

    def payment_method_gateway(header, shop)
      header.select { |item| get_gateway(shop).include? item }
    end

    def exists_operation?(operation_type)
      operation_type.present? && operation_type!= 'delivery'
    end

    def date_format(date)
      date.strftime('%d-%m-%Y %H:%M:%S')
    end

    def it_shipment?(partial_suborder)
      partial_suborder.items.any?
    end

    def shipment_kind_date(suborder)
      return date_format(suborder.updated_at) unless suborder.shipments.any?

      update_date = suborder.shipments.order(:created_at).where(status: 'delivered').last
      return date_format(update_date.updated_at) if update_date.present?

      date_format(suborder.shipments.last.updated_at)
    rescue StandardError
      date_format(suborder.created_at)
    end

    def logistic_type(shipment)
      kind = {
        'exchange_refund' => 'exchange',
        'exchange_change' => 'exchange',
        'refund' => 'refund'
      }
      return kind[shipment] unless kind[shipment].nil?

      'normal'
    end

    def status_product(operation, shipment_status) # rubocop:disable Metrics/MethodLength
      if exists_operation?(operation)
        case operation
        when 'refund'
          shipment_status == 'delivered' ? 'refund' : 'delivered'
        when 'exchange_refund', 'exchange_change', 'exchange'
          'delivered'
        else
          shipment_status
        end
      else
        shipment_status
      end
    end

    def suborder_payment_collection_date(suborder)
      return '--' if suborder.payment.blank?
      return '--' if suborder.payment.collected_at.blank?

      date_format(suborder.payment.collected_at)
    end

    def shipment_label_update_date(suborder)
      if suborder.shipments.any?
        suborder.shipments.last.status_last_updated_at
      else
        date_format(suborder.updated_at)
      end
    end

    def external_sku(item)
      item.variant.sku.presence || '--'
    end

    def address(suborder)
      return unless valid_address?(suborder)

      address = get_address(suborder)
      human_readable_address = [
        address.address,
        address.street_number,
        address.address_2
      ]
      human_readable_address.compact.join(', ')
    end

    def zip_code(suborder)
      return unless valid_address?(suborder)

      address = get_address(suborder)
      address.zip
    end

    def city(suborder)
      return unless valid_address?(suborder)

      address = get_address(suborder)
      address.city
    end

    def state(suborder)
      return unless valid_address?(suborder)

      address = get_address(suborder)
      address.state
    end

    def coupon_discount(suborder)
      if suborder.order.coupon_discount.positive?
        to_currency(suborder.order.coupon_discount)
      else
        to_currency(suborder.coupon_discount)
      end
    end

    def destination_address(suborder)
      suborder.shipment&.destination_address&.full_name
    end

    def shipping_method(suborder)
      return '--' unless suborder.shipment
      return 'Retira el comprador' if suborder.shipment.is_pickup?
      return ' --- ' unless suborder.shipment.has_labels?

      suborder.shipment.labels.map(&:courier).join(', ')
    rescue StandardError
      ' - '
    end

    def last_name(suborder)
      suborder.customer.last_name
    end

    def first_name(suborder)
      suborder.customer.first_name
    end

    def email(suborder)
      suborder.customer.email
    end

    def dni(suborder)
      suborder.customer.try(:doc_number) || suborder.payment.try(:get_customer_legal_id)
    end

    def suborder_total(suborder)
      suborder.total_without_points
    end

    def order_total(suborder)
      suborder.order.total.to_f
    end

    def order_item_price(item)
      item.price.to_f
    end

    def order_item_sale_price(item)
      item.sale_price.to_f
    end

    def shipment_tracking_id(suborder)
      return '--' unless suborder.shipment
      return ' --- ' if suborder.shipment.is_pickup?
      return ' --- ' unless suborder.shipment.has_labels?

      suborder.shipment.labels.map(&:tracking_number).join(', ')
    rescue StandardError
      ' - '
    end

    # Utility methods.

    def get_address(suborder)
      if suborder.shipment.present?
        suborder.shipment.address
      else
        suborder.pickup_address
      end
    end

    def get_variant_cost(item, order)
      item.variant.current_cost(order.created_at) || '--'
    end

    def valid_address?(suborder)
      valid_shipment?(suborder) && suborder.shipment.origin_address.present?
    end

    def valid_shipment?(suborder)
      suborder.shipment.present? && has_shipment_information?(suborder)
    end

    def has_shipment_information?(suborder) # rubocop:disable Naming/PredicateName
      suborder.shipment &&
        suborder.shipment.destination_address.present? &&
        suborder.shipment.origin_address.present?
    end

    def phone(suborder)
      suborder.shipment&.address&.telephone
    end

    def charged_amount(suborder)
      suborder.shipment&.charged_amount
    end

    def to_currency(amount)
      ActionController::Base.helpers.number_to_currency(amount, precision: 2)
    end

    def get_id_cobis(suborder)
      suborder.order.customer.try(:uuid) || '-'
    end

    def get_total_consumed_points(suborder)
      suborder.try(:total_points) || '-'
    end

    def get_total_consumed_money(suborder)
      suborder.try(:total_without_points) || '-'
    end

    def get_points_relation_with_iva(item)
      item.try(:point_equivalent_with_iva) || '-'
    end

    def points_relation_without_iva(item)
      item.try(:point_equivalent) || '-'
    end

    def get_total_with_interests(suborder)
      begin
        shopStore = Mkp::ShopStore.where(store_id: suborder.store.id, shop_id: suborder.shop.id)[0]
        coef = Installment.where(number: suborder.payment.installments, payment_program_id: shopStore.payment_program_id)[0].coef
        total = coef > 0 ? suborder.total * coef : suborder.total
        total.to_f.round(2)
      rescue => exception
        suborder.total.to_f
      end
    end

    def get_installments(suborder)
      suborder.payment.installments
    end

    def get_points_money_value(item)
      item.try(:point_price) || '-'
    end

    def points_without_iva_money_value(item)
      item.try(:point_price_without_iva) || '-'
    end

    def get_iva(item)
      iva = item.iva.presence || item.product.iva
      "#{format('%<value>.2f', value: iva)}%"
    end

    def get_payment_external_id(item) # rubocop:disable Metrics/AbcSize
      return ' - ' unless item.order.payments.any? || item.payments.any?

      if item.product.points?
        item.order.payments.map(&:gateway_object_id).join(' | ')
      else
        (item.payments + item.order.payments.where.not(gateway: 'VisaPuntos'))
          .map(&:gateway_object_id).join(' | ')
      end
    end

    def billing_address(suborder)
      suborder&.shipment&.billing_address
    end

    def billing_address_info(suborder)
      info = {
        "address" => "#{billing_address(suborder).try(:address) || '-'}",
        "number" => "#{billing_address(suborder).try(:street_number) || '-'}",
        "depth" => "Dpto: #{billing_address(suborder).try(:depth) || '-'}"
      }

      "#{ info["address"] } #{ info["number"] } #{ info["depth"] }"
    end
  end
end

module Lux
  module Integration
    module Shopify
      class Product < ::Lux::Integration::Product
        def initialize(integration)
          super

          @active = integration.activate_session
        end

        def all
          find(:all)
        end

        def find(products_ids)
          return unless @active

          products_ids = products_ids.respond_to?(:to_ary) ? products_ids.join(',') : products_ids
          params = { params: { limit: 250 } }
          params.deep_merge!({ params: { ids: products_ids } }) unless products_ids == :all

          candidates = ShopifyAPI::Product.find(:all, params).map do |product|
            ShopifyProduct.new(product, @integration)
          end

          candidates.select! do |product|
            product.properties.present? || product.variants.present?
          end

          candidates
        end
      end

      class ShopifyProduct < ::Lux::Integration::ComplexItem
        attr_reader :data, :network, :shop, :payload

        DATA_MAP = {
          external_id:  :id,
          title:        :title,
          description:  :body_html,
          category:     :product_type,
          available_on: :published_at,
          manufacturer: :vendor,
          properties:   :options,
          picture:      :image,
          pictures:     :images,
          variants:     :variants
        }

        AVAILABLE_PROPERTIES = Mkp::Product::AVAILABLE_PROPERTIES.dup

        def initialize(shopify_item, integration)
          @integration = integration
          @shop = @integration.shop
          @network = @shop.network
          @payload = shopify_item
          super(shopify_item)
        end

        def description
          return title if data[:description].blank?
          description = ActionView::Base.full_sanitizer.sanitize(data[:description])
          description = description.gsub(/&gt;|&lt|&amp;/, '')
          description.blank? ? title : description
        end

        def properties
          @properties = data[:properties].sort_by { |p| p.position.to_i }.map do |property|
            name = property.name.downcase

            if AVAILABLE_PROPERTIES.include?(name.to_sym)
              ShopifyProperty.new(property)
            else
              {
                slug: name.gsub(/\W+/, '-'),
                name: name,
                position: property.position
              }
            end
          end
        end

        def pictures
          @pictures ||= data[:pictures].sort_by { |picture| picture.position.to_i }.map do |picture|
            ShopifyPicture.new(picture)
          end
        end

        def variants
          @variants ||= data[:variants].map do |variant|
            ShopifyVariant.new(variant, self)
          end
        end

        def without_properties?
          @properties.blank?
        end

        def without_variants?
          data[:variants].blank?
        end

        def with_variants?
          data[:variants].present?
        end

        def available_properties          
          if without_properties?
            @available_properties = [:noproperty]
          else
            @available_properties = build_properties
          end
        end

        def build_properties
          properties.map do |property|
            if property.is_a?(Hash)
              { slug: property[:slug], name: property[:name] }
            else
              property.name_sym
            end
          end
        end

        def picture
          @picture ||= ShopifyPicture.new(data[:picture])
        end

        def price
          if get_compare_price.present? && should_be_sale_price?
            get_compare_price
          else
            maximum_price
          end
        end

        def get_compare_price
          variants.map(&:compare_at_price).compact.map(&:to_f).max
        end

        def should_be_sale_price?
          get_compare_price > variants.map(&:price).map(&:to_f).min
        end

        def compare_at_price
          if get_compare_price.present? && should_be_sale_price?
            minimum_price
          end
        end

        def maximum_price
          variants.map(&:price).map(&:to_f).max
        end

        def minimum_price
          variants.map(&:price).map(&:to_f).min
        end

        def price_range
          get_from_item(:price_range)
        end

        def weight
          @weight ||= variants.map(&:weight).max
        end

        def weight_unit
          @weight_unit ||= variants.first.weight_unit
        end

        def stock_total
          @stock_total ||= variants.sum(&:quantity)
        end

        def get_picture(id)
          pictures.detect { |pic| pic.id == id }
        end

        def has_color_property?
          properties.any? { |p| p.name_sym == :color }
        end

        def manufacturer_mapper
          return @gp_manufacturer if @gp_manufacturer.present?

          # First we try the obvious choice.
          manufacturer = shop.try(:brand).try(:manufacturer)

          # Fallback to a prefixed manufacturer.
          manufacturer ||= Mkp::Manufacturer.first

          @gp_manufacturer = manufacturer
        end

        def category_mapper
          return @gp_category if @gp_category.present?

          if data[:category].present?
            conditions = ["name LIKE ?", "%#{data[:category]}%"]
            category = Mkp::Category.active.where(network: network).where(conditions).first
            return (@gp_category = category) if category.present?
          end

          settings = @integration.settings
          if settings['defaults'] && settings['defaults']['category_id'].present?
            category = Mkp::Category.find_by_id(settings['defaults']['category_id'])
            return (@gp_category = category) if category.present?
          end

          @gp_category = Mkp::Category.active.by_network(network).find_by_slug('clothing')
        end

        def sport_mapper
          return @gp_sports if @gp_sports.present?
          settings = @integration.settings
          @gp_sports = if settings['defaults'] && settings['defaults']['sport_ids'].present?
                        Sport.where(id: settings['defaults']['sport_ids']).map(&:id)
                       else
                        [Sport.first.id]
                       end
        end

        def currency_id
          Network.for(network).currency_id
        end

        def available_on
          data[:available_on]
        end

        def to_hash(debuggable = false)
          hash = {
            available_on: available_on,
            available_properties: available_properties,
            category_id: category_mapper.id,
            currency_id: currency_id,
            description: description,
            display_variants: true,
            handle_stock: true,
            height: 0.0,
            length: 0.0,
            length_unit: Network[network].length_unit,
            manufacturer_id: manufacturer_mapper.id,
            mass_unit: Network[network].mass_unit,
            regular_price: price,
            sale_price: compare_at_price,
            shop_id: shop.id,
            sport_ids: sport_mapper,
            title: title,
            weight: 0.0,
            width: 0.0
          }

          if compare_at_price.present?
            hash.merge!({
              sale_on: Time.now.beginning_of_day,
              sale_until: Time.now.end_of_day + 2.week
            })
          end

          if debuggable
            hash = hash.merge({
              variants_attributes: variants.map(&:to_hash),
              payload: @payload
            })
          end

          hash
        end
      end

      class ShopifyVariant < ::Lux::Integration::ComplexItem
        attr_reader :data, :product, :properties

        DATA_MAP = {
          external_id:      :id,
          name:             :title,
          picture_id:       :image_id,
          sku:              :sku,
          quantity:         :inventory_quantity,
          inventory_policy: :inventory_policy,
          inventory_owner:  :inventory_management,
          price:            :price,
          compare_at_price: :compare_at_price,
          weight:           :weight,
          weight_unit:      :weight_unit
        }

        def initialize(shopify_item, product)
          @product = product
          @properties = {}

          super(shopify_item)

          setup_properties
        end

        def external_id
          data[:external_id]
        end

        def setup_properties
          if product.without_properties?
            properties[:noproperty] = true
          else
            product.properties.each do |property|
              if property.is_a?(Hash)
                properties[property[:slug].to_sym] = get_from_item("option#{property[:position]}")
              else
                if property.name_sym == :color
                  properties[property.name_sym] = { name: get_from_item("option#{property.position}") }
                else
                  properties[property.name_sym] = get_from_item("option#{property.position}")
                end
              end
            end
          end
        end

        def picture
          product.get_picture(picture_id)
        end

        def to_hash
          {
            quantity: quantity,
            properties: properties,
            sku: sku,
            visible: true
          }
        end
      end

      class ShopifyProperty
        attr_reader :id, :name, :position

        def initialize(shopify_property)
          @id = shopify_property.id
          @name = shopify_property.name
          @position = shopify_property.position
        end

        def name_sym
          @name.downcase.to_sym
        end
      end

      class ShopifyPicture
        attr_reader :external_id, :position, :src, :variant_ids, :styles

        # Following the sizing specify in https://docs.shopify.com/themes/liquid-documentation/filters/url-filters#product_img_url
        SIZES = {
          st: :small,
          t: :compact,
          m: :medium,
          ml: :large,
          l: :grande,
          original: :original
        }

        def initialize(shopify_image)
          @external_id = shopify_image.id
          @position = shopify_image.position
          @src = shopify_image.src
          @variant_ids = shopify_image.variant_ids

          generate_style_urls
        end

        def generate_style_urls
          @styles = SIZES.each_with_object(HashWithIndifferentAccess.new) do |(k, v), hash|
            hash[k] = url(v)
          end
        end

        def url(style = nil)
          return src if style.nil? || style == :original

          regexp_match = "/\\1_#{style}.\\2"
          src.gsub(/\/(.*)\.(\w{2,4})/, regexp_match)
        end
      end
    end
  end
end

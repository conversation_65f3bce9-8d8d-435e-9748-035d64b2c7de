module Lux
  module Integration
    class Product
      def initialize(integration)
        @integration = integration
        @shop = integration.shop
        @network = @shop.network
        @errors = {}
      end

      def find(products_ids)
        raise NotImplementedError
      end
    end

    class ComplexItem
      DATA_MAP = {}

      def initialize(item)
        @item = item
        @data = HashWithIndifferentAccess.new

        setup_data
      end

      def method_missing(key, *args, &block)
        super unless @data.key?(key)
        @data[key]
      end

      def item
        raise NoMethodError if Rails.env.production?
        @item
      end

      protected

      def setup_data
        self.class::DATA_MAP.each do |key, value|
          @data[key] = @item.send(value)
        end
      end

      def get_from_item(method)
        @item.send(method)
      end
    end
  end
end

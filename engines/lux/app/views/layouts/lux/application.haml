!!!
%html.no-js{lang: I18n.locale.to_s}
  %head
    -#%script{type: "text/javascript", src: '//static.queue-it.net/script/queueclient.min.js'}
    -#%script{"data-queueit-c": 'avenida', type: 'text/javascript', src: '//static.queue-it.net/script/queueconfigloader.min.js'}
    = render partial: 'partials/base_layout_head'

    %meta{ content: @shop.id, name: 'shop_id' }

    %title<
      = content_for?(:title) ? yield(:title) : "Manage Shop"

    = stylesheet_link_tag '//fonts.googleapis.com/css?family=Open+Sans:400,700&subset=latin,latin-ext'

    - if content_for?(:main_stylesheet)
      = yield(:main_stylesheet)
    - else
      = stylesheet_link_tag 'lux/application'

    = render partial: 'partials/banners/fonts'
    = yield :css

  %body{ id: body_id }
    = render partial: 'lux/partials/header'
    = render partial: 'lux/partials/menu'
    = render partial: 'lux/partials/alerts'
    .row
      = yield

    = yield :before_js

    - if content_for?(:main_javascript)
      = yield(:main_javascript)
    - else
      = javascript_include_tag 'lux/application'

    = yield :js

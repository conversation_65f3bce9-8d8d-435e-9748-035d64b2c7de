#detail
  .row
    .small-3.columns
      label.right.inline> Account Name:
    .small-6.columns
      .row.collapse
        .small-7.columns
          input type="text" value="#{@integration.account_name}" class='with_postfix' disabled="disabled"
        .small-5.columns
          span.postfix> .myshopify.com
    .small-3.columns
  .row#permissions
    .small-3.columns
      label.right.inline> Permissions:
    .small-7.columns
      ul
        - @integration.scopes.each do |scope|
          li>
            = t("lux.integrations.shopify.setup.#{scope}_html")
    .small-2.columns

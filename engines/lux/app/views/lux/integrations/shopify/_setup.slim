= form_for @integration, url: shop_authorize_integration_path(integration_name: :shopify) , method: :post, :html => { :class => "custom" } do |f|
  .row
    .small-3.columns
      label.right.inline> Nombre de la cuenta:
    .small-7.columns
      .input-group
        = f.text_field :account_name, class: 'form-control', placeholder: "demo-shop-name"

      / .row.collapse
      /   .small-8.columns
      /     = f.text_field :account_name, class: 'with_postfix', placeholder: "demo-shop-name"
      /   .small-4.columns
      /     span.postfix .myshopify.com
    .small-2.columns
  .row#permissions
    .small-3.columns
      label.right.inline> Permisos:
    .small-7.columns
      - scopes = Mkp::Integration::Shopify::AVAILABLE_SCOPES
      - scopes.each do |scope|
        .row
          .small-12.columns
            = check_box_tag "#{form_parameter(@integration)}[scopes][]", "#{scope}", @integration.scopes.include?("#{scope}"), :class => 'hidden-field'
            span.labelco
              = t(".#{scope}_html")
    .small-2.columns
  .row#actions
    .small-3.columns
    .small-9.columns
      = f.submit 'Integrar con Shopify', class: 'button red', disabled: true

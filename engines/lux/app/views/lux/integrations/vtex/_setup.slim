= form_for @integration, url: shop_authorize_integration_path(integration_name: :shopify) , method: :post, :html => { :class => "form-horizontal" } do |f|
  .row
    .small-3.columns
      label.right.inline> Nombre cuenta:
    .small-7.columns
      .input-group
        input.form-control type="text" placeholder="Nombre de cuenta" aria-describedby="basic-addon1"
  .row
    .small-3.columns
      label.right.inline> Api App Key:
    .small-7.columns
      .input-group
        input.form-control type="text" placeholder="Api app key" aria-describedby="basic-addon1"
  .row
    .small-3.columns
      label.right.inline> Api App Token:
    .small-7.columns
      .input-group
        input.form-control type="text" placeholder="Api app token" aria-describedby="basic-addon1"
  .row#actions
    .small-3.columns
    .small-9.columns
      = f.submit 'Integrar con Vtex', class: 'button red', disabled: true

= form_for @integration, url: shop_authorize_integration_path(integration_name: :shopify) , method: :post, :html => { :class => "form-horizontal" } do |f|
  .row
    .small-3.columns
      label.right.inline> Tienda ID:
    .small-7.columns
      .input-group
        input.form-control type="text" placeholder="STORE ID" aria-describedby="basic-addon1"
  .row
    .small-3.columns
      label.right.inline> ACCESS TOKEN:
    .small-7.columns
      .input-group
        input.form-control type="text" placeholder="ACCESS TOKEN" aria-describedby="basic-addon1"
  .row#actions
    .small-3.columns
    .small-9.columns
      = f.submit 'Integrar con Tienda Nube', class: 'button red', disabled: true

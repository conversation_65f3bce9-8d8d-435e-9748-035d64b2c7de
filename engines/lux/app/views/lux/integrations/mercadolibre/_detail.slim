#detail
  .row
    .small-3.columns
      label.right.inline> Cuenta asociada:
    .small-9.columns
      input class="associated_account" type="text" value="#{@integration.username}" disabled="disabled"
  .row
    .small-3.columns
      label.right> Tipo de cuenta:
    .small-9.columns
      label= t(".account_type.#{@integration.user_type}_html")
  .row#permissions
    .small-3.columns
      label.right.inline> Permisos:
    .small-9.columns
      ul
        - @integration.scopes.each do |scope|
          li>
            = t(".#{scope}")
  - unless @integration.configuration_complete?
    .row
      .large-10.small-offset-1.columns
        .missing_store_id=t(".missing_store_id_html")

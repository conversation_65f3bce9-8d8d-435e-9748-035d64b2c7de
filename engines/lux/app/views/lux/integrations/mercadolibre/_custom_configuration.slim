- official_store_config = @integration.settings['official_store']
.row
  - if @integration.can_change_configuration?
    .small-5.columns
      label.right class="#{ 'missing' unless @integration.configuration_complete?}"
        =t('.choose_store')
    .small-7.columns
      = select_tag 'integration[custom_configuration[official_store_id]]]', options_for_select(official_stores_options(@integration), official_store_config && official_store_config['id']), include_blank: true, id: 'official_store-select'
  - else
    .small-12.columns
      .message.alert=t('.unable_to_change_official_store_html', store_name: @integration.official_store_name)

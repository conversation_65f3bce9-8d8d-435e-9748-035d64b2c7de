= form_for @integration, url: shop_authorize_integration_path(integration_name: :mercadolibre) , method: :post, :html => { :class => "custom" } do |f|
  .row#permissions
    .small-6.small-offset-3.columns
      h2="#{t('.permissions')}"
      - scopes = Mkp::Integration::Mercadolibre::AVAILABLE_SCOPES
      - scopes.each do |scope|
        .row
          .small-12.columns
            .permission= t(".#{scope}_message_html")
  .row#actions
    .small-3.columns
    .small-9.columns
      = f.submit t(".authorize"), class: 'button red'

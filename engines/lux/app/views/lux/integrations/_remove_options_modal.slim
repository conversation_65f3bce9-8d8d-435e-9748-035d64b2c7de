#remove-integration-modal.reveal-modal
  .close.close-reveal-modal aria-label="Close"
    | &#215;
  .modal-content
    = form_for @integration, as: :integration, url: shop_integration_path(nil, @shop, @integration), class: 'custom', form_class: 'delete-form' do |f|
      = hidden_field_tag '_method', 'delete'
      .headline
        h2 = t('.title')
        hr
      .body
        p.message = t('.message', integration_name: @integration.type.demodulize)
        .options
          - Mkp::Integration::Base::AFTER_REMOVE_OPTIONS.each do |option|
            .option
              = radio_button_tag 'integration[remove_option]', option
              label = t(".#{option}")
      .actions
        = f.submit t('.remove'), class: 'button red'


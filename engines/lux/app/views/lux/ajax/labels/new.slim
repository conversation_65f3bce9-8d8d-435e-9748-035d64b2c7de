.row
  .small-12.columns
    .options
      .row
        - if has_gateway_for_labels?(@shipment)
          .medium-6.small-12.columns
            span data-option='prepaid'
              i.i-radio-off
              =t('.buy_prepaid_label')
        .small-12.columns class="#{'medium-6' if has_gateway_for_labels?(@shipment)}"
          span data-option='other'
            i.i-radio-off
            =t('.own_label')
    .options-setup
      - if @suborder.shop.delivery_by_matrix
        - if has_gateway_for_labels?(@shipment)
          .prepaid.hidden
            - if customer_choose_label?(@shipment)
              .customer-election
                form onsubmit="return false;"
                  - if @shipment.present?
                    - info = @shipment.extra_info
                    - courier = Couriers.get_settings_for(info[:courier])
                    = hidden_field_tag 'prepaid[type]', 'customer-election'
                    = hidden_field_tag 'prepaid[shipment_id]', @shipment.id
                    .carrier
                      - if courier.icon.present?
                        .icon= image_tag courier.icon, size: '48x48', alt: courier.name
                      h4= courier.name
                      .service=(info[:service_name]).titleize
            - elsif allowed_to_purchase_labels_on_demand?(@shop)
              .on-demand
                form onsubmit="return false;"
                  = hidden_field_tag 'prepaid[type]', 'on-demand'
                  = hidden_field_tag 'prepaid[shipment_id]', @shipment.id
                  - destination_address = @shipment.destination_address
                  - gateway_name, configs = preference(@on_demand_labels_config, destination_address)
                  = hidden_field_tag 'prepaid[gateway]', gateway_name
                  - unless gateway_name.eql? 'OcaEpak' and configs.nil?
                    - if Smartcarrier::Const::CARRIERS.include?(gateway_name)
                      - gateway = Gateways::Labels::SmartCarrier.new(configs, gateway_name.to_sym)
                    -else
                      - gateway = "Gateways::Labels::#{gateway_name}".constantize.new(configs)
                    .service-detail data-gateway="#{gateway_name}"
                      - courier = gateway.courier
                      - if courier && courier.icon.present?
                        .icon= image_tag courier.icon, size: '48x48', alt: courier.name, title: courier.name
                      .information
                        - tag = courier.name.eql?('Krabpack') && courier.name
                        h3= tag || label_gateway_service_name(gateway)
                        - if info = label_gateway_information(gateway)
                          = info
                        = label_gateway_package(gateway, @shipment)
                  - else
                    .message=t('.missing_configuration_to_purchase_on_demand_labels_html')

            - else
              .message=t('.missing_configuration_to_purchase_on_demand_labels_html')
      - else
        form onsubmit="return false;"
          = hidden_field_tag 'prepaid[type]', 'on-demand'
          = hidden_field_tag 'prepaid[shipment_id]', @shipment.id
          = hidden_field_tag 'prepaid[gateway]'
          - if @suborder.refunded? || @suborder.exchange?
            h3 = "Fecha y hora de retiro de la orden"
            = 'desde: '
            = datetime_select 'prepaid', :shipment_from_date, default: 3.days.from_now
            .br
            = 'hasta: '
            = datetime_select 'prepaid', :shipment_to_date, default: (3.days.from_now + 8.hours)
          - destination_address = @shipment.destination_address
          - if @suborder.shop.warehouse.present?
            .service-detail
              .information
                h3= "Krabpack"
                = label_krabpack_package(@shipment)
          - else
            .message=t('.missing_configuration_to_purchase_on_demand_labels_html')

      .other.hidden
        .row
          form onsubmit="return false;"
            .medium-5.small-12.columns
              h5=t('.tracking_number')
              = text_field_tag 'other[tracking_number]', nil, placeholder: t('.tracking_number')
            .medium-7.small-12.columns
              .row
                .small-8.columns
                  h5=t('.courier')
                  - default_courier = Couriers.get_default_for(@shop.network)
                  = select_tag 'other[courier]', options_for_select(couriers_select_options(@shop.network), default_courier)
                .small-4.columns
                  h5=t('.price')
                  .input-group
                    span $
                    = text_field_tag 'other[price]'

    .notification
      i.i-mail
      span=t('.email_notification_to_customer')
    .actions
      .message.cancel-it= t('.cancel')
      .message or
      span.confirm-button= t('.fulfill_items_html', quantity: @items.to_a.sum(&:quantity))

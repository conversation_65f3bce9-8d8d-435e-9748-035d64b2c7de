- if flash.present?
  .row
    .small-12.columns
      - flash.each do |name, msg|
        - if msg.class != String
          - msg.select{|msg| msg if !msg.nil?}.each do |message|
            .alert-box{class: name, 'data-alert' => ""}
              = message if !message.nil? || message != ""
              %a.close{href: '#'} &times;
        - else
          .alert-box{class: name, 'data-alert' => ""}
            = msg if !msg.nil?
            %a.close{href: '#'} &times;

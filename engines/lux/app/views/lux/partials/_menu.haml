#topnav-container
  = render partial: 'lux/partials/shop_title'
  .row
    .small-12.columns
      .topnav
        - if current_user.office.nil?
          .topnav-item{ class: controller_name.match(/products|product_imports/) && 'active'}
            .section
              %a{:href => shop_products_url}= t('.products')
              - if controller_name.match(/products|product_imports/)
                %span.arrow-up
        .topnav-item{ class: controller_name.match(/carts|orders/) && 'active'}
          .section
            %a{:href => shop_suborders_url }= t('.orders')
            - if controller_name.match(/carts|orders/)
              %span.arrow-up
        -# .topnav-item{ class: controller_name == 'questions' && 'active'}
        -#  .section
        -#    %a{:href => shop_questions_url }= t('.questions')
        -#    - if controller_name == 'questions'
        -#      %span.arrow-up
        -# .topnav-item{ class: controller_name.match(/coupons|facebook_app|banners|wide_discount/) && 'active'}
        -#   .section
        -#     %a{:href => shop_coupons_url(shop_id: @shop.id) }= t('.marketing')
        -#     - if controller_name.match(/coupons|facebook_app|banners|wide_discount/)
        -#       %span.arrow-up
        - if current_user.office.nil?
          .topnav-item{ class: controller_name.match(/accounts|shipping_methods|zones|warehouses|merchants|transactions|taxes/) && 'active'}
            .section
              %a{:href => shop_account_url }= t('.account')
              - if controller_name.match(/accounts|shipping_methods|zones|warehouses|merchants|transactions|taxes/)
                %span.arrow-up
#submenu
  .row
    - if controller_name.match(/coupons|facebook_app|banners|wide_discount/)
      = render partial: 'lux/marketing/menu'

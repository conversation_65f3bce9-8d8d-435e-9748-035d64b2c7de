- if current_user_shops.size > 1
	.row
	  .small-12.columns
	    .shop-selector
	      select.managed-shops
	        - current_user_shops.each do |shop|
	          - shop_url = change_shop_path(shop, params[:controller])
	          - is_current_shop = shop.id == @shop.id
	          option[value=shop.id data-shop-url=shop_url data-data='{"shopUrl": "#{shop_url.to_s}"}' selected=("selected" if is_current_shop)] #{shop.title} (#{shop.network})

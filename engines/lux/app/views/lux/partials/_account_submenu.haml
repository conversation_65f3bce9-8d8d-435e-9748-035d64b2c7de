%ul#account-submenu
  %li{ class: controller_name == 'accounts' && 'active' }
    %a.full-link{ href: shop_account_url }
    %p= t('.settings')
  %li{ class: controller_name.match(/warehouses|shipping_methods|zones/) && 'active' }
    %a.full-link{ href: shop_warehouses_url }
    %p= t('.shipping')
  - if Network[@shop.network].charges_taxes?
    %li{ class: controller_name == 'taxes' && 'active' }
      %a.full-link{href: shop_taxes_path}
      %p= t('.taxes')
  - if Network[@shop.network].shop_integrations.present?
    %li{ class: controller_name == 'integrations' && 'active' }
      %a.full-link{href: shop_integrations_url}
      %p= t('.integrations')

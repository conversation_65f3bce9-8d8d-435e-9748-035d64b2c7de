= af.hidden_field(:country, value: 'Argentina')
.row
  .small-12.columns
    = af.text_field(:first_name, placeholder: t('.title_placeholder'), class: 'required')
.row
  .small-12.columns
    = af.text_field(:address, placeholder: t('.address_1'), class: 'required')
- if Network[@shop.network].uses_2_address_fields?
  .row
    .small-12.columns
      = af.text_field(:address_2, placeholder: 'Address 2')
.row
  .small-12.medium-4.columns
    = af.text_field(:city, placeholder: t('.city'), class: 'required')
  .small-12.medium-4.columns
    %label
      = af.select( :state, options_for_select(GeoConstants::States.for_ar_network.sort, @warehouse&.state))
  .small-12.medium-4.columns
    = af.text_field(:zip, placeholder: t('.zip'), class: 'required')
.row
  .small-12.columns
    #warehouse_country
.row
  .small-12.columns
    = af.text_field(:telephone, placeholder: t('.address_placeholder'), class: 'required')
.row
  .small-12.columns
    = af.check_box :pickup, label: t('.pickup')
.row.pickup
  .small-12.columns
    = af.time_field(:open_hours, placeholder: t('.open_hours'))
  .small-12.columns
    = af.time_field(:closing_hours, placeholder: t('.closing_hours'))
  .small-12.columns
    = af.text_field(:retardment, placeholder: t('.delay'))

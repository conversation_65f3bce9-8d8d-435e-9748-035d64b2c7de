.large-3.columns
  = render partial: 'lux/partials/account_submenu'
.large-9.columns
  - if @shop.fulfillment_by_other?
    %section.gp-simple-panel
      %h4= t('.shop_with_fulfillment')
  - else
    .section-container.auto{"data-section" => ""}
      %section
        %p.title{"data-section-title" => ""}
          %a{:href => "#panel1"}= t('.warehouses')
        .content.row.gp-simple-panel{"data-section-content" => ""}
          %h2= t('.manage')
          %hr
          %h3= t('.create')
          %br
          = form_for @new_warehouse, as: 'warehouse', url: shop_warehouses_url, html: {id: 'new-warehouse'}  do |af|
            .row.new-warehouse
              .small-12.medium-6.columns
                = render partial: 'lux/warehouses/form', locals: {af: af}
                .row
                  .small-12.medium-6.columns
                    = af.submit t('.add'), class: 'button small add'
          - if @warehouses.any?
            %hr
            %h3= t('.existing')
            %br
            .warehouses
              .row
                = render(partial: 'lux/warehouses/warehouse', collection: @warehouses)
            %p{style: 'width: 50%'}
              = t('.warn_msg')

.small-6.columns
  .shipping-box
    %div= warehouse.first_name
    %div= warehouse.address.presence && "#{warehouse.address} - #{warehouse.address_2}" || "Address no set"
    %div #{warehouse.city} - #{warehouse.state} - #{GeoConstants::Countries.name_for_code(warehouse.country)}
    %div= warehouse.zip
    %div= warehouse.telephone.presence || "Phone no set"
    %div= warehouse.pickup ? "#{t('.pick_up_enabled')} - #{t('.delay')}: #{warehouse.retardment} - #{t('.open_hours')}: #{warehouse.open_hours} - #{warehouse.closing_hours}" : t('.pick_up_disabled')
    .actions
      = button_to t('.remove'),
                  shop_warehouse_url('ar', @shop, warehouse.id),
                  method: :delete,
                  class: 'button red small',
                  disabled: !warehouse_removable?(warehouse) && 'disabled'
      = link_to t('.edit'),
                edit_shop_warehouse_url('ar', @shop, warehouse),
                class: 'button small'

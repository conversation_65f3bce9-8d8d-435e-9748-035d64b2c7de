.large-3.columns
  = render partial: 'lux/partials/account_submenu'
.large-9.columns
  .gp-simple-panel
    = form_tag shop_taxes_update_path, method: :put do
      .row
        .small-12.columns
          %h1 Tax Rates
      .row.add-tax-rate
        .small-6.columns
          = select_tag 'us_states', options_for_select(@us_states)
        .small-6.columns
          .add-rate.button.small Add Tax Rate
      .row.tax-rates
        .small-12.columns
          %hr
          %table
            %tr.states-header
              %th State
              %th Tax Rate
              %th Apply rate to shipping?
              %th
            = render partial: 'lux/taxes/tax_rates'
          - if @tax_rates.blank?
            %p.setup-tax-rate Choose a state above to setup a tax rate
      .row
        = submit_tag 'Save', class: 'save-tax-rates button small'

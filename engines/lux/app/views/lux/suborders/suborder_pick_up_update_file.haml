= content_for(:title) { t(".page_title") }

.large-12.columns
  .row.gp-simple-panel.collapse
    .panel-active
    .large-12.columns
      %h1.thin.text-center
        = t(".title_for_update")
        .subheader
          = t(".subtitle_for_update")
      %hr
    .large-12.columns
      = form_for :suborder_pick_up_update_shop_suborders, as: :suborder_label_import_create, method: :put do |f|
        = f.label t('.choose_csv')
        = f.file_field :file, label: t('.order_list'), class: 'required', required: true
        = f.hidden_field :strategy
        %hr
        = f.submit t('.import'), id: 'save', class: 'button red secondary'

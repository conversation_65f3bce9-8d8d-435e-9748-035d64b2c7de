= content_for :title do
  = t('.head_title')

.large-12.columns
  .row
    = form_tag shop_suborders_path, method: :get, id: 'filter'
      .large-12.columns
        .search.medium-7.small-6.columns
          = hidden_field_tag(:status, @status) if @status.present?
          = text_field_tag :query, @query, placeholder: t('.search')
          input type='submit' style='display:none'
        hr
      .large-12.columns
        = render partial: '/lux/suborders/partials/filters'

        #suborders-list.large-12.columns
          table width='100%'
            thead
              tr
                th width='80' = t('.image')
                th width='80'= t('.order_id')
                th width='80'= t('.date')
                th.details= t('.customer')
                th.details= t('.details')
                th width='80' = t('.shipment_kind')
                th width='80'= t('.order_status')
                th width='80'= t('.payment_status')
                th width='80'= t('.shipment_status')
            tbody.suborders-table-list
              = render partial: '/lux/suborders/partials/suborder', collection: @suborders.select{|s| s.order.present?}
          .large-12.columns
            ul.pagination
              = will_paginate @suborders, container: false

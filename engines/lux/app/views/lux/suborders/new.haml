= content_for(:title) { t(".page_title_for_create") }

.large-12.columns
  .row.gp-simple-panel.collapse
    .panel-active
    .large-12.columns
      %h1.thin.text-center
        = t(".title_for_create")
        .subheader
          = t(".subtitle_for_create")
      %hr
    .large-12.columns
      = form_for @suborder_label_import_form, url: :suborder_label_import_create_shop_suborders, as: :suborder_label_import_create do |f|
        = f.label t('.choose_csv')
        = f.file_field :suborder_label_list, label: t('.order_list'), class: 'required', required: true
        = f.hidden_field :strategy
        %hr
        = f.submit t('.import'), id: 'save', class: 'button red secondary'



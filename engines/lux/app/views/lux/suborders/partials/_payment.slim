- payment = suborder.payment
- points = suborder.subtotal_points
- coupon = suborder.coupon

.div.text-center
  h3=t('.title')
  - if suborder.have_taxes?
    .taxes=t('.taxes', amount: number_to_currency(suborder.taxes, precision: 2))
  .details
    .row
      - if payment
        div class=(payment_column_class(suborder))
          h4=t('.credit-card')
          h3.subtotal= number_to_currency suborder.total_without_points, precision: 2
          - payment_status = payment.status
          span.payment_status class="#{payment_status}" = t(".#{payment_status}")
      - if points > 0
        div class=(payment_column_class(suborder))
          h4= t('.points')
          h3.subtotal= points
          - if suborder.payments.present?
            - payment_status = suborder.payments.last.status
            span.payment_status class="#{payment_status}" = t(".#{payment_status}")
          - else
            span.payment_status class="#{payment_status}" = t(".collected")
      - if coupon
        div class=(payment_column_class(suborder))
          h4= t('.coupon')
          span = t(".ticket", code: coupon.code)

.customer
  - fallback_color = Social::Attachment::AvatarPicture.fallback_color_for(customer)
  - fallback_text = Social::Attachment::AvatarPicture.fallback_text_for(customer)
  - onerror = "this.parentNode.className += ' show-fallback'"
  .avatar
    = image_tag customer_avatar_url(customer), height: "48px", alt: "#{customer.full_name}", onerror: onerror
    .fallback style="background-color:#{fallback_color};"
      span= fallback_text
  h3= customer.full_name
  .email= customer.email

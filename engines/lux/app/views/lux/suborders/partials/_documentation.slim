= form_for(Mkp::Document.new, :url =>{:controller => :suborders ,:action => :upload_document }, :html => { :multipart => true }) do |form|
  h3 Factura.
  .div.gp-panel
    .row
      .small-12.columns
        h3 Utilice este espacio para gestionar carga y descarga de la factura del vehiculo reservado
    .row
      .small-6.columns
        = form.file_field :pdf_file, label: false, class: 'button'
        = form.select :pdf_file_type, [['Factura','Factura'],['Comprobante de Transferencia','Comprobante de Transferencia']], label: 'Tipo de documento'
        = form.hidden_field(:suborder_id, :value => @suborder.id)
        br
        - if @suborder.documents.any?
          - @suborder.documents.each do |x|
            = link_to x.pdf_file_type ,x.pdf_file.url, class: 'button grey', download: 'Factura_Mimoto'
            br
            br
  br
  = form.submit 'Subir documentos', class: 'button red'

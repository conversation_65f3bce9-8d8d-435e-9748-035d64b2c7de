- payment = suborder.payment

.div.gp-panel
  .div.text-center
    h3=t('.title')
    .details
    - if suborder.all_payments.count == 1
      .row
        .medium-6.columns
        h4 = t('.coef_amount', amount: number_to_currency(payment.amount_coef(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
        h4 = t('.total_installment', amount: number_to_currency(payment.sub_payment_amount(suborder), delimiter: '.', separator: ',', precision: 2))
        =t('.installments', number: payment.government_installments.present? ? payment.government_installments : payment.installments)
    - else
      .row
      - suborder.all_payments.each_with_index do |pay, index|
        .medium-6.columns
          h3=t('.card', number: (index + 1).to_s)
          h4=t('.coef_amount', amount: number_to_currency(pay.amount_coef(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
          h4=t('.total_installment', amount: number_to_currency(pay.sub_payment_amount(suborder), delimiter: '.', separator: ',', precision: 2))
          =t('.installments', number: pay.government_installments.present? ? pay.government_installments : pay.installments)
    - if payment.government_installments.present?
      =t('.government_plan', present: "Si")
    - else
      =t('.government_plan', present: "No")

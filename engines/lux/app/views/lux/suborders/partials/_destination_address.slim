- is_pickup = shipment.is_pickup?
.div.text-center
  h3=t('.title')
  - if is_pickup
    span=t('.pickup')
  .details
    .email= address.email if address.email
    .name= address_full_name(address)
    .address= full_address(address)
    .country= full_country(address)
    - if @suborder.payment&.gateway == "Empty" && @suborder.payment&.gateway_data&.dig(:cuil).present?
      - cuil = @suborder.payment.gateway_data.dig(:cuil)
      .dni = "CUIL: " + cuil
    - else
      - if (dni = @suborder.payment.try(:get_customer_legal_id)).present?
        .dni= t('.doc_number', number: dni)
    - address = shipment.suborders.first.customer.addresses.last if is_pickup
    - if address && (number = phone_number(address)).present?
      .phone= t('.phone', number: number)
.delivery
  - labels = shipment.labels
  .actions
    - if suborder.fulfilled_by_gp?
      .message
        i.i-envio
        = t('.fulfilled_by_gp_html', date: l(shipment.created_at, format: :shortest))
    - else
      - if labels.present?
        .message
          i.i-envio
          = t('.fulfilled_by_you_html', date: l(labels.first.created_at, format: :shortest))
      - else
        .message.unknown
          i.i-info
          = t('.fulfilled_by_you_without_label')
  - if labels.any?
    = render partial: '/lux/suborders/partials/label', collection: labels, locals: { suborder: suborder }

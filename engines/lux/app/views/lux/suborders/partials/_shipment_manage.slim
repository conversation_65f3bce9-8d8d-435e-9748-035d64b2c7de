main.suborder.large-12.columns
  h3= t('.shipments')
  - @suborder.shipments.each do |shipment|
    .div.gp-panel
      = render partial: '/lux/suborders/partials/shipment', locals: { shipment: shipment, suborder: @suborder }
    br
  - if @suborder.can_show_reservable_states?
    h3=t('.states')
    div.gp-panel style="margin-top:20px;"
      div.row#actions-section
        div.general-actions.large-12.small-12.columns
          .panel.panel-default
          - suborder_status = @suborder.shipments.map(&:shipment_kind).first
          - type = suborder_status == 'pickup' ? 'pickup' : 'bna'
          - possible_actions = @suborder.state_possible_actions(type)
          - if possible_actions.present? && possible_actions.size.positive? && (@suborder.office || suborder_status == 'pickup')
            - actions = current_user.office.present? ? %w[approved declined posted cancelled] : %w[delivered pending_cancellation billed]
            - @suborder.state_display_buttons(type).each do |button|
              - if actions.include?(button[:label])
                - message = t("lux.suborders.state_validation.#{button[:label]}", order_id: @suborder.order_id)
                = form_for @suborder, url: apply_state_change_shop_suborders_path, method: :post, :html => {:style => 'display: inline-block; float: left; margin-right: 5px;', :id => button[:label]} do |f|
                  = hidden_field_tag :next_state_action, button[:id]
                  = hidden_field_tag :suborder_id, @suborder.id
                  button.button.red.small onclick="submitForm('#{message}', #{button[:label]})" type="button"
                    = t("lux.suborders.state_buttons.#{button[:label]}")
          - else
            h6= t("lux.suborders.state_buttons.#{@suborder.status}")

- content_for(:js) do
  javascript:
    function submitForm(message, form) {
      if(this.confirm(message)){
        form.submit()
      }
    } 


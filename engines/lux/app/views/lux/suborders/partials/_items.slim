- to_fulfill ||= false
h3= t('.title', quantity: items.to_a.sum(&:quantity))
.items
  - items.each do |item|
    .div.gp-panel
      .row
        .small-2.columns
          = image_tag item.variant.picture.url(:st), size: '100x100', style: "border-radius: 50%; border: 2px solid #73AD21;" if item.variant.picture
        .small-5.columns
          h4=item.title
          .details
            p=variant_properties(item.variant)
        .small-3.columns
          .details
            p style="font-size: 0.85em"
              = item_detail(item)
        .small-2.columns.text-center
          h4= "Total"
          h3= "#{number_to_currency(item.total_without_points, delimiter: '.', separator: ',', precision: 2)}"
          - if item.points > 0
            h4= t('.total-points')
            h3= "#{item.points}"
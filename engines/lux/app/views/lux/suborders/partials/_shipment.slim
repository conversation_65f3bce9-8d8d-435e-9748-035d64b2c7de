.shipment id="#{shipment.id}"
  h3 = t('.title', id: shipment.id) + " - " + t('.charged_amount', charged_amount: number_to_currency(shipment.charged_amount, unit: '', delimiter: '.', separator: ',', precision: 2))
  = render partial: '/lux/suborders/partials/items', object: shipment.items
  - if shipment.unfulfilled?
    h3 = t('.manage')
    .div.gp-panel
      = render partial: '/lux/suborders/partials/unfulfilled', locals: {suborder: suborder, shipment: shipment}
  - else
    h3 = t('.delivery_information')
    .div.gp-panel
      .deliveries
        = render partial: '/lux/suborders/partials/delivery', locals: { shipment: shipment, suborder: suborder }

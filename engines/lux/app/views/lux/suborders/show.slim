= content_for :title do
  = t('.title', public_id: @suborder.public_id, title:@suborder.title)
= content_for :main_javascript do
  = javascript_include_tag 'lux/v2/application'
css:
  /* Style the tab */
  .tab {
    overflow: hidden;
    background-color: #ffffff;
    border-bottom: 1px solid #ccc;
  }

  /* Style the buttons inside the tab */
  .tab button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 14px 16px;
    transition: 0.3s;
    font-size: 17px;
  }

  /* Change background color of buttons on hover */
  .tab button:hover {
    background-color: #ddd;
  }

  /* Create an active/current tablink class */
  .tab button.active {
    background-color: #ccc;
  }

.suborder.large-12.columns
  h2.thin= "#{@suborder.public_id} - #{placed_at(@suborder)}"
  - if @suborder.external_redemption_completed.present?
    - unless @suborder.external_redemption_completed
      h4.thin= t('.external-redemption-failed')
  .div class="tab"
    button class="tablinks active" onclick="openTab(event, 'general-data')"
      = "#{t('.general')}"
    button class="tablinks" onclick="openTab(event, 'shipment-manage')"
      = "#{t('.shipment-manage')}"
    - if @suborder.can_show_reservable_states?
      button class="tablinks" onclick="openTab(event, 'documentation')"
        = "#{t('.documentation')}"
  .div id="general-data" class="tabcontent" style="display: block;"
    = render partial: '/lux/suborders/partials/general'
  .div id="shipment-manage" class="tabcontent" style="display: none;"
    = render partial: '/lux/suborders/partials/shipment_manage'
  - if @suborder.can_show_reservable_states?
    .div id="documentation" class="tabcontent" style="display: none;"
      = render partial: '/lux/suborders/partials/documentation'

- content_for(:js) do
  javascript:
      function openTab(evt, tabName) {
          var i, tabcontent, tablinks;
          tabcontent = document.getElementsByClassName("tabcontent");
          for (i = 0; i < tabcontent.length; i++) {
              tabcontent[i].style.display = "none";
          }
          tablinks = document.getElementsByClassName("tablinks");
          for (i = 0; i < tablinks.length; i++) {
              tablinks[i].className = tablinks[i].className.replace(" active", "");
          }
          document.getElementById(tabName).style.display = "block";
          evt.currentTarget.className += " active";
      }
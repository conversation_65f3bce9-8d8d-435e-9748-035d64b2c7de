.new-order-item
  a.add-new.link-button href='#'
    = t('.add_new')

  / #add-item-form
  /   .row
  /     .col-sm-12.columns
  /       p.form-errors
  /         = t('.errors')
  /   - parameters = { network: @network.downcase, shop_id: params[:shop_id], suborder_id: params[:id] }
  /   = form_tag shop_order_items_path(parameters), class: 'order-item-form' do
  /     = select('order_item', 'variant_id', Lux::OrderItemsHelper::OrderItemOptionsPresenter.new(@shop_variants).options)
  /     = text_field_tag 'order_item[quantity]', nil, placeholder: t('.how_many')
  /     | &nbsp;
  /     span.current-stock
  /     | &nbsp;&nbsp;
  /     = button_tag t('.add'), data: { confirm: t('.confirmation_message') }

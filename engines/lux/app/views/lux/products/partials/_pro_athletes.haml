- shop_athletes = get_shop_pro_athletes(product)
- product_athletes_ids = product.pro_athletes.map(&:id)

- if shop_athletes.present?
  .row
    .small-12.columns
      %label.with_explanation= t('.pro_athletes')
      .explanation= t('.explanation')
  .row
    .sports-tree.small-12.columns
      %select#pro_athletes{name: 'product[pro_athlete_ids][]', multiple: 'multiple', 'data-values' => product_athletes_ids}
        - shop_athletes.each do |pro_athlete|
          %option{value: pro_athlete.id}= pro_athlete.full_name
      %small{class: "error medium input-text", style: @product.errors.messages[:pro_athletes].present? && 'display: inherit;'}
        = @product.errors.messages[:pro_athletes].try([0])

- currencies = Mkp::Currency.select('id, identifier, symbol').all.collect { |c| [ "#{c.symbol.to_s} (#{c.identifier.to_s})", c.id ] }
.row
  .small-12.columns
    %label.with_explanation= t('.sale')
    .explanation= t('.explanation')
.row
  .small-6.medium-4.columns
    = f.text_field :sale_on , id: 'product_sale_on', label: t('.start_date'), placeholder: t('.click_here'), value: (@product.sale_on.strftime('%Y/%m/%d') unless (@product.sale_on.blank?) ), disabled: has_integration
  .small-6.medium-4.columns
    = f.text_field :sale_until , id: 'product_sale_until', label: t('.end_date'), placeholder: t('.click_here'), value: (@product.sale_until.strftime('%Y/%m/%d') unless (@product.sale_until.blank?)), disabled: has_integration
  .small-12.medium-4.columns
    = f.text_field :sale_price, placeholder: '0.00', label: t('.price'), class: 'text-right', disabled: has_integration
.row
  .small-12.medium-4.columns
    = f.text_field :sale_price_without_taxes, placeholder: 'Sin valor', label: t('.sale_price_without_taxes'), class: 'text-right'

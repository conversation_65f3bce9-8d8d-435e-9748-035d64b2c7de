%script#color-template{type: 'text/template'}
  .row.collapse.color-container.property-value
    .small-1.columns &nbsp;
    .small-10.columns
      .row.collapse.variant-color-selector
        .small-3.columns
          %span.prefix Color:
        .small-7.columns
          %input.color-name{ type: 'hidden', value: '<%= data.name %>', name: 'properties[][color][name]' }
          %input.color-name-selector.no-custom.with_prefix.with_postfix{ type: 'text', value: '<%= data.name %>', required: true, placeholder: 'Name', maxlength: 63 }
        .small-2.columns
          %span.postfix.color-hex-preview{ style: 'background-color:<%= data.hex %>;'}
            %input.color-hex.no-custom{ type: 'hidden', value: '<%= data.hex %>', name: 'properties[][color][hex]' }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

%script#property-container-template{type: 'text/template'}
  %div{class: 'row property-container', 'data-property' => '<%= data.name %>'}
    %input{type: 'hidden', name: '[product][available_properties][]', value: '<%= data.name %>'}
    .row
      .small-12.columns
        %label.with_explanation #{'<%= I18n.t("javascripts.lux.products.properties."+data.name) %>'}
        .explanation= '<%= data.msg %>'
    .row
      .small-12.columns
        %div{class: '<%= data.name %>-list property-values', 'data-property-name' => '<%= data.name %>'}
    .row
      %hr
      .small-6.columns
        %a{ class: '<%= data.name %> delete-property button alert tiny'}= t('lux.products.partials.properties.delete_property')
      .small-6.columns.right
        %a.add-new-property-value.button.tiny.success{'data-property' => '<%= data.name %>'}= t('lux.products.partials.properties.add')

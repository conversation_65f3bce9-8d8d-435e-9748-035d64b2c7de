%script#length-template{type: 'text/template'}
  .length-container.row.collapse.property-value
    .small-3.columns &nbsp;
    .small-9.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            #{t('lux.products.partials.properties.length')}:
        .small-7.columns
          %input.length-value.required.with_prefix{ type: 'text', name: 'properties[][length]', value: '<%= data.length %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

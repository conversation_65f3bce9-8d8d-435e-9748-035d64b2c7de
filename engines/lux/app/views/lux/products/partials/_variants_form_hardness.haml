%script#hardness-template{type: 'text/template'}
  .hardness-container.row.collapse.property-value
    .small-3.columns &nbsp;
    .small-9.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            #{t('lux.products.partials.properties.hardness')}:
        .small-7.columns
          %input.hardness-value.required.with_prefix{ type: 'text', name: 'properties[][hardness]', value: '<%= data.hardness %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

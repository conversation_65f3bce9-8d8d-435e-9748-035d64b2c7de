%script#material-template{type: 'text/template'}
  .material-container.row.collapse.property-value
    .small-3.columns &nbsp;
    .small-9.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            Material:
        .small-7.columns
          %input.material-value.required.with_prefix{ type: 'text', name: 'properties[][material]', value: '<%= data.material %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

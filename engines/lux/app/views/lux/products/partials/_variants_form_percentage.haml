%script#percentage-template{type: 'text/template'}
  .row.collapse.percentage-container.property-value
    .small-1.columns &nbsp;
    .small-6.columns
      .row.collapse
        .small-6.columns
          %span.prefix
            #{t('lux.products.partials.properties.percentage')}:
        .small-6.columns
          - selected = product.variants.find{|v| v.properties && v.properties[:percentage].present?}
          - selected_value = selected.present? ? selected.properties[:percentage].to_i : Mkp::Product::PERCENTAGE.first[:value]
          %select{ name: 'properties[][percentage]', class:"percentage-value custom dropdown", required: true}
            - Mkp::Product::PERCENTAGE.each do |percentage|
              %option{ value: percentage[:value], selected: percentage[:value] == selected_value}= percentage[:value]
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

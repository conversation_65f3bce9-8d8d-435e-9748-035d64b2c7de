- has_integration = @product.has_any_shop_integration? || @product.has_any_network_integration?
.row
  .small-12.columns
    %label.with_explanation= t('.title')
    .explanation= t('.explanation')
    .explanation
      %strong= t('.titles.purchasable')
      = t('.details.purchasable')
    .explanation
      %strong= t('.titles.reservable')
      = t('.details.reservable')
    .explanation
      %strong= t('.titles.other')
      = t('.details.other')
    .explanation
      %strong= t('.titles.voucher')
      = t('.details.voucher')
    .explanation
      %strong= t('.titles.points')
      = t('.details.points')

.row
  .small-3.medium-3.columns
    = f.select :transaction_type, options_for_select(@transaction_types, @product.transaction_type), {include_blank: false, class: 'form-control', label: false}, {disabled: has_integration}
    
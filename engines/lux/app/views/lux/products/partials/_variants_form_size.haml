%script#size-template{type: 'text/template'}
  .size-container.row.collapse.property-value
    .small-6.columns &nbsp;
    .small-4.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            #{t('lux.products.partials.properties.size')}:
        .small-7.columns
          %input.size-value.required.with_prefix{ type: 'text', name: 'properties[][size]', value: '<%= data.size %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

.row
  .small-12.columns
    %label.with_explanation= t('.sport')
    .explanation= t('.explanation')
.row
  .sports-tree.small-12.columns
    %select#sports-tree{name: 'product[sport_ids][]', multiple: 'multiple', 'data-values' => product.sports.map(&:id)}
      - Sport.roots.each do |sport|
        %optgroup{label: sport.name}
          - sport.children.each do |sub_sport|
            %option{value: sub_sport.id}= sub_sport.name
    %small{class: "error medium input-text", style: @product.errors.messages[:sports].present? && 'display: inherit;'}
      = @product.errors.messages[:sports].try([0]) or t('.at_least_one_sport')

%ul#pictures-container.row{ 'data-id' => @product.id }
  - @product.pictures.each do |p|
    %li#picture.large-4.medium-4.small-6.columns.end{ 'data-pic_id' => p.id }
      = image_tag p.url(:m)
      - unless has_integration
        .delete-btn{ 'data-picture_id' => p.id }
          = image_tag 'lux/delete.png'

= render partial: 'social/partials/file_uploader',
                  locals: { url: shop_unpublished_pictures_path, type: 'Mkp::Attachment::ProductPicture' }

%a.button.add-photos{ 'data-shop-id' => @shop.id, disabled: has_integration }= t('.upload_pictures')
.hiddens

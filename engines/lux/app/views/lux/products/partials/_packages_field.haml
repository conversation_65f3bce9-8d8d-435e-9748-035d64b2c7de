- require_dimensions = Network[@network].requires_product_dimensions?
%fieldset
  = link_to image_tag('/assets/pioneer/delete.png'), '#', {class: "remove_fields", style:"cursor: pointer; width: 10px; float: right", title: "Eliminar"}
  .row
    .small-12.medium-3.columns
      = f.text_field :height, label: t('.height'), placeholder: t('.height'), class: require_dimensions && 'required'
    .small-12.medium-3.columns
      = f.text_field :length, label: t('.length'), placeholder: t('.length'), class: require_dimensions && 'required'
    .small-12.medium-3.columns
      = f.text_field :width,  label: t('.width'), placeholder: t('.width'), class: require_dimensions && 'required'
    .small-12.medium-3.columns
      = f.select :length_unit, Mkp::Unit::Length.all.map { |measure| [ t(".#{measure}"), measure ] }.reverse, {label: t('.length-unit')}, {selected: @product.length_unit || Network[@network].length_unit}
  .row
    .small-12.medium-3.columns
      = f.text_field :weight, label: t('.weight'), placeholder: t('.weight'), class: require_dimensions && 'required'
    .small-12.medium-3.columns
      = f.select :mass_unit, Mkp::Unit::Mass.all.map { |measure| [ t(".#{measure}"), measure ] }.reverse, {label: t('.mass-unit')}, {selected:  @product.mass_unit || Network[@network].mass_unit}
    .small-12.medium-3.columns
      = f.hidden_field :_destroy

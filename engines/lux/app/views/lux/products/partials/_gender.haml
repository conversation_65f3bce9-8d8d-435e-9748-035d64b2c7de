.row
  .small-12.columns
    %label.with_explanation= t('.gender')
    .explanation= t('.explanation')
  #gender-options.cf
    - @genders.each do |rg|
      .small-6.columns
        %h5
          = rg.name
        - rg.children.each do |g|
          .row
            .small-12.columns.gender-container
              = radio_button_tag "product[gender_ids][]", g.id, @product.genders.include?(g), { class: 'hidden-field', id: "product_gender_ids_#{g.id}" }
              %span.gender
                = g.name

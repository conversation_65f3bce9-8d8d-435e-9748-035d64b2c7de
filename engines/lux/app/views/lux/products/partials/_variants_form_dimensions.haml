%script#dimensions-template{type: 'text/template'}
  .dimensions-container.row.collapse.property-value
    .small-4.columns &nbsp;
    .small-6.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            #{t('lux.products.partials.properties.dimensions')}:
        .small-7.columns
          %input.dimensions-value.required.with_prefix{ type: 'text', name: 'properties[][dimensions]', value: '<%= data.dimensions %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

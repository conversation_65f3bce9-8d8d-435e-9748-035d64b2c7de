%script#payment_method-template{type: 'text/template'}
  .row.collapse.payment_method-container.property-value
    .small-1.columns &nbsp;
    .small-6.columns
      .row.collapse
        .small-6.columns
          %span.prefix
            #{t('lux.products.partials.properties.payment_method')}:
        .small-6.columns
          - selected = product.variants.find{|v| v.properties && v.properties[:payment_method].present?}
          - selected_value = selected.present? ? selected.properties[:payment_method] : Mkp::Product::PAYMENT_METHODS.first[:value]
          %select.payment_method-value.required.with_prefix.custom.dropdown{ name: 'properties[][payment_method]'}
            - Mkp::Product::PAYMENT_METHODS.each do |payment|
              %option{ value: payment[:value], selected: payment[:value] == selected_value }= payment[:value]
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')

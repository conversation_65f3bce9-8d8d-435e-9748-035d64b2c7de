.large-12.columns
  .row.gp-simple-panel
    .panel-active
    .large-12.columns
      %h1.thin.text-center
        Custom Properties: #{@product.title}
        .subheader
          or
          %a{:href => params[:return_to]} cancel
      %hr
    .large-12.columns
      = render 'form_custom_properties'

- content_for :js do
  :javascript
    var number = 0;
    function duplicateProperty() {
      $.ajax({
        url: '#{duplicate_property_shop_product_url(@product.network.downcase, @product.shop.id, @product.id)}',
        data: {number: number},
        method: "POST",
        success: function(response) {
          number = number + 1;
          $('.all-variants').append(response);
        }
      });
    }


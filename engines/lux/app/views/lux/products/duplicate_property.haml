.row
  - @properties.each do |property|
    - if property == :color
      .small-4.columns
        .color-list.property-values{"data-property-name" => "color"}
          .row.collapse.color-container.property-value
            .small-12.columns
              .row.collapse.variant-color-selector
                .small-2.columns
                  %span.prefix Color:
                .small-5.columns
                  %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{@number}][properties][#{property}][name]", :maxlength => "63", :placeholder => "Name", :type => "text", :value => ""}/
                .small-5.columns
                  %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{@number}][properties][#{property}][hex]", :maxlength => "63", :placeholder => "Name", :required => "", :type => "text", :value => ""}/
    - else
      - unless property.is_a?(String)
        - property =  property[:slug]
        %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{@number}][properties][#{property}][slug]", :type => "hidden", :value => property}/

      .small-2.columns
        .property-values
          .row.collapse.color-container.property-value
            .small-12.columns
              .row.collapse.variant-color-selector
                .small-4.columns
                  %span.prefix #{property}:
                .small-8.columns
                  %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{@number}][properties][#{property}]", :maxlength => "63", :placeholder => property, :required => "", :type => "text", :value => ""}/
  .small-2.columns
    .property-values
      .row.collapse.color-container.property-value
        .small-12.columns
          .row.collapse.variant-color-selector
            .small-4.columns
              %span.prefix Quantity:
            .small-8.columns
              %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{@number}][quantity]", :maxlength => "63", :required => "", :type => "number", :value => ""}/
  .small-4.columns

= content_for :title do
  = t('.head_title')
.large-12.columns
  .row
    .large-12.columns
      .row#actions-section
        .large-12.small-12.columns.pt-1
          = render 'top_links'

      %hr
    = content_for :js do
      :javascript
        if( window.gp ) {
          gp.sports = new gp.Lux.Sports(#{raw @sports.to_json})
          gp.categories = new gp.Lux.Categories(#{raw @categories.to_json})
          gp.manufacturers = new gp.Lux.Manufacturers(#{raw @manufacturers.to_json})
        }

    .large-12.columns
      .products-list.loading
        .row.products-list-actions
          .small-12.medium-5.columns
            .refinements
              %button.button.tiny{ 'data-refinement' => 'on_sale' }= t('.on_sale')
              %button.button.tiny{ 'data-refinement' => 'with_stock' }= t('.with_stock')
              %button.button.tiny{ 'data-refinement' => 'without_stock' }= t('.without_stock')
              %button.button.tiny{ 'data-refinement' => 'available' }= t('.available')
              %button.button.tiny{ 'data-refinement' => 'unavailable' }= t('.unavailable')
            - shop = Mkp::Shop.find_by_slug(params["shop_id"])
            - shop_id = shop.present?.present? ? shop.id : params["shop_id"].to_i
            %button.button.tiny.enable_all{'data-shop' => shop_id}
              = t('.enable_all')
            %button.button.tiny.disabled_all{'data-shop' => shop_id}
              = t('.disabled_all')
          .br
          .small-12.medium-4.columns
            %select.products-list-for-category-search.form-control{:name => 'category'}
              %option{ value: 0 }= 'Seleccione una categoria'
              - @categories.order(name: :asc).each do |category|
                %option{ value: category.id }= category.name
          .small-12.medium-3.columns
            %input.products-list-search{ type: 'text', placeholder: t('.search') }
        %h3.products-list-msg.empty-msg= t('.no_products')
        %h3.products-list-msg.loading-msg= t('.loading')
        .products-list-list-wrapper
          %ul.products-list-pagination
          %table{ width: '100%' }
            %thead
              %tr
                %th{ width: '80' }= check_box_tag "select_all"
                %th{ width: '80' } &nbsp;
                %th.text-left.orderable{ 'data-order-column' => 'title' }= t('.title')
                %th.orderable{ 'data-order-column' => 'manufacturer' }= t('.manufacturer')
                %th= t('.category')
                %th= t('.gp_sku')
                %th.orderable{ 'data-order-column' => 'regular_price' }= t('.price')
                %th.orderable{ 'data-order-column' => 'sale_price' }= t('.sale_price')
                %th= t('.sale_on')
                %th= t('.sale_until')
                %th= t('.visible')
                %th.orderable{ 'data-order-column' => 'stock' }= t('.stock')
                %th.orderable{ 'data-order-column' => 'sold' }= t('.sold')
                %th.text-right{ width: '150' } &nbsp;
            %tbody.products-list-list
          %script#products-list-item{ type: 'text/template' }
            %tr.products-list-item{ id: 'product-<%= data.id %>', 'data-product-id' => '<%= data.id %>' }
              %td.list-item
                %input{type: 'checkbox', class: 'checkbox-visibility', id: '<%= data.id %>'}
              %td.list-item-thumb
                %img{ src: '<%= data.thumb %>' }
                  <% if (data.has_integration) {
                  print('<img class="i-integration" src="'+ data.integration_image +'"/>')
                  } %>
              %td.list-item-title.text-left.editable.not-editing{ 'data-value' => '<%= data.title %>' } <%= data.title %>
              %td.list-item-manufacturer.editable.not-editing{ 'data-value' => '<%= data.manufacturer_id %>' } <%= data.manufacturer_name %>
              %td.list-item-category.editable.not-editing{ 'data-value' => '<%= data.category_id %>' } <%= data.category_name %>
              %td.list-item-gp-sku.editable.not-editing <%= data.gp_sku %>
              %td.list-item-regular_price.editable.not-editing{ 'data-value' => '<%= data.regular_price %>' } <%= data.currency_symbol %><%= data.regular_price %>
              %td.list-item-sale_price.editable.not-editing <%= data.sale_price_text %>
              %td.list-item-sale_on.editable.not-editing <%= data.sale_on_text %>
              %td.list-item-sale_until.editable.not-editing <%= data.sale_until_text %>
              %td.visible
                %a{ href: '<%= data.has_integration ? "javascript:;" : data.hide_url %>', title: t('.hide'), class: '<%= data.available ? "display" : "hide" %>' }
                  %i{ class: "fa fa-check" }
                %a{ href: '<%= data.has_integration ? "javascript:;" : data.display_url %>', title: t('.display'), class: '<%= data.available ? "hide" : "display" %>' }
                  %i{ class: "fa fa-close" }
              %td <%= data.stock %>
              %td <%= data.sold_count %>
              %td.text-left.list-item-actions><
                %a.should-return{ href: '<%= data.edit_url %>', title: t('.edit') }
                  %i{ class: "fa fa-edit" }
                &nbsp;
                <% print('<a class="should-return" href="'+ data.delete_url +'" title="Eliminar" onclick="return confirm('+ "\'¿Seguro que desea eliminar este producto?\'" +');"><i class="fa fa-trash-o"></i></>') %>
          %ul.products-list-pagination

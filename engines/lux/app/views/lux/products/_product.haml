.large-12.medium-6.small-12.columns
  .gp-simple-panel.row
    .picture.large-1.medium-4.small-3.columns
      %a{href: '#'}
        - if !product.pictures.first.nil?
          = image_tag product.pictures.first.url(:m)
        - else
          .text-center
            This product does not have a picture
    .general-data.large-11.medium-8.small-9.columns
      .row
        .large-5.small-12.columns
          %h3.product-title
            = link_to product.title, shop_product_path(id: product)
            .subheader
              = link_to 'View', shop_product_path(id: product)
              - if product.variants.any?
                |
                = link_to 'Preview', product.variants.first.get_url, target: '_new'
              |
              = link_to 'Clone', clone_shop_product_path(id: product)
              |
              = link_to 'Edit', edit_shop_product_path(id: product)
              |
              = link_to 'Delete', delete_shop_product_path(id: product), onclick: "return confirm('Are you sure want to delete the product?')"
        .large-5.small-12.columns
          #product-data
            %span.id ID:&nbsp;#{product.id}
            %span.price Price:&nbsp;#{number_to_currency product.price, precision: 2}
            %span.stock Stock:&nbsp;#{product.total_stock}
          -# .item Date: #{product.created_at.strftime('%d %b %y')}
          -# .item Currently: #{product_available_info(product)}
        .large-2.small-12.columns
          #sale-data
            - unless product.suborders.blank?
              %span.selled_date
                Last sold: #{product.suborders.last.created_at.strftime(t('date.formats.short')+' %Y')}
            %span.selled_count{ class: product.suborders.blank? && 'zero' }
              Sold:&nbsp;#{product.order_items.count}

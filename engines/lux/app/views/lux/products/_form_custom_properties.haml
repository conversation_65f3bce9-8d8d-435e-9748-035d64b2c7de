= form_for @product, multipart: true, as: 'product', url: save_properties_shop_product_url(@product.network.downcase, @product.shop.id, @product.id), html: {class: 'custom', id: 'product-form'} do |f|
  = hidden_field_tag :return_to, params[:return_to]
  .row.all-variants.property-container{"data-property" => "color"}
    .row
      .small-12.columns
        %label.with_explanation Variants
        .explanation
    - @product.variants.each do |variant|
      .row
        - variant.properties.each do |property, value|
          - if property.to_sym == :color
            .small-4.columns
              .color-list.property-values{"data-property-name" => "color"}
                .row.collapse.color-container.property-value
                  .small-12.columns
                    .row.collapse.variant-color-selector
                      .small-2.columns
                        %span.prefix Color:
                      .small-5.columns
                        %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{variant.id}][properties][#{property}][name]", :maxlength => "63", :placeholder => "Name", :type => "text", :value => value[:name]}/
                      .small-5.columns
                        %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{variant.id}][properties][#{property}][hex]", :maxlength => "63", :placeholder => "Name", :required => "", :type => "text", :value => value[:hex]}/
          - elsif value.is_a?(String)
            .small-2.columns
              .property-values
                .row.collapse.color-container.property-value
                  .small-12.columns
                    .row.collapse.variant-color-selector
                      .small-4.columns
                        %span.prefix #{property}:
                      .small-8.columns
                        %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{variant.id}][properties][#{property}]", :maxlength => "63", :placeholder => property, :required => "", :type => "text", :value => value}/
        .small-2.columns
          .property-values
            .row.collapse.color-container.property-value
              .small-12.columns
                .row.collapse.variant-color-selector
                  .small-4.columns
                    %span.prefix Quantity:
                  .small-8.columns
                    %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{variant.id}][quantity]", :maxlength => "63", :required => "", :type => "number", :value => variant.quantity}/
        .small-4.columns
          .property-values
            .row.collapse.color-container.property-value
              .small-12.columns
                .row.collapse.variant-color-selector
                  .small-4.columns
                    %span.prefix SKU:
                  .small-8.columns
                    %input.color-name-selector.no-custom.with_prefix.with_postfix{name: "product[#{variant.id}][sku]", :maxlength => "63", :required => "", :type => "text", :value => variant.sku}/

  .row
    %hr/
    .small-6.columns
      %a.add-new-property-value.button.tiny.success{onclick: "duplicateProperty()"} Agregar +

  .large-12.columns
    %hr
    .product-actions
      %a.button{ href: params[:return_to] }= t('.cancel')
      %button.button.red#save{ type: 'submit' }= t('.save')


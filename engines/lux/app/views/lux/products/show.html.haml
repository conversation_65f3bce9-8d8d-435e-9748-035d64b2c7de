= content_for :title do
  = @product.title

#update-form-js
  = form_for @product, as: 'product', url: ajax_variants_update_picture_url(@shop.to_param), remote: true, method: 'put' do |f|
    .hiddens

.large-12.columns
  .row.gp-simple-panel
    .panel-active
    .large-12.columns
      %h1.thin.text-center
        %a{ href: @product.variants.count > 0 && @product.variants.first.get_url, target: '_blank' }= @product.title
        .subheader
          = link_to 'Edit', edit_shop_product_path(id: @product, return_to: params[:return_to])
          |
          = link_to 'Preview', @product.get_url, target: '_new'
          | #{link_to ' Edit Custom Properties', custom_properties_shop_product_url(@product.network.downcase, @product.shop.id, @product.id) if @product.has_custom_properties?}
      %hr
    .large-12.columns
      .small-12.columns
        %strong Descripción:
        %p= simple_format(@product.description)
      .large-7.columns
        .section.row
          .small-12.columns
            %h4 Propiedades y especificaciones
          .small-12.columns
            .field
              .row
                .small-12.columns
                  %label.with_explanation Categoría del producto
              .row
                .small-12.columns
                  - categories = category_ancestry(@product.category)
                  - categories.each_with_index do |category, index|
                    .button.tiny= category.name
                    = '  ' unless index+1 == categories.size
            .field
              .row
                .small-12.columns
                  %label.with_explanation Fabricante
              .row
                .small-12.columns
                  = @product.manufacturer.name
            .field
              .row
                .small-12.columns
                  %label.with_explanation Precio
              .row
                .small-4.columns.quantity
                  .row.collapse
                    .small-5.columns
                      %span.prefix
                        = @product.currency.identifier
                    .small-7.columns
                      = text_field_tag 'name', number_to_currency(@product.price, unit: @product.currency.symbol), class: 'with_prefix disabled', disabled: 'disabled'

        - if @product.has_property?(:color)
          .section.row
            .small-12.columns
              %h4
                Fotos del Producto
            .small-12.columns
              .field
                #pictures-container.row
                  .loader
                  - @product.pictures.each do |p|
                    .picture.small-6.medium-4.columns.end
                      = image_tag p.url(:m)
                      - pictures_of_colors = @product.variants.map(&:picture_id)
                      - if @product.variants.length > 0
                        %h5 Color de la foto:
                        .color-form.row.collapse
                          %select.color-hex-selector{ name: 'variant[id]', 'data-picture_id' => p.id }
                            %option{ value: '0', selected: (!pictures_of_colors.include?(p.id)) } Ninguno
                            - @product.variants.each do |variant|
                              %option{ value: variant.id, 'data-color_hex' => variant.properties[:color][:hex], selected: (variant.id == p.variant_id) }= variant.color_name
                  %button.button.save-button{ type: :submit } Guardar

      .large-5.columns
        .section.row
          .small-12.columns
            %h4 Variantes del producto
          .small-12.columns
            .field
              .row.variants-container
                .small-12.columns
                  - @product.variants.each do |variant|
                    .row.collapse.color-container
                      .small-7.columns.title
                        %p
                          - unless @product.has_no_property?
                            - variant.properties.each do |name, value|
                              - value = value[:name] if name == :color
                              - name = @product.available_properties_names.key?(name) ? @product.available_properties_names[name] : Mkp::Product::COMPATIBILITY_PROPERTIES.key?(name) ? Mkp::Product::COMPATIBILITY_PROPERTIES[name] : name
                              %span.property #{name}: <b>#{value}</b> |
                          - sku = variant.sku.present? ? "#{variant.gp_sku} (#{variant.sku})" : "#{variant.gp_sku}"
                          %span SKU: <b>#{sku}</b>
                      .small-3.columns
                        %p
                          Stock:
                          %b= variant.quantity

    %a.button{ href: params[:return_to] } Volver a la lista
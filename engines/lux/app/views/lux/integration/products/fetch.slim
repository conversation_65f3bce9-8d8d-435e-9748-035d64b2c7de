= content_for :title do
  = t('.head_title', service: @integration.simple_type)

= content_for :js do
  = javascript_include_tag 'lux/integrations/base'

.large-12.columns.row
  .general-actions.large-12.small-12.columns
    a#import_all.button.small href="#"
      = t('.import_all', count: @products.size)
  .row
    .large-12.columns
      h2.title> = t('.new_ones')
  .row
    .large-12.columns.products-block
     = form_tag shop_products_integration_import_path(integration_name: :shopify) , method: :post, id: "import_products", class: "custom" do
      - @products.each do |id, product|
        = render partial: 'product', locals: { id: id, product: product }

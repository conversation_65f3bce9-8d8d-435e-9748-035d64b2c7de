.row.product
  .small-1.columns.text-center
    = check_box_tag "integration_pids[]", "#{id}", false,  class: 'hidden-field'
  .small-11.columns
    .row
      .large-1.medium-2.small-3.columns.text-center
        img src="#{product.picture.url(:small)}" height="80px"
      .large-11.medium-10.small-9.columns
        .line
          span.product_title> = product.title
          span.id>
            b> ID:
            = id
          span.price>
            b> Price:
            = number_to_currency product.price, precision: 2
        .line.description
          - if product.description.present?
            = truncate(product.description, length:400, escape: false).html_safe
          - else
            p
              = t('.no_description')
        .line
          - if product.manufacturer_mapper
            span.manufacturer>
              b> Manufacturer:
              = product.manufacturer
          - if product.category_mapper
            span.category>
              b> Category:
              = product.category_mapper.full_path
    - if product.variants.present?
      .row.variants
        .large-1.medium-2.small-3.columns.text-center
          span.label Variants:
        .large-11.medium-10.small-9.columns
          ul
            - product.variants.each do |variant|
              = render partial: '/lux/integration/products/list/variant', locals: { variant: variant }
    - if product.pictures.present?
      .row.pictures
        .large-1.medium-2.small-3.columns
          span.label Pictures:
        .large-11.medium-10.small-9.columns
          ul
            - product.pictures.each do |picture|
              li.picture
                img src="#{picture.url(:thumb)}" height="50px"



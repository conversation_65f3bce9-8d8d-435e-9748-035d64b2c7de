- asker = question.user
.large-12.medium-6.small-12.columns
  .gp-simple-panel.row
    - if question.product.pictures.any?
      .picture.large-1.medium-4.small-3.columns
        .picture
          %a{:id => "question-#{question.id}",:href => '#', "data-reveal-id" => "question-#{question.id}-#{asker.id}", 'class' => 'reveal-question-link' }
            = image_tag question.product.pictures.first.url(:st)
    .large-11.medium-8.small-9.columns
      .row
        .large-12.columns
          %h5
            = time_ago_in_words(question.created_at) + " (#{question.created_at.strftime('%H:%m - %d %b')})"
          %h4.user-id
            %a{href: '#'} #{question.product.title}
            .subheader
              %span.id
                = t('.user', full_name: asker.full_name, login: asker.login)
              .question
                = question.description
        .actions.large-12.columns
          - if question.answer
            %a{:id => "question-#{question.id}",:href => '#', "data-reveal-id" => "question-#{question.id}-#{asker.id}", 'class' => 'reveal-question-link button tiny' }
              = t('.view')
          - else
            %a{:id => "question-#{question.id}",:href => '#', "data-reveal-id" => "question-#{question.id}-#{asker.id}", 'class' => 'reveal-question-link button tiny red', read_path: read_shop_product_question_path(product_id: question.product.id, id: question.id, shop_id: @shop.id) }
              = t('.answer')
- content_for :before_js do
  .question-modal.reveal-modal{ id: "question-#{question.id}-#{asker.id}" }
    .question-show
      %a.close-reveal-modal &times;
      %h2
        = t('.chat_with')
        .login @#{asker.login}
      .question-section
        .login @#{asker.login}:
        #{question.description}
      - if question.answer.present?
        = render partial: 'lux/questions/answer', object: question.answer
        %br
        .back.small.close-reveal-modal.left= t('.back')
      - else
        = form_tag shop_product_question_answer_path(product_id: question.product.id, question_id: question.id, format: 'js'), id: "form-#{question.id}-#{asker.id}", class: 'new-answer-form' do
          = text_field_tag 'description'
          .back.small.close-reveal-modal.left= t('.back')
          %button#reply.button.small.success.right= t('.reply')


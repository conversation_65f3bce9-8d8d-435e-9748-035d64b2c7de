= content_for :title do
  = t('.head_title')

.large-12.columns
  .row
    .large-12.columns
      .row
        .search.large-12.columns
          = form_tag shop_questions_path, :method => :get do
            = hidden_field_tag :status, @status
            = text_field_tag :query, @query, placeholder: t('.description')
      %hr
    .large-12.columns
      .general-filters.large-12.columns
        %dl.sub-nav
          %dt Filter:
          %dd{class: @status.blank?  && 'active'}
            %a{href: shop_questions_path(status: '', query: @query)}= t('.all')
          %dd{class: @status == 'unread'  && 'active'}
            %a{href: shop_questions_path(status: 'unread', query: @query)}= t('.unread')
          %dd{class: @status == 'replied' && 'active'}
            %a{href: shop_questions_path(status: 'replied', query: @query)}= t('.replied')
          %dd{class: @status == 'not_replied' && 'active'}
            %a{href: shop_questions_path(status: 'not_replied', query: @query)}= t('.not_replied')

      = render partial: '/lux/questions/list', locals: { questions: @questions}

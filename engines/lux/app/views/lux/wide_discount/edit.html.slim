= content_for :title do
  = t('.head_title')

= content_for :js do
  javascript:
    if( window.gp ) {
      gp.categories = new gp.Lux.Categories(#{raw @categories.to_json});
      gp.manufacturers = new gp.Lux.Manufacturers(#{raw @manufacturers.to_json});
    }

.wide-discount-pagination.small-12.columns
  .row.gp-simple-panel.wide-sale
    .panel-active
    .small-12.columns
      h1.thin.text-center
        = t('.set_sale')
      hr
    .small-12.medium-10.large-10.small-centered.columns
      = form_for @shop.setting, url: shop_commit_wide_discount_path(@shop), html: { class: 'edit-wide-sale' } do |f|
        .row
          .small-12.medium-8.columns
            = f.label :discount_percent, t('.discount_percent')
            = f.label :discount_percent, t('.discount_percent_explanation'), class: 'sub'
            .row.collapse
              .small-3.medium-2.columns
                = f.text_field :discount_percent, label: false, class: 'with_postfix', required: true, placeholder: '20', pattern: '(\d{1,2}(\.\d+)?|100)', max: 100, min: 0, title: t('.percent_warn')
              .small-3.medium-2.columns.end
                span.postfix %
        .row
          .small-12.medium-6.columns
            = f.label :discount_starts_at, t('.start_date')
            = f.label :discount_starts_at, t('.start_date_explanation'), class: 'sub'
            = f.text_field :discount_starts_at, label: false, required: true, placeholder: t('.click_here')
          .small-12.medium-6.columns.end
            = f.label :discount_ends_at, t('.end_date')
            = f.label :discount_ends_at, t('.end_date_explanation'), class: 'sub'
            = f.text_field :discount_ends_at, label: false, required: true, placeholder: t('.click_here')
        .row.restrictions
          .small-12.medium-6.columns
            = f.label :restriction_label, t('.categories')
            = select_tag('categories_ids',
                         filter_options_for_select(@_categories),
                         multiple: true,
                         id: 'categories-restriction',
                         class: 'restriction-select')
          .small-12.medium-6.columns
            = f.label :restriction_label, t('.manufacturers')
            = select_tag('manufacturers_ids',
                         filter_options_for_select(@_manufacturers),
                         multiple: true,
                         id: 'manufacturers-restriction',
                         class: 'restriction-select')
        .row
          .small-12.columns.apply-to-all-checkbox
            = f.check_box :discount_is_over_product_sale, label: t('.apply_all')

        hr
        button#save.button.small.red.right type= 'submit' = t('.apply')

        .row
        .small-6.columns
          - if @shop.setting.discount_applying_now?
            = form_for @shop.setting, url: shop_commit_wide_discount_path(@shop) do |f|
              = hidden_field_tag :discount_remove, 'true'
              button.button.small type= 'submit' = t('.delete')

        hr
        .row.restrictions
          .small-12.columns
            #products-container
            script id="products-results" type="text/template"
              #affected-products-amount
              ul.products-list-pagination
              table
                thead
                  tr
                    th = t('.title')
                    th = t('.manufacturer')
                    th = t('.category')
                    th = t('.price')
                    th = t('.sale_price')
                    th = t('.sale_on')
                    th = t('.sale_until')
                tbody
                  <% _.each(products, function(product) { %>
                  tr
                    td <%= product.title %>
                    td <%= product.manufacturer_name %>
                    td <%= product.category_name %>
                    td <%= product.currency_symbol %><%= product.regular_price %>
                    td <%= product.currency_symbol %><%= product.sale_price %>
                    td <%= product.sale_on_text %>
                    td <%= product.sale_until_text %>
                  <% }); %>

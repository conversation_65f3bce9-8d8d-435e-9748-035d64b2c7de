= content_for(:title) { t(".title_for_#{params[:strategy]}_status") }

.large-12.columns
  .row.gp-simple-panel.collapse
    .panel-active
    .large-12.columns
      %h1.thin.text-center
        = t(".title_for_#{params[:strategy]}_status")
      %hr
    .large-12.columns
      .product-imports-item
        .product-imports-progress
          .meter{ style: 'width: 0' }
        %p
          %span Status:
          %span.product-imports-item-status{ 'data-status' => 'in_progress' } In Progress
        .product-imports-item-errors{ style: 'display:none' }
    .large-12.columns
      .product-imports-summary
    .large-12.columns
      %a.button.small.back-to-products{ href: shop_products_url }
        Back to Products
      %a.button.small.red.back-to-import-products{ href: new_shop_product_import_path(return_to: "/manage/shops/#{@shop.to_param}/products", strategy: params['strategy']) }
        Back to Import Products

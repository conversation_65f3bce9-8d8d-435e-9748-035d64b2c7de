.guidlines
  .row.gp-simple-panel.collapse
    .large-12.columns
      h2.thin.text-center
        p.title= t('.title')
      .general-guidelines
        table
          thead
            tr
              th Column
              th Description
          tbody
            tr
              td AV SKU
              td= t('.avsku_d')
            tr
              td Title
              td= t('.title_d')
            tr
              td Description
              td= t('.description_d')
            tr
              td Category
              td= t('.category_d')
            tr
              td Transaction Type
              td= t('.transaction_type_d')
            tr
              td Manufacturer
              td= t('.manufacturer_d')
            tr
              td Price
              td= t('.price_d')
            tr
              td Available On
              td= t('.available_on_d')
            tr
              td Height
              td= t('.height_d')
            tr
              td Length
              td= t('.length_d')
            tr
              td Width
              td= t('.width_d')
            tr
              td Weight
              td= t('.weight_d')
            tr
              td Stock
              td= t('.stock_d')
            tr
              td Pickeable
              td= t('.pickeable_d')
        p
          span= t('.details1')
          span= link_to 'categories', '#categories-popup', class: 'open_categories_popup text_bold'
          span= ', '
          span= link_to 'manufacturers', '#manufacturers-popup', class: 'open_manufacturers_popup text_bold'
          span= t('.details2') + Network[@shop.network].length_unit
          span= t('.details3') + Network[@shop.network].mass_unit
          span= t('.details4')
        table.margin-top
          thead
            tr
              th= t('.Property Names')
              th= t('.Description')
          tbody
            tr
              td Color
              td= t('.color_d')
            tr
              td Size
              td= t('.size_d')
            tr
              td Dimensions
              td= t('.dimensions_d')
            tr
              td Hardness
              td= t('.hardness_d')
            tr
              td Length
              td= t('.length_d')
            tr
              td Material
              td= t('.material_d')
            tr
              td Percentage
              td= t('.percentage_d')
            tr
              td Payment_Method
              td= t('.payment_method_d')

        p
          span= t('.details5')
          span= link_to 'colors', '#colors-popup', class: 'open_colors_popup text_bold'
          span= t('.details6')

        p.margin-top
          - if @network == 'US'
            span= 'You can download an example '
            span= link_to 'spreadsheet', '/Spreadsheet Product Import Tool.xlsx'
            span= ' and the resulting ' 
            span= link_to 'csv file', '/CSV Product Import Tool.csv'
            span= ' to assist you.'
          - else
            span= t('.details7')
            span= link_to 'spreadsheet', '/spreadsheet_importacion_producto.xlsx'
            span= t('.details8') 
            span= link_to 'csv file', '/csv_importacion_producto.csv'
            span= t('.details9')
  #categories-popup.mfp.mfp-hide
    = render partial: 'categories'
  #sports-popup.mfp.mfp-hide
    = render partial: 'n_column_list', locals: { list: @sports, columns: 3, heading: 'sports' }
  #genders-popup.mfp.mfp-hide
    = render partial: 'n_column_list', locals: { list: @genders, columns: 2, heading: 'genders' }
  #manufacturers-popup.mfp.mfp-hide
    = render partial: 'n_column_list', locals: { list: @manufacturers, columns: 5, heading: 'manufacturers' }
  #colors-popup.mfp.mfp-hide
    = render partial: 'n_column_list', locals: { list: @colors, columns: 3, heading: 'colors' }

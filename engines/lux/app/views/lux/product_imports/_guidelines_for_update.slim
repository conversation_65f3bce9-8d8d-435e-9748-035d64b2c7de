.guidlines
  .row.gp-simple-panel.collapse
    .large-12.columns
      h2.thin.text-center General guidelines for completing your csv file
      .general-guidelines
        table
          thead
            tr
              th Column
              th Description
          tbody
            tr
              td Price
              td A decimal number used to specify the regular price of a product on the marketplace
            tr
              td Stock
              td An integer number used to specify how many units of a distinct product are available for sale on the marketplace

        p You can edit the price and the stock of each product, the remaining values shouldn't be changed.

  - if @network == 'AR'
    .row.gp-simple-panel.collapse
      .large-12.columns
        h2.thin.text-center General guidelines for completing your csv file to update stock/price/sale price/sale on/sale until by AV SKU (Shipnow file)
        .general-guidelines
          table
            thead
              tr
                th Column
                th Description
            tbody
              tr
                td Descripcion
                td A name used to identify a specific product. Not Mandatory
              tr
                td AV SKU
                td Stock keeping unit, a unique identifier for each distinct product. Mandatory
              tr
                td Stock
                td 	An integer number used to specify how many units of a distinct variant are going to be available for sale on the marketplace. Not Mandatory
              tr
                td Price
                td   An integer/float number used to specify the regular price. Not Mandatory
              tr
                td Sale price
                td   An integer/float number used to specify the sale price. Not Mandatory
              tr
                td Sale on
                td   A datetime to specify when a offer begins. Eg: 2017-01-01. Not Mandatory
              tr
                td Sale until
                td   A datetime to specify when a offer ends. Eg: 2017-01-01. Not Mandatory


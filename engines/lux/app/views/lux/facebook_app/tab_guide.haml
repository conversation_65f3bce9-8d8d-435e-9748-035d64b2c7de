= content_for :title do
  Manage Shop - Facebook Page Tab

.small-12.columns
  %h4 Facebook Page Tab Guide
  %p This guide covers setting up a Page Tab to feature and run the GoodPeople Facebook App.

.small-12.columns
  %h5 Guide Assumptions
  %p.no-margin To get the most out of this guide, you need to have some prerequisites:
  %ul
    %li A #{link_to 'Facebook Page', 'https://www.facebook.com/pages/create/'}.
    %li A #{link_to 'GoodPeople Facebook App', shop_facebook_app_setup_guide_path}.

.small-12.columns
  %h5 Configuring your Page Tab
  %p.no-margin Click "Settings" on the left menu, click "+ Add Platform" at the bottom of the page, and select the "Page Tab" platform on the popup dialog.
  %img{ src: asset_path('facebook_app/page_tab.png'), size: '700x420', alt: 'Page Tab' }
  %p Find the "Page Tab Name" box and give your new tab a name, this name will appear on your Facebook Page.
  %p.no-margin Find the "Page Tab URL" box and add the following URL:
  = text_field_tag 'page_tab_url', facebook_app_goodpeople_shop_url(@shop.id, network: @network.downcase, protocol: 'http') + '/'
  %p.no-margin Next, find the "Secure Page Tab URL" box and add the following URL:
  = text_field_tag 'secure_page_tab_url', facebook_app_goodpeople_shop_url(@shop.id, network: @network.downcase, protocol: 'https') + '/'
  %p.no-margin Find the "Page Tab Image" uploader and upload the "GoodPeople FB App Image.png" file below to identify your GoodPeople Facebook App.
  %img{ src: asset_path('facebook_app/GoodPeople FB App Image.png'), size: '111x74', alt: 'GoodPeople FB App Image' }
  %p Go to the bottom of the page and click "Save Changes".
  %p.no-margin Click "App Details" on the left menu, on the "Icons" section find the "Icon" uploader.
  %img{ src: asset_path('facebook_app/icon.png'), size: '730x215', alt: 'Icon' }
  %p.no-margin Upload the "GoodPeople FB App Icon.png" file below to identify your GoodPeople Facebook App.
  %img{ src: asset_path('facebook_app/GoodPeople FB App Icon.png'), size: '16x16', alt: 'GoodPeople FB App Icon' }

.small-12.columns
  %h5 Adding your GoodPeople Facebook App to a Page Tab
  %p
    Copy and paste <b>https://www.facebook.com/dialog/pagetab?app_id=YOUR_APP_ID&next=YOUR_URL</b> into your browser and replace <b>YOUR_APP_ID</b> and <b>YOUR_URL</b> with your information, these values can be found in your GoodPeople Facebook App "Settings" on the left menu as "App ID" and "Canvas Page" respectevely.
  %p.no-margin Finally, select the Facebook Page to add your GoodPeople Facebook App and click "Add Page Tab".
  %img{ src: asset_path('facebook_app/add_page_tab.png'), size: '570x225', alt: 'Add Page Tab' }
  %p The GoodPeople Facebook App should be running in your Facebook Page.
  %p Congratulations!

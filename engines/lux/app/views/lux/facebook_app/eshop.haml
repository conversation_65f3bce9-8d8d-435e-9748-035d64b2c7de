!!!
%html{ lang: I18n.locale.to_s }
  %head
    %meta{ charset: 'UTF-8' }
    %meta{ content: 'IE=edge,chrome=1', 'http-equiv' => 'X-UA-Compatible' }
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no', name: 'viewport' }
    %meta{ content: I18n.locale.to_s, name: 'locale' }
    - if current_user.present?
      %meta{ content: current_user.id, name: 'user' }
    - if Rails.env.development?
      %meta{ content: 'development', name: 'environment' }
    %meta{ content: @network, name: 'network' }

    %meta{ property: 'og:site_name', content: 'GoodPeople Shop' }
    %meta{ property: 'fb:app_id', content: FACEBOOK_API }
    - if content_for?(:meta)
      = yield :meta
    - else
      %meta{ content: t('mkp.description'), name: 'description' }

    %title<
      = content_for?(:title) ? yield(:title) : 'GoodPeople | '+t('mkp.title')

    = stylesheet_link_tag '//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&subset=latin,latin-ext'
    = stylesheet_link_tag 'mkp/application'

    = render partial: 'partials/v5/javascripts/services', locals: { load_facebook: true }

    %meta{ name: 'robots', content: 'noindex, nofollow' }

  %body#facebook-eshop
    .main_header_wrapper
      .main_header
        .logo
          %a.full-link{:href => "http://goodpeople.com", :target => "_blank"}
        #search-form
          #main_header_search
            %form{ method: 'GET', target: '_blank', action: Mkp::Catalog::UrlHandler.url(network: @network.downcase) }
              = text_field_tag :q, "#{params[:q] if params[:q]}", :placeholder => t('mkp.variants.search.placeholder'), :autofocus => true
        %a.wicon{:href => mkp_root_url(network: @network.downcase), :target => "_blank"}
          %span Shop
        .clear
    .fb_mkp_subheader
      - if @footer_mkp_menu[:has_sales]
        .subheader_item{:class => 'sale' }
          %a.full-link{:href => Mkp::Catalog::UrlHandler.url(d: 1, network: @network.downcase), :target => "_blank"}
          = t('mkp.partials.subheader.on_sale')
      - @footer_mkp_menu[:items].each do |item|
        .subheader_item{:class => item[:slug] }
          %a.full-link{:href => item[:url], :target => "_blank"}
          = item[:title].html_safe
      .clear

    - if @shop.id == 1
      .fb_mkp_services
        - if network_services = t('mkp.variants.v5.partials.services.types')
          - free_shipping_from = Network[@network].free_shipping_from
          - network_services.each do |service|
            %div{ class: service['name'] }
              %strong>= I18n.interpolate(service['title'], from: free_shipping_from).html_safe
              %span>= service['subtitle'].html_safe
        .contacto
          %strong> Contactanos
          %span>
            %a{ href: "mailto:#{@shop.owner.email}", target: '_blank'}>= @shop.owner.email

    #variants-on-sale.variants-list-wrapper
      = render partial: 'partials/v5/variants/list', locals: { variants: @variants, list_name: "facebook-eshop-app" }
    :javascript
      (function(){
        var variants = document.getElementById("variants-on-sale");
        var links = variants.getElementsByTagName("a")
        for (var i = 0; i < links.length; i++) links[i].target = '_blank'
      })()

    .fb_mkp_footer
      %span.contact
        %strong> #{t('social.partials.footer.sitemap.contact')}:&nbsp;
        %a{ href: "mailto:#{@shop.owner.email}", target: '_blank'}>= @shop.owner.email
  :scss
    #facebook-eshop {
      position: relative;
      width: 795px;
      margin-left: auto;
      margin-right: auto;
      padding: 3px 6px;
      background-color: white !important;
      background-image: none !important;
      .main_header_wrapper {
        width: 100%;
        border-bottom: 1px solid #111;
        background: -webkit-linear-gradient(top, #4e4e4e 0%, #2a2a2a 60%, #1c1c1c 99%, #3e3e3e 100%);
        background: -moz-linear-gradient(top, #4e4e4e 0%, #2a2a2a 60%, #1c1c1c 99%, #3e3e3e 100%);
        background: -ms-linear-gradient(top, #4e4e4e 0%, #2a2a2a 60%, #1c1c1c 99%, #3e3e3e 100%);
        background: -o-linear-gradient(top, #4e4e4e 0%, #2a2a2a 60%, #1c1c1c 99%, #3e3e3e 100%);
        background: linear-gradient(top, #4e4e4e 0%, #2a2a2a 60%, #1c1c1c 99%, #3e3e3e 100%);
        border-radius: 3px 3px 0 0;
        #search-form {
          float: left;
          position: relative;
          margin: 5px 0 0 0;
          width: 300px;
          input {
            margin: 0;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) inset, 0 0 0 #000000;
            border: 1px solid rgba(0, 0, 0, 0.35);
            border-radius: 3px 3px 3px 3px;
            display: inline-block;
            font-size: 13px;
            width: 100%;
            &:-moz-placeholder { color: #999999; }
            &::-webkit-input-placeholder { color: #999999; }
            line-height: 25px;
            padding: 2px 8px;
            height: auto;
          }
        }
        .main_header {
          width: 100%;
          padding: 8px;
          a {
            color: #fff;
          }
          .logo {
            position: relative;
            float: left;
            width: 150px;
            height: 28px;
            background-size: 100%;
            background-size: contain;
            background-image: url("#{asset_path('gp-isologo.png')}");
            background-repeat: no-repeat;
            background-position: left center;
            margin-right: 16px;
            top: 1px;
          }
          #search-form {
            margin: 0;
            top: 0;
          }
          .wicon {
            position:relative;
            top: 1px;
            float: right;
            font-size: 14px;
            font-weight: 800;
            line-height: 28px;
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
            text-transform: uppercase;
            color: #fff;
            i {
              font-size:21px;
              position: absolute;
              top: -3px;
              right: 107%;
              text-decoration: none !important;
            }
          }
        }
      }
      .fb_mkp_subheader {
        border-bottom: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
        border-top: 1px solid white;
        margin-bottom: 10px;
        border-radius: 0 0 3px 3px;
        background: none repeat scroll 0 0 whitesmoke;
        .subheader_item {
          cursor: pointer;
          position: relative;
          display: inline-block;
          border-radius: 3px;
          height: 32px;
          margin: 8px 0 8px 8px;
          line-height: 32px;
          font-size: 14px;
          font-weight: bold;
          padding: 0 10px;
          text-transform: uppercase;
          color: #333;
          &:hover {
            background-color: #333;
            color: #f5f5f5;
          }
          &.sale {
            float: right;
            margin-right: 8px;
            color: #04a0e3;
            &:hover {
              background-color: #04a0e3;
              color: #f5f5f5;
            }
          }
        }
      }
      .fb_mkp_services {
        font-size: 12px;
        padding-top: 9px;
        margin-bottom: 15px;
        div {
          display: block;
          display: inline-block;
          height: 40px;
          margin-right: 3px;
          padding-left: 37px;
          background: url("#{asset_path('mkp/ecommerce-icons.png')}") no-repeat left top;
          strong {
            display: block;
          }
          a {
            color: rgb(51, 51, 51);
          }
          &.envio { background-position: -12px -12px; }
          &.cuotas { background-position: -12px -59px; }
          &.devolucion { background-position: -12px -110px; }
          &.contacto {
            padding-left: 47px;
            background-position: -7px -209px;
          }
        }
      }
      .fb_mkp_footer {
        position: relative;
        margin: 20px 0;
        padding-top: 5px;
        border-top: 1px solid #cdcdcd;
        font-size: 11px;
        color: #777;
        .contact {
          float: right;
          padding-right: 8px;
        }
      }
      .variants-list-module {
        .variant:nth-child(n+7) {
          margin-bottom: 0;
        }
      }
    }

  = yield :js

  :javascript
    fbReady(function(){
      FB.Canvas.setSize({ height: 1060 });
    })

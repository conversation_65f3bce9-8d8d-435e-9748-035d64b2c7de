!!!
%html{lang: "en", "xml:lang" => "en", xmlns: "http://www.w3.org/1999/xhtml"}
  %head
    %meta{content: "text/html; charset=UTF-8", "http-equiv" => "Content-Type"}/

  %body{onload: "number_pages"}
    .left
      %h2=t('.package_list')
      %p
        %b=t('.order_number')
        \#{@suborder.public_id}

      %p
        %h4
          %b=t('.ship_from')
        %dl
          %dd= @suborder.shop.title
          %dd #{@warehouse.address} #{@warehouse.address_2}
          %dd #{@warehouse.city} - #{@warehouse.state} - #{@warehouse.zip} - #{@warehouse.country}

      %p
        %h4
          %b=t('.ship_to')
        %dl
          %dd= @destination.full_name
          %dd #{@destination.address}, #{@destination.address_2}
          %dd #{@destination.city} - #{@destination.state} - #{@destination.zip} - #{@destination.country}


    .right
      = wicked_pdf_image_tag 'logos/isologo-dark.png'
      %p
        %b=t('.order_date')
        = I18n.l @suborder.created_at, format: :shortest
        - if @shipment.extra_info[:courier] && @shipment.extra_info[:service_name]
          %br
            %b Courier:
            = @shipment.extra_info[:courier].upcase
          %br
            %b Service:
            = @shipment.extra_info[:service_name]

    %br.clear

    %table
      %thead
        %th.item=t('.item')
        %th.properties=t('.properties')
        %th=t('.description')
        %th.qty=t('.quantity')

      %tbody
        - @shipment.items.available.each do |item|
          %tr{ class: cycle('', 'odd') }
            %td= item.product.title
            %td
              - item.variant.properties.each do |name, value|
                #{name.capitalize}:
                = (name == :color) ? value[:name] : value
            %td
              - description = item.product.description[0..130]
              = description
              = '...' if item.product.description.length > description.length
            %td= item.quantity

:css
  dd{ margin-left: 0px; }
  h4{ margin-bottom: 0px; }
  dl{ margin-top: 0px; }
  dd{ font-size: 13px; }
  td{ font-size: 12px; line-height: 1rem; padding: 0.3rem 0.35556rem;}
  th{ text-align: left; }
  table{ width: 100%; border: solid 1px #ddd; padding: 1px;}
  thead { background: #f5f5f5}
  .odd { background: #f9f9f9; }
  p { font-size: 14px; }
  .left{ float: left; }
  .right{ float: right; }
  .clear{ clear: both; }
  .item{ width: 80px; }
  .qty{ width: 40px; }
  .properties{ width: 120px; }

.row.methods
  .small-12.columns
    %label.with_explanation= t('.methods')
    .explanation= t('.method_exaplanation')
  .small-12
    - methods = load_shipping_methods
    - methods.each do |shipping_method_name|
      .columns{class: "small-#{12 / methods.size }"}
        = label_tag 'mkp_shipping_method[service]' do
          = radio_button_tag 'mkp_shipping_method[service]', shipping_method_name, @shipping_method.service == shipping_method_name, { class: 'hidden', id: shipping_method_name.downcase}
          = t(".#{shipping_method_name.downcase}", country: Network[@network].title)
.services-attrs

  .row.express-service
    .small-12.medium-4.columns
      .row
        .small-12.columns
          %label.with_explanation= t('.price')
          .explanation=t ('.price_explanation')
      .row.collapse
        .small-3.large-2.columns
          .prefix $
        .small-9.large-10.columns
          = text_field_tag 'mkp_shipping_method[price]', @shipping_method.price, placeholder: t('.price_placeholder'), class: 'required', disabled: 'disabled'


  .row.fixed-service
    .small-12.columns
      #fixed-setup
        .row
          .small-12.columns
            %label.with_explanation= t('.price')
            .explanation=t ('.price_explanation')
        .row.price
          .small-12.medium-4.columns
            .row.collapse
              .small-3.large-2.columns
                .prefix $
              .small-9.large-10.columns
                = text_field_tag 'mkp_shipping_method[price]', @shipping_method.price, placeholder: t('.price_placeholder'), class: 'required', disabled: 'disabled'
        .row.checkbox-container
          .small-12.columns
            %label
              = check_box_tag 'states_prices_checkbox', '', @shipping_method.states.present?, class: 'hidden'
              = t('.manage_price')
        .row.states
          - load_states.each do |state|
            .small-12.medium-6.columns
              .row.collapse
                .small-3.large-6.columns
                  .state= state
                .small-3.large-2.columns
                  .prefix $
                .small-6.large-4.columns
                  = text_field_tag "mkp_shipping_method[states][#{state}]", @shipping_method.states.try(:[], state) || @shipping_method.price, placeholder: t('.price_placeholder'), class: 'required', disabled: 'disabled'

  .row.easypost-service
    .small-12.columns
      .row.label
        .small-12.columns
          %label.with_explanation Available Carriers
          .explanation Choose which carriers you want to use
      .row.carriers
        - Couriers.get_list('US').each_with_index do |carrier, index|
          - carrier_id = carrier[0]
          - carrier_info = carrier[1]
          - checked =  @shipping_method.persisted? ? @shipping_method.carriers.try(:include?, carrier_id.to_s)  : index == 0
          .small-12.medium-4.columns.carrier
            - if carrier_info[:icon].present?
              .carrier-logo-container
                = image_tag carrier_info[:icon], size: '64x64'
            - name_attr = 'mkp_shipping_method[carriers][]'
            = label_tag name_attr do
              = check_box_tag name_attr, carrier_id.to_s, checked,  class: 'hidden', disabled: 'disabled'
              = t(".#{carrier_id}")
      .row.carrier-warn
        .small-12
          %p *Only the lowest cost option for each delivery speed will be displayed to the user. Ex. Seller activates both UPS and FedEx for International Express Shipping. Only the cheaper of the 2 options will be shown to the buyer at checkout.

      .row.speeds
        .small-12.columns
          .row
            .small-12.columns
              %label.with_explanation Available Services
              .explanation Select the service and its speeds that are going to be available

          .row
            .small-6.columns
              = label_tag 'Domestic' do
                = check_box_tag 'domestic', '', true, class: 'hidden'
                Domestic (Within USA only)
              .domestic-speeds
                - Gateways::Labels::Easypost::DOMESTIC_SPEEDS.each_with_index do |speed, index|
                  - checked = @shipping_method.persisted? ? @shipping_method.speeds.try(:include?, speed)  : index == 0
                  - name_attr = 'mkp_shipping_method[speeds][]'
                  = label_tag name_attr do
                    = check_box_tag name_attr, speed, checked, class: 'hidden', disabled: 'disabled'
                    = t(".#{speed}")

            .small-6.columns
              = label_tag 'International' do
                - checked = @shipping_method.persisted? ? (@shipping_method.speeds & [ 'intl_priority', 'intl_express' ]).present? : false
                = check_box_tag 'international', '', checked, class: 'hidden'
                International
              .international-speeds
                - Gateways::Labels::Easypost::INTERNATIONAL_SPEEDS.each_with_index do |speed, index|
                - checked = @shipping_method.persisted? ? @shipping_method.speeds.try(:include?, speed)  : index == 0
                  - name_attr = 'mkp_shipping_method[speeds][]'
                  = label_tag name_attr do
                    = check_box_tag name_attr, speed, checked, class: 'hidden', disabled: 'disabled'
                    = t(".#{speed}")
    .small-12.columns
      = render partial: 'customs_signer_name'

  .row.oca-service

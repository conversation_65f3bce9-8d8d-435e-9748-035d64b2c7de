- one_service_available = @shop.shipping_methods.count < 2
.row
  .large-12.columns
    %h2.thin
      = t('.existing_services')
    %hr
.row.shipping-methods-list
  .large-12.columns
    %table{ width: '100%' }
      %thead
        %tr
          %th= t('.name')
          %th= t('.warehouse')
          %th= t('.countries')
          - if @network == 'US'
            %th Carriers
            %th Speeds
          %th= t('.price')
          %th.th-actions
      %tbody
      - @shipping_methods.each do |shipping_method|
        %tr
          %td
            = shipping_method.title
          %td= shipping_method.zone.warehouse.full_name
          %td
            - countries = shipping_method.zone.countries
            = countries.any? ? ( countries.size < 6 ? countries.map{|country| GeoConstants::Countries.name_for_code(country)}.join(' - ') : t('.many', count: countries.size) ) : t('.all')
          - if @network == 'US'
            %td
              - shipping_method.carriers.try(:each) do |carrier|
                - carrier_info = Couriers.get_settings_for(carrier)
                - if carrier_info.icon.present?
                  = image_tag carrier_info.icon, size: '32x32'
            %td
              = shipping_method.speeds.try(:map) {|speed| t("lux.shipping_methods.services.#{speed}")}.try(:join, ' - ')
          %td
            = display_price(shipping_method) || t('.variable')
          %td
            - button_title = one_service_available ? t('.delete') + '*' : t('.delete')
            =  button_to button_title, { :action => "destroy", zone_id: shipping_method.zone.id, :id => shipping_method.id}, :confirm => t('.warn'), :method => :delete, class: 'button red tiny', disabled: one_service_available && true, form_class: 'delete'
            = link_to t('.edit'), edit_shop_shipping_method_path(@shop.to_param, shipping_method.to_param), class: 'button tiny'

    - if one_service_available
      %p.small= t('.last_service_warn')

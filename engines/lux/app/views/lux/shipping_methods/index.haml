.large-3.columns
  = render partial: 'lux/partials/account_submenu'
.large-9.columns

  .section-container.auto{"data-section" => ""}
    %section
      %p.title{"data-section-title" => ""}
        %a{:href => shop_warehouses_url}= t('.warehouses')

    %section{class: 'active'}
      %p.title{"data-section-title" => ""}
        %a{:href => shop_shipping_methods_url, class: 'go-to-services'}= t('.services')
      .content.row.gp-simple-panel.shipping-methods{"data-section-content" => ""}
        .row
          .large-12.columns
            %h1.thin.text-center
              = t('.manage')
            %hr
        = render partial: 'listing' if @shipping_methods.any?
        .row
          .large-12.columns
            %button.button.success.small.new-service-button
              = t('.add')
            %hr
        = render partial: 'lux/shipping_methods/form', locals: { submit_title: t('.submit'), url: shop_shipping_methods_url }

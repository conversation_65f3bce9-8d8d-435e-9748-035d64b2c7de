= form_for [@question.product.shop, @question.product, @question, @answer], :url => shop_product_question_answer_path do |f|
  -if @answer.errors.any?
    #error_explanation
      %h2= "#{pluralize(@answer.errors.count, "error")} prohibited this answer from being saved:"
      %ul
        - @answer.errors.full_messages.each do |msg|
          %li= msg

  .field
    = f.label :description
    = f.text_area :description
  .field
    = f.label :created_at
    = f.datetime_select :created_at
  .field
    = f.label :updated_at
    = f.datetime_select :updated_at
  .actions
    = f.submit 'Save'

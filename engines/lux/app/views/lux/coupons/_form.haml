- url = params[:action] == 'new' ? shop_coupons_url(@shop) : shop_coupons_url(@shop)
.coupon-form.row
  = form_for @coupon, as: 'coupon', url: url, html: {class: 'custom', id: 'coupon-form'} do |f|
    %input{type: 'hidden', name: 'coupon[shop_id]', value: @shop.id}
    .small-12.columns
      .row
        .field.large-6.medium-8.small-12.columns
          = f.text_field :code, :placeholder => t('.code_placeholder'), :class => 'required', explanation: t('.code_explanation'), label_options: { class: 'with_explanation' }, autofocus: 'true', label: t('.code')
      .field
        = f.text_area :description, :placeholder => t('.description_explanation'), :class => 'required', :rows => 5, label: t('.description')

      %hr

      .row
        .field.medium-4.small-12.columns
          = f.select :policy, options_for_select([[t('.value'), :value], [t('.percent_policy'),:percent]]), {:prompt => false, label: t('.policy')}, { "data-placeholder" => 'Select one'}
        .field.medium-4.small-12.columns
          .amount
            = f.text_field :amount, label: t('.amount'), :class => 'required', placeholder: '$ 0.00'
          .percent
            = f.text_field :percent, label: t('.percent'), :placeholder  => '0 %'
        .field.medium-4.small-12.columns
          = f.text_field  :minimum_value, :class => 'required', label: t('.minimum_value'), :placeholder  => '$ 0.00'
      .row
        .field.medium-4.small-12.columns
          = f.text_field :starts_at , id: 'starts_at',  label: t('.starts_at'), class: 'required', placeholder: t('.click_here')
        .field.medium-4.small-12.columns
          = f.text_field :expires_at, id: 'expires_at', label: t('.expires_at'), class: 'required', placeholder: t('.click_here')
        .field.medium-4.small-12.columns
          = f.text_field  :total_available, :class => 'required', label: t('.total_available'), :placeholder  => '0'

      %hr

      .row.restrictions
        %label.small-12.columns= t('.restrictions')
        .field.medium-4.small-12.columns
          %label.restriction-label
            = radio_button_tag :categories_restriction_action, 'included_categories', false, style: 'visibility: hidden;'
            = t('.included_categories')
          %label.restriction-label
            = radio_button_tag :categories_restriction_action, 'excluded_categories', false, style: 'visibility: hidden;'
            = t('.excluded_categories')
            = select_tag('coupon[restrictions][included_categories]',
                         categories_options_for_select(@restrictions[:categories]),
                         multiple: true,
                         id: 'categories-restriction')

        .field.medium-4.small-12.columns
          %label.restriction-label
            = radio_button_tag :manufacturers_restriction_action, 'included_manufacturers', false, style: 'visibility: hidden;'
            = t('.included_categories')
          %label.restriction-label
            = radio_button_tag :manufacturers_restriction_action, 'excluded_manufacturers', false, style: 'visibility: hidden;'
            = t('.excluded_manufacturers')
            = select_tag('coupon[restrictions][included_manufacturers]',
                         options_from_collection_for_select(@restrictions[:manufacturers], 'id', 'name'),
                         multiple: true,
                         id: 'manufacturers-restriction')

        .field.medium-4.small-12.columns
          %label.restriction-label
            = radio_button_tag :users_restriction_action, 'included_users', false, style: 'visibility: hidden;'
            = t('.included_users')
          %label.restriction-label
            = radio_button_tag :users_restriction_action, 'excluded_users', false, style: 'visibility: hidden;'
            = t('.excluded_users')
            = hidden_field_tag('coupon[restrictions][included_users]',
                               nil,
                               id: 'users-restriction',
                               'data-restriction_users_url' => ajax_users_path)

        .field.medium-4.small-12.columns
      %br
      .row
        .field.small-12.columns.apply-on-sale
          = f.check_box :apply_on_sale, class: 'hidden', label: t('.apply_on_sale')

    .large-12.columns
      %hr
      .coupon-actions
        %a.cancel.secondary.button{ href: shop_coupons_url}= t('.cancel')
        %button#save.button.red.secondary= t('.save')

.large-12.medium-6.small-12.columns
  .gp-simple-panel.row
    .small-12.medium-8.columns
      %h4.ellipsis
        = t('.code')
        %a{href: shop_coupon_url(@shop, coupon.to_param)} #{coupon.code}
      .coupon-description.subheader
        - if coupon.policy == 'value'
          .item #{t('.value')}: #{number_to_currency coupon.amount}
        -else
          .item #{t('.percent')}: #{coupon.percent}%
        .item #{t('.ends')}: #{coupon.display_expires_at}
        .item #{t('.used')}: #{coupon.used}
    .small-12.medium-4.columns
      .status{class: coupon.status}
        = t("lux.coupons.status.#{coupon.status}")
    .actions.large-12.columns
      = link_to t('.view'), shop_coupon_url(@shop, coupon.to_param), class: 'button tiny'
      = link_to t('clone'), clone_shop_coupon_url(@shop, coupon.to_param), class: 'button tiny'
      - unless coupon.deleted_at
        = button_to t('.delete'), shop_coupon_url(@shop, coupon.to_param), method: :delete, class: 'button tiny', form_class: 'delete-form'

= content_for :title do
  Manage Shop - Coupon

.medium-12.columns
  %h3.title= t('.coupon_details')
  .gp-simple-panel.row
    .row
      .small-12.medium-8.columns
        .id #{t('.code')} #{@coupon.code}
        - if @coupon.policy == 'value'
          #{t('.discount')}: #{number_to_currency @coupon.amount, precision: 2}
        - else
          #{t('.discount')}: #{@coupon.percent}%
        .order-id #{t('.description')}: #{@coupon.description}
        .subheader
          .created_at #{t('.created_at')}: #{@coupon.created_at.strftime("%d-%m-%y")}
          .starts_at #{t('.starts_at')}: #{@coupon.starts_at.strftime("%d-%m-%y")}
          .expires_at #{t('.expires_at')}: #{@coupon.expires_at.strftime("%d-%m-%y")}
          .deleted_at= "#{t('.deleted_at')}: #{@coupon.deleted_at.strftime("%d-%m-%y")}" if @coupon.deleted_at
          .minimum-value #{t('.minimum_value')} #{number_to_currency @coupon.minimum_value, precision: 2}
          .total-available #{t('.total_available')} #{@coupon.total_available}
          .used #{t('.used')} #{@coupon.used}
      .small-12.medium-4.columns
        .status{class: @coupon.status}= t("lux.coupons.status.#{@coupon.status}")
    %hr
    .links
      %a.back{href: shop_coupons_url(@shop)}= t('.back')


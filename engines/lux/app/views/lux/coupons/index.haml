= content_for :title do
  = t('.head_title')

.small-12.columns
  .row#actions-section
    .search.small-6.medium-7.columns
      = form_tag shop_coupons_path(policy: @policy), method: :get do
        = text_field_tag :name, @name, placeholder: t('.search')
    .general-actions.small-6.medium-5.columns
      %a{ class: 'button small red', href: new_shop_coupon_url }= t('.new_coupon')
.small-12.columns
  .general-filters.small-12.columns
    %dl.sub-nav
      %dt Filter:
      %dd{class: @policy.blank? && 'active'}
        %a{href: shop_coupons_path(name: @name)}= t('.all')
      %dd{class: @policy == 'value'   && 'active'}
        %a{href: shop_coupons_path(policy: 'value', name: @name)}= t('.value')
      %dd{class: @policy == 'percent' && 'active'}
        %a{href: shop_coupons_path(policy: 'percent', name: @name)}= t('.percent')
  = render partial: '/lux/coupons/list', locals: {coupons: @coupons}

module Lux
  class ProductImportForm
    extend ActiveModel::Naming
    extend ActiveModel::Callbacks

    include ActiveModel::Validations
    include ActiveModel::Conversion
    include Paperclip::Glue

    define_model_callbacks :save, only: [:after]
    define_model_callbacks :commit, only: [:after]
    define_model_callbacks :destroy, only: [:before, :after]

    attr_accessor :product_list, :product_list_file_name, :token, :strategy

    has_attached_file :product_list,
                      storage: :filesystem,
                      path: ':rails_root/public/system/:attachment/:token.:extension'

    validates_attachment_presence :product_list
    do_not_validate_attachment_file_type :product_list

    def initialize(attributes = {})
      attributes ||= {}
      self.token = generate_token
      self.product_list = attributes[:product_list]
      self.strategy = attributes[:strategy]
    end

    def destroy
      run_callbacks(:destroy) {}
    end

    def persisted?
      false
    end

    def save
      if valid?
        run_callbacks(:save) {}
        true
      else
        false
      end
    end

    private

    def generate_token
      token ||= Digest::MD5.hexdigest("#{rand.to_s}#{Time.now.to_i.to_s}")
    end
  end
end

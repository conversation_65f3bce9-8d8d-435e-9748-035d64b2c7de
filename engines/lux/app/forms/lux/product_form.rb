module Lux
  class ProductForm
    attr_reader :product

    AVENIDA_EMAIL = '<EMAIL>'.freeze
    BNA_MIMOTO_ID = 43

    def initialize(shop, product_id, product_attributes, variants_attributes, current_user)
      @shop = shop
      @current_user = current_user
      @product_attributes = product_attributes
      @product_id = product_id
      get_proper_variants(variants_attributes)
      @make_all_colors_visible = product_attributes[:display_variants]
    end

    def save
      symbolize_available_properties!
      persisted? ? assign_product_attrs : build_product
      begin
        @product.save!
        persisted? ? update_variants_for_product : create_variants_for_product
        @product.reload.recreate_variants_visibility

        true
      rescue ActiveRecord::RecordInvalid => e
        handle_error(e)
      end
    end

    private

    def handle_error(message)
      e = "Lux::Product::SaveError: Product: #{@product.inspect}. The error was: #{message.inspect}"
      Rails.logger.info(e)
      false
    end

    def bna_provider?
      return false if @current_user.shops.count > 1

      bna_store = Mkp::Store.find(BNA_MIMOTO_ID)
      @current_user.shops.map(&:stores).reduce([], :concat).include?(bna_store)
    end

    def get_proper_variants(variants_attributes)
      Rails.logger.info('********** BNA ***********')
      Rails.logger.info("> BNA SHOP: #{@shop.id}")
      Rails.logger.info("> BNA IS PROVIDER?: #{bna_provider?}")
      if bna_provider?
        @variants_attributes = variants_attributes.map do |each|
          { 'id' => each['id'], 'quantity' => each['quantity'] }
        end
      else
        @variants_attributes = variants_attributes
      end
    end

    def assign_product_attrs
      @product = @shop.products.find_by(id: @product_id)
      @product = @shop.products.find_by(slug: @product_id) if @product.nil?
      @product_attributes.each(&method(:handler_attr)) unless bna_provider?
    end

    def build_product
      return if bna_provider?

      @product = @shop.products.build
      @product_attributes.each(&method(:handler_attr))
    end

    def handler_attr(key, value)
      value = set_or_destroy_voucher(key, value)
      value = {} if %w[data_shipment data].include?(key) && value == []
      return unless value

      @product.send("#{key}=", value)
    end

    def set_or_destroy_voucher(key, value)
      return value unless key == 'voucher_attributes'
      return value.to_hash if valid_key_value?(key, value)

      @product.voucher && { id: @product.voucher.id, _destroy: true } || false
    end

    def valid_key_value?(key, value)
      key == 'voucher_attributes' && value.present? && value.dig(:supplier).present?
    end

    def create_variants_for_product
      return if @variants_attributes.blank?

      attributes = add_no_property(@variants_attributes)
      attributes.each do |att|
        variants_attributes = att.deep_symbolize_keys
        variants_attributes[:properties] = remove_white_spaces(variants_attributes[:properties])
        product.variants.create(variants_attributes)
      end
    end

    def persisted?
      @product_id.present?
    end

    def symbolize_available_properties!
      if @product_attributes['available_properties'].present?
        @product_attributes['available_properties'] = @product_attributes['available_properties'].map!(&:to_sym)
      else
        @product_attributes['available_properties'] = [:noproperty]
      end
    end

    def update_variants_bna
      @variants_attributes.each do |attrs|
        variant = product.variants.detect { |each| each.id == attrs['id'].to_i }
        variant&.update!(quantity: attrs['quantity'].to_i)
      end
    end

    def update_variants_for_product
      return if @variants_attributes.blank?

      return update_variants_bna if bna_provider?

      Rails.logger.info(@variants_attributes)
      variant_ids_updated = @variants_attributes.map { |attrs| attrs[:id].to_i }
      deleted_variant_ids = @product.variant_ids - variant_ids_updated

      @product.variants.where(id: deleted_variant_ids).destroy_all if deleted_variant_ids.present?

      out_of_stock_variants = []
      attributes = add_no_property(@variants_attributes)
      attributes.each do |variant_attributes|
        variant_attributes = variant_attributes.deep_symbolize_keys

        if variant_attributes.key?(:properties)
          variant_attributes[:properties] = remove_white_spaces(variant_attributes[:properties])
        end

        if variant_attributes[:id].present?
          variant = @product.variants.find(variant_attributes[:id])

          if variant_attributes[:quantity].to_i <= 0
            out_of_stock_variants << variant_attributes[:id].to_i
          end

          variant_attributes.each do |k, v|
            variant.send("#{k}=", v)
          end

          variant.save
        else
          variant = @product.variants.build
          variant_attributes.each do |k, v|
            variant.send("#{k}=", v)
          end

          variant.save
          siblings = @product.available_siblings_for_variant(variant)
          sibling_with_picture_id = siblings.detect do |sibling|
            sibling[:picture_id].present?
          end
          if sibling_with_picture_id
            variant[:picture_id] = sibling_with_picture_id[:picture_id]
            variant.save!
          end
        end
      end
      return if out_of_stock_variants.blank?

      Mkp::Integration::RemoteSyncWorker.perform_async(@product.id, out_of_stock_variants)
    end

    def remove_white_spaces(properties)
      properties.each do |key, value|
        properties[key] = value.strip unless key == :color
      end
    end

    def add_no_property(properties)
      # AWFUL AND TERRIBLE FIX!!!
      if properties.size == 1 && properties.first[:properties].nil?
        properties.first.tap do |hash|
          hash[:properties] = { noproperty: 'true' }
        end
        return properties
      end
      properties
    end
  end
end

module Lux
  class IntegrationProductImportWorker
    include Sidekiq::Worker

    sidekiq_options retry: false

    def perform(integration_model_name, integration_id, shop_id)
      date = Date.today.strftime("%Y%m%d")
      @import_log ||= Logger.new("#{Rails.root}/log/ImportProducts-#{date}.log")
      @import_log.info("@#{'=' * 80}@")
      @import_log.info("SHOP_ID: #{shop_id}")
      @import_log.info("INTEGRATION_ID: #{integration_id}")
      @import_log.info("MODEL_NAME: #{integration_model_name}")
      error_class = error_message = error_backtrace = '---'
      begin
        integration_model = integration_model_name.constantize
        integration = integration_model.find(integration_id)
        integration.import_and_sync
      rescue => e
        error_class = e.class
        error_message = e.message
        error_backtrace = e.backtrace
      end
      @import_log.info("ERROR_CLASS: #{error_class}")
      @import_log.info("ERROR_MESSAGE: #{error_message}")
      @import_log.info("ERROR_BACKTRACE: #{error_backtrace}")
      @import_log.info("@#{'=' * 80}@ \n")
      true
    end
  end
end

module Lux
  class AttendNotificationCallbacksWorker
    include Sidekiq::Worker

    sidekiq_options retry: false

    def perform
      attended_notifications = []
      NotificationCallback.all.each do |notification_callback|
        shop = notification_callback.shop
        integration = ("Mkp::Integration::#{notification_callback.integration_name}".constantize)
                        .for(shop)
        external_product = integration.integration_product_model
                                      .find([notification_callback.external_id])
                                      .first
        if integration.integrated_product_exists?(external_product)
          offer = integration.get_offer_info(external_product.external_id)
          integration.update_related_product(external_product, offer)
          integration.sync_variants(external_product)
        end

        attended_notifications << notification_callback
      end
      NotificationCallback.delete(attended_notifications)
    end
  end
end

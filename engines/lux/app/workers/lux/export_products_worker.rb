module Lux
  class ExportProductsWorker
    include Sidekiq::Worker
    sidekiq_options queue: :critical, retry: false

    HEADER = ['slug',
              'ID',
              'SKU Externo',
              'SKU Avenida',
              'EAN_CODE',
              'Valor en puntos',
              'Tope de descuento',
              'Titulo',
              'Descripcion',
              'Categoria',
              'Directorio de categoria',
              'Tipo de producto',
              'Nombre de propiedad 1',
              'Valor de propiedad 1',
              'Nombre de propiedad 2',
              'Valor de propiedad 2',
              'Nombre de propiedad 3',
              'Valor de propiedad 3',
              'Fabricante',
              'Precio',
              'Precio Sin Impuestos',
              'Precio de oferta',
              'Precio de oferta Sin Impuestos',
              'Fecha comienzo de oferta',
              'Fecha fin de oferta',
              'Fecha de disponibilidad',
              'Alto',
              'Largo',
              'Ancho',
              'Unidad de medida',
              'Peso',
              'Unidad de masa',
              'Iva',
              'Costo',
              'Stock',
              'Visibildiad de variante',
              'Visibilidad del proveedor',
              'Titulo del proveedor',
              'Fecha de creacion',
              'Cantidad venidad',
              'Imagenes'
            ]

    def perform(products, store_id = nil)
      store = store_id.present? ? Mkp::Store.find(store_id) : nil
      CSV.generate do |csv|
        csv << build_header(store)
        variants = Mkp::Variant.where(product_id: products).includes(:shop, :costs).includes(:product => [:packages, :category]).order('mkp_variants.product_id desc')
        @lists_manufacturer = Mkp::Manufacturer.pluck(:id, :name).to_h
        variants.each_with_index do |variant, index|
          if index == 0 || variant.product_id != variants[index - 1].product_id
            @product = variant.product
            @dimensions = {
                width: @product.packages.to_a.sum(&:width),
                length: @product.packages.to_a.sum(&:length),
                height: @product.packages.to_a.sum(&:height),
                weight: @product.packages.to_a.sum(&:weight)
            }
          end
          csv << build_content_row(variant, store)
        end
      end
    end

    private

    def build_header(store)
      if store.present?
        store.product_approval ? HEADER << 'Approval status' : HEADER
      else
        HEADER
      end
    end

    def build_content_row(variant, store)
      row = [
          @product.slug,
          @product.id,
          sanatize(variant.sku),
          variant.gp_sku,
          variant.try(:ean_code),
          variant.try(:points_price),
          variant.try(:discount_top),
          sanatize(@product.title).gsub(/[\r\n]?/, ''),
          sanatize(@product.description.gsub(/[\r\n]?/, '').tr('"', "'")),
          sanatize(@product.category.try(:name)),
          sanatize(@product.category.try(:full_path)),
          sanatize(@product.try(:transaction_type)),
          variant.property_name(1),
          sanatize(variant.property_value(1)),
          variant.property_name(2),
          parse_number(variant.property_value(2)),
          variant.property_name(3),
          parse_number(variant.property_value(3)),
          @lists_manufacturer[@product.manufacturer_id],
          @product.regular_price.to_f,
          @product.regular_price_without_taxes.to_f,
          @product.sale_price.try(:to_f),
          @product.sale_price_without_taxes.try(:to_f),
          @product.sale_on.try(:strftime, '%d/%m/%Y'),
          @product.sale_until.try(:strftime, '%d/%m/%Y'),
          @product.available_on.try(:strftime, '%d/%m/%Y'),
          data_or_dd(@dimensions[:height]),
          data_or_dd(@dimensions[:length]),
          data_or_dd(@dimensions[:width]),
          data_or_dd(@product.length_unit),
          data_or_dd(@dimensions[:weight]),
          data_or_dd(@product.mass_unit),
          @product.iva,
          variant.current_cost.to_f,
          variant.quantity,
          @product.available? && variant.quantity > 0 ? "visible" : "no visible",
          variant.shop.visible? ? "visible" : "--",
          data_or_dd(variant.shop.title.to_s),
          variant.created_at.strftime('%d-%m-%Y'),
          @product.sold_count,
          @product.pictures.count
      ]

      if store.present?
        if store.product_approval
          product_store = store.product_stores.find_by(product: @product)
          status = product_store.present? ? product_store.status : ' - '
          row << status
        end
      end

      row
    end

    def sanatize(field)
      field.gsub(/[^A-Za-z0-9_.\- ]/, '') if field.present?
    end

    def parse_number(number)
      number.tr(',', '.') if number.present?
    end

    def data_or_dd(data)
      data.blank? || data == 0 ? '--' : data
    end

  end
end

#Headers en ingles
=begin
'slug',
'ID',
'SKU',
'AV SKU',
'EAN_CODE',
'Points Price',
'Discount Top',
'Title',
'Description',
'Category',
'Category Full Path',
'Product Type',
'Property Name 1',
'Property Value 1',
'Property Name 2',
'Property Value 2',
'Property Name 3',
'Property Value 3',
'Manufacturer',
'Price',
'Price Without Taxes',
'Sale Price',
'Sale Price Without Taxes',
'Sale start date',
'Sale end date',
'Available On',
'Height',
'Length',
'Width',
'Length Unit',
'Weight',
'Mass Unit',
'Iva',
'Current Cost',
'Stock',
'Variants Visibility',
'Shop Visibility',
'Shop Title',
'Created At',
'Sold Quantity',
'Images'
=end

module Lux
  class Product < Mkp::Product
    def self.clone_from(product)
      new_product = product.dup
      new_product.category         = product.category
      new_product.sports           = product.sports
      new_product.genders          = product.genders
      new_product.manufacturer     = product.manufacturer
      new_product.regular_price    = product.regular_price
      new_product.sale_price       = product.sale_price
      new_product.sale_on          = product.sale_on
      new_product.sale_until       = product.sale_until
      new_product.display_variants = product.display_variants
      new_product.handle_stock     = product.handle_stock
      new_product.variants         = product.variants

      new_product
    end

    # Due to a decimal column issue:
    # e.g. AR returns same number but with different object id
    def changed?
      super &&
        !(changes.size == 1 &&
          regular_price_changed? &&
          regular_price_change.map(&:to_f).uniq.size < 2)
    end
  end
end

module Lux
  class IntegrationsController < Lux::ApplicationController
    force_ssl if SSL_ENABLED

    include IntegrationsLoaders

    before_filter :load_available_integrations

    helper_method :form_parameter

    def index
      @integrations ||= @available_integrations.each_with_object({}) do |integration, hash|
        hash[integration.downcase.to_sym] = get_integration(integration)
      end
      @categories = Mkp::Category.active.by_network(@network)
      @sports = Sport.all
    end

    def create
      env_auth = session.delete('omniauth.auth')
      integration = get_integration(integration_name)

      if integration.blank? || env_auth.blank? || env_auth['provider'] != params[:integration_name]
        redirect_to shop_integrations_path and return
      end

      if integration.save_authorization(params, env_auth)
        session.delete(params[:integration_name])
      end

      if integration.errors.present?
        flash[:alert] = integration.errors.full_messages.join(' ')
      end

      redirect_to shop_integrations_path
    end

    def authorize
      unless (integration = get_integration(integration_name)).present?
        redirect_to :back and return
      end

      integration.set_client
      redirect_to integration.client.authenticate_url
    rescue ActiveRecord::RecordInvalid => e
      flash[:alert] = e.message
      redirect_to :back
    end

    def complete
      @shop = Mkp::Shop.find params[:shop_id]
      integration = get_integration(params[:integration_name])

      integration.save_authorization(params[:code])
      integration.save_user_data

      flash[:success] = 'Integración autorizada con éxito'
      redirect_to shop_integrations_path(nil, @shop.id)
    end

    def destroy
      integration = @shop.integrations.find(params[:id])
      option = integration_params[:remove_option].to_sym

      if integration.present? &&
         integration.can_be_deleted? &&
         can_manage_integration?(integration) &&
         Mkp::Integration::Base::AFTER_REMOVE_OPTIONS.include?(option)

        if option == :nothing
          integration.destroy
        else
          integration.deleting
          IntegrationDeleteDependencyWorker.perform_async(integration.id, option)
        end

        message = t("lux.controllers.integrations.destroy.#{option}.success") + \
                  t("lux.controllers.integrations.destroy.integration_message",
                    integration_name: integration.type.demodulize)

        flash[:notice] = message

      else
        flash[:alert] = t('lux.controllers.integrations.destroy.trouble')
      end
      redirect_to shop_integrations_path
    rescue ActiveRecord::RecordNotFound => e
      flash[:alert] = e.message
      redirect_to :back
    end

    def update
      integration = @shop.integrations.find(params[:id])

      if integration.need_custom_configuration? && integration.can_change_configuration?
        integration.handle_custom_configuration(integration_params[:custom_configuration])
      end

      integration.settings['defaults'] = integration_params[:settings][:defaults]
      integration.save!

      redirect_to shop_integrations_url
    end

    protected

    def form_parameter(object)
      object.class.to_s.parameterize('_')
    end

    private

    def integration_name
      params[:integration_name].dup
    end

    def can_manage_integration?(integration)
      integration.shop_id == @shop.id
    end

    def integration_params
      params.require(:integration).permit(:remove_option, settings: [ defaults: [:category_id, sport_ids: []]]).tap do |allowed_params|
        allowed_params[:custom_configuration] = params[:integration][:custom_configuration]
      end
    end
  end
end

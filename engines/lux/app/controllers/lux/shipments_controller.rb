

module Lux
  class ShipmentsController < Lux::ApplicationController
    def show

      label = Mkp::ShipmentLabel.find(params[:id])
      @shipment = label.shipment
      @store = @shipment.order.store
      gateway = @shipment.label.try(:gateway) || 'Lion'
      label_with_gateway = "Gateways::Labels::#{gateway}".constantize.new(shipment: @shipment)
      @label = label_with_gateway.by_pdf(@shipment.label)
      @image = label_with_gateway.image_code(@label.barcode)
      respond_to do |format|
        format.pdf do
          render pdf: "show"
        end
      end
    end
  end
end

module Lux
  class ProductsController < Lux::ApplicationController # rubocop:disable Metrics/ClassLength
    protect_from_forgery with: :exception

    before_filter :find_product, only: %i[show edit destroy reset_properties]
    before_filter :set_data_param, only: :update

    def index
      # return redirect_to shop_suborders_path(shop_id: @shop.id) if current_user.office.present?
      respond_to do |format|
        format.html do
          load_format
        end
      end
    end

    def show
      load_genders
    end

    def new
      @product = @shop.products.build(handle_stock: true)
      @product.packages.build
      @product.build_voucher

      load_properties(false)
    end

    def edit
      @product.build_voucher if @product.voucher.blank?
      load_properties(false)
    end

    def create
      product_form = ProductForm.new(@shop, *params.values_at(:id, :product, :variants), current_user)
      if product_form.save
        product_form.product.set_approved_for_stores
        redirect_to_product(product_form.product, t('lux.controllers.products.created_success'))
      else
        @variants_not_saved = params[:variants]

        load_properties(true)
        render 'new'
      end
      product_form.product.save
    end

    def update # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      product_form = ProductForm.new(@shop, *params.values_at(:id, :product, :variants), current_user)
      if product_form.save
        delete_pictures
        product_form.product.set_approved_for_stores(true)

        if request.xhr?
          render nothing: true
        else
          redirect_to_product(product_form.product, t('lux.controllers.products.created_success'))
        end
      else
        @product = product_form.product
        @variants_not_saved = params[:variants]
        load_properties(true)
        render :edit
      end
      product_form.product.save
    end

    def destroy
      @product.destroy

      if params[:return_to].present?
        redirect_to ERB::Util.html_escape(params[:return_to])
      else
        redirect_to shop_products_url(@shop)
      end
    end

    def export_data
      export = @shop.exports.create!(owner: 1)
      ExportProductsHandlerWorker.perform_async(@shop.products.map(&:id), export.id)

      flash[:success] = 'Se esta generando el export, en unos minutos podras visualizarlo en \'Listado de Exportaciones\''

      redirect_to shop_products_path(shop_id: @shop.id)
    end

    def exports_list # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      @exports = @shop.exports
                     .where(owner: 1)
                     .where('created_at > ?', 3.days.ago)
                     .order('created_at DESC')
    end

    def download_export # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      export = Mkp::Export.find_by(id: params[:export_id])
      file = open(export.csv_file.url)
      send_data file.read,
                filename: export.title,
                type: 'text/csv',
                disposition: 'attachment',
                stream: 'true',
                buffer_size: '4096'
    end

    def clone
      original_product = Mkp::Product.find(params[:id])
      @product = clone_product(original_product)

      @variants_not_saved = @product.variants
      load_available_properties
      load_available_colors
      load_genders
      load_manufacturers
      load_transaction_types
      render 'new'
    end

    def change_visibility # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      product_ids = []
      product = Mkp::Product.find(params[:id])
      product_ids << product.id
      service = Lux::ChangeVisibility.new(product_ids: product_ids, visibility: params[:visibility])
      results = service.perform
      results.each do |result|
        if result.success?
          notice = result.payload[:notice]
          flash[:notice] = notice
        else
          error = result.payload[:error]
          flash[:error] = error
        end
      end
      redirect_to shop_products_url('ar', product.shop)
    end

    def reset_properties
      @product.variants.destroy_all
      @product.update(available_properties: [])

      render nothing: true, status: 200, content_type: 'text/html'
    end

    def custom_properties # rubocop:disable Metrics/AbcSize
      @product = Mkp::Product.find(params[:id])
      redirect_to shop_products_url(@product.shop) unless @product.has_custom_properties?

      params[:return_to] = shop_product_url(
        @product.shop.network.downcase,
        @product.shop.to_param,
        @product.to_param
      )
      load_properties(false)
    end

    def save_properties
      product = Mkp::Product.find(params[:id])

      if params[:product].present?
        params[:product].each do |key, values|
          update_variants(product, key, values)
        end
      end

      flash[:success] = 'Variants were successfully updated'
      redirect_to_product(product)
    end

    def duplicate_property
      product = Mkp::Product.find(params[:id])
      @properties = product.available_properties
      @number = params[:number]
      render :duplicate_property, layout: false
    end

    def enable_all
      service = Lux::ChangeVisibility.new(product_ids: params[:product_ids], visibility: "show")
      results = service.perform
      errors = results.map{|result| result.payload[:error] if result.success? == false}.compact
      success = results.map{|result| result.payload[:notice] if result.success? == true}.compact
      flash[:notice] = success
      flash[:error] = errors
      respond_to do |format|
        format.json {render json: {ok: true}}
      end
    end

    def disabled_all
      service =  Lux::ChangeVisibility.new(product_ids: params[:product_ids], visibility: "hide")
      results = service.perform
      errors = results.map{|result| result.payload[:error] if result.success? == false}.compact
      success = results.map{|result| result.payload[:notice] if result.success? == true}.compact
      flash[:notice] = success
      flash[:error] = errors
      respond_to do |format|
        format.json {render json: {ok: true}}
      end
    end

    private

    def update_variants(product, id, values)
      variant = Mkp::Variant.where(id: id, product_id: product.id).first
      if variant.nil?
        variant = Mkp::Variant.create!(values.merge({ product_id: product.id }))
      else
        variant.update(values)
        variant.save
      end

      variant.properties = values[:properties].symbolize_keys
      variant.save
    end

    def load_format
      load_sports
      load_categories
      load_manufacturers
      load_transaction_types
    end

    def load_properties(error)
      load_available_properties
      load_available_colors
      load_product_json
      load_genders
      load_manufacturers
      load_transaction_types
      flash[:error] = @product.errors.full_messages.to_sentence if error
    end

    def find_product
      @product = @shop.products.find_by(id: params[:id])
      @product = @shop.products.find_by(slug: params[:id]) if @product.nil?
    end

    def update_visibility_attributes(product, visibility)
      product.external_object.update(display: visibility)

      product.variants.flat_map(&:external_objects).each do |external_object|
        external_object.update(display: visibility)
      end
    end

    def delete_pictures
      deleted_picture_ids = params[:deleted_picture_ids]
      return unless deleted_picture_ids.present?

      Mkp::Attachment::ProductPicture.destroy_all(id: deleted_picture_ids)
    end

    def clone_product(product)
      Product.clone_from(product)
    end

    def load_sports
      @sports = Sport.select(%i[id name ancestry])
    end

    def load_categories
      @categories = Mkp::Category.active.by_network(@shop.network).select(%i[id name ancestry])
    end

    def load_manufacturers
      @manufacturers = Mkp::Manufacturer.all
    end

    def load_transaction_types
      @transaction_types = Mkp::Product.transaction_types.collect { |t| [t[0].camelize, t[0]] }
    end

    def load_genders
      @genders = Mkp::Gender.roots_in_network(@shop.network)
    end

    def redirect_to_product(product, notice = nil)
      url = shop_product_url(
        product.shop.network.downcase,
        product.shop.to_param,
        product.to_param,
        return_to: params[:return_to]
      )

      redirect_to url, notice: notice
    end

    def load_product_json
      @product_json = Rabl.render(
        @product, 'products/show', view_path: 'app/views/api/mkp', format: :hash
      )
    end

    def load_available_properties
      @available_properties = Mkp::Product::AVAILABLE_PROPERTIES.map(&:to_s)
    end

    def load_available_colors
      @available_colors = Mkp::ProductColors.all(@shop.network)
    end

    def set_data_param
      params[:product][:data] ||= []
      params[:product][:data_shipment] ||= []
    end
  end
end

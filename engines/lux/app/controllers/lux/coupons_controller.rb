module Lux
  class CouponsController < Lux::ApplicationController
    def index
      @coupons = @shop.coupons.includes(:shop).order(created_at: :desc).paginate(page: params[:page])

      filter_by_policy
      filter_by_query
    end

    def new
      @coupon = @shop.coupons.new
      load_restrictions
    end

    def clone
      coupon = Mkp::Coupon.where(id: params[:id]).select([:starts_at,
                                                          :expires_at,
                                                          :policy,
                                                          :amount,
                                                          :percent,
                                                          :minimum_value,
                                                          :total_available,
                                                          :code,
                                                          :description]).last
      @coupon = @shop.coupons.new(coupon.attributes)
      load_restrictions

      render template: '/lux/coupons/new'
    end

    def create
      @coupon = Mkp::Coupon::Shop.new params[:coupon]
      _remove_coupons_with_same_code(@coupon.code) if @coupon.valid?
      if @coupon.save
        redirect_to shop_coupons_url(@shop)
      else
        load_restrictions
        render 'new'
      end
    end

    def destroy
      coupon = @shop.coupons.find(params[:id])
      coupon.destroy

      redirect_to shop_coupons_url(@shop)
    end

    def show
      @coupon = @shop.coupons.find(params[:id])
    end

    private

    def filter_by_policy
      if (@policy = params[:policy]).present?
         @coupons = @coupons.where(policy: @policy)
      end
    end

    def filter_by_query
      if (@name = params[:name]).present?
        query = "%" + @name.strip + "%"
        @coupons = @coupons.where('description LIKE ? OR code LIKE ?', query, query)
      end
    end

    def load_restrictions
      @restrictions = {
        categories: Mkp::Category.active.by_network(@shop.network),
        manufacturers: Mkp::Manufacturer.all
      }
    end

    def _remove_coupons_with_same_code(code)
      coupons = @shop.coupons.where(code: code)
      coupons.each(&:destroy)
    end
  end
end

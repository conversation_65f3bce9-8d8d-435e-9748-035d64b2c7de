module Lux
  class UnpublishedPicturesController < Lux::ApplicationController

    before_action :set_view_order

    def create
      # This is a published image now
      @unpublished_picture =
        ::Mkp::Attachment::ProductPicture.new(product_picture_params)
      @unpublished_picture.processing = false

      if @unpublished_picture.save
        render json: { picture: { id: @unpublished_picture.id } },
               status: :ok
      else
        render json: { errors: @unpublished_picture.errors.full_messages },
               status: :error
      end
    end

    private

    def product_picture_params
      params.require(:unpublished_picture).permit(:photo)
    end

    def set_view_order
      params.tap do |p|
        if (product_id = params[:product_id]).present?
          order = Mkp::Attachment::Picture.where(product_id: product_id).count
          p[:unpublished_picture][:product_id] = params[:product_id]
          p[:unpublished_picture][:view_order] = order
        end
      end
    end
  end
end

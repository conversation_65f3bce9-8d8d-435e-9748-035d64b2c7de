module Lux
  class TaxesController < Lux::ApplicationController
    def index
      @tax_rates = @shop.tax_rates
      @us_states = Lux::TaxesHelper.us_states(@tax_rates)
    end

    def update
      tax_rates = if params[:tax_rates].present?
        Lux::Tax.tax_rates_hash(params[:tax_rates])
      else
        nil
      end

      @shop.setting.update_attribute(:tax_rates, tax_rates)
      flash[:notice] = 'Your tax rates were saved successfully'
      redirect_to shop_taxes_path
    end
  end
end

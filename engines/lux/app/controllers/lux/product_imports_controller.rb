module Lux
  class ProductImportsController < Lux::ApplicationController
    def create
      @product_import_form = ProductImportForm.new(params[:product_import])
      if @product_import_form.save
        product_list_path = @product_import_form.product_list.path
        worker_id = ProductImportWorker.perform_async(product_list_path, @shop.id)
        redirect_to progress_shop_product_import_path(id: worker_id, strategy: params[:product_import][:strategy])
      else
        render 'new'
      end
    end

    def new
      @product_import_form = ProductImportForm.new({strategy: params[:strategy]})
      @sports = Sport.where('ancestry IS NOT NULL').pluck(:name)
      @genders = Mkp::Gender.where('ancestry IS NOT NULL AND network = ?', params[:network]).pluck(:name)
      @manufacturers = Mkp::Manufacturer.pluck(:name)
      @colors = Mkp::ProductColors.all(params[:network]).map{ |color| color[:name] }.sort
      @categories = Mkp::Category.active.where('network = ?', 'ar').
        all.
        select { |category| category.children.empty? }.
        map { |category| [category.path.map(&:name).join('/'), category.id] }.
        sort_by(&:first)
    end

    def status
      # TODO: Validate that current shop has access to the requested worker.

      status_container = SidekiqStatus::Container.load(params[:id])

      if status_container.status == 'complete'
        data = Hash.new
        data['strategy'] = params['strategy']
        data['products'] = status_container.payload['products']
        data['variants'] = status_container.payload['variants']
        data['errors'] = status_container.payload['error_messages'] if status_container.payload['error_messages'].present?
        data['errors_html'] = render_to_string(partial: 'lux/product_imports/errors', locals: { data: data }) if status_container.payload['error_messages'].present?

        render json: { status: :ok, data: data }
      else
        render json: { status: :processing,
                       data: { pct_complete: status_container.pct_complete,
                               status: 'in_progress' } }
      end
    end
  end
end

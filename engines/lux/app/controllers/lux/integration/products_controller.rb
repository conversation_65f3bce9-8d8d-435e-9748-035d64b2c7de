module Lux
  class Integration::ProductsController < Lux::ApplicationController
    include IntegrationsLoaders

    before_filter :load_available_integrations
    before_filter :set_integration

    def import_and_sync
      if @integration.syncing?
        flash[:alert] = t('lux.controllers.integrations.destroy.trouble')
      else
        @integration.syncing!
        Lux::IntegrationProductImportWorker.perform_async(@integration.class.name, @integration.id, @integration.shop_id)

        flash[:success] = I18n.t('lux.integration.products.import.async_message')
      end

      redirect_to shop_integrations_url
    end

    private

    def set_integration
      @integration = load_authorized_integration(params[:integration_name].downcase)
    end
  end
end

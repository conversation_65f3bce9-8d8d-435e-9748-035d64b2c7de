module Lux
  class ShippingMethodsController < Lux::ApplicationController
    def create
      begin
        ActiveRecord::Base.transaction do
          @zone = @shop.zones.build zone_params
          @shipping_method = Mkp::ShippingMethod.new shipping_method_params
          @zone.save!
          @zone.shipping_methods.create! shipping_method_params
          if params[:shop].present? && params[:shop][:customs_signer_name].present?
            @shop_settings = @shop.setting
            @shop_settings.customs_signer_name = params[:shop][:customs_signer_name]
            @shop_settings.save
          end
        end
      rescue ActiveRecord::RecordInvalid => e
        ExceptionNotifier.notify_exception(e, data: { msg: t('lux.controllers.methods.creating_error') })
        flash[:error] = t('lux.controllers.methods.flash_creating_error')
        load_shipping_methods
        render 'lux/shipping_methods/index' and return
      end
      redirect_to shop_shipping_methods_url,
                  notice: t('lux.controllers.methods.creating_success')
    end

    def destroy
      if @shop.shipping_methods.count < 2
        redirect_to shop_shipping_methods_url,
                    alert: t('lux.controllers.methods.last_method') and return
      end

      zone = @shop.zones.find(params[:zone_id])
      zone.shipping_methods.find(params[:id]).destroy
      zone.destroy

      redirect_to shop_shipping_methods_url,
                  notice: t('lux.controllers.methods.deleted_success')
    end

    def edit
      redirect_if_no_any_warehouse
      @shipping_method = @shop.shipping_methods.find params[:id]
      @zone = @shipping_method.zone
    end

    def index
      redirect_if_no_any_warehouse
      load_shipping_methods
      @zone = @shop.zones.build
      @shipping_method = @zone.shipping_methods.build
    end

    def update
      begin
        ActiveRecord::Base.transaction do
          @shipping_method = @shop.shipping_methods.find params[:id]
          @zone = @shipping_method.zone
          @zone.update_attributes zone_params
          @shipping_method.update_attributes shipping_method_params
          if params[:shop].present? && params[:shop][:customs_signer_name].present?
            @shop_settings = @shop.setting
            @shop_settings.customs_signer_name = params[:shop][:customs_signer_name]
            @shop_settings.save
          end
        end
      rescue ActiveRecord::RecordInvalid
        render 'lux/shipping_methods/edit' and return
      end
      redirect_to shop_shipping_methods_url,
                  notice: t('lux.controllers.methods.updated_successfully')
    end

    protected

    def load_shipping_methods
      @shipping_methods = @shop.shipping_methods
    end

    def redirect_if_no_any_warehouse
      if @shop.warehouses.empty?
        redirect_to shop_warehouses_url,
                    alert: t('lux.controllers.methods.create_warehouse')
      end
    end

    def shipping_method_params
      params.require(:mkp_shipping_method).permit(:title, :service, :price, :carriers => [], :speeds => []).tap do |whitelisted|
        whitelisted[:states] = params[:mkp_shipping_method][:states]
      end
    end

    def zone_params
      filtered_params = params.require(:mkp_zone).permit(:warehouse_id, :countries => [])
      filtered_params.merge!(countries: []) if not filtered_params.key?(:countries)
      filtered_params
    end
  end
end

module Lux
  class WarehousesController < Lux::ApplicationController
    before_filter :current_warehouse, only: [:edit, :update, :destroy]

    def create
      @new_warehouse = @shop.warehouses.build(warehouse_params)

      if verify_warehouse && @new_warehouse.save
        redirect_to shop_warehouses_url, notice: t('lux.controllers.warehouses.created_success')
      else
        flash[:alert] ||= t('lux.controllers.warehouses.created_success')
        @warehouses = @shop.warehouses.select(&:id)
        render 'lux/warehouses/index'
      end
    end

    def index
      @new_warehouse = @shop.warehouses.build
      @warehouses = @shop.warehouses - [@new_warehouse]
    end

    def edit
    end

    def update
      if @warehouse.update_attributes(warehouse_params)
        redirect_to shop_warehouses_path('ar', @shop),
                    notice: t('lux.controllers.warehouses.updated_success')
      else
        render :edit
      end
    end

    def destroy
      @warehouse.destroy

      redirect_to shop_warehouses_url
    end

    protected
    def warehouse_params
      params.require(:warehouse)
            .permit(:address,
                    :address_2,
                    :country,
                    :city,
                    :doc_type,
                    :doc_number,
                    :first_name,
                    :open_hours,
                    :closing_hours,
                    :pickup,
                    :retardment,
                    :state,
                    :telephone,
                    :zip)
    end

    def verify_warehouse
      return true if @network != 'US'
      warehouse_fields = Mkp::Address::EasypostFormat.for(@new_warehouse)
      EasyPost::Address.create_and_verify(warehouse_fields)
    rescue EasyPost::Error => e
      flash[:alert] = "The address you entered could not be recognized as a real address. #{e.message}"
      false
    end

    private

    def current_warehouse
      @warehouse = @shop.warehouses.find(params[:id])
    end
  end
end

module Lux
  module Ajax
    class PropertyOptionsController < ActionController::Base
      protect_from_forgery with: :exception
      
      def categories
        if params[:category_id].present?
          @category = Mkp::Category.find(params[:category_id])

          if @category.present?
            respond_to do |format|
              format.json do
                render layout: false, json: data_hash(@category)
              end
            end
          else
            respond_to do |format|
              format.json do
                render layout: false, json: { title: I18n.t('ajax.errors.generic').to_s, msg: '' },
                       status: :not_found
              end
            end
          end
        else
          respond_to do |format|
            format.json do
              render layout: false, json: { title: I18n.t('ajax.errors.generic').to_s, msg: '' },
                     status: :not_found
            end
          end
        end
      end

      def modifiers; end

      private

      def data_hash(object)
          hash = {}
          hash[:id] = object.id
          hash[:name] = object.name
          hash[:children] = []
          object.children.each do |o|
            child_hash = {}
            child_hash[:id] = o.id
            child_hash[:name] = o.name
            child_hash[:has_children] = o.has_children?
            hash[:children] << child_hash
          end
          hash
        end
    end
  end
end

module Lux
  module Ajax
    class VariantsController < ActionController::Base

      protect_from_forgery with: :exception

      def update_picture
        params['pictures_data'].each do |pd|
          picture_id = pd['picture_id'].to_i
          variant_id = pd['variant_id'].to_i.zero? ? nil : pd['variant_id'].to_i
          picture = Mkp::Attachment::ProductPicture.find(picture_id)

          begin
            picture.variant_id = variant_id if picture.present?
            picture.save!
            flash[:success] = 'Fotos actualizadas correctamente.'
          rescue => exception
            flash[:error] = "Error al actualizar foto  la foto con id#{picture_id}: #{exception}."
          end
        end

        render nothing: true if request.xhr?
      end
    end
  end
end
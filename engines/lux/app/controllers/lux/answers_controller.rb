module Lux
  class AnswersController < Lux::ApplicationController
    def create
      question = @shop.questions.find(params[:question_id])
      @answer = Mkp::Answer.new(description: params[:description])
      @answer.question = question
      @answer.user     = current_user

      respond_to do |format|
        if @answer.save
          @answer.send_user_notification
          format.js { render layout: false }
        end
      end
    end

    def edit
      @answer = @shop.questions.find(params[:question_id]).answer
    end

    def update
      @answer = @shop.questions.find(params[:question_id]).answer

      if @answer.update_attributes(description: params[:description])
        respond_to do |format|
          format.js { head :ok }
        end
      end
    end
  end
end

module Lux
  class OrderItemsController < Lux::ApplicationController
    before_filter :set_order_item, except: [:create, :new]

    def create
      suborder = Mkp::Suborder.find_by_id(params[:suborder_id])
      variant = Mkp::Variant.find_by_id(params[:order_item][:variant_id])
      quantity = params[:order_item][:quantity].to_i

      if variant.present? && quantity.present?
        product = variant.product

        order_items = suborder.items.available
        if (item = order_items.detect { |item| item.variant.id == variant.id }).blank?
          order_item = Mkp::OrderItem.create do |object|
            object.variant = variant
            object.product = product
            object.price = product.price
            object.sale_price = product.sale_price
            object.currency = variant.currency
            object.quantity = quantity
            object.suborder_id = suborder.id
          end
        else
          item.quantity += quantity
          item.save!
        end

        variant.decrease_quantity(quantity)

        redirect_to_suborder notice: t('lux.controllers.order_items.create.success')
      else
        redirect_to_suborder notice: t('lux.controllers.order_items.create.failure')
      end
    end

    def cancel
      if @order_item.present? && @order_item.cancel!
        warn "[FUTURE FEATURE] This should check if we have to decrease the reserved_quantity or quantity"
        @order_item.variant.increase_quantity(@order_item.quantity)
        redirect_to_suborder \
          notice: t('lux.controllers.order_items.transition.success', state: 'cancelled')
      else
        redirect_to_suborder \
          notice: t('lux.controllers.order_items.transition.failure', state: 'cancelled')
      end
    end

    def return
      if @order_item.present? && @order_item.return!
        redirect_to_suborder \
          notice: t('lux.controllers.order_items.transition.success', state: 'returned')
      else
        redirect_to_suborder \
          notice: t('lux.controllers.order_items.transition.failure', state: 'returned')
      end
    end

    private

    def set_order_item
      @order_item = ::Mkp::OrderItem.find_by_id(params[:id])
    end

    def redirect_to_suborder(options = {})
      redirect_to shop_suborder_path(
        network: @network.downcase, shop_id: params[:shop_id], id: params[:suborder_id]
      ), notice: options[:notice]
    end
  end
end

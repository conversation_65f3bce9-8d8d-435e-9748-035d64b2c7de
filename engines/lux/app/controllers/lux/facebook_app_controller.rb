module Lux
  class FacebookAppController < Lux::ApplicationController
    include C<PERSON>rencyExposure
    helper ::AnalyticsHelper
    helper ::ApplicationHelper
    helper Mkp::ApplicationHelper
    helper Mkp::VariantsHelper

    skip_before_filter :require_current_logged_in_user_can_manage_a_shop,
                       :set_current_shop,
                       :stay_same_area_when_change_shop,
                       only: [:eshop, :goodpeople_shop]

    skip_before_filter :load_authorized_integrations, if: proc { Network[@network].shop_integrations.present? }

    def tab_guide
    end

    def eshop
      @shop = Mkp::Shop.find(1)
      set_locale_and_network_for_fb
      @variants = Mkp::Variant.with_stock
                              .active
                              .by_network(:AR)
                              .on_sale
                              .newest_random(9)

      render layout: nil
    end

    def goodpeople_shop
      @shop = Mkp::Shop.where(id: params[:shop_id])
      not_found unless @shop.length
      @shop = @shop.first
      set_locale_and_network_for_fb
      @variants = @shop.variants.with_stock.active.random(9)

      render 'eshop', layout: nil
    end

    private

    def set_locale_and_network_for_fb
      @network = @shop.network
      I18n.locale = Network[@network].locale
      @footer_mkp_menu = { items: Mkp::Menu.get(@network) }
    end
  end
end

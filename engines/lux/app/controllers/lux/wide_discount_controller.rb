module Lux
  class WideDiscountController < Lux::ApplicationController
    before_filter :get_filters_options

    PRODUCTS_PER_PAGE = 2000

    def update
      @shop_setting = @shop.setting

      if params.key?(:categories_ids) || params.key?(:manufacturers_ids)
        if (@products = retrieve_selected_products).present?
          remove_wide_discount!
          apply_discounts_on_products(@products)
        end
      else
        if params[:discount_remove].present? && params[:discount_remove]
          remove_wide_discount!
        else
          apply_wide_discount!
        end
      end

      render :edit
    end

    private

    def get_filters_options
      shop = @shop

      search = Mkp::Product.search do
        with :shop_id, shop.id
        with :with_stock, true

        facet :manufacturer_id
        facet :category_id
      end

      @_manufacturers = get_facet_values(search, :manufacturer_id).map { |m| { id: m[:id], name: m[:name] } }
      @_categories = get_facet_values(search, :category_id).map { |c| { id: c[:id], name: c[:name] } }

      @categories = Mkp::Category.where(id: @_categories.map(&:first)).select('id, name, ancestry')
      @manufacturers = Mkp::Manufacturer.where(id: @_manufacturers.map(&:first))
    end

    def wide_discount_attrs
      params.require(:mkp_shop_setting).permit(:discount_percent,
                                               :discount_ends_at,
                                               :discount_is_over_product_sale,
                                               :discount_starts_at)
    end

    def remove_wide_discount!
      @shop_setting.update_attributes(wide_discount: nil)
    end

    def apply_wide_discount!
      @shop_setting.update_attributes(wide_discount_attrs)
    end

    def apply_discounts_on_products(products)
      updated_products = 0

      products.each do |product|
        unless (product.has_sale? && wide_discount_attrs[:discount_is_over_product_sale] == '0')
          product.sale_price = product.regular_price - get_discount_amount(product)
          product.sale_on = wide_discount_attrs[:discount_starts_at]
          product.sale_until = wide_discount_attrs[:discount_ends_at]
          updated_products += 1 if product.save
        end
      end

      set_flash_message(updated_products)
    end
    def get_discount_amount(product)
      (product.regular_price * wide_discount_attrs[:discount_percent].to_f / 100.0).round
    end

    def retrieve_selected_products
      shop = @shop

      search = Mkp::Product.search do
        with :shop_id, shop.id
        with :with_stock, true
        with :manufacturer_id, params[:manufacturers_ids] if params[:manufacturers_ids]
        with :category_id, params[:categories_ids] if params[:categories_ids]

        paginate(page: 1, per_page: PRODUCTS_PER_PAGE)
      end

      search.results
    end

    def set_flash_message(updated_products)
      message = "#{updated_products} #{'product'.pluralize(updated_products)} updated."
      flash[updated_products > 0 ? :success : :notice] = message
    end

    def get_facet_values(search,facet_name)
      if (facet_results = search.facet(facet_name))
        facet_results.rows.each_with_object([]) do |facet, bucket|
          bucket << { id: facet.value, name: facet.instance.name }
        end
      else
        []
      end
    end
  end
end

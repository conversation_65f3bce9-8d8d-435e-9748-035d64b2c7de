module Lux
  module Defused
    class ShipmentsController < ApplicationController
      protect_from_forgery with: :exception

      include LabelGatewaysHelper

      before_filter :load_suborder_and_shipment

      def retrieve_shipment_label
        shipment_label = @shipment.label
        if shipment_label.present?
          redirect_to shipment_label.url
        else
          shop = @suborder.shop
          if customer_choose_label?(@shipment)
            shipment_label = @shipment.labels.create do |label|
              label.courier = @shipment.extra_info[:courier]
              label.gateway = @shipment.extra_info[:gateway_class]
            end
          elsif allowed_to_purchase_labels_on_demand?(shop)
            # Only purchase ONE Label through the last Gateway that the shop has configured
            gateway_name = @on_demand_labels_config.keys.last
            configs = if gateway_name == 'Oca'
                        shop.setting.on_demand_labels_config.slice(gateway_name.underscore.to_s.to_sym)
                      else
                        {}
                      end
            new_extra_info = @shipment.extra_info.dup
            new_extra_info[:gateway_class] = gateway_name
            new_extra_info[:gateway_info] = configs
            @shipment.update!(extra_info: new_extra_info)

            shipment_label = @shipment.labels.create do |label|
              label.gateway = @shipment.extra_info[:gateway_class]
            end
          end

          if shipment_label.present? && shipment_label.purchase!
            @shipment.shipped!
            redirect_to shipment_label.url
          elsif shipment_label.present?
            shipment_label.destroy
            render(nothing: true) && return
          else
            redirect_to(shop_suborder_url(id: @suborder.public_id, shop_id: @suborder.shop.friendly_id, network: @suborder.shop.network.downcase)) && return
          end
        end
      end

      def retrieve_shipment_label_extra_docs
        shipment_label = @shipment.label
        if shipment_label.present?
          extra_docs = shipment_label.extra_docs
          if extra_docs.length == 1
            redirect_to(extra_docs[0][:form_url]) && return
          else
            extra_docs_title = "<h4>#{I18n.t('lux.defused.shipments.extra_docs_title')}</h4>"
            download_links = extra_docs.each_with_object(extra_docs_title) do |doc, result|
              result.concat("<div><a href=#{doc[:form_url]} target: '_blank'>#{doc[:form_type].titleize}</a></div>")
            end
            render(text: "<pre>#{download_links}</pre>") && return
          end
        else
          render(text: "<pre>#{I18n.t('lux.defused.shipments.create_label_first')}</pre>") && return
        end
      end

      def retrieve_shipment_packing_list
        @shop = @suborder.shop
        @warehouse = @shipment.origin_address
        @destination = @shipment.destination_address

        I18n.locale = Network[@shop.network].locale

        paper_size = case @shop.network
                     when 'US' then 'Letter'
                     when 'AR' then 'A4'
        end

        respond_to do |format|
          format.html { render template: 'lux/shippings/print_packing_list' }
          format.pdf do
            name = "packing_list_#{@shipment.id}"
            pdf = render_to_string template: 'lux/shippings/print_packing_list',
                                   pdf: name,
                                   paper_size: paper_size
            send_data pdf,
                      filename: "#{name}.pdf",
                      type: 'application/pdf'
          end
        end
      end

      private

      def load_suborder_and_shipment
        suborder_id, token = params.values_at(:suborder_id, :token)

        @suborder = Mkp::Suborder.find_by(id: suborder_id)
        @shipment = decrypt_shipment(token)

        render(nothing: true) && return unless @shipment.present? && @suborder.present?
      end

      def decrypt_shipment(token)
        encryptor = NaiveTokenEncryptor.new(
          @suborder.encryptor_key,
          @suborder.encryptor_initialization_vector
        )

        shipment_id = encryptor.decode_and_decrypt(token)

        Mkp::Shipment.find_by(id: shipment_id)
      end

      def suborder_url_parameters
        {
          network: @suborder.shop.network.downcase,
          id: @suborder.public_id,
          shop_id: @suborder.shop.slug
        }
      end
    end
  end
end

module Lux
  class QuestionsController < Lux::ApplicationController
    def index
      @questions = @shop.questions.
                    includes(:product, :answer).
                    order('mkp_questions.created_at DESC').
                    paginate(page: params[:page], per_page: 10)
      filter_by_status
      filter_by_query
    end

    def read
      question = @shop.questions.find(params[:id])
      question.read!
      render nothing: true
    end

    private

    def filter_by_status
      if (@status = params[:status]).present?
        if @status == "unread"
          @questions = @questions.where(read: 0)
        elsif @status == "replied"
          @questions = @questions.joins([:answer])
        elsif @status == "not_replied"
          @questions = @questions.includes(:answer).where("mkp_answers.mkp_question_id" => nil)
        end
      end
    end

    def filter_by_query
      if (@query = params[:query]).present?
        query = "%" + @query.strip + "%"
        @questions = @questions.
          where('mkp_questions.description LIKE ?', query)
      end
    end
  end
end

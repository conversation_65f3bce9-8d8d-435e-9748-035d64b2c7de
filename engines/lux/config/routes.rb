Lux::Engine.routes.draw do
  class NetworkIntegrations
    def self.matches?(request)
      Network[request.params[:network]].shop_integrations.present?
    end
  end

  root to: 'application#home'

  scope path: 'd', as: :defused do
    get 'shipment/:suborder_id/packing_list', \
        to: 'defused/shipments#retrieve_shipment_packing_list', \
        as: 'shipment_packing_list', \
        defaults: { format: 'pdf' }

    get 'shipment/:suborder_id/labels', \
        to: 'defused/shipments#retrieve_shipment_label', \
        as: 'shipment_label'

    get 'shipment/:suborder_id/extra_docs', \
        to: 'defused/shipments#retrieve_shipment_label_extra_docs', \
        as: 'shipment_label_extra_docs'
  end

  scope '(:network)', network: Network.all_active_param_regex do
    root to: 'application#home', as: :lux_home

    resources :shipments, only: [:show]

    resources :shops, only: [] do
      resource  :account, only: [:show, :update], protocol: SECURE_PROTOCOL
      resources :transactions, only: [:index]
      resources :warehouses, only: [:create, :destroy, :index, :edit, :update]
      resources :coupons, except: [:edit, :update] do
        get :clone, on: :member
      end

      get 'wide_sale', to: 'wide_discount#edit', as: :wide_discount
      put 'wide_sale', to: 'wide_discount#update', as: :commit_wide_discount

      resources :suborders, path: 'orders', only: [:index, :show] do
        collection do
          post :apply_state_change
          post :upload_document, as: :upload_document
          put 'suborder_pick_up_status_update', to: 'suborders#suborder_pick_up_updater', as: 'suborder_pick_up_update'
          get 'suborder_pick_up_status_update', to: 'suborders#suborder_pick_up_update_file', as: 'suborder_pick_up'
          get 'suborder_imports/new', to: "suborders#new", as: "suborder_import"
          post 'suborder_imports/create', to: "suborders#suborder_label_import_create", as: "suborder_label_import_create"
          get 'suborder_imports/progress', to: "suborders#suborder_label_processing", as: "label_processing"
        end
      end

      resources :products do
        collection do
          get :export_data
          get :exports_list
          get :download_export
          post :enable_all
          post :disabled_all
        end

        member do
          get :clone
          get :delete, to: 'products#destroy'
          get :change_visibility, to: 'products#change_visibility'

          post :reset_properties, to: 'products#reset_properties'
          get :custom_properties, to: 'products#custom_properties', as: :custom_properties
          put :save_properties ,  to: 'products#save_properties', as: :save_properties
          post :duplicate_property ,  to: 'products#duplicate_property', as: :duplicate_property
        end

        resources :questions, only: [] do
          put :read, to: 'questions#read', on: :member
          resource :answer, only: [:create, :edit, :update]
        end
      end

      resources :product_imports, only: [:create, :new] do
        get :progress, on: :member
        get :status, on: :member
      end

      resources :questions, only: [:index]
      resources :shipping_methods, only: [:create, :index, :edit, :update, :destroy], path: 'services'
      resource :unpublished_pictures, only: [:create]
      get 'facebook_app/tab_guide', to: 'facebook_app#tab_guide'
      get 'taxes/index', to: 'taxes#index', as: :taxes
      put 'taxes/update', to: 'taxes#update'

      resources :order_items, only: :create do
        post :cancel, on: :member
        post :return, on: :member
      end

      scope protocol: SECURE_PROTOCOL do
        # Manage, creation and destroy of an integration
        resources :integrations, except: [:show, :edit]
        post 'integrations/authorize/:integration_name', to: 'integrations#authorize', as: 'authorize_integration'
        get 'integrations/complete/:integration_name', to: 'integrations#complete', on: :collection
        get 'integrations/:integration_name', to: 'integrations#create', as: 'lux_integration'
        post 'integrations/notify/:integration_name', to: "notification_callbacks#notification"

        # Takes advantage of an integration
        post 'integrations/:integration_name/import_and_sync', to: 'integration/products#import_and_sync', as: 'integration_import_and_sync'
      end

    end

    # Facebook uses POST to get a page
    match 'facebook_app/eshop', to: 'facebook_app#eshop', via: [:get, :post]
    match 'facebook_app/goodpeople_shop/:shop_id', to: 'facebook_app#goodpeople_shop', via: [:get, :post], as: 'facebook_app_goodpeople_shop'
  end

  namespace :ajax do
    resources :products, only: [:destroy, :index, :show, :update] do
      put 'sort_pictures', to: 'products#sort_pics'
    end

    post 'labels', to: 'labels#create'
    post 'labels/new', to: 'labels#new'
    post 'labels/:id/status', to: 'labels#status', as: 'labels_status'

    put   'variants/update_picture',     controller: 'variants',         action: 'update_picture', defaults: { format: 'json' }

    get :users,       to: 'users#all'
    get :item_stock,  to: 'order_items#item_stock'
    get 'property_options/categories', controller: 'property_options', action: 'categories',     defaults: { format: 'json' }
    get 'property_options/sports',     controller: 'property_options', action: 'sports',         defaults: { format: 'json' }
    get 'property_options/modifiers',  controller: 'property_options', action: 'modifiers',      defaults: { format: 'json' }
  end
end

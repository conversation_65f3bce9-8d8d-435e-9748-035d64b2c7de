en:
  lux:
    product_imports:
      new:
        page_title_for_create: Import Products
        page_title_for_update: Update Products
        title_for_create: 'Import Products'
        subtitle_for_create: 'Import products from a csv file on your computer.'
        title_for_update: 'Bulk Update of Products'
        subtitle_for_update: 'Bulk update of products from a csv file on your computer.'
      progress:
        title_for_create_status: 'Importing Products'
        title_for_update_status: 'Updating Products'
      categories:
        title: Title
      form:
        choose_csv: Choose a CSV file
        product_list: Product List
        choose_file: Select CSV file
        import: Import
      guidelines_for_creat:
        title: Guidelines for completing your csv file
        avsku_d: 'Stock keeping unit, a unique identifier for each distinct product'
        title_d: 'A name used to identify a specific product'
        description_d: 'A statement that tells how a product looks, what is made of, etc'
        category_d: 'A number used to clasify a product on the marketplace'
        transaction_type_d: 'It define the kind of transaction for the product. Available options: purchasable - reservable - other - voucher'
        manufacturer_d: 'A name used to specify the manufacturer of a product'
        price_d: 'A decimal number used to specify the regular price of a product on the marketplace'
        available_on_d: 'A date used to specify when a product is available for purchase on the marketplace. Dates must be formatted as follows: MM/DD/YYYY'
        height_d: 'A decimal number used to specify the height of a product'
        length_d: 'A decimal number used to specify the length of a product'
        width_d: 'A decimal number used to specify the width of a product'
        weight_d: 'A decimal number used to specify the weight of a product'
        stock_d: 'An integer number used to specify how many units of a distinct product are available for sale on the marketplace'
        pickeable_d: 'Indicates if the product is pickeable. Available options: 0 - 1 - 2 (0: Not applicable - 1: Not pickeable - 2: Pickeable)'
        property_name: 'Property name'
        color_d: 'A name used to specify the color of a product.'
        size_d: 'A name used to specify the size of a product.'
        dimentions_d: 'A name used to specify the dimensions of a product.'
        hardness_d: 'A name used to specify the hardness of a product.'
        length_d2: 'A name used to specify the length of a product'
        material_d: 'A name used to specify the material of a product.'
        percentage_d: "Represent the variant's percentage discount. That can be: 10, 20, 30, 40 and 50."
        payment_method_d: "Represent the variant's payment brand. That can be: Visa, Visa Debito, Mastercard and Amex"
        details1: 'You can see the complete list of '
        details2: ' available on the platform. The default length unit for the height, length and width of a product is '
        details3: '. The defaultdefault mass unit for the weight of a product is '
        details4: ". Each product can have up to three different properties, that combined form a variant of a product. The Property Names and the Property Values columns are expected to specify the color property and shouldn't be changed. To specify other properties, write the name of the property separated by | in Property Names columns, and the corresponding value for each property on the Property Values separated by | as the case may be. The property names must match one of the following propeties available on the platform:"
        details5: 'You can see the complete list of '
        details6: ' available on the platform.'
        details7: 'You can download an example for creation '
        details8: ' and the resulting '
        details9: ' to assist'
      guidelines_for_update:
        title: Guidelines for completing your csv file
        avsku_d: 'Stock keeping unit, a unique identifier for each distinct product'
        title_d: 'A name used to identify a specific product'
        description_d: 'A statement that tells how a product looks, what is made of, etc'
        category_d: 'A number used to clasify a product on the marketplace'
        transaction_type_d: 'It define the kind of transaction for the product. Available options: purchasable - reservable - other - voucher'
        manufacturer_d: 'A name used to specify the manufacturer of a product'
        price_d: 'A decimal number used to specify the regular price of a product on the marketplace'
        available_on_d: 'A date used to specify when a product is available for purchase on the marketplace. Dates must be formatted as follows: MM/DD/YYYY'
        height_d: 'A decimal number used to specify the height of a product'
        length_d: 'A decimal number used to specify the length of a product'
        width_d: 'A decimal number used to specify the width of a product'
        weight_d: 'A decimal number used to specify the weight of a product'
        stock_d: 'An integer number used to specify how many units of a distinct product are available for sale on the marketplace'
        property_name: 'Property name'
        color_d: 'A name used to specify the color of a product.'
        size_d: 'A name used to specify the size of a product.'
        dimentions_d: 'A name used to specify the dimensions of a product.'
        hardness_d: 'A name used to specify the hardness of a product.'
        length_d2: 'A name used to specify the length of a product'
        material_d: 'A name used to specify the material of a product.'
        percentage_d: "Represent the variant's percentage discount. That can be: 10, 20, 30, 40 and 50."
        payment_method_d: "Represent the variant's payment brand. That can be: Visa, Visa Debito, Mastercard and Amex"
        details1: 'You can see the complete list of '
        details2: ' available on the platform. The default length unit for the height, length and width of a product is '
        details3: '. The defaultdefault mass unit for the weight of a product is '
        details4: ". Each product can have up to three different properties, that combined form a variant of a product. The Property Names and the Property Values columns are expected to specify the color property and shouldn't be changed. To specify other properties, write the name of the property separated by | in Property Names columns, and the corresponding value for each property on the Property Values separated by | as the case may be. The property names must match one of the following propeties available on the platform:"
        details5: 'You can see the complete list of '
        details6: ' available on the platform.'
        details7: 'You can download an example for creation '
        details8: ' and the resulting '
        details9: ' to assist'
      structure:
        unavailable: 'Campo: %{type} no existe, gp_sku: %{info}'
        presences: 'FIELD: %{header}, Falta el valor en la columna.'
        obsolete: 'FIELD: %{header}, Es un campo solo para %{type}.'
        invalid_headers: 'HEADERS: columna %{header} no disponible para realizar una actualización masiva'
        invalid_date: 'FIELD: %{header}, Fecha con formato invalido.'
    controllers:
      updated_successfully: 'It was successfully updated.'
      updating_trouble: 'There was a problem updating the shop: %{errors}'
      integration_product_show: 'Product visibility updated according to %{integration_name}'
      product_show: 'The product is now visible'
      product_hide: 'The product is now hidden'
      products:
        created_success: 'Product was successfully created.'
      methods:
        creating_error: 'Shipping method error on create'
        flash_creating_error: 'There was an error creating the service.'
        creating_success: 'The service was created properly'
        last_method: 'You cannot delete your last service. Please create another first.'
        deleted_success: 'The service was deleted properly'
        updated_successfully: 'The service was updated properly'
        create_warehouse: 'Create a Warehouse first in order to manage services'
      warehouses:
        created_success: 'Warehouse was successfully created.'
        updated_success: 'Warehouse was successfully updated.'
      integrations:
        destroy:
          integration_message: " Your items in %{integration_name} won't be affected."
          delete_integrables:
            success: 'The integration and your local products will be removed.'
          remove_integrables_stock:
            success: "The integration will be removed and the stock of your local products' stock set to zero."
          hide_shop:
            success: 'The integration was properly removed and your GoodPeople shop was hidden.'
          nothing:
            success: 'The integration was properly removed.'
          success: 'The integration was properly removed.'
          trouble: 'There was an error trying to remove the integration.'
      order_items:
        create:
          success: "The order item has been successfully created."
          failure: "Couldn't create the Order Item! Sorry about that."
        transition:
          success: "The order item has been successfully marked as %{state}."
          failure: "Couldn't mark the order item as %{state}! Sorry about that."
    wide_discount:
      edit:
        head_title: 'Sale for all products'
        set_sale: Create Sale for all Products
        discount_percent: Discount Percentage
        discount_percent_explanation: This discount will be applied to every product in your shop. If you would like to only put some products for sale, you can do that from the Product Tab within the Product List
        percent_warn: Is the percentage to discount on all the products of your shop (0%~100%).
        start_date: 'Start Date'
        start_date_explanation: 'Sale will start immediately if the current day is selected or at 12am on the day selected.'
        end_date: End Date
        end_date_explanation: 'Sale will end at midnight on the day selected'
        click_here: Click Here
        apply_all: 'Apply to all products. (Even products already on sale.)'
        apply: Apply
        delete: Delete
        categories: Categories
        manufacturers: Manufacturers
    marketing:
      menu:
        facebook_app: Facebook App
        coupons: Coupons
        sale: Sale
        banners: Banners
    warehouses:
      index:
        shop_with_fulfillment: This shop is being fulfilled
        warehouses: Warehouses
        manage: Manage your warehouses
        create: Create new warehouse
        add: Create new warehouse
        warn_msg: "(*) It's not possible to remove the last warehouse. At least one warehouse must be provided. It's not possible to remove a warehouse with services. Please remove services before."
        services: Services
        existing: Existing Warehouses
      warehouse:
        edit: Edit
        remove: 'Delete *'
        open_hours: Open Hours
        closing_hours: Closing Hours
        delay: Demora
        pick_up_enabled: Pick Up Enabled
        pick_up_disabled: Pick Up Disabled
      edit:
        save: Save
      form:
        title_placeholder: 'Title about this warehouse'
        address_1: Address 1
        city: City
        zip: Zipcode
        pickup: Enable Pickup
        open_hours: Open Hours
        closing_hours: Closing Hours
        delay: Delay
        address_placeholder: 'Phone: eg. (*************'
    products:
      index:
        head_title: Manage Shop - Products
        on_sale: On Sale
        with_stock: With Stock
        without_stock: Without Stock
        available: available
        search: Search title, description, SKU or manufacturer
        unavailable: Unavailable
        hide: 'Product Visible: Click to Hide'
        display: 'Product Hidden: Click to Display'
        delete: Delete
        edit: Edit
        preview: Preview
        clone: Clone
        title: Title
        manufacturer: Manufacturer
        category: Category
        sports: Sports
        price: Price
        sale_price: Sale Price
        sale_on: Sale On
        sale_until: Sale Until
        visible: Visible
        stock: Stock
        sold: Sold
        no_products: "We couldn't find any products."
        loading: Loading...
      edit:
        head_title: 'Manage Shop - Edit: %{title}'
      new:
        head_title: Manage Shop - Add new product
        add_your_product: Add your product
        cancel: cancel
        or: or
      top_links:
        new: New Product
        import: Import Products
        export: Export Products
        bulk_update: Bulk Update Products
      form:
        properties:  Properties and specifications
        variants: Variants of the product
        cancel: Cancel
        save: Save
        product_pictures: Product pictures
      partials:
        title:
          label: Name
          title: Add Product Name
          explanation: 'Something like: Maple Skateboard, X Games Cubes shirt, etc.'
        description:
          description: Add the description here
          label: Describe the product
        categories:
          category: Product category
          explanation: 'Select the type of product you want to sell. Example: Hoodie, T-Shirt, Boardshort'
          choose_correctly:  Please select category correctly
          pick_one: Pick one
          loading: Loading
        manufacturer:
          manufacturer: Product manufacturer
          explanation: 'Pick the brand of the product. Example: Quiksilver, Volcom, Neff.'
        sports:
          sport: 'Sport/s'
          at_least_one_sport: 'Choose at least one'
          explanation:  'If the product is associated with a sport, you can link it here.'
        pro_athletes:
          pro_athlete: 'Pro Athlete/s'
          explanation:  'If the Shop is associated with a Pro Athlete, you can link it here.'
        gender:
          gender: Gender
          explanation: Is for an particular gender or age?
        price:
           price: Price
           explanation: Set the price of the product
           errors:
             equal_zero: Price can't be zero
        sale_price:
          sale: Sale
          explanation: Set the time span of the sale and the price for that days
          click_here: Click Here
          start_date: 'Sale start date:'
          end_date: 'Sale end date:'
          price: Sale Price
          errors:
            lower_than_zero: can't be less than 0
            higher_than_regular_price: can't be higher than regular price
            dont_allow_sale_price_on_zero: Sale price can't be zero
        availability:
          title: Product Availability
          explanation:  Set the date at which the product will be available
          click_here: Click Here
        shipping:
          title: Shipping Specifications
          explanation: Set the nedeed fields for calculate shipping price
          weight: Weight
          packages: Packages
          height: Height
          width: Width
          length: Length
          inches: Inches
          kilograms: Kilograms
          millimeters: Millimeters
          length-unit: Length unit
          mass-unit: Mass unit
          pounds: Pounds
          errors:
            weight: can't be more than 70 pounds
        images:
          upload_pictures: Upload Pictures
        variants_form:
          title: Define the properties
          explanation: Pick the follow available properties
          stock-management: Stock Management
          perform_stock_management: Perform stock management
          warning_properties:  'Warning: Adding/Editing/Removing properties reset the stock.'
          manage_property: 'Manage names for properties in product page'
          show_all_variants: 'Show all color variations on product list'
          handle_stock: 'I want to handle the stock for the variants'
          edit_stock_disabled: 'This product has an active integration, please manage their variant properties and stock from the remote application.'
          submit: Submit
          new_property: + new property
        properties:
          remove: Remove
          add:  Add +
          delete_property: Delete Property
          property: '%{property} property'
          dimensions: Dimensions
          hardness: Property one
          length: Property two
          material: Property three
          size: Size
          percentage: Percentage
          payment_method: Payment Method
    questions:
      index:
        head_title: Admin Shop - Preguntas
        description: 'Search description'
        all: All
        unread: Unread
        replied: Replied
        not_replied: Not Replied
      question:
        answer: Answer
        view: View
        reply: Reply
        chat_with: Chat With
        back: Back
        new_coupon: New Coupon
    accounts:
      show:
        before_going_visible: 'Before going visible, please provide a warehouse and a shipping service for that warehouse.'
        description: Shop Description
        digest: Receive purchase email notifications as a daily digest
        no_active: No
        people: People to notify on purchases
        title: Name
        update: Update
        visible: 'Make products visible to everyone:'
        yes_active: Yes
        phone: Phone
        public_name: Public name
        shop_web: Website
        public_email: Email
    coupons:
      status:
        deleted: Deleted
        acive: Active
        inactive: Inactive
      index:
        head_title: 'Manage Shop - Marketing'
        search: 'Enter Coupon Name or Code'
        all: All
        percent: Percent
        value: Value
        new_coupon: New Coupon
      coupon:
        view: View Details
        clone: Clone
        delete: Delete
        value: Value
        percent: Percent
        ends: Ends
        used: Used
        code: 'Code:'
      new:
        head_title: Add new coupon
        title: Add your coupon
        cancel: Cancel
      form:
        code: Code
        code_explanation: 'The identifier that the user will use in our platform'
        description: Description
        description_explanation: 'Write the coupon description'
        code_placeholder: Coupon Code
        quantity: Quantity
        value: Value
        percent_policy: Percent
        policy: Policy
        minimum_value: Minimum Value
        expires_at: Expires At
        starts_at: Starts At
        total_available: Total Available
        click_here: Click Here
        restrictions: Restrictions
        included_categories: Include Categories
        excluded_categories: Exclude Categories
        included_manufacturers: Include Manufacturers
        excluded_manufacturers: Exclude Manufacturers
        included_users: Include Users
        excluded_users: Exclude Users
        apply_on_sale: 'Apply to all products (even if they are already on sale)'
        cancel: Cancel
        save: Save
      show:
        code: 'Code:'
        created_at: Created At
        starts_at: Starts At
        expires_at: Expires At
        deleted_at: Deleted At
        description: Description
        minimum_value: 'Minimum Value:'
        total_available: 'Total Available:'
        back: Back
        used: 'Used:'
        discount: Discount
        coupon_details: Coupon Details
        description: Description
    partials:
      account_submenu:
        settings: Settings
        shipping: Shipping
        billing: Billing
        taxes: Taxes
        integrations: Integrations
      menu:
        products: Products
        orders: Orders
        questions: Questions
        marketing: Marketing
        account: Account
      header:
        go_back: Go Back
        logout: Logout
        manuals: Manuals
    suborders:
      partials:
        filters:
          status: 'Show: '
          order: 'Order by: '
          filter_by: 'Filter by: '
          in_process: 'In Process'
          returned: 'Returned'
          cancelled: 'Cancelled'
          all: All
          created_at: Created
          payment_received_at: Payment received
          date:
            from: 'From: '
            to: 'To: '
          filter: Filter
          export_csv: Download CSV
        suborder:
          order_status:
            in_process: En Proceso
            fulfilled: Entregado
            unfulfilled: Sin despachar
            returned: Devolución
            exchange: Cambio
            cancelled: Cancelado
            not_delivered: No Entregado
            refund: Devuelto
            delivered: Entregado
            shipped: Enviado
            booked: Reservado
            available: En proceso
            pending_cancellation: Pendiente a cancelacion
            approved: Aprobado
            declined: No Aprobado
            billed: Facturad
            posted: Contabilizado
          operation:
            refund: Devolucion
            exchange: Cambio
            normal: Normal
          total_products:
            one: '1 product'
            other: '%{count} products'
          subtotal: 'Subtotal: %{amount}'
          payment:
            pending: Pending
            collected: Collected
            cancelled: Cancelled
            refunded: Refunded
          shipment:
            unfulfilled: --
            in_process: Requested
            shipped: Shipped
            delivered: Delivered
            not_delivered: Not Delivered
            cancelled: Cancelled
        destination_address:
          title: Shipping Address
          phone: "Phone #: %{number}"
        payment:
          taxes: '+ collected Taxes: %{amount}'
          pending: Pending
          collected: Collected
          cancelled: Cancelled
          refunded: Refunded
          ticket: "Order paid with coupon: %{code}"
        items:
          properties:
            color: 'Color: '
            dimensions: 'Dimensions: '
            hardness: 'Property one: '
            length: 'Property two: '
            material: 'Property three: '
            size: 'Size: '
            percentage: 'Percentage: '
            payment_method: 'Payment Method: '
            noproperty: "Information about this product's properties not available."
          quantity_html: 'Qty. <strong>%{number}</strong>'
        unfulfilled:
          title: 'Unfulfilled Items (Quantity: %{quantity})'
          fulfilled_by_you: 'You are in charge to fulfill this order'
          fulfill_items: 'Fulfill Items'
        shipments:
          title: 'Shipment #%{id}'
        delivery:
          fulfilled_by_you_html: 'On <strong>%{date}</strong> you fulfilled this order'
          fulfilled_by_you_without_label: "You fulfilled this order but we don't have any information about the label you used or when you ship it"
          download_packing_list: 'Download Packing List'
        label:
          tracking_url: 'Tracking URL'
          cancel: 'Cancel Label'
          deliver: 'Mark as Delivered'
          shipping_documents: 'Download & print the following documents to ship the package:'
          print_label: 'Shipment Label (PDF)'
          forms_comment: (*) This is MANDATORY to clear Customs for International orders.
          forms_commercial_invoice_explanation: ' Commercial Invoice paperwork is required for most nondocument commodities. You will need 5 total Invoices. You must submit one signed original and 3 copies. We recommend placing a 5th inside of the package as well. For multiple-piece shipments to a single recipient, only one complete set is needed. You need 5 total copies. 1 signed placed inside the package. 1 signed original and 3 copies place on outside of box (Each carrier offers "Waybill Pouches").'
          other_label_formats: 'or you can download the label on other formats (for thermal printers):'
          zpl: ZPL
          epl2: EPL2
      index:
        head_title: Manage Shop - Orders
        search: 'Search by Order ID, Sku, purchase ID or Order title'
        order_id: Order ID
        date: Date
        customer: Customer
        details: Details
        payment_status: Payment/s
        shipment_status: Shipment/s
      show:
        title: "Order: #%{public_id} (%{title})"
        order_placed: 'Ordered on '
    shippings:
      print_packing_list:
        package_list: 'Packing List'
        order_number: 'Order Number:'
        ship_from: 'Ship From:'
        ship_to: 'Ship To:'
        order_date: 'Order Date:'
        item: 'Item'
        properties: 'Properties'
        description: 'Description'
        quantity: 'Qty'
    carts:
      show:
        head_title: Manage Shop - Cart
        title: Cart Details
        created: Created At
        updated: Last Update
        quick: Quick
        type: 'Type:'
        coupon: 'Coupon:'
        products:
          one: 'product'
          other: 'products'
        none: None
        shipping: Shipping
        payment_no_chose: Payment no chose
        incomplete: 'No completed'
        shipping_no_chose: Shipping Method no chose
        pickup_to: Pick Up To
        shipping_to: Destination Address
        full_name: 'Full Name:'
        address: 'Address:'
        location: 'Location:'
        zip: 'Zipcode:'
        shipping_method: Shipping Method
        phone: 'Phone:'
        carrier: 'Carrier:'
      index:
        all: All
        regular: Regular
        quick: Quick
        orders: Orders
        search: Search by ID
        filter:  'Filter:'
        head_title: Manage Shop - Left Carts
      cart:
        statuses:
          initialized: Initialized
          review: Review
          address: Shipping Address
          payment: Payment
          shipping_method: Shipping Method
          awaiting_confirm: Gateway
        product_deleted: The product associated was deleted
        customer: 'Customer:'
        quick: Quick
        shipping: 'Shipping: '
        view: View Details
        coupon: Coupon
        products:
          one: 'product'
          other: 'products'
    shipping_methods:
      edit:
        edit: Edit your service
        cancel: cancel
        update: Update
        or: o
        warehouses: Warehouses
        services: Services
      index:
        manage: Manage your services
        warehouses: Warehouses
        services: Services
        add: Add New Service
        submit: Save
      listing:
        name: Name
        warehouse: Warehouse
        countries: Countries
        price: Price
        warn: "Are you sure?"
        delete: Delete
        edit: Edit
        last_service_warn: "(*) You cannot delete your last service. Please, create another first."
        existing_services: Existing Services
        all: All
        many: 'To %{count} Countries'
        variable: Variable
      countries:
        countries: Countries
        countries_explanation: Restrict specific countries for shipping or leave empty for enable all countries
      warehouse:
        warehouse: Warehouse Address
        warehouse_explanation: Address from where your products will be delivered
      title:
        placeholder: Add Title
        explanation: 'This name will be displayed to user when purchasing'
        title: Title
      services:
        fixed: Fixed Price (Only %{country})
        easypost: Carriers
        oca: Oca
        ground: Ground (3-5 Business Days)
        1day: 1-2 Business Day
        2days: 2 Business Days
        3days: 3 Business Days
        intl_priority: Priority (Normal)
        intl_express: Express (Fast)
        usps: USPS
        ups: UPS
        fedex: Fedex
        method_exaplanation:  You can choose USA carriers like USPS, UPS or Fedex or you can manage on your own
        methods: Methods
        price: Price
        price_explanation: This price will be charged when user buy
        price_placeholder: 'e.g. 10.00'
        manage_price: I want to manage prices for each state
    defused:
      shipments:
        create_label_first: 'You first need to get the Shipping Label in order to afterwards get this documents.'
        extra_docs_title: 'This are the other documents that you need to download and print in order to properly make the shipment:'

  javascripts:
    lux:
      warehouses:
        choose_country: You should choose country
        required_fields_warn: You should fill all required fields
      products:
        pick_one: Pick one
        click_here: Click Here
        properties:
          dimensions: Dimensions
          hardness: Property one
          length: Property two
          size: Size
          color: Color
          material: Property three
          noproperty: Only property
          explanation: Please provide values with the corresponding unit
          regular_price: You set the product price in $0.0, is this correct? Are you sure you want to continue?
          at_least_three: At most three properties capable to choose
          category_required: Please, select a category to add
          percentage: Percentage
          payment_method: Payment Method
      orders:
        show:
          status_confirm: 'Are you sure you want to change the status to "%{status}"?'
      coupons:
        new:
          user_restriction_placeholder: 'Type an email...'
          fields_not_required: 'Please, fill all required fields.'

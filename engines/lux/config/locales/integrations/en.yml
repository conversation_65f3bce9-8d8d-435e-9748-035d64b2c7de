en:
  lux:
    integrations:
      index:
        already_syncing: There is already a task importing and syncing products
        authorized: 'Authorized since %{since}'
        remove: 'Remove'
        deleting: 'Removing...'
        title: 'Available Integrations for %{shop}'
        import_and_sync: Import and Synchronize Products
        stop: Stop Synchronization
      shopify:
        setup:
          read_products_html: '<strong>Read</strong> products, variants and collections'
          write_products_html: '<strong>Read</strong> and <strong>create</strong> products, variants and collections'
          read_orders_html: '<strong>Read</strong> orders, transactions and fulfillments'
          write_orders_html: '<strong>Read</strong> and <strong>create</strong> orders, transactions and fulfillments'
          read_fulfillments_html: '<strong>Read</strong> fulfillment services'
          read_shipping_html: '<strong>Read</strong> shipping rates'
      configurations:
        title: 'Setup'
        caption: "Select the defaults category and sports that will be associated with the integrated products in case that don't match with ours."
        category: 'Category'
        sports: 'Sports'
        save: 'Save'
      remove_options_modal:
        title: 'Remove Integration'
        message: "Please select what to do with your products on GoodPeople.com after removing the integration. Your items in %{integration_name} won't be affected."
        delete_integrables: 'Remove integration and delete all my items on GoodPeople.com.'
        remove_integrables_stock: 'Remove integration and put all my items with zero stock.'
        hide_shop: 'Remove integration and hide my GoodPeople.com shop.'
        nothing: "Remove integration and don't do anything with my items."
        remove: 'Remove'
    integration:
      products:
        import:
          trouble: 'We are already running a task for import and sync products, please wait until is finished to start another one.'
          async_message: "We'll notify you as soon as your products are successfully imported."
        fetch:
          head_title: "Products in %{service}"
          import_all: "Import all (%{count})"
          new_ones: New products to import
        product:
          no_description: No description given

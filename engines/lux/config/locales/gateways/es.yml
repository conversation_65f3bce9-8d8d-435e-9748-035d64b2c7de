es:
  lux:
    gateways:
      labels:
        lion:
          operation:
            '0':
              action: "Pasarán a buscar el paquete por:"
        oca_epak:
          operation:
            '291671':
              title: "Estandar Puerta a Puerta"
              action: "Van a pasar a retirar el paquete por:"
            '291672':
              title: "Estandar Sucursal a Puerta"
              action: "Van a pasar a retirar el paquete por:"
            '266769':
              title: "Sucursal a Puerta | Standard c/Seguro"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270768':
              title: "Sucursal a Puerta | Prioritaria c/pago en destino"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270769':
              title: "Sucursal a Puerta | Prioritaria"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270770':
              title: "Sucursal a Sucursal | Standard"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270771':
              title: "Sucursal a Sucursal | Standard c/pago en destino"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270772':
              title: "Sucursal a Sucursal | Prioritaria"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270773':
              title: "Sucursal a Sucursal | Prioritaria c/pago en destino"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
            '270774':
              title: "Logistica Inversa (Sucursal a Puerta)"
              action: "Debes dejar el paquete en la sucursal ubicada en:"
        package:
          size: "%{height} cm. x %{width} cm. x %{length} cm. (Ancho x Alto x Profundidad)"
          title: "Paquete:"
          weight_html: "<strong>Peso</strong>: %{weight} Kgs."

require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lu<PERSON>
  describe '#save' do
    let(:shop) { create(:shop) }
    let(:category) { create(:category) }
    let(:sport) { create(:sport) }
    let(:variants_count) { rand(4) + 1 }
    let(:manufacturer) { create(:manufacturer) }
    let(:product_form) { ProductForm.new(shop, nil, attrs_product, attrs_variants).save }

    context 'with invalid product attributes' do
      let(:attrs_product) { attributes_for(:product, shop: shop, title: '').with_indifferent_access }
      let(:attrs_variants) { [] }

      it { expect(product_form).to be_falsey }
    end

    context 'with valid product attributes' do
      let(:attrs_product) do
        attributes_for(
          :product,
          available_properties: available_properties,
          shop: shop,
          manufacturer_id: manufacturer.id,
          category_id: category.id,
          sport_ids: [sport.id]
        ).with_indifferent_access
      end

      context "with variants with color" do
        let(:available_properties) { [:color, :size] }
        let(:attrs_variants) do
          attributes_for_list(:variant, variants_count, :having_color_and_size_properties).map do |v|
            v.reject{|k,v| k == :created_at || k == :deleted_at }
          end
        end
        it { expect(product_form).to be_truthy }
        it 'create the product' do
          expect {
            product_form
          }.to change(Mkp::Product, :count).by(1)
        end
        it 'create the variant/s' do
          expect {
            product_form
          }.to change(Mkp::Variant, :count).by(variants_count)
        end
      end
      context "with colorless variants" do
        let(:available_properties) { [:size] }
        let(:attrs_variants) do
          attributes_for_list(:variant, variants_count, :having_size_property).map do |v|
            v.reject{|k,v| k == :created_at || k == :deleted_at }
          end
        end
        it { expect(product_form).to be_truthy }
        it 'create the product' do
          expect {
            product_form
          }.to change(Mkp::Product, :count).by(1)
        end
        it 'create the variant/s' do
          expect {
            product_form
          }.to change(Mkp::Variant, :count).by(variants_count)
        end
      end
      context 'update/create variants on a product' do
        let(:picture_id) { 123 }
        let(:color_hex)  { '#ffffff' }
        let(:available_properties) { [:color, :size] }
        let(:product) do
          create(
            :product,
            available_properties: available_properties,
            shop: shop,
            manufacturer_id: manufacturer.id,
            category_id: category.id,
            sport_ids: [sport.id]
          )
        end
        let(:variants) do
          create_list(
            :variant,
            2,
            :having_color_and_size_properties,
            color_hex: color_hex,
            picture_id: picture_id,
            product: product,
          )
        end
        let(:attrs_variants) do
          list = []
          list << new_variant_hash
          variants.each do |variant|
            list << attributes_for(:variant, properties: variant[:properties], id: variant.id)
          end
          list.map do |v|
            v.reject{|k,v| k == :created_at || k == :deleted_at }
          end
        end

        before do
          ProductForm.new(shop, product.id, attrs_product, attrs_variants).save
        end

        context "under the same color" do
          let(:new_variant_hash) do
            {
              properties: {
                size: "XXS",
                color: variants.sample.properties[:color]
              },
              quantity: 4
            }
          end

          it 'associate the corresponding picture' do
            expect(product.reload.variants.last.picture_id).to eq(picture_id)
          end
        end
        context "under another color" do
          let(:new_variant_hash) do
            {
              properties: {
                size: variants.sample.properties[:size],
                color: {
                  hex: "#000000",
                  name: "Black"
                }
              },
              quantity: 2
            }
          end

          it 'not associate a picture' do
            expect(product.reload.variants.last.picture_id).to be_nil
          end
        end
      end
    end
  end
end

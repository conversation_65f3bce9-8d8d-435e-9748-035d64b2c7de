require File.expand_path('../../../spec_helper.rb', __FILE__)

feature 'as a shop admin', type: :feature, js: true do
  setup :activate_authlogic
  set_up_currencies

  given(:shop_admin) { shop.merchants.first }
  given(:network) { shop.network.downcase}

  skip 'creating a service on AR network' do
    given(:shop) { create(:shop, :with_merchant) }
    given(:warehouse) { create(:warehouse, shop: shop) }

    scenario 'with CABA address' do
      visit '/'
      login_via_capybara shop_admin
      visit shop_shipping_methods_path(network, shop.to_param)
    end

  end
end

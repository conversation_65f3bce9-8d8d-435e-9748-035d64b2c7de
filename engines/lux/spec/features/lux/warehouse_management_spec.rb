require File.expand_path('../../../spec_helper.rb', __FILE__)

feature 'as a shop admin', type: :feature, js: true do
  setup :activate_authlogic
  set_up_currencies

  given(:shop_admin) { shop.merchants.first }
  given(:network) { shop.network.downcase}
  given(:warehouses_page) { Lux::WarehousesPage.new(country, state) }

  skip 'creating a warehouse on AR network', type: :feature, js: true do
    given(:warehouse_attrs) { build_stubbed(:warehouse, :from_capital_federal) }
    given(:shop) { create(:shop, :with_merchant) }
    given(:country) { nil }
    given(:state) { warehouse_attrs.state }

    scenario 'with CABA address' do
      visit '/'
      login_via_capybara shop_admin
      visit shop_warehouses_path(network, shop.to_param)
      warehouses_page.create warehouse_attrs
      warehouses_page.validate_warehouse_presence(warehouse_attrs)
    end

  end

  skip 'creating a warehouse on US network' do
    given(:shop) { create(:shop, :with_merchant, :us_network) }

    feature 'with Canada address' do
      given(:warehouse_attrs) { build_stubbed(:warehouse, :from_ca_on_ab_state) }
      given(:country) { 'Canada' }
      given(:state)  { 'Alberta' }
      scenario '' do
        visit '/'
        login_via_capybara shop_admin
        visit shop_warehouses_path(network, shop.to_param)
        warehouses_page.create warehouse_attrs
        warehouses_page.validate_warehouse_presence(warehouse_attrs)
      end
    end

    feature 'with USA address' do
      given(:warehouse_attrs) { build_stubbed(:warehouse, :from_usa_on_ca_state) }
      given(:country) { 'United States' }
      given(:state)  { 'California' }
      scenario '' do
        visit '/'
        login_via_capybara shop_admin
        visit shop_warehouses_path(network, shop.to_param)
        warehouses_page.create warehouse_attrs
        warehouses_page.validate_warehouse_presence(warehouse_attrs)
      end
    end
  end
end

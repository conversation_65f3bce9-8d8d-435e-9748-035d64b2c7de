require File.expand_path('../../../spec_helper.rb', __FILE__)

feature 'As a shop admin', js: true do
  include_context "Logged as Shop Admin"
  given(:products_page) { Lux::ProductsPage.new }
  given(:products_url) { shop_products_path(network: network.downcase, shop_id: shop.slug) }

  context 'on a US shop' do
    given(:network)     { 'US' }

    context 'on the products index' do
      context 'no products' do
        background do
          products_page.load(network: network.downcase, shop_id: shop.slug)
          set_network(network)
        end
        scenario 'display products page' do
          expect(products_page).to be_displayed
          expect(products_page.current_path).to eq(products_url)
          expect(products_page).to have_product_list
          expect(products_page.product_list).to have_empty_products_msg
        end
      end

      context 'with products' do
        given(:nbr_products) { rand(5) + 1 }
        background do
          create_list(:product, nbr_products, :with_size_and_color_properties, shop: shop)
          products_page.load(network: network.downcase, shop_id: shop.slug)
          set_network(network)
          products_page.wait_for_product_list
        end
        scenario 'display products page' do
          sleep(5)
          expect(products_page).to be_displayed
          expect(products_page).to have_product_list
          expect(products_page.product_list).to have(nbr_products).products
        end
      end
    end

    context 'create new product' do
      given(:product_form_page) { Lux::ProductFormPage.new }

      background do
        products_page.load(network: network.downcase, shop_id: shop.slug)
        set_network(network)
        products_page.new_product_button.click
      end
      scenario 'display new product form page' do
        expect(product_form_page).to be_displayed
        expect(product_form_page).to have_form
      end
    end

  end
end

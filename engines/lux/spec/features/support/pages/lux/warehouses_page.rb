# require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  class WarehousesPage < Struct.new(:country, :state)
    include Capybara::DSL
    include RSpec::Matchers

    def create(warehouse)
      warehouse ||= FactoryGirl.build_stubbed(:warehouse)

      within warehouse_form do
        fill_in 'warehouse[first_name]', with: warehouse.first_name
        fill_in 'warehouse[address]',    with: warehouse.address
        fill_in 'warehouse[city]',       with: warehouse.city
        fill_in 'warehouse[zip]',        with: warehouse.zip
        fill_in 'warehouse[telephone]',  with: warehouse.telephone

        select_country(country) if country
        select_state(state) if state

        warehouse_submit_form.click
      end
    end

    def validate_warehouse_presence(warehouse)
      within warehouses_list do
        expect(page).to have_content warehouse.first_name
        expect(page).to have_content warehouse.address
        expect(page).to have_content warehouse.city
        expect(page).to have_content warehouse.zip
        expect(page).to have_content warehouse.telephone
        expect(page).to have_content warehouse.state
        expect(page).to have_content country
      end
    end

    private

    def select2(value, css_id)
      find(css_id).find('.select2-choice').click()
      find(:xpath, '//body').find('.select2-drop li', text: value).click
    end

    def select_country(country)
      select2(country, '#s2id_warehouse_country')
    end

    def select_state(state)
      select2(state, '#s2id_province')
    end

    def warehouse_form
      find('#new-warehouse')
    end

    def warehouses_list
      find('.warehouses')
    end

    def warehouse_submit_form
      find('.button')
    end
  end
end

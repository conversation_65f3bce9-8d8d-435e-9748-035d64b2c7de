require File.expand_path('../../../../spec_helper.rb', __FILE__)

shared_context "Logged as Shop Admin" do
  set_up_currencies
  given(:shop)        { create(:shop, :visible, :with_settings, :with_merchant, network: network) }
  given(:shop_admin)  { shop.merchants.first }

  background do
    authlogic_controller = activate_authlogic
    UserSession.create shop_admin
    allow_any_instance_of(Authlogic::ControllerAdapters::RailsAdapter).to receive(:cookies) do |instance|
      authlogic_controller.cookies
    end
    I18n.locale = Network[network].locale
  end
end

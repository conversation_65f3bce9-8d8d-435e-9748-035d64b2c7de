require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  module ProductImport
    describe AttributesBuilder do
      set_up_currencies

      describe '#build' do
        let!(:shop) { create(:shop) }
        let!(:gender) { create(:gender, name: 'Male') }
        let!(:manufacturer) { create(:manufacturer) }
        let!(:category) { create(:category) }
        let!(:sport) { create(:sport) }

        context 'when updating' do
          let!(:product) do
            create(:product,sports: [sport], category: category,
                    manufacturer: manufacturer, shop: shop, genders: [gender])
          end
          let!(:variant_1) do
            create(:variant, :having_color_and_size_properties, product: product)
          end
          let!(:variant_2) do
            create(:variant, :having_color_and_size_properties, product: product)
          end
          let(:new_stock) { variant_2.quantity + 1 }
          let!(:new_price) { product.price + 1 }
          let(:product_list) do
            csv_options = { headers: :first_row,
                            header_converters: [:downcase, :symbol] }

           csv = CSV.new(ProductExporter.perform(Product.where(id: product.id).scoped),csv_options).read
           csv[1][:stock] = new_stock
           csv[1][:price] = new_price
           csv[0][:price] = new_price
           csv
          end
          let!(:mapper) { Mapper.new(product_list.headers, shop) }
          it 'Builds products and variants attributes' do
            builder = AttributesBuilder.new(mapper, shop)
            product_list.each { |row| builder.add(row) }
            products_attributes = builder.build

            expect(products_attributes.first[:regular_price]).to eq(new_price.to_f)
            expect(products_attributes.first[:variants_attributes][1][:quantity]).to eq(new_stock)
          end
        end

        context 'when importing' do
          let(:manufacturer_name) { manufacturer.name }
          let!(:dummy_product) do
            create(:product, sports: [sport], category: category,
                    manufacturer: manufacturer, shop: shop, genders: [gender])
          end
          let(:product_list) do
            csv_data =
              ProductExporter::HEADER.join(',') + "\n" +
              ",,BSA,,Buzo Standard,Buzo con capucha.,#{category.id},#{sport.name},Male,Color,Amarillo,Size,M,,,#{manufacturer_name},100,,50,,,millimeters,2.0,kilograms,2\n" +
              ",,BSR,,Buzo Standard,Buzo con capucha.,#{category.id},#{sport.name},Male,Color,Rojo,Size,S,,,#{manufacturer_name},100,,50,,,millimiteres,2.0,kilograms,4"

            csv_options = { headers: :first_row,
                            header_converters: [:downcase, :symbol] }

            CSV.new(csv_data, csv_options).read
          end
          let!(:mapper) { Mapper.new(product_list.headers, shop) }

          it 'Builds products and variants attributes' do
            builder = AttributesBuilder.new(mapper, shop)
            product_list.each { |row| builder.add(row) }
            products_attributes = builder.build
            expected_product_attrs = { title: "Buzo Standard",
                                       description: "Buzo con capucha.",
                                       shop_id: shop.id,
                                       category_id: category.id,
                                       sport_ids: [sport.id],
                                       gender_ids: [gender.id],
                                       manufacturer_id: manufacturer.id,
                                       regular_price: "100",
                                       available_on: Date.today,
                                       currency_id: 1,
                                       width: 0.0,
                                       height: 50.0,
                                       length: 0.0,
                                       weight: 2.0,
                                       mass_unit: "kilograms",
                                       length_unit: "millimeters",
                                       available_properties: [:color, :size],
                                       handle_stock: true }

            expected_variant_1_attrs = { sku: 'BSA', quantity: 2, properties: { color: { name: 'Amarillo', hex: '#ffda10'}, size: 'M' } }
            expected_variant_2_attrs = { sku: 'BSR', quantity: 4, properties: { color: { name: 'Rojo', hex: '#da251d'}, size: 'S' } }
            expected_product_attrs[:variants_attributes] = [expected_variant_1_attrs, expected_variant_2_attrs]

            expect(products_attributes).to eq([expected_product_attrs])
          end
        end
      end
    end
  end
end

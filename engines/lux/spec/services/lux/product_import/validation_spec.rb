require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  module ProductImport
    describe Validation do
      let(:shop) { create(:shop) }
      let(:manufacturer) { create(:manufacturer) }
      let(:category) { create(:category) }
      let(:sport_ids) { create_list(:sport, 1).map(&:id) }

      context 'creating' do
        let!(:product){ create(:product, :with_variant, manufacturer_id: manufacturer.id)}
        let(:product_attrs) do
          attributes_for(
            :product,
            shop: shop,
            manufacturer_id: manufacturer.id,
            category_id: category.id,
            sport_ids: sport_ids
          ) { |attrs| attrs.delete(:created_at) }
        end

        let(:variant_attrs) do
          attributes_for(:variant) { |attrs| attrs.delete(:created_at) }
        end
        let(:validation) do
          Validation.of(product_attrs,variant_attrs,row,shop, :create).keys
        end
        let(:row) do
          {
            categories: ['one'],
            sports: ['second'],
            genders: 'some',
            manufacturer: 'another'
          }
        end

        context 'with invalid product and variant' do
          before do
            product_attrs[:title] = nil
            variant_attrs[:quantity] = nil
          end

          describe '.of' do
            it do
              expect(validation).to match_array([:title, :quantity])
            end
          end
        end

        context 'with product and variant' do
          describe '.of' do
            it { expect(validation).to be_empty }
          end
        end
      end

      context 'updating' do
        skip 'non changing'
        skip 'changing'
        let(:product) { create(:product, shop: shop) }
        let(:variant) { create(:variant, :having_color_property, product: product) }
        let(:product_attrs) do
          product.attributes.except('slug',
                                    'created_at',
                                    'updated_at',
                                    'relevance_score',
                                    'score_multiplier').symbolize_keys
        end
        let(:variant_attrs) do
          variant.attributes.except('slug',
                                    'created_at',
                                    'updated_at',
                                    'visible',
                                    'deleted_at',
                                    'picture_id',
                                    'availability_score').symbolize_keys
        end
        context 'with invalid product and variant' do
          before do
            product_attrs[:title] = nil
            variant_attrs[:quantity] = nil
          end

          describe '.of' do
            it { expect(Validation.of(product_attrs, variant_attrs, { categories: [''], sports: [''] }, shop, :update).keys).to match_array([:quantity, :title]) }
          end
        end

        context 'with product and variant' do
          describe '.of' do
            it { expect(Validation.of(product_attrs, variant_attrs, { categories: [''], sports: [''] }, shop, :update)).to be_empty }
          end
        end
      end

    end
  end
end

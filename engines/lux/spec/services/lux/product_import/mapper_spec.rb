require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  module ProductImport
    describe Mapper do
      let!(:currency) { create(:currency, network: 'AR') }
      let!(:shop) { create(:shop) }
      let!(:manufacturer) { create(:manufacturer) }
      let!(:category) { create(:category) }
      let!(:sport) { create(:sport) }
      let!(:mapper) { Mapper.new(row.keys, shop) }
      let!(:row) do
        { sku: 'BSA', title: 'Buzo Standard', description: 'Buzo con capucha.',
          category: "#{category.id}", sports: sport.name, genders: 'Men',
          property_name_1:  'Color', property_value_1: 'Amarillo',
          property_name_2: 'Size', property_value_2: 'M', property_name_3: '',
          property_value_3: '', manufacturer: manufacturer.name, price: 100,
          available_on: nil, height: nil, length: nil, width: nil, weight: nil, mass_unit: 'kilograms' , length_unit: 'millimeters', stock: 2 }
      end

      describe '#to_product_attributes' do
        describe 'for update' do
          it 'Maps row to product attributes' do
            expected_product_attrs = { regular_price: 100, height: 0.0, width: 0.0, length: 0.0, weight: 0.0, length_unit: 'millimeters', title: 'Buzo Standard', mass_unit: 'kilograms'}
            expect(mapper.to_product_attributes(row, true)).to eq(expected_product_attrs)
          end
        end

        describe 'non for update' do
          it 'Maps row to product attributes' do
            expected_product_attrs = { shop_id: shop.id,
                                       manufacturer_id: manufacturer.id,
                                       category_id: category.id,
                                       sport_ids: [sport.id],
                                       title: 'Buzo Standard',
                                       description: 'Buzo con capucha.',
                                       available_on: Date.today,
                                       regular_price: 100.0,
                                       currency_id: 1,
                                       available_properties: [:color, :size],
                                       mass_unit: 'kilograms',
                                       gender_ids: [],
                                       handle_stock: true,
                                       height: 0.0,
                                       length: 0.0,
                                       width:  0.0,
                                       weight: 0.0,
                                       length_unit: 'millimeters' }

            expect(mapper.to_product_attributes(row)).to eq(expected_product_attrs)
          end
        end
      end

      describe '#to_variant_attributes' do
        context 'non for update' do
          it 'Maps row to variant attributes' do
            expected_properties_attr = { color: { name: 'Amarillo', hex: '#ffda10'},
                                         size: 'M' }
            expected_variant_attrs = { sku: 'BSA',
                                       quantity: 2,
                                       properties: expected_properties_attr }

            expect(mapper.to_variant_attributes(row)).to eq(expected_variant_attrs)
          end
        end
        context 'for update' do
          it 'Maps row to variant attributes' do
            expected_properties_attr = { color: { name: 'Amarillo', hex: '#ffda10'},
                                         size: 'M' }
            expected_variant_attrs = { sku: 'BSA',
                                       quantity: 2 }

            expect(mapper.to_variant_attributes(row, true)).to eq(expected_variant_attrs)
          end
        end
      end
    end
  end
end

require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lux
  describe ProductExporter do
    describe '.perform' do
      let(:product_1) { create(:lux_product) }
      let(:product_2) { create(:lux_product) }
      let!(:variants_1) { create_list(:variant, 2, :having_color_and_size_properties, product: product_1) }
      let!(:variants_2) { create_list(:variant, 2, :having_color_property, product: product_2) }
      let(:expected_variant) do
        variant = ([variants_1.first, variants_2.first]).sample
        properties = variant.properties.flat_map do |k,v|
          [ k.to_s, "#{k == :color ? v[:name] : v.to_s}"]
        end.in_groups_of(6).flatten
        [
          variant.product.slug,
          variant.id.to_s,
          variant.sku,
          variant.gp_sku,
          variant.product.title,
          variant.product.description,
          variant.product.category.try(:name),
          variant.product.sports.map(&:name).join('-'),
          variant.product.genders.map(&:name).join('-')
        ].concat(properties).concat([
          variant.product.manufacturer.name,
          variant.product.price.to_f.to_s,
          variant.product.available_on.strftime('%m/%d/%Y'),
          variant.product.height.to_s,
          variant.product.length.to_s,
          variant.product.width.to_s,
          variant.product.length_unit,
          variant.product.weight.to_s,
          variant.product.mass_unit,
          variant.quantity.to_s
        ])
      end
      let(:products){ Lux::Product.scoped }
      before do
        @csv =  CSV.parse(ProductExporter.perform(products))
      end
      it do
        expect(@csv.first).to eq(ProductExporter::HEADER)
        expect(@csv.size).to eq(products.map(&:variants).flatten.size + 1)
        expect(@csv).to include(expected_variant)
      end
    end
  end
end

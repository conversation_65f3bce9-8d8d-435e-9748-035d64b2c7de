require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lux
  describe ApplicationController, type: :controller do
    include_lux_routes

    let(:get_out_url){'http://test.host/login'}
    let(:network){ 'AR' }

    describe "BeforeFilters" do
      describe "#is_current_user_merchant?" do
        context "with no current_user" do
          before { get :home, network: network }
          it { expect(response).to redirect_to get_out_url }
        end
        context "with current user" do
          context "as merchant role" do
            let(:shop) { create(:shop) }
            before do
              login_as shop.merchants.first
            end
            it do
              get :home, shop_id: shop.id, network: shop.network
            end
          end
          context "who isn't merchant" do
            let(:user) { create(:user) }
            before do
              UserSession.create user
              get :home, network: network
            end
            it { expect(response).to redirect_to get_out_url }
          end
        end
      end
      describe "#set_shop_if_exists" do
        context "with current_user at least one shop" do
          let(:shop){ create(:shop, :with_merchant) }
          before do
            login_as shop.merchants.first
            get :home, network: shop.network
          end
          it { expect(response).to redirect_to shop_products_path(shop, network: shop.network.downcase)}
        end
        context "with current_user without any shops" do
          let(:merchant) { create(:merchant) }
          before do
            login_as merchant
            get :home, network: network
          end
          it { expect(response).to redirect_to('/shop') }
        end
      end
    end
  end
end

require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lu<PERSON>
  describe AnswersController, type: :controller do
    include_lux_routes

    let(:shop) { create(:shop, :with_merchant) }

    before(:each) do
      login_as shop.merchants.first
    end

    describe "POST to #create" do
      let(:description) {"The answer description"}
      let(:product)     {create(:product, shop: shop)}
      let(:question)    {create(:question, product: product)}
      before do
        expect_any_instance_of(Mkp::Answer).to receive(:send_user_notification)
        post :create, shop_id: shop.id, question_id: question.id, product_id: question.product.id, :format => 'js', description: description, network: shop.network
      end
      it do
        expect(response.status).to eq 200
        expect(question.answer.description).to eq description
      end
    end
  end
end

require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lux
  describe AccountsController do
    include_lux_routes

    let(:network) { 'US' }
    let(:shop) { create(:shop, :with_merchant, network: network) }
    let(:current_merchant) { shop.merchants.first }

    before do
      login_as(current_merchant)
    end

    describe 'GET to #show' do
      before do
        request.env['HTTPS'] = 'on'

        get :show,
            network: network.downcase,
            shop_id: shop.id,
            protocol: SECURE_PROTOCOL
      end

      it { expect(response.status).to eq(200) }
    end

    describe 'PUT to #update' do
      let(:new_title) { 'New Title' }
      let(:new_signer) { 'Some Name' }

      before do
        request.env['HTTPS'] = 'on'

        put :update,
            network: network.downcase,
            shop_id: shop.id,
            shop: { title: new_title },
            shop_setting: { customs_signer_name: new_signer },
            protocol: SECURE_PROTOCOL
      end

      it { expect(shop.reload.customs_signer_name).to eq new_signer }
      it { expect(shop.reload.title).to eq(new_title) }
      it { expect(response).to redirect_to shop_account_url(shop.network.downcase, shop.id) }
    end
  end
end

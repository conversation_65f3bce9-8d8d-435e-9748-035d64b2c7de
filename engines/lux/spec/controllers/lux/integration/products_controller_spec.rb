require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  describe Integration::ProductsController, type: :controller do
    include_lux_routes

    describe 'Shopify' do
      let(:network) { 'US' }
      let(:shop) { create(:shop, :with_merchant, network: network) }
      let(:current_merchant) { shop.merchants.first }

      let!(:shopify_integration) { create(:shopify_integration, shop: shop) }

      before do
        setup_environment
        login_as(current_merchant)

        allow(ShopifyAPI::Product).to receive(:find).and_return([shopify_object])
        controller.instance_variable_set(:@available_integrations, [Mkp::Integration::Shopify])
        controller.instance_variable_set(:@authorized_integrations, [shopify_integration])
      end

      describe 'Internals' do
        before do
          request.env['HTTPS'] = 'on'
          get :import_and_sync, integration_name: shopify_integration.simple_type,
                                shop_id: shop.id,
                                network: network.downcase,
                                protocol: SECURE_PROTOCOL
        end
        it 'properly handle the integration before performing import or sync actions' do
          expect(assigns(:integration)).to be_a(Mkp::Integration::Shopify)
          expect(response.status).to eq(302)
          expect(response).to redirect_to shop_integrations_url(shop_id: shop.id, network: network.downcase, protocol: SECURE_PROTOCOL)
        end
      end
    end

    # Helper methods.

    def setup_environment
      create(:category, network: shop.network)
      create(:gender, :child, network: shop.network)
      create(:manufacturer)
      create(:sport)
    end

    def shopify_object
      ShopifyAPI::Base.activate_session(shopify_session)
      ShopifyAPI::Product.new(JSON.parse(shopify_json_object))
    end

    def shopify_json_object
      fixture_file = File.read('engines/lux/spec/fixtures/shopify_product_json.json')
      JSON.load(fixture_file)
    end

    def shopify_session
      ShopifyAPI::Session.new(
        "#{shopify_integration.account_name}.myshopify.com", shopify_integration.access_token
      )
    end
  end
end

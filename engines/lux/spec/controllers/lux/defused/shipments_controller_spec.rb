require File.expand_path('../../../../spec_helper.rb', __FILE__)

module Lux
  module Defused
    describe ShipmentsController do
      include_lux_routes

      let(:shop) { create(:shop, :with_merchant, network: 'US') }
      let(:suborder) { create(:suborder, :with_1_item, shop: shop) }
      let(:shipment) { create(:shipment, :easypost, with_label: true, items: suborder.items) }
      let(:token) do
        encryptor = NaiveTokenEncryptor.new(
          suborder.encryptor_key,
          suborder.encryptor_initialization_vector
        )

        encryptor.encrypt_and_encode(shipment.id.to_s)
      end
      let(:invalid_token) { "ZXZlcnlvbmUgc3RlYWxz\n" }

      before(:each) do
        allow_any_instance_of(Mkp::Suborder).to receive(:shipment).and_return(shipment)
      end

      describe "GET #retrieve_shipment_label" do
        context "valid token" do
          before do
            get(:retrieve_shipment_label, suborder_id: suborder.id, token: token)
          end

          it { expect(response.status).to eq(302) }
          it { expect(response).to redirect_to(shipment.label.url) }
        end

        context "invalid token" do
          before do
            get(:retrieve_shipment_label, suborder_id: suborder.id, token: invalid_token)
          end

          it { expect(response.status).to eq(200) }
          it { expect(response.body).to be_blank }
        end
      end

      describe "GET #retrieve_shipment_packing_list" do
        context "valid token" do
          before do
            get(:retrieve_shipment_packing_list, {
              suborder_id: suborder.id,
              token: token,
              format: :pdf
            })
          end

          it { expect(response.status).to eq(200) }
          it { expect(response.body).to be_present }
        end

        context "invalid token" do
          before do
            get(:retrieve_shipment_packing_list, {
              suborder_id: suborder.id,
              token: invalid_token,
              format: :pdf
            })
          end

          it { expect(response.status).to eq(200) }
          it { expect(response.body).to be_blank }
        end
      end
    end
  end
end

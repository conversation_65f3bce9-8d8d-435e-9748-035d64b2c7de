require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lux
  describe WarehousesController, type: :controller do
    include_lux_routes
    let(:shop) { create(:shop, :with_merchant) }

    before(:each) do
      login_as shop.merchants.first
    end

    describe "POST to #create" do
      let(:warehouse_attrs) { attributes_for(:warehouse) }
      it{ expect { post :create,
                        shop_id: shop.id,
                        warehouse: warehouse_attrs, network: shop.network}.to change{Mkp::Warehouse.count}.by(1) }
    end
    describe "GET to #index" do
      let(:warehouse){create(:warehouse, shop: shop)}
      it do
        get :index, shop_id: shop.id, network: shop.network
        expect(response.status).to eq 200
      end
    end
    describe "DELETE to #destroy" do
      let(:warehouse){create(:warehouse, shop: shop)}
      before{delete :destroy, shop_id: shop.id, id: warehouse.id, network: shop.network}
      it {expect(warehouse.reload.deleted_at.present?).to be_truthy}
    end
  end
end

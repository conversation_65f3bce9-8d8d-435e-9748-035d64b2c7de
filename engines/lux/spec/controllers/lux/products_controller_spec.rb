require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lux
  describe ProductsController, type: :controller do
    include_lux_routes

    let(:shop) { create(:shop, :with_merchant) }

    before(:each) do
      login_as shop.merchants.first
    end

    describe "GET to #index" do
      describe 'with html format' do
        let(:product) { create(:product, shop: shop) }

        before do
          allow(controller).to receive(:is_current_user_merchant?)
          get :index, shop_id: product.shop.to_param, network: shop.network
        end

        it { expect(response.status).to eq(200) }
      end

      describe 'with csv format' do
        let(:expected_csv_report) { 's,o,m,e' }

        before do
          expect(ProductExporter).to receive(:perform).and_return(expected_csv_report)
          get :index, format: 'csv', shop_id: shop.id, network: shop.network
        end

        it do
          expect(response.body).to eq(expected_csv_report)
          expect(response.content_type.to_s).to match('text/csv')
        end
      end
    end

    describe "GET to #show" do
      let(:product) { create(:product, shop: shop) }

      before { get :show, id: product.to_param, shop_id: shop.to_param, network: shop.network }

      it { expect(response.status).to eq(200) }
    end

    describe "GET to #new" do
      let(:product) { create(:product, shop: shop) }

      before { get :new, shop_id: product.shop.to_param, network: shop.network }

      it { expect(response.status).to eq(200) }
    end

    describe "GET to #edit" do
      let(:product) { create(:product, shop: shop) }

      before { get :edit, shop_id: product.shop.to_param, id: product.to_param, network: shop.network }

      it { expect(response.status).to eq(200) }
    end

    describe "POST to #create" do
      let(:manufacturer) { create(:manufacturer) }
      let(:category) { create(:category)}
      let(:sport_ids) { create_list(:sport, 1).map(&:id) }
      let(:new_product_attrs) do
        attributes_for(
          :product,
          shop: shop,
          manufacturer_id: manufacturer.id,
          category_id: category.id,
          sport_ids: sport_ids
        )
      end

      before do
        post :create, shop_id: shop.to_param, product: new_product_attrs, network: shop.network
        @product = Mkp::Product.last
      end

      it { expect(response).to redirect_to shop_product_url(shop.network.downcase, shop.to_param, @product.to_param) }
    end

    describe "PUT to #update" do
      context "without new variants" do
        let(:product) { create(:product, shop: shop) }

        before do
          put :update, shop_id: shop.id, id: product.id, network: shop.network,
              product: { title: 'I made a mistake', available_properties: ["color"]}
        end

        it do
          expect(response).to \
            redirect_to shop_product_url(shop.network.downcase, shop.to_param, product.reload.to_param)
        end
      end
    end

    describe "DELETE to #destroy" do
      let(:product) { create(:product, shop: shop) }

      before { delete :destroy, shop_id: shop.id, id: product.id, network: shop.network }

      it { expect(Mkp::Product.unscoped.find_by_id(product.id)).to be_nil }
    end

    describe "GET to #clone" do
      let(:product) { create(:product) }

      before { get :clone, shop_id: shop.id, id: product.id, network: shop.network }

      it { expect(response.status).to eq 200 }
    end
  end
end

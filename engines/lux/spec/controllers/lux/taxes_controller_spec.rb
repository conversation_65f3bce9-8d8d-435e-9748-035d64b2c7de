require File.expand_path('../../../spec_helper.rb', __FILE__)

module Lu<PERSON>
  describe TaxesController, type: :controller do
    include_lux_routes

    before(:each) do
      login_as shop.merchants.first
    end

    describe 'GET to #index' do
      let!(:shop) { create(:shop, :with_merchant, :us_network, :with_settings, settings_traits: [:have_taxes]) }
      before { get :index, shop_id: shop.id, network: shop.network }
      it { expect(response.status).to eq(200) }
    end

    describe 'PUT to #update' do
      let(:shop) { create(:shop, :with_merchant, :us_network) }
      let(:tax_rates) { { 'FL' => {'rate' => '5.11', 'apply_to_shipping' => 'true' } } }
      before { put :update, shop_id: shop.id, tax_rates: tax_rates, network: shop.network }
      it { expect(shop.reload.tax_rates).to eq tax_rates }
    end
  end
end

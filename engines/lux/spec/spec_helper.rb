require 'spec_helper'
require_relative 'factories/products.rb'

LUX_ROOT = File.join(File.dirname(__FILE__), '../')

# Requires supporting ruby files with custom matchers and macros, etc,
# in spec/support/ and spec/steps/ and its subdirectories.
Dir[File.join(LUX_ROOT, 'spec/features/support/**/*.rb'),
    File.join(LUX_ROOT, 'spec/support/**/*.rb')].each {|f| require f}

RSpec.configure do |config|
  Lux::Engine.routes.default_url_options[:host] = 'test.host'
  config.extend LuxRoutesIncluder
  config.include Logginator
  config.include Lux::Engine.routes.url_helpers
end

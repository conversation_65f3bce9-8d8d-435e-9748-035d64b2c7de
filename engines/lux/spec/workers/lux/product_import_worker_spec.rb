require 'spec_helper'

module Lux
  describe ProductImportWorker, sidekiq: :inline do
    set_up_currencies
    let(:network) { 'AR' }
    let(:shop) { create(:shop, network: network) }

    context 'when updating' do
      context 'with valid data' do
        describe '#perform' do
          let(:product) { create(:product) }
          let!(:variant_1) do
            create(:variant, :having_color_and_size_properties, product: product)
          end
          let!(:variant_2) do
            create(:variant, :having_color_and_size_properties, product: product)
          end
          let(:new_stock) { variant_2.quantity + 1 }
          let!(:new_price) { product.price + 1 }
          let!(:csv_to_update) do
            csv_options = { headers: :first_row,
                            header_converters: [:downcase, :symbol] }
            csv = CSV.new(ProductExporter.perform(Product.where(id: product.id).scoped),csv_options).read
            csv[1][:stock] = new_stock
            csv[1][:price] = new_price
            csv[0][:price] = new_price
            csv
          end
          before do
            create(:category)
            create(:sport, name: '<PERSON><PERSON>')
            create(:manufacturer, name: '<PERSON><PERSON><PERSON>ple')
            create(:gender, name: 'Men')
            expect_any_instance_of(ProductImportWorker).to receive(:read_csv) { csv_to_update }
          end

          it 'Change a product and a variant based on csv file' do
            ProductImportWorker.perform_async('', shop.id)
            ProductImportWorker.drain
            expect(product.reload.regular_price).to eq(new_price.to_f)
            expect(product.handle_stock).to eq(true)
            expect(variant_2.reload.quantity).to eq(new_stock)
          end
        end
      end
    end

    context 'when importing' do
      context 'with valid data' do
        describe '#perform' do
          let!(:product){ create(:product, :with_variant, shop: shop)}
          let(:product_list_path) do
            File.join(Engine.root, 'spec', 'fixtures', 'product_list_for_import.csv')
          end

          before do
            create(:category)
            create(:sport, name: 'Enduro')
            create(:manufacturer, name: 'GoodPeople')
            create(:gender, name: 'Men')
          end

          it 'Creates a product, based on the CSV file' do
            expect do
              ProductImportWorker.perform_async(product_list_path, shop.id)
              ProductImportWorker.drain
            end.to change{ Mkp::Product.count }.by(1)
          end

          it 'Creates variants, based on the CSV file' do
            expect do
             ProductImportWorker.perform_async(product_list_path, shop.id)
             ProductImportWorker.drain
            end.to change{ Mkp::Variant.count }.from(1).to(3)
          end
        end
      end

      context 'with invalid data' do
        describe '#perform' do
          let!(:product){ create(:product, :with_variant, shop: shop)}
          let(:product_list_path) do
            File.join(Engine.root, 'spec', 'fixtures', 'product_list_invalid.csv')
          end

          before do
            create(:category)
            create(:sport, name: 'Enduro')
            create(:manufacturer, name: 'GoodPeople')
            ProductImportWorker.perform_async(product_list_path, shop.id)
            ProductImportWorker.drain
          end

          it { expect(Mkp::Product.count).to eq(1) }
          it { expect(Mkp::Variant.count).to eq(1) }
        end
      end
    end
  end
end

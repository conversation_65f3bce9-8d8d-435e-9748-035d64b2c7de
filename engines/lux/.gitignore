*.rbc
*.sassc
*.rdb
.sass-cache
capybara-*.html
.rspec
/.bundle
/vendor/bundle
/log/*
/tmp/*
/db/*.sqlite3
/public/system/*
/coverage/
/spec/tmp/*
**.orig
rerun.txt
pickle-email-*.html

/.bundle

/db/schema.rb
/db/*.sqlite3

/log/*.log
/tmp

/.DS_Store
/config/database.yml

.bundle
.sass-cache/
.DS_Store
config/database.yml
config/facebooker.yml
config/environments/development.rb
config/development.sphinx.conf
config/production.sphinx.conf
config/sphinx.yml
config/mongrel_cluster.yml
db/schema.rb
log/*.log
log
tmp
coverage/
nbproject
public/attachments
public/assets
public/company_logos
public/file_values
public/image_values
public/item
public/pdfs
public/values
public/stylesheets/gp*.css
public/stylesheets/good*
public/*/cache/*
/test
*~
*#
.#*
*.swp
fixtures/support/enhanced.rb
public/system
*.sqlite3*
*.sql
config/development.sphinx.conf
config/production.sphinx.conf
solr
*.komodoproject
vendor/plugins/rails-dev-boost/*
vendor/cache
Gemfile.lock
Guardfile
.redcar
.rvmrc
.gemrc
.powenv
.powrc

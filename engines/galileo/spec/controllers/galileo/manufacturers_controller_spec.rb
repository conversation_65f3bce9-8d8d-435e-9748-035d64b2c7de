require File.expand_path('../../../spec_helper.rb', __FILE__)

module <PERSON>
  describe ManufacturersController, type: :controller do
    include_context 'with logged in galileo admin'

    describe 'GET #index' do
      it "populates an array of manufacturers" do
        manufacturer = create(:manufacturer)
        get :index, use_route: 'galileo'
        expect(assigns(:manufacturers)).to eq([manufacturer])
      end

      it "renders the :index view" do
        get :index, use_route: 'galileo'
        expect(response).to render_template("index")
        expect(response.status).to eq(200)
      end
    end

    describe 'GET to #new' do
      before { get :new, use_route: 'galileo' }
      it { expect(response.status).to eq(200) }
    end

    describe 'GET to #edit' do
      let(:manufacturer) { create(:manufacturer) }
      it { expect(response.status).to eq(200) }
    end
  end
end

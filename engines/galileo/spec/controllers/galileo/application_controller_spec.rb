require File.expand_path('../../../spec_helper.rb', __FILE__)

module <PERSON>
  describe ApplicationController, type: :controller do
    context 'authenticated' do
      include_context 'with logged in galileo admin'

      describe 'GET to #home' do
        before { get :home, use_route: 'galileo' }
        it { expect(response).to redirect_to(manufacturers_url) }
      end
    end
    context 'not authenticated' do
      describe 'GET to #home' do
        before { get :home, use_route: 'galileo' }
        it { expect(response).to redirect_to(new_session_url) }
      end
    end
  end
end

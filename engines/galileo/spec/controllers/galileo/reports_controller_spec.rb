require File.expand_path('../../../spec_helper.rb', __FILE__)

module <PERSON>
  describe ReportsController, type: :controller do
    include_context 'with logged in galileo admin'

    before do
      allow_any_instance_of(Sunspot::Rails::StubSessionProxy::Search).to receive(:stats) do
        OpenStruct.new(count: 50, sum: 5000)
      end
    end

    describe 'GET to #overview' do
      before do
        create(:user)
        create(:order)
        create(:brand)
        get :overview, use_route: 'galileo'
      end
      it { expect(response.status).to eq(200) }
    end

    describe 'GET to #sales' do
      before do
        create(:order)
        get :sales, use_route: 'galileo'
      end
      it { expect(response.status).to eq(200) }
    end
  end
end

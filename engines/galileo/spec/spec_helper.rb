require 'spec_helper'

GALILEO_ROOT = File.join(File.dirname(__FILE__), '../')

# Requires supporting ruby files with custom matchers and macros, etc,
# in spec/support/ and its subdirectories.
Dir[File.join(GALILEO_ROOT, 'spec/support/**/*.rb')].each do |f|
  require f
end

RSpec.configure do |config|
  config.include Galileo::Engine.routes.url_helpers
  Galileo::Engine.routes.default_url_options[:host] = 'test.host'
  config.render_views
end

FactoryGirl.define do
  factory :galileo_admin, class: Galileo::Admin do
    sequence(:email) {|n| "admin#{n}@email.com" }
    password 'nadanada'
    password_confirmation {|u| u.password}
    galilei true
    network 'AR'
  end
end

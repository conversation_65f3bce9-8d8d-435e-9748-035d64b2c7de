module Galileo
  class Shop < ::Mkp::Shop
    def total_paid_orders(start_date, end_date)
      suborders.where('mkp_suborders.created_at BETWEEN ? AND ?', start_date, end_date)
               .count
    end

    def total_sales(start_date, end_date)
        suborders.where('mkp_suborders.created_at BETWEEN ? AND ?', start_date, end_date)
                 .all
                 .reduce(0) { |t, s| s.total + t }
    end

    def total_price
      products.inject(0){ |sum, p| sum + p.price } || 0
    end

    def average_price
      products_count > 0 ? total_price / products_count : 0
    end

    def products_count
      @products_count_cache ||= products.count
    end
  end
end

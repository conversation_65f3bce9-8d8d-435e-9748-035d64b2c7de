module Galileo
  class Manufacturer < ::Mkp::Manufacturer
    include ::Concerns::SoftDestroy

    scope :not_deleted, -> { where(['mkp_manufacturers.deleted_at IS ?', nil]) }
    default_scope { not_deleted }

    def all_products
      Mkp::Product.where(manufacturer_id: self.id)
    end

    def should_generate_new_friendly_id?
      return false if changes.key?(:slug) || !changes.key?(:name)
      super
    end

    # def normalize_friendly_id(string)
    #  In order to override {FriendlyId::Slugged#normalize_friendly_id}
    # end

  end
end

module Galileo
  module Reports
    module Overview

      def self.new_brands_count(network, start_date, end_date)
        Brand.by_network(network)
             .where('users.created_at BETWEEN ? AND ?', start_date, end_date)
             .count
      end

      def self.new_shops_count(network, start_date, end_date)
        Shop.by_network(network)
                 .where('mkp_shops.created_at BETWEEN ? AND ?', start_date, end_date)
                 .count
      end

      def self.shops_with_sales_count(network, start_date, end_date)
        Shop.by_network(network)
            .joins(:suborders)
            .where('mkp_suborders.created_at BETWEEN ? AND ?', start_date, end_date)
            .count
      end

      def self.shops_with_stock_count
        Shop.by_network(network)
            .joins(:products)
            .merge(Produc.with_stock)
            .count
      end

      def self.orders_with_products_on_sale_count(network, start_date, end_date)
        Mkp::Order.by_network(network)
                  .joins([:items, :coupon])
                  .merge(OrderItem.with_items_on_sale)
                  .where('mkp_orders.created_at BETWEEN ? AND ?', start_date, end_date)
                  .count
      end

      def self.active_shops(network, start_date, end_date)
        Shop.by_network(network)
            .joins(:products)
            .merge(Product.with_stock)
            .where('mkp_shops.visible = true')
            .count
      end

      def self.new_brands_count(network, start_date, end_date)
        Brand.by_network(network)
             .where('users.created_at BETWEEN ? AND ?', start_date, end_date)
             .count
      end
    end
  end
end

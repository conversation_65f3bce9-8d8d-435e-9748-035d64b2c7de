;(function(){
  if( !gp.controller.reports ) return

  var $startDate = $('#start_date')
  var $endDate = $('#end_date')

  $startDate.fdatepicker({ format: 'yyyy-mm-dd' })
  $endDate.fdatepicker({ format: 'yyyy-mm-dd' })

  var $startDateText = $('.start-date-text')
  var $endDateText = $('.end-date-text')

  $startDate.on('input changeDate', function(){
    $startDateText.html(moment($startDate.val()).format('LL'))
  })

  $endDate.on('input changeDate', function(){
    $endDateText.html(moment($endDate.val()).format('LL'))
  })

  $startDate.add($endDate).trigger('input')
})()

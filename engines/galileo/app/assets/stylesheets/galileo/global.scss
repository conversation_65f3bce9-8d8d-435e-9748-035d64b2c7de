.ellipsis {
  padding-right: 0.4em;
  white-space: nowrap;
  overflow: hidden !important;
  @include prefixer(text-overflow, ellipsis, o ms spec);
  -moz-binding: url("/xml/ellipsis.xml#ellipsis");
}

$em-base: 16px;

@function emCalc($pxWidth) {
  @return $pxWidth / $em-base * 1em;
}

%header-nav-sprite {
  background-repeat: no-repeat;
  background-image: image-url('header/default-header.png');
}

@mixin header-nav-sprite-icon($type) {
  @extend %header-nav-sprite;
  @if $type == flagUS {
    width: 19px;
    height: 12px;
    background-position: -284px top;
  }
  @if $type == flagAR {
    width: 19px;
    height: 12px;
    background-position: -284px -12px;
  }
  @if $type == flagCL {
    width: 19px;
    height: 12px;
    background-position: -284px -24px;
  }
}
.US { @include header-nav-sprite-icon(flagUS) }
.AR { @include header-nav-sprite-icon(flagAR) }
.CL { @include header-nav-sprite-icon(flagCL) }

!!!
%html
  %head
    %title
      - if content_for? :title
        = yield :title
      - else
        Galileo

    = stylesheet_link_tag 'https://cdn.jsdelivr.net/foundation/5.0.3/css/foundation.min.css'
    = stylesheet_link_tag 'galileo/application'
    = csrf_meta_tags

  %body{ id: body_id }
    = render partial: 'galileo/partials/header'
    %div{ style: 'height:17px' } &nbsp;
    = render partial: 'galileo/partials/flash_msgs'

    .row
      .small-12.columns
        = yield

    - if Rails.env.development?
      = javascript_include_tag 'galileo/base'
    - else
      = javascript_include_tag 'https://cdn.jsdelivr.net/modernizr/2.7.1/modernizr.min.js'
      = javascript_include_tag 'https://cdn.jsdelivr.net/jquery/2.1.0/jquery.min.js'
      = javascript_include_tag 'https://cdn.jsdelivr.net/foundation/5.0.3/js/foundation.min.js'
    = javascript_include_tag 'galileo/application'

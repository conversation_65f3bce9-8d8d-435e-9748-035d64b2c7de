= content_for :title do
  Galileo - Products

.row
  .small-12.large-6.columns
    %h4 Products

.row
  .small-12.columns
    = page_entries_info(@products)
    = will_paginate(@products, class: 'right')
    %br
    %br
.row
  .small-12.columns
    - @products.each do |product, quantity|
      .panel.radius{ 'data-id' => product.id }
        .row.collapse
          .small-12.large-9.columns
            %h5.subheader{ style: 'margin:0;' }
              = product.title
              &nbsp;
              %small Brand: <strong>#{product.manufacturer.name}</strong>
              &nbsp;
              %small Created: <strong>#{product.created_at.to_formatted_s(:long_ordinal)}</strong>
              - if quantity
                &nbsp;
                %small Quantity: <strong>#{quantity}</strong>
.row
  .small-12.columns
    = will_paginate(@products, class: 'right')

= content_for :title do
  Galileo - Sales

.row
  .small-12.large-6.large-centered.columns
    %h3 Sales

= render partial: 'reports_date_selector'

.row
  .small-12.large-6.large-centered.columns
    %dl.sub-nav
      %dd{class: @network.blank? && 'active'}
        %a{href: sales_url(start_date: @start_date, end_date: @end_date)} Global
      - Network.all.each do |network|
        %dd{class: @network == network && 'active' }
          %a{href: sales_url(network: network, start_date: @start_date, end_date: @end_date)}
            %span.network{class: network}
            = network
.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        %tr
          %th Shop
          %th # Sales
          %th Total Sales
      %tbody
        - @shops.each do |shop|
          %tr
            %td= shop.title
            %td= shop.total_paid_orders(@start_date, @end_date)
            %td= number_to_currency(shop.total_sales(@start_date, @end_date))
      = page_entries_info(@shops)
      = will_paginate(@shops, class: 'right')

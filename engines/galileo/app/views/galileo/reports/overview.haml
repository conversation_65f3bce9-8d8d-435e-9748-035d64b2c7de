= content_for :title do
  Galileo - Reports

= render partial: 'reports_date_selector'

.row
  .small-12.large-6.large-centered.columns
    %dl.sub-nav
      %dd{class: @network.blank? && 'active'}
        %a{href: reports_url(start_date: @start_date, end_date: @end_date)} Global
      - Network.all.each do |network|
        %dd{class: @network == network && 'active' }
          %a{href: reports_url(network: network, start_date: @start_date, end_date: @end_date)}
            %span.network{class: network}
            = network

.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        Orders
      %tr
        %td
          %a.has-tip{ "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Orders without Cancelled" }
            Amount of Orders
        %td.total
          = @not_cancelled_orders_count
      %tr
        %td
          %a.has-tip{ "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Cancelled Orders" }
            Cancelled Orders
        %td.total
          = @cancelled_orders_count
      %tr
        %td
          %a.has-tip{ "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Paid or Pending for Payment Orders and Total" }
            Total Sales
        %td.total
          = @paid_orders_count
          = "(#{number_to_currency(@total_sales, precision: 2)})" if @network.present?
      - if @network.present?
        %tr
          %td
            %a.has-tip{ "data-tooltip" => true, "aria-haspopup" => "true", title: "Average Order Total" }
              Average Ticket
          %td.total
            = "#{number_to_currency(@average_ticket, precision: 2)}"
      %tr
        %td
          %a.has-tip{ "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Orders with Products on Sale" }
            Orders with Products on Sale
        %td.total
          = @orders_with_products_on_sale_count
          %a{ }
.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        Products
      %tr
        - url = products_url(sold: true, network: @network, start_date: @start_date, end_date: @end_date)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Products Sold" }
            Products Sold
        %td.total
          = @products_sold_count
          %a{ href: url }
            %small (Details)
      %tr
        - url = products_url(on_sale: true, network: @network, start_date: @start_date, end_date: @end_date)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Number of Products on Sale (Not SKU)" }
            * Products On Sale
        %td.total
          = @products_on_sale_count
          %a{ href: url }
            %small (Details)
      %tr
        - url = products_url(online: true, network: @network, start_date: @start_date, end_date: @end_date)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Number of Products with Stock (Not SKU)" }
            * Products Online
        %td.total
          = @products_online_count
          %a{href: url}
            %small (Details)
      %tr
        - url = products_url(network: @network, start_date: @start_date, end_date: @end_date, uploaded: true)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Number of New Products in Stock (Not SKU)" }
            * Products Uploaded
        %td.total
          = @products_uploaded_count
          %a{ href: url }
            %small (Details)
.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        Shops
      %tr
        - url = shops_url(network: @network, start_date: @start_date, end_date: @end_date, new_shops: true)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Number of New Shops" }
            New Shops
        %td.total
          = @new_shops_count
          %a{ href: url }
            %small (Details)
      %tr
        - url = shops_url(network: @network, start_date: @start_date, end_date: @end_date, with_sales: true)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Shops with Sales" }
            Shops with Sales
        %td.total
          = @shops_with_sales_count
          %a{ href: url }
            %small (Details)
      %tr
        - url = shops_url(network: @network, start_date: @start_date, end_date: @end_date, active: true)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Visible Shops with Products in Stock" }
            * Active Shops
        %td.total
          = @active_shops
          %a{ href: url }
            %small (Details)
      %tr
        - url = brands_url(network: @network, start_date: @start_date, end_date: @end_date)
        %td
          %a{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Number of New Brands" }
            New Brands
        %td.total
          = @new_brands_count
          %a{ href: url }
            %small (Details)
.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        Users
      %tr
        - url = users_url(new_users: true, network: @network, start_date: @start_date, end_date: @end_date)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of New Users" }
            New Users
        %td.total
          = @new_users_count
          %a{ href: url }
            %small (Details)
      %tr
        - url = users_url(network: @network, end_date: @end_date)
        %td
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Registered Users" }
            Registered Users
        %td.total
          = @registered_users_count
          %a{ href: url }
            %small (Details)
      %tr
        %td
          - url = users_url(network: @network, active: true, start_date: @start_date, end_date: @end_date)
          %a.has-tip{ href: url, "data-tooltip" => true, "aria-haspopup" => "true", title: "Amount of Users logged in at least one on the time frame" }
            Active Users
        %td.total
          = @active_users_count
          %a{ href: url }
            %small (Details)
.row
  .small-12.large-7.large-centered.columns
    %p.report-caption
      * Metrics without history, wont be accurate on time frames.


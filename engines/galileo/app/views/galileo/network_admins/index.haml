.row
  .small-12.columns
    %h3 Network Admins

.row
  .small-12.columns

    %table{style: 'width: 100%;'}
      %thead
        %tr
          %th Id
          %th Full Name
          %th Email
          %th Network
          %th Be Notified?
          %th Galileo?
          %th Created At
          %th Actions

      %tbody
        - @network_admins.each do |network_admin|
          %tr
            %td= network_admin.id
            %td= network_admin.full_name
            %td= network_admin.email
            %td= network_admin.network
            %td= 'Yes' if network_admin.be_notified
            %td= network_admin.galilei ? 'Yes' : 'No'
            %td= network_admin.created_at.to_formatted_s(:long_ordinal)
            %td
              - if not network_admin.galilei
                = link_to 'Delete', network_admin_url(network_admin), :confirm => 'Are you sure?', method: :delete

%br
%hr
.row
  .small-12.large-6.columns
    %a{class: 'button small new-network-admin'} Add +
    = form_for @new_network_admin, url:  network_admins_url do |f|
      %fieldset
        %legend New Network Admin

        = f.text_field :first_name, placeholder: 'First Name'
        = f.text_field :last_name, placeholder: 'Last Name'
        = f.text_field :email, placeholder: 'Email'
        = f.password_field :password, placeholder: 'Password'
        = f.password_field :password_confirmation, placeholder: 'Password Confirmation'
        = f.select :network, options_for_select(Network.all.map { |symbol|  [ Network[symbol].title,  symbol ] })
        %a.button.alert.small.cancel Cancel
        = f.submit 'Create', class: 'right button small success'

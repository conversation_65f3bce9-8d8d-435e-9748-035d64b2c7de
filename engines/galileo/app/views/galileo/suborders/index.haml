= content_for :title do
  Galileo - Suborders

.row
  .small-12.large-6.large-centered.columns
    %h3 Suborders

.row
  .small-12.large-7.large-centered.columns
    %table.full-width
      %thead
        %tr
          %th Shop
          %th # Sales
          %th Total Sales
      %tbody
        - @suborders.each do |suborder|
          %tr
            %td= suborder.title
      = page_entries_info(@suborders)
      = will_paginate(@suborders, class: 'right')

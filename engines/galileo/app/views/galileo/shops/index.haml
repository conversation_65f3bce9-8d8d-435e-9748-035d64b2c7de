= content_for :title do
  Galileo - Shops

.row
  .small-12.large-6.columns
    %h4 Shops

.row
  .small-12.columns
    = page_entries_info(@users)
    = will_paginate(@users, class: 'right')
    %br
    %br
.row
  .small-12.columns
    - @shops.each do |shop|
      .panel.radius{ 'data-id' => shop.id }
        .row.collapse
          .small-12.large-9.columns
            %h5.subheader{ style: 'margin:0;' }
              = shop.title
              &nbsp;
              %small Created: <strong>#{shop.created_at.to_formatted_s(:long_ordinal)}</strong>
              %small | Products: <strong>#{shop.products_count}</strong>
              %small | Average: <strong>#{number_to_currency(shop.average_price)}</strong>
              %small | Total: <strong>#{number_to_currency(shop.total_price)}</strong>
        .row.collapse
          .small-12.large-9.columns
            %h5.subheader{ style: 'margin:0;' }
              - if shop.monthly_fee.present?
                %small Monthly Fee: <strong>#{number_to_currency(shop.monthly_fee, presicion: 2, separator: '.', delimiter: ',')}</strong>
              - if shop.sale_commission.present?
                %small #{shop.monthly_fee.present? ? '|' : ''} Sale Commission: <strong>#{shop.sale_commission}%</strong>
.row
  .small-12.columns
    = will_paginate(@users, class: 'right')

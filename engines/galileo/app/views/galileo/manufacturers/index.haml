= content_for :title do
  Galileo - Manufacturers

.row
  .small-12.columns
    .clearfix
    .left
      %h3 Manufacturers
    .right
      %a.button.small.alert.radius{href: new_manufacturer_url} Add +
.row
  - @manufacturers.each do |manufacturer|
    .large-4.medium-6.small-3.columns
      .panel.clearfix
        %h5.left
          - if manufacturer.logo.present?
            %a{href: edit_manufacturer_url(manufacturer.to_param)}
              = image_tag manufacturer.logo(:st), class: 'logo'
          %a.name{href: edit_manufacturer_url(manufacturer.to_param)}= manufacturer.name

        %span.right
          %button.label.radius prods ##{manufacturer.products_count}

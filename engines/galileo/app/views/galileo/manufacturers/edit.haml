= content_for :title do
  Galileo - #{@manufacturer.name}

.row
  .medium-6.large-4.columns
    %h3.left
      = image_tag @manufacturer.logo.url(:st) if @manufacturer.logo.present?
      #{@manufacturer.name}
    - if @manufacturer.persisted?
      = form_tag manufacturer_url(@manufacturer.to_param), method: 'delete' do
        = hidden_field_tag '_method', 'delete'
        %button.button.tiny.alert.radius.right Drop

    = render partial: 'form', locals: {action: 'Update', url: manufacturer_url(@manufacturer)}

%hr
%h4 Related Brands
.brands.row
  - Network.all_active.each do |network|
    - if brand = @manufacturer.related_brand(network).presence
      .small-12.medium-6.columns
        .panel.callout.radius.clearfix
          .network{class: network}
          %br
          .brand
            %a{href: "/#{brand.login}"}
              = image_tag brand.avatar.url(:t) if brand.avatar
            %h4
              %a{href: "/#{brand.login}"}= brand.profile.name
          %span
            = button_to 'Copy logo', assign_logo_manufacturer_url(@manufacturer, brand_id: brand.to_param), method: :put, class: 'button alert tiny right'
%hr
%h4 Products(#{@manufacturer.products.count})
.row
  - @manufacturer.products.each do |product|
    .product
      .large-4.medium-6.small-12.columns
        .panel.clearfix
          = image_tag product.picture.url(:t), class: 'right' if product.picture
          %h5.ellipsis
            %a{href: product.variants.first && product.variants.first.get_url, target: '_blank'}= product.title
          %h4
            .network{class: product.shop.network}
            .shop-title
              %small
                Shop:
                %em= product.shop.title
            .price
              %small #{number_to_currency product.price}

= content_for :title do
  Galileo - Users

.row
  .small-12.columns
    = page_entries_info(@users, model: 'User')
    = will_paginate(@users, class: 'right')

    %br
    %br

    - @users.each do |user|
      .panel.radius
        %h5.subheader
          = user.full_name
          %small
            %a{ href: "#profiles_url", target: '_blank' } @#{user.login}
          %small &nbsp;|&nbsp;
          %small ID: #{user.id}
          %small &nbsp;|&nbsp;
          %small Joined: #{user.created_at.to_formatted_s(:long_ordinal)}

    = will_paginate(@users, class: 'right')

= content_for :title do
  Galileo - Brands

.row
  .small-12.columns
    - @brands.each do |brand|
      .panel.radius
        .row
          .small-12.large-2.columns
            = render partial: 'social/partials/user_avatar', locals: { user: brand }
          .small-12.large-9.columns
            %h5.subheader
              = brand.full_name
              %small
                %a{ href: "#profiles_url", target: '_blank' } @#{brand.login}
              %small &nbsp;|&nbsp;
              %small id: #{brand.id}
              %small &nbsp;|&nbsp;
              %small Joined: #{brand.created_at.to_formatted_s(:long_ordinal)}
            .manufacturer
              - if brand.profile.manufacturer
                Manufacturer:
                - if brand.profile.manufacturer.logo.file?
                  - manufacturer_logo = brand.profile.manufacturer.logo.url(:lt)
                  = render partial: 'partials/hexagon',
                           locals: { shape: :square,
                                     type: 'mfr-logo',
                                     img: manufacturer_logo,
                                     link: main_app.mkp_variants_url(b: brand.profile.manufacturer.to_param) }
                %span.subheader= brand.profile.manufacturer.name

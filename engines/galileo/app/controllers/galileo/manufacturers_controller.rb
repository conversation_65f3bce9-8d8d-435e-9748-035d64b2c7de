module Galileo
  class ManufacturersController < Galileo::ApplicationController
    def assign_logo
      entity = if params[:brand_id]
        Brand.find(params[:brand_id])
      end
      manufacturer = Manufacturer.find(params[:id])
      manufacturer.assign_logo_from_url(entity.avatar.url(:original))
      if manufacturer.save
        redirect_to edit_manufacturer_url(manufacturer),
          notice: 'Manufacturer Logo udpated correctly'
      else
        redirect_to edit_manufacturer_url(manufacturer),
          alert: 'There was an error trying to update logo'
      end
    end

    def create
      @manufacturer = Manufacturer.new(manufacturer_params)
      if @manufacturer.save
        redirect_to manufacturers_url, notice: 'The manufacturer was created successfully'
      else
        render 'new'
      end
    end

    def destroy
      @manufacturer = Manufacturer.find_by(slug: params[:id])
      @manufacturer.destroy
      redirect_to manufacturers_url, notice: 'The manufacturer was deleted successfully'
    end

    def index
      @manufacturers = Galileo::Manufacturer.all
    end

    def new
      @manufacturer = Manufacturer.new
    end

    def edit
      @manufacturer = Manufacturer.find_by_slug(params[:id])
    end

    def update
      @manufacturer = Manufacturer.find_by_slug(params[:id])
      if @manufacturer.update_attributes(manufacturer_params)
        redirect_to manufacturers_url, notice: 'The manufacturer was udpated successfully'
      else
        render 'edit'
      end
    end

    protected
    def manufacturer_params
      params.require(:manufacturer).permit(:logo, :name, :slug, :sku_id)
    end
  end
end

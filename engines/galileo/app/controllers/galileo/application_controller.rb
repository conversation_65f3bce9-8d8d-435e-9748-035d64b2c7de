module Galileo
  class ApplicationController < ActionController::Base
    force_ssl if SSL_ENABLED

    helper :all

    protect_from_forgery with: :exception
    
    before_filter :store_return_to_location,
                  :require_admin

    helper_method :current_admin_session, :current_admin

    def home
      redirect_to manufacturers_url
    end

    private

    def require_admin
      redirect_to(new_session_path) if current_admin.nil?
    end

    def require_no_admin
      redirect_to(home_url) if current_admin.present?
    end

    def current_admin_session
      return @current_admin_session if defined?(@current_admin_session)
      @current_admin_session = Session.find
    end


    def current_admin
      return @current_admin if defined?(@current_admin)
      @current_admin = current_admin_session && current_admin_session.admin
    end

    private

    def store_return_to_location
      if !current_admin.present? && request.get? && !request.xhr?
        session[:return_to] = request.url
      end
    end

  end
end

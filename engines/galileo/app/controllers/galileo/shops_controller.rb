module Galileo
  class ShopsController < Galileo::ApplicationController
    def index
      @shops = Shop.by_network(params[:network])
                   .order('mkp_shops.created_at DESC')
                   .paginate(page: params[:page])
      if params[:new_shops].present?
        @shops.where('mkp_shops.created_at BETWEEN ? AND ?', params[:start_date], params[:end_date])
      elsif params[:active].present?
        @shops = Shop.by_network(params[:network])
                     .merge(Product.with_stock)
                     .where('mkp_shops.visible = true')
                     .paginate(page: params[:page])
      elsif params[:with_sales].present?
        @shops = @shops.includes(:suborders)
        if params[:start_date].present? && params[:end_date].present?
          @shops = @shops.where('mkp_suborders.created_at BETWEEN ? AND ?', params[:start_date], params[:end_date])
        end
      elsif params[:start_date].present? && params[:end_date].present?
        @shops = @shops.where('mkp_shops.created_at BETWEEN ? AND ?', params[:start_date], params[:end_date])
      end
    end
  end
end

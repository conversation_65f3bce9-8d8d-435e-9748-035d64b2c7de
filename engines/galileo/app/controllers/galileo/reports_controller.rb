module Galileo
  class ReportsController < Galileo::ApplicationController
    before_filter :load_dates
    before_filter :load_network
    before_filter :paid_orders_search

    def overview
      @new_users_count = new_users_count
      @registered_users_count = registered_users_count
      @active_users_count = active_users_count
      @new_brands_count = Reports::Overview.new_brands_count(@network, @start_date, @end_date)
      @new_shops_count = Reports::Overview.new_shops_count(@network, @start_date, @end_date)
      @shops_with_sales_count = Reports::Overview.shops_with_sales_count(@network, @start_date, @end_date)
      @products_online_count = products_online_count
      @products_on_sale_count = products_on_sale_count
      @products_sold_count = products_sold_count
      @products_uploaded_count = products_uploaded_count
      @cancelled_orders_count = cancelled_orders_count
      @not_cancelled_orders_count = not_cancelled_orders_count
      @paid_orders_count = paid_orders_count
      @total_sales = total_sales
      @average_ticket = @paid_orders_count > 0 ? @total_sales / @paid_orders_count : 0
      @orders_with_products_on_sale_count = Reports::Overview.orders_with_products_on_sale_count(@network, @start_date, @end_date)
      @active_shops = Reports::Overview.active_shops(@network, @start_date, @end_date)
      @new_brands_count = Reports::Overview.new_brands_count(@network, @start_date, @end_date)
    end

    def sales
      @shops = Shop.by_network(@network)
                   .includes('suborders')
                   .where('mkp_suborders.created_at BETWEEN ? AND ?', @start_date, @end_date)
                   .select('mkp_shops.*, count(mkp_suborders.id) as sales_count')
                   .group('mkp_shops.id')
                   .order('count(mkp_suborders.id) desc')
                   .paginate(page: params[:page])
    end

    private

    def load_dates
      @start_date = (params[:start_date] && params[:start_date].to_datetime.beginning_of_day) || Date.today.beginning_of_month
      @end_date = (params[:end_date] && params[:end_date].to_datetime.end_of_day) || Date.today.to_datetime.end_of_day
    end

    def load_network
      @network = params[:network]
    end

    def paid_orders_search
      network = @network
      start_date = @start_date
      end_date = @end_date

      @paid_orders_search = Mkp::Order.search(include: order_includes) do
        with :created_at, start_date..end_date
        with :network, network unless network.blank?
        with :payment_status, ['collected']
        stats :total
      end
    end

    def products_uploaded_count
      start_date = @start_date
      end_date = @end_date

      search = Mkp::Product.search(include: [:shop]) do
        with :network, params[:network] if params[:network].present?
        with(:available_on).less_than(end_date)
        all_of do
          with(:created_at, start_date..end_date)
          any_of do
            with :deleted_at, nil
            with(:deleted_at).greater_than(end_date)
          end
        end
      end

      search.total
    end

    def products_online_count
      search = Mkp::Product.search(include: [:shop]) do
        with :network, params[:network] if params[:network].present?
        with :with_stock, true
      end

      search.total
    end

    def products_on_sale_count
      search = Mkp::Product.search(include: [:shop]) do
        with :network, params[:network] if params[:network].present?
        with :with_stock, true
        with :on_sale, true
      end

      search.total
    end

    def products_sold_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = Mkp::Suborder.search(include: [:shop, :products]) do
        with :network, network unless network.blank?
        with :created_at, start_date..end_date
        without :paid_at, nil
        stats :product_ids
      end

      (search.stats(:product_ids).count if search.hits.length > 0) || 0
    end

    def paid_orders_count
      @paid_orders_search.total
    end

    def total_sales
      (@paid_orders_search.stats(:total).sum if @paid_orders_search.hits.length > 0) || 0
    end

    def cancelled_orders_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = Mkp::Order.search(include: [suborders: [:shop]]) do
        with :created_at, start_date..end_date
        with :payment_status, ['cancelled']
        with :shipment_status, ['cancelled']
        with :network, network unless network.blank?
      end

      search.total
    end

    def not_cancelled_orders_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = Mkp::Order.search(include: [suborders: [:shop]]) do
        with :created_at, start_date..end_date
        with :network, network unless network.blank?
        without :payment_status, ['cancelled']
        without :shipment_status, ['cancelled']
      end

      search.total
    end

    def new_users_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = User.search do
        with :created_at, start_date..end_date
        with :network, network unless network.blank?
      end

      search.total
    end

    def registered_users_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = User.search do
        with(:created_at).less_than(end_date)
        with :network, network unless network.blank?
      end

      search.total
    end

    def active_users_count
      start_date = @start_date
      end_date = @end_date
      network = @network

      search = User.search do
        with :last_request_at, start_date..end_date
        with :network, network unless network.blank?
      end

      search.total
    end

    def order_includes
      [:payment, suborders: [:items, :shipment, :shop]]
    end
  end
end

require 'will_paginate/array'

module Galileo
  class ProductsController < Galileo::ApplicationController
    def index
      load_dates
      get_reports_products
    end

  private

    def load_dates
      @start_date = (params[:start_date] && params[:start_date].to_datetime.beginning_of_day) || Date.today.beginning_of_month
      @end_date = (params[:end_date] && params[:end_date].to_datetime.end_of_day) || Date.today.to_datetime.end_of_day
    end

    def get_reports_products
      if params[:sold].present?
        get_products_sold
      else
        start_date = @start_date
        end_date = @end_date
        search = Mkp::Product.search(include: [:shop]) do
          with :network, params[:network] if params[:network].present?

          if params[:uploaded].present?
            with(:available_on).less_than(end_date)
            all_of do
              unless start_date.nil?
                with(:created_at, start_date..end_date)
              end
              any_of do
                with :deleted_at, nil
                with(:deleted_at).greater_than(end_date)
              end
            end
          end

          if params[:online].present?
            with :with_stock, true
          end

          if params[:on_sale].present?
            with :with_stock, true
            with :on_sale, true
          end
          paginate page: (params[:page] || 1), per_page: 25
        end
        @products = search.results
      end
    end

    def get_products_sold
      start_date = @start_date
      end_date = @end_date
      search = Mkp::Suborder.search(include: [:products]) do
        with :network, params[:network] if params[:network].present?
        with(:paid_at).less_than(end_date)
        with :created_at, start_date..end_date
        facet :product_ids, :limit => -1
      end
      @products = search.facet(:product_ids).rows.map do |row|
        [row.instance, row.count]
      end
      @products = @products.paginate(page: params[:page], per_page: 25)
    end
  end
end

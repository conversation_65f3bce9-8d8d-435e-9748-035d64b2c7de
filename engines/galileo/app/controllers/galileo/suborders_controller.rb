module Galileo
  class SubordersController < Galileo::ApplicationController
    before_filter :load_dates
    before_filter :load_network

    def index
      @suborders = if params.key?(:cancelled) && params.key?(:start_date) && params.key?(:end_date)
        Suborder.cancelled
                .includes(:shop)
                .merge(Shop.by_network(params[:network]))
                .where('mkp_suborders.created_at BETWEEN ? AND ?', params[:start_date], params[:end_date])
                .paginate(page: params[:page])
      elsif params.key?(:network) && params.key?(:start_date) && params.key?(:end_date)
        Suborder.not_cancelled_orders(params[:network],params[:start_date],params[:end_date])
                .paginate(page: params[:page])
      else
        Suborder.includes(:shop)
                .merge(Shop.by_network(params[:network]))
                .where('mkp_suborders.created_at BETWEEN ? AND ?', params[:start_date], params[:end_date])
                .paginate(page: params[:page])
      end
    end

    private

    def load_dates
      @start_date = (params[:start_date] && Date.parse(params[:start_date])) || Date.today.beginning_of_month
      @end_date = (params[:end_date] && Date.parse(params[:end_date])) || Date.today
    end

    def load_network
      @network = params[:network]
    end
  end
end

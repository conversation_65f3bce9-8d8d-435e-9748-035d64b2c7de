module Galileo
  class NetworkAdminsController < Galileo::ApplicationController
    def create
      @new_network_admin = Pioneer::Admin.new admin_params
      if @new_network_admin.save
        redirect_to network_admins_url, notice: 'Network Admin created successful'
      else
        @network_admins = Pioneer::Admin.all
        render 'index'
      end
    end

    def destroy
      Pioneer::Admin.find(params[:id]).destroy
      redirect_to network_admins_url, notice: 'Network Admin deleted properly'
    end

    def index
      @new_network_admin = Pioneer::Admin.new
      @network_admins    = Pioneer::Admin.all
    end

    protected

    def admin_params
      params.require(:admin).permit(:email,
                                    :first_name,
                                    :last_name,
                                    :password,
                                    :password_confirmation,
                                    :network)
    end
  end
end

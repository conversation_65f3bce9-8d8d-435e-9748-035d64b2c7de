require_dependency 'galileo/application_controller'

module Galileo
  class SessionsController < Galileo::ApplicationController
    before_filter :require_admin, only: :destroy
    before_filter :require_no_admin, only: [:create, :new]

    skip_before_filter :store_return_to_location

    def new
      @session = Session.new
    end

    def create
      @session = Session.new(session_params.to_h)
      if @session.save
        redirect_to_previous_location
      else
        render action: :new
      end
    end

    def destroy
      current_admin_session.destroy
      redirect_to home_url
    end

    protected
    def redirect_to_previous_location
      if session[:return_to].present?
        redirect_to session[:return_to]
      else
        redirect_to home_url
      end
    end

    private

    def session_params
      params.require(:session).permit(:email, :password)
    end

  end
end

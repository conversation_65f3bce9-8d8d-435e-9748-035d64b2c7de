module Galileo
  class UsersController < Galileo::ApplicationController

    def index
      load_dates
      @users = User.by_network(params[:network])
                   .order('created_at DESC')
                   .paginate(page: params[:page])

      if params.key?(:aggregated)
        @users = @users.with_aggregated_profiles
      end


      if params.key?(:active)
        @users = @users.where('last_request_at BETWEEN ? AND ?', @start_date, @end_date)
      elsif params.key?(:new_users)
        @users = @users.where('created_at BETWEEN ? AND ?', @start_date, @end_date)
      end
    end

    private

    def load_dates
      @start_date = (params[:start_date] && params[:start_date].to_datetime.beginning_of_day) || Date.today.beginning_of_month
      @end_date = (params[:end_date] && params[:end_date].to_datetime.end_of_day) || Date.today.to_datetime.end_of_day
    end
  end
end

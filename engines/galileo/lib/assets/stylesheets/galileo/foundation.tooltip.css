/* Tooltips */
.has-tip {
  border-bottom: dotted 1px #CCCCCC;
  color: #008cba;
  cursor: help;
  font-weight: normal; }
  .has-tip:hover, .has-tip:focus {
    border-bottom: dotted 1px #134b5b; }
  .has-tip.tip-left, .has-tip.tip-right {
    float: none !important; }

.tooltip {
  background: #333333;
  color: #FFFFFF;
  display: none;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.3;
  max-width: 300px;
  padding: 0.75rem;
  position: absolute;
  width: 100%;
  z-index: 1006;
  left: 50%; }
  .tooltip > .nub {
    border-color: transparent transparent #333333 transparent;
    border: solid 5px;
    display: block;
    height: 0;
    pointer-events: none;
    position: absolute;
    top: -10px;
    width: 0;
    left: 5px; }
    .tooltip > .nub.rtl {
      left: auto;
      right: 5px; }
  .tooltip.radius {
    border-radius: 3px; }
  .tooltip.round {
    border-radius: 1000px; }
    .tooltip.round > .nub {
      left: 2rem; }
  .tooltip.opened {
    border-bottom: dotted 1px #134b5b !important;
    color: #2ba6cb !important; }

.tap-to-close {
  color: #777777;
  display: block;
  font-size: 0.625rem;
  font-weight: normal; }

@media only screen and (min-width: 40.0625em) {
  .tooltip > .nub {
    border-color: transparent transparent #333333 transparent;
    top: -10px; }
  .tooltip.tip-top > .nub {
    border-color: #333333 transparent transparent transparent;
    bottom: -10px;
    top: auto; }
  .tooltip.tip-left, .tooltip.tip-right {
    float: none !important; }
  .tooltip.tip-left > .nub {
    border-color: transparent transparent transparent #333333;
    left: auto;
    margin-top: -5px;
    right: -10px;
    top: 50%; }
  .tooltip.tip-right > .nub {
    border-color: transparent #333333 transparent transparent;
    left: -10px;
    margin-top: -5px;
    right: auto;
    top: 50%; } }

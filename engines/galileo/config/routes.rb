Galileo::Engine.routes.draw do
  root to: 'application#home', as: :home

  match 'logout' => "sessions#destroy", as: :logout, via: [:get, :post, :options]
  resources :sessions

  resources :brands, only: [:index]
  get 'reports', to: 'reports#overview', as: :reports
  get 'sales', to: 'reports#sales', as: :sales
  resources :manufacturers, except: [:show] do
    put 'assign_logo', on: :member
  end
  resources :products, only: [:index]
  resources :shops, only: [:index]
  resources :users, only: [:index]
  resources :network_admins, only: [:create, :destroy, :edit, :index]
  resources :suborders, only: [:index]
end

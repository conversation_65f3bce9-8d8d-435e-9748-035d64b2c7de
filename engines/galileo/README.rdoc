= Galileo

The God Admin Panel

== Steps to initialize the engine

Set the repository and <PERSON><PERSON><PERSON> to grab the repo from the local directory instead of going to github.

===== Standing on the directory of the Rails app run this commands:
  mkdir engines
  cd engines
  <NAME_EMAIL>:goodpeople/galileo.git
  cd ..
  bundle config local.galileo engines/galileo



And now you can run +bundle+ and in development would use the local repository instead the one in github

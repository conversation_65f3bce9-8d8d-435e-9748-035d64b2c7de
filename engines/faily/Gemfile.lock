PATH
  remote: .
  specs:
    faily (0.0.1)
      bootstrap-sass
      chart-js-rails
      rails (~> 4.2.10)
      sass-rails

GEM
  remote: https://rubygems.org/
  specs:
    actionmailer (4.2.11.3)
      actionpack (= 4.2.11.3)
      actionview (= 4.2.11.3)
      activejob (= 4.2.11.3)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (4.2.11.3)
      actionview (= 4.2.11.3)
      activesupport (= 4.2.11.3)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (4.2.11.3)
      activesupport (= 4.2.11.3)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (4.2.11.3)
      activesupport (= 4.2.11.3)
      globalid (>= 0.3.0)
    activemodel (4.2.11.3)
      activesupport (= 4.2.11.3)
      builder (~> 3.1)
    activerecord (4.2.11.3)
      activemodel (= 4.2.11.3)
      activesupport (= 4.2.11.3)
      arel (~> 6.0)
    activesupport (4.2.11.3)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    arel (6.0.4)
    autoprefixer-rails (9.8.6.1)
      execjs
    bootstrap-sass (3.4.1)
      autoprefixer-rails (>= 5.2.1)
      sassc (>= 2.0.0)
    builder (3.2.4)
    chart-js-rails (0.1.7)
      railties (> 3.1)
    concurrent-ruby (1.1.6)
    crass (1.0.6)
    erubis (2.7.0)
    execjs (2.7.0)
    ffi (1.13.1)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    loofah (2.6.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mini_mime (1.0.2)
    mini_portile2 (2.4.0)
    minitest (5.14.1)
    nokogiri (1.10.10)
      mini_portile2 (~> 2.4.0)
    rack (1.6.13)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (4.2.11.3)
      actionmailer (= 4.2.11.3)
      actionpack (= 4.2.11.3)
      actionview (= 4.2.11.3)
      activejob (= 4.2.11.3)
      activemodel (= 4.2.11.3)
      activerecord (= 4.2.11.3)
      activesupport (= 4.2.11.3)
      bundler (>= 1.3.0, < 2.0)
      railties (= 4.2.11.3)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.3)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    railties (4.2.11.3)
      actionpack (= 4.2.11.3)
      activesupport (= 4.2.11.3)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (12.3.3)
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (2.3.3)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      sprockets (>= 2.8, < 4.0)
    thor (0.20.3)
    thread_safe (0.3.6)
    tilt (2.0.10)
    tzinfo (1.2.7)
      thread_safe (~> 0.1)

PLATFORMS
  ruby

DEPENDENCIES
  faily!

BUNDLED WITH
   1.17.3

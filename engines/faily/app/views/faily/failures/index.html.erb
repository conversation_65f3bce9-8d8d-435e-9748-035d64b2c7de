<h2>Failures</h2>
<br>
<%= render 'filters' %>
<br>
<table>
  <tr>
    <th>occurrence</th>
    <th>exception</th>
    <th>last occurrence</th>
    <th></th>
  </tr>
  <% @entities_to_show.each do |hash| %>
    <tr>
      <td><%= hash[:occurrence] %></td>
      <td><%= hash[:exception] %></td>
      <td><%= hash[:last_occurrence_date] %></td>
      <td width="100px"><%= link_to "<i class=\"fa fa-eye\"></i>".html_safe, failure_path(hash[:last_occurrence_id]), {:method => :get, :class => "btn btn-success", :type => "button", :style => "margin: 5px;"} %></td>
    </tr>
  <% end %>
</table>


module Faily
  class Failure < ActiveRecord::Base
    scope :with_created_from, ->(created_from) { where('faily_failures.created_at >= ?', "#{created_from} 00:00:00") }
    scope :with_created_to, ->(created_to) { where('faily_failures.created_at <= ?', "#{created_to} 23:59:59") }
    scope :search_query, ->(query) do
      query = query.to_s
      return if query.blank?
      where("faily_failures.exception like '%#{query}%'")
    end

    def backtrace
      read_attribute(:backtrace).split('|')
    end
  end
end

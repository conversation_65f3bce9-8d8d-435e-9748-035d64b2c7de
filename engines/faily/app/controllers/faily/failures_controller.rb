require_dependency "faily/application_controller"

module Faily
  class FailuresController < ApplicationController
    before_filter :set_filter_values, only: [:index]
    before_filter :find_entities, only: [:index]

    def index
      grouped_entities = @entities.group_by(&:exception)
      ranked_failures = grouped_entities.keys.dup.sort_by { |failure | grouped_entities[failure].size  }
      @entities_to_show = ranked_failures.reverse.map {|exception|
        sorted_occurrences = grouped_entities[exception].sort_by { |each| each.created_at }
        {
            occurrence: grouped_entities[exception].count,
            exception: exception,
            last_occurrence_date: sorted_occurrences.last.created_at.strftime("%d-%m-%Y - %H:%M:%S"),
            last_occurrence_id: sorted_occurrences.last.id
        }
      }
    end

    def show
      @entity =  entity_class.find(params[:id])
    end

    private

    def entity_class
      Failure
    end

    def set_filter_values
      @created_from = Time.parse(params[:created_from]).strftime("%m-%d-%Y") if params[:created_from].present?
      @created_to = Time.parse(params[:created_to]).strftime("%m-%d-%Y") if params[:created_to].present?
      @search_query = params[:search_query] if params[:search_query].present?
    end

    def find_entities
      @entities = entity_class.all
      @entities = @entities.with_created_from(params[:created_from]) if params[:created_from].present?
      @entities = @entities.with_created_to(params[:created_to]) if params[:created_to].present?
      @entities = @entities.search_query(params[:search_query]) if params[:search_query].present?
      @entities.order('created_at desc')
    end
  end
end

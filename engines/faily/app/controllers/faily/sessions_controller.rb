require_dependency "faily/application_controller"

module Faily
  class SessionsController < ApplicationController
    skip_before_action :authorized, only: [:new, :create, :welcome]

    def new
    end

    def create
      @user = user_class.find_by(email: params[:email])
      if @user && @user.valid_password?(params[:password]) && @user.is_devs?
        session[:user_id] = @user.id
        redirect_to failures_path
      else
        redirect_to login_path
      end
    end

    def login
    end

    def destroy
      session[:user_id] = nil
      redirect_to login_path
    end

    private

    def user_class
      Pioneer::Admin
    end
  end
end

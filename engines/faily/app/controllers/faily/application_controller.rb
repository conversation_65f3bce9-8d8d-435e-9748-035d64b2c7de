module Faily
  class ApplicationController < ActionController::Base
    protect_from_forgery with: :exception

    before_action :authorized

    helper_method :current_user
    helper_method :logged_in?

    def home
      if logged_in?
        redirect_to failures_path
      else
        redirect_to login_path
      end
    end

    def current_user
      Pioneer::Admin.find_by(id: session[:user_id])
    end

    def logged_in?
      !current_user.nil?
    end

    def authorized
      redirect_to login_path unless logged_in?
    end
  end
end

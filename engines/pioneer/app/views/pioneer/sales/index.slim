= content_for :title do
  | Pioneer - Órdenes

.panel.panel-default
  .panel-body.p-0
    .row.p-3
      .col-md-6
        h2 = t('pioneer.orders.title')
    hr
    .mx-5
      = render partial: 'filters'

    .row.pt-5
      .col-md-12.table-responsive
        table.table id="accordion"
          thead
            tr
              th
              th = t('pioneer.orders.id')
              th = t('pioneer.orders.date')
              th = t('pioneer.orders.quantity')
              th = t('pioneer.orders.order-title')
              th = t('pioneer.orders.client')
              th = t('pioneer.orders.store')
              th = t('pioneer.orders.amount')
              th
          tbody
            - @orders.each do |order|
              = render partial: 'sale', locals: { order: order }

    .row.mb-2.p-4
      .col-md-6
        = page_entries_info(@orders)
      .col-md-6.text-right
        = will_paginate(@orders, class: 'pagination')

= render partial: 'shipment_details'
= render partial: 'confirm_return_and_cancel_modal'
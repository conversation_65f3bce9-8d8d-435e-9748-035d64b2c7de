- url ||= ajax_components_path
- id ||= nil
.component.cols1-pics2-v1 id=(id if id.present?) data-component-name='cols1_pics2_v1'
  = form_for component, as: :component, url: url do |component_builder|
    = component_builder.hidden_field :landing_id, value: component.landing.id
    = component_builder.hidden_field :type, value: component.type
    .panel.panel-default
      .panel-heading
        .row
          .col-xs-6
            h4 2 Small banners
          .col-xs-6.text-right.actions
            = component_builder.submit 'Save',class: 'btn btn-primary mr-2'
            - remove_url = component.persisted? ? ajax_component_path(component) : nil
            a.btn.btn-secondary.remove data-action=remove_url
              | Remove
        = render partial: 'pioneer/landings/components/shared/activation_section', locals: { component: component, component_builder: component_builder }
      .panel-body
        .row
          .col-xs-12.col-md-12
            = text_field_tag "component[title]", component[:title], placeholder: "Component title", class: "form-control"
          - component.setup[:items].each_with_index do |item, index|
            - pic = ::Pages::Picture.find_by_id(item[:picture_id])
            .setup.col-md-6.p-5
              = render partial: 'pioneer/landings/components/picture_form', locals: {pic: pic, banner: true}
              = hidden_field_tag :token
              = text_field_tag "component[setup][items][][link]", item[:link], placeholder: 'Link',class: 'form-control'
              = hidden_field_tag "component[setup][items][][picture_id]", item[:picture_id], class: 'desktop_picture_id'

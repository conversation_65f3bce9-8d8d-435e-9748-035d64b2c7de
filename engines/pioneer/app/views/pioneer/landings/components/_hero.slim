- url ||= ajax_components_path
- id ||= nil
.component.hero id=(id if id.present?) data-component-name='hero'
  = form_for component, as: :component, url: url do |component_builder|
    = component_builder.hidden_field :landing_id, value: component.landing.id
    = component_builder.hidden_field :type, value: component.type
    .panel.panel-default
      .panel-heading
        .row
          .col-xs-6
            h4 Hero Component
          .col-xs-6.text-right.actions
            = component_builder.submit 'Save', class:'btn btn-primary mr-2'
            - remove_url = component.persisted? ? ajax_component_path(component) : nil
            a.remove.btn.btn-secondary data-action=remove_url
              | Remove
        = render partial: 'pioneer/landings/components/shared/activation_section', locals: { component: component, component_builder: component_builder }
      .panel-body
        .row
          - component.setup[:items].each_with_index do |item, index|
            - pic = ::Pages::Picture.find_by_id(item[:desktop_picture_id])
            - mobile_pic = ::Pages::Picture.find_by_id(item[:mobile_picture_id])
            .setup.col-xs-12.col-md-4
              = render partial: 'pioneer/landings/components/picture_form', locals: { pic: pic }
              = hidden_field_tag "component[setup][items][][desktop_picture_id]", item[:desktop_picture_id], class: 'desktop_picture_id'

              = render partial: 'pioneer/landings/components/mobile_picture_form', locals: { pic: mobile_pic }
              = hidden_field_tag "component[setup][items][][mobile_picture_id]", item[:mobile_picture_id], class: 'mobile_picture_id'

              = hidden_field_tag :token
              .p-4
                = text_field_tag "component[setup][items][][title]", item[:title], placeholder: 'Name', class:'form-control'
              .p-4
                = text_field_tag "component[setup][items][][link]", item[:link], placeholder: 'Link', class:'form-control'
              .p-4
                = text_area_tag "component[setup][items][][description]", item[:description], placeholder: 'Description', class:'form-control'



module Pioneer
  module Mimoto
    class CustomerReservationPurchasesController < Pioneer::ApplicationController
      before_filter :set_filter_values, only: [:index]
      before_filter :find_reservation, only: [:destroy]
      before_filter :check_ns_role
      before_filter :check_owner_mimoto

      layout 'bootstrap_layout'

      BNA_MIMOTO_ID = 43

      def index
        # @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]
        # se fuerza a reservas de mimoto
        @stores = [Mkp::Store.find(BNA_MIMOTO_ID)]
        @reservations = CustomerReservationPurchases.where(store_id: @stores.map(&:id)).includes(:product).includes(:store)
        @reservations = @reservations.order(created_at: :desc).paginate(page: params[:page])
        @reservations = @reservations.search_query(@search_query) if @search_query.present?
      end


      def destroy
       @reservation.destroy
       redirect_to mimoto_customer_reservation_purchases_url
      end

      private

      def find_reservation
        @reservation = CustomerReservationPurchases.find(params[:id])
      end

      def set_filter_values # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
        @search_query = params[:query] if params[:query].present?
      end

      def check_ns_role
        redirect_to home_url, alert: 'Have not permissions' unless current_user.nacion_servicios_admin? or current_user.has_role?(:administrator)
      end

      def check_owner_mimoto
        redirect_to home_url, alert: 'Have not permissions' unless current_user.is_store_owner?(Mkp::Store.find(BNA_MIMOTO_ID))
      end
    end
  end
end

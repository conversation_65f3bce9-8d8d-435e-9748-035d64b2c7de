on_app_master do

#  if config.environment_name =~ /production/
#    config_file = ["#{config.shared_path}", "config", "hipchat.yml"].join("/")

#    if File.exists? config_file
#      hipchat_options = YAML.load_file(config_file)
#     # room  = hipchat_options['room'] || ''
#     # token = hipchat_options['token'] || ''

#      message  = "#{config.deployed_by} start a deploy to #{config.environment_name}"
#      message += " (with migrations)" if config.migrate?
#      message += " of the branch #{config.input_ref}." unless config.input_ref.nil?

#      # Check if it master
#      if config.input_ref != 'master' &&
#        message  = "ERROR: #{config.deployed_by} start a deploy to #{config.environment_name}"
#        message += " of the branch #{config.input_ref}."
#        #run "cd #{config.previous_release} && bundle exec rake hipchat:send MESSAGE='#{message}' COLOR='red'"
#       # run "curl -s -H 'Content-Type: application/json' \
#       #           -X POST \
#       #           -d '{\"notify\": true, \
#       #                \"color\": \"red\", \
#       #                \"message_format\": \"html\", \
#       #                \"message\": \"#{message}\" }' \
#       #           https://api.hipchat.com/v2/room/#{room}/notification?auth_token=#{token}" unless room.empty? && token.empty?
#       # raise StandardError
#      end

#      # Send a message via rake task assuming a hipchat.yml in your config like above
#      # run "cd #{config.previous_release} && bundle exec rake hipchat:send MESSAGE='#{message}' COLOR='yellow'"
#      #run "curl -s -H 'Content-Type: application/json' \
#      #          -X POST \
#      #          -d '{\"notify\": true, \
#      #               \"color\": \"yellow\", \
#      #               \"message_format\": \"html\", \
#      #               \"message\": \"#{message}\" }' \
#      #          https://api.hipchat.com/v2/room/#{room}/notification?auth_token=#{token}" unless room.empty? && token.empty?
#    end
#  end

#  if config.environment_name =~ /staging/
#    config_file = ["#{config.shared_path}", "config", "hipchat.yml"].join("/")

#    if File.exists? config_file
#      hipchat_options = YAML.load_file(config_file)
#      room  = hipchat_options['room'] || ''
#      token = hipchat_options['token'] || ''

#      message  = "#{config.deployed_by} start a deploy to #{config.environment_name}"
#      message += " (with migrations)" if config.migrate?
#      message += " of the branch #{config.input_ref}." unless config.input_ref.nil?

#      # Send a message via rake task assuming a hipchat.yml in your config like above
#      # run "cd #{config.previous_release} && bundle exec rake hipchat:send MESSAGE='#{message}' COLOR='yellow'"
#      run "curl -s -H 'Content-Type: application/json' \
#                -X POST \
#                -d '{\"notify\": true, \
#                     \"color\": \"purple\", \
#                     \"message_format\": \"html\", \
#                     \"message\": \"#{message}\" }' \
#                https://api.hipchat.com/v2/room/#{room}/notification?auth_token=#{token}" unless room.empty? && token.empty?
#    end
#  end
  if config.environment_name =~ /production/
    #non  here make senses elsewherer!!! mutherfocker
  end

  if config.environment_name =~ /staging/

  end
end

on_app_master do
  if config.environment_name =~ /production/
    config_file = ["#{config.shared_path}", "config", "hipchat.yml"].join("/")

    if File.exists? config_file
      hipchat_options = YAML.load_file(config_file)
      room  = hipchat_options['room'] || ''
      token = hipchat_options['token'] || ''

      message  = "The deploy that #{config.deployed_by} started,"
      message += " of the branch #{config.input_ref unless config.input_ref.nil?}"
      message += " and commit #{config.revision[0...6]}"
      message += " (#{`git log -1 HEAD --pretty=format:%s`}) to #{config.environment_name}"
      message += " (with migrations)" if config.migrate?
      message += " has ended."

      # Send a message via rake task assuming a hipchat.yml in your config like above
      # run "cd #{config.release_path} && bundle exec rake hipchat:send MESSAGE='#{message}' COLOR='green'"
      run "curl -s -H 'Content-Type: application/json' \
                -X POST \
                -d '{\"notify\": true, \
                     \"color\": \"green\", \
                     \"message_format\": \"html\", \
                     \"message\": \"#{message}\" }' \
                https://api.hipchat.com/v2/room/#{room}/notification?auth_token=#{token}" unless room.empty? && token.empty?
    end
  end

  if config.environment_name =~ /staging/
    config_file = ["#{config.shared_path}", "config", "hipchat.yml"].join("/")

    if File.exists? config_file
      hipchat_options = YAML.load_file(config_file)
      room  = hipchat_options['room'] || ''
      token = hipchat_options['token'] || ''

      message  = "The deploy that #{config.deployed_by} started,"
      message += " of the branch #{config.input_ref unless config.input_ref.nil?}"
      message += " and commit #{config.revision[0...6]}"
      message += " (#{`git log -1 HEAD --pretty=format:%s`}) to #{config.environment_name}"
      message += " (with migrations)" if config.migrate?
      message += " has ended."

      # Send a message via rake task assuming a hipchat.yml in your config like above
      # run "cd #{config.release_path} && bundle exec rake hipchat:send MESSAGE='#{message}' COLOR='green'"
      run "curl -s -H 'Content-Type: application/json' \
                -X POST \
                -d '{\"notify\": true, \
                     \"color\": \"purple\", \
                     \"message_format\": \"html\", \
                     \"message\": \"#{message}\" }' \
                https://api.hipchat.com/v2/room/#{room}/notification?auth_token=#{token}" unless room.empty? && token.empty?
    end
  end
end

on_app_servers do
  # Force creation of tmp/cache dir to avoid being created by root
  run "cd #{config.release_path} && mkdir -p tmp/cache/assets"
end
